export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      article_drafts: {
        Row: {
          author_id: string | null
          content: string | null
          created_at: string | null
          id: number
          image_url: string | null
          tags: string[] | null
          title: string | null
          updated_at: string | null
        }
        Insert: {
          author_id?: string | null
          content?: string | null
          created_at?: string | null
          id?: number
          image_url?: string | null
          tags?: string[] | null
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          author_id?: string | null
          content?: string | null
          created_at?: string | null
          id?: number
          image_url?: string | null
          tags?: string[] | null
          title?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "article_drafts_author_id_fkey1"
            columns: ["author_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      article_tags: {
        Row: {
          article_id: number
          created_at: string
          tag_id: number
        }
        Insert: {
          article_id: number
          created_at?: string
          tag_id: number
        }
        Update: {
          article_id?: number
          created_at?: string
          tag_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "article_tags_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "article_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
        ]
      }
      article_views: {
        Row: {
          article_id: number
          viewed_at: string
        }
        Insert: {
          article_id: number
          viewed_at?: string
        }
        Update: {
          article_id?: number
          viewed_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "article_views_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
        ]
      }
      articles: {
        Row: {
          author: string | null
          author_avatar: string | null
          author_id: string | null
          comments_count: number | null
          content: string | null
          created_at: string | null
          id: number
          image_url: string | null
          likes: number | null
          shares: number | null
          tags: string[] | null
          timestamp: string | null
          title: string
          updated_at: string | null
          views: number | null
        }
        Insert: {
          author?: string | null
          author_avatar?: string | null
          author_id?: string | null
          comments_count?: number | null
          content?: string | null
          created_at?: string | null
          id?: number
          image_url?: string | null
          likes?: number | null
          shares?: number | null
          tags?: string[] | null
          timestamp?: string | null
          title: string
          updated_at?: string | null
          views?: number | null
        }
        Update: {
          author?: string | null
          author_avatar?: string | null
          author_id?: string | null
          comments_count?: number | null
          content?: string | null
          created_at?: string | null
          id?: number
          image_url?: string | null
          likes?: number | null
          shares?: number | null
          tags?: string[] | null
          timestamp?: string | null
          title?: string
          updated_at?: string | null
          views?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "articles_author_id_fkey1"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      comments: {
        Row: {
          article_id: number | null
          comment_timestamp: string | null
          content: string
          created_at: string | null
          id: number
          parent_id: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          article_id?: number | null
          comment_timestamp?: string | null
          content: string
          created_at?: string | null
          id?: number
          parent_id?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          article_id?: number | null
          comment_timestamp?: string | null
          content?: string
          created_at?: string | null
          id?: number
          parent_id?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comments_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      discord_users: {
        Row: {
          avatar_url: string | null
          experience: number
          id: string
          last_text_channel: string | null
          level: number
          rank: number | null
          reputation: number | null
          tx_number: string | null
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          experience?: number
          id: string
          last_text_channel?: string | null
          level?: number
          rank?: number | null
          reputation?: number | null
          tx_number?: string | null
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          experience?: number
          id?: string
          last_text_channel?: string | null
          level?: number
          rank?: number | null
          reputation?: number | null
          tx_number?: string | null
          username?: string | null
        }
        Relationships: []
      }
      follows: {
        Row: {
          created_at: string | null
          follower_id: string
          following_id: string
          id: number
        }
        Insert: {
          created_at?: string | null
          follower_id: string
          following_id: string
          id?: number
        }
        Update: {
          created_at?: string | null
          follower_id?: string
          following_id?: string
          id?: number
        }
        Relationships: [
          {
            foreignKeyName: "follows_follower_id_fkey1"
            columns: ["follower_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "follows_following_id_fkey1"
            columns: ["following_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      level_thresholds: {
        Row: {
          experience_required: number
          level: number
        }
        Insert: {
          experience_required: number
          level: number
        }
        Update: {
          experience_required?: number
          level?: number
        }
        Relationships: []
      }
      tags: {
        Row: {
          created_at: string
          id: number
          name: string
        }
        Insert: {
          created_at?: string
          id?: number
          name: string
        }
        Update: {
          created_at?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      top_discord_users: {
        Row: {
          experience: number
          id: string
          level: number
          username: string | null
        }
        Insert: {
          experience: number
          id: string
          level: number
          username?: string | null
        }
        Update: {
          experience?: number
          id?: string
          level?: number
          username?: string | null
        }
        Relationships: []
      }
      user_article_interactions: {
        Row: {
          article_id: number
          created_at: string
          id: number
          interaction_count: number | null
          interaction_timestamp: string | null
          is_bookmarked: boolean | null
          is_liked: boolean | null
          user_id: string
        }
        Insert: {
          article_id: number
          created_at?: string
          id?: number
          interaction_count?: number | null
          interaction_timestamp?: string | null
          is_bookmarked?: boolean | null
          is_liked?: boolean | null
          user_id: string
        }
        Update: {
          article_id?: number
          created_at?: string
          id?: number
          interaction_count?: number | null
          interaction_timestamp?: string | null
          is_bookmarked?: boolean | null
          is_liked?: boolean | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_article_interactions_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_article_interactions_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_settings: {
        Row: {
          account_private: boolean | null
          activity_status: boolean | null
          allow_mentions: boolean | null
          allow_messages: boolean | null
          created_at: string
          email_enabled: boolean | null
          followers_enabled: boolean | null
          id: string
          mentions_enabled: boolean | null
          post_visibility: string | null
          push_enabled: boolean | null
          show_activity: boolean | null
          updated_at: string
          user_id: string
        }
        Insert: {
          account_private?: boolean | null
          activity_status?: boolean | null
          allow_mentions?: boolean | null
          allow_messages?: boolean | null
          created_at?: string
          email_enabled?: boolean | null
          followers_enabled?: boolean | null
          id?: string
          mentions_enabled?: boolean | null
          post_visibility?: string | null
          push_enabled?: boolean | null
          show_activity?: boolean | null
          updated_at?: string
          user_id: string
        }
        Update: {
          account_private?: boolean | null
          activity_status?: boolean | null
          allow_mentions?: boolean | null
          allow_messages?: boolean | null
          created_at?: string
          email_enabled?: boolean | null
          followers_enabled?: boolean | null
          id?: string
          mentions_enabled?: boolean | null
          post_visibility?: string | null
          push_enabled?: boolean | null
          show_activity?: boolean | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_settings_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      username_updates: {
        Row: {
          created_at: string
          id: number
          new_username: string
          old_username: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: number
          new_username: string
          old_username: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: number
          new_username?: string
          old_username?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "username_updates_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      users: {
        Row: {
          article_count: number | null
          avatar_position: Json | null
          avatar_url: string | null
          banner_position: Json | null
          banner_url: string | null
          bio: string | null
          created_at: string
          email: string | null
          followers: string[] | null
          followers_count: number | null
          following_count: number | null
          full_name: string | null
          id: number
          is_following: string[] | null
          points: number | null
          updated_at: string | null
          user_id: string
          username: string | null
        }
        Insert: {
          article_count?: number | null
          avatar_position?: Json | null
          avatar_url?: string | null
          banner_position?: Json | null
          banner_url?: string | null
          bio?: string | null
          created_at?: string
          email?: string | null
          followers?: string[] | null
          followers_count?: number | null
          following_count?: number | null
          full_name?: string | null
          id?: number
          is_following?: string[] | null
          points?: number | null
          updated_at?: string | null
          user_id: string
          username?: string | null
        }
        Update: {
          article_count?: number | null
          avatar_position?: Json | null
          avatar_url?: string | null
          banner_position?: Json | null
          banner_url?: string | null
          bio?: string | null
          created_at?: string
          email?: string | null
          followers?: string[] | null
          followers_count?: number | null
          following_count?: number | null
          full_name?: string | null
          id?: number
          is_following?: string[] | null
          points?: number | null
          updated_at?: string | null
          user_id?: string
          username?: string | null
        }
        Relationships: []
      }
      voice_activity: {
        Row: {
          active: boolean
          discord_id: string
          exp: boolean | null
          id: number
          is_deafened: boolean | null
          is_muted: boolean | null
          joined_at: string | null
          muted_at: string | null
          username: string | null
          voice_channel_id: string | null
        }
        Insert: {
          active?: boolean
          discord_id: string
          exp?: boolean | null
          id?: never
          is_deafened?: boolean | null
          is_muted?: boolean | null
          joined_at?: string | null
          muted_at?: string | null
          username?: string | null
          voice_channel_id?: string | null
        }
        Update: {
          active?: boolean
          discord_id?: string
          exp?: boolean | null
          id?: never
          is_deafened?: boolean | null
          is_muted?: boolean | null
          joined_at?: string | null
          muted_at?: string | null
          username?: string | null
          voice_channel_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voice_activity_discord_id_fkey"
            columns: ["discord_id"]
            isOneToOne: true
            referencedRelation: "discord_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_activity_username_fkey"
            columns: ["username"]
            isOneToOne: true
            referencedRelation: "discord_users"
            referencedColumns: ["username"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_username_availability: {
        Args: {
          p_username: string
          p_user_id: string
        }
        Returns: boolean
      }
      cleanup_stale_sessions: {
        Args: {
          p_hours?: number
        }
        Returns: number
      }
      create_article_with_tags: {
        Args: {
          p_author_id: string
          p_content: string
          p_tags: string[]
          p_image_url: string
          p_title: string
        }
        Returns: {
          id: number
          title: string
          content: string
          author_id: string
          image_url: string
          created_at: string
          tags: string[]
          views: number
          likes: number
          shares: number
          comments_count: number
        }[]
      }
      create_comment: {
        Args: {
          p_user_id: string
          p_article_id: number
          p_content: string
          p_parent_id?: number
        }
        Returns: Json
      }
      create_or_update_discord_user: {
        Args: {
          p_discord_id: string
          p_username: string
          p_channel_id: string
        }
        Returns: undefined
      }
      end_voice_session: {
        Args: {
          p_discord_id: string
        }
        Returns: undefined
      }
      get_active_voice_session: {
        Args: {
          p_discord_id: string
        }
        Returns: {
          id: number
          voice_channel_id: string
          joined_at: string
          is_muted: boolean
          is_deafened: boolean
          muted_at: string
          exp: boolean
        }[]
      }
      get_article_comments: {
        Args: {
          p_article_id: number
          p_limit?: number
          p_offset?: number
        }
        Returns: {
          comments: Json
          total_count: number
        }[]
      }
      get_article_stats: {
        Args: {
          p_author_id: string
        }
        Returns: Json
      }
      get_articles_with_interactions: {
        Args: {
          p_user_id: string
          p_last_timestamp?: string
          p_limit?: number
          p_time_range?: string
          p_sort_field?: string
          p_sort_direction?: string
        }
        Returns: {
          id: number
          title: string
          content: string
          author_id: string
          author: string
          author_avatar: string
          created_at: string
          updated_at: string
          image_url: string
          likes: number
          shares: number
          comments_count: number
          is_liked: boolean
          is_bookmarked: boolean
          tags: string[]
        }[]
      }
      get_popular_tags: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: number
          name: string
          article_count: number
        }[]
      }
      get_trending_articles: {
        Args: {
          p_hours?: number
        }
        Returns: {
          id: number
          title: string
          views: number
        }[]
      }
      get_voice_statistics: {
        Args: {
          p_discord_id: string
          p_start_date?: string
          p_end_date?: string
        }
        Returns: {
          total_time_minutes: number
          active_time_minutes: number
          muted_time_minutes: number
          deafened_time_minutes: number
        }[]
      }
      increment_article_metric: {
        Args: {
          p_article_id: number
          p_metric: string
        }
        Returns: Json
      }
      increment_article_views: {
        Args: {
          article_id: number
        }
        Returns: undefined
      }
      record_article_view: {
        Args: {
          p_article_id: number
        }
        Returns: undefined
      }
      save_article_draft: {
        Args: {
          p_author_id: string
          p_title: string
          p_content: string
          p_tags: string[]
          p_image_url: string
        }
        Returns: number
      }
      search_articles: {
        Args: {
          search_query: string
        }
        Returns: {
          id: number
          title: string
          created_at: string
        }[]
      }
      should_receive_xp: {
        Args: {
          p_discord_id: string
        }
        Returns: boolean
      }
      start_voice_session: {
        Args: {
          p_discord_id: string
          p_channel_id: string
        }
        Returns: undefined
      }
      toggle_article_interaction: {
        Args: {
          p_user_id: string
          p_article_id: number
          p_interaction_type: string
        }
        Returns: Json
      }
      toggle_follow: {
        Args: {
          p_follower_id: string
          p_following_id: string
        }
        Returns: Json
      }
      track_article_share: {
        Args: {
          p_article_id: number
          p_user_id?: string
        }
        Returns: undefined
      }
      update_article: {
        Args: {
          p_article_id: number
          p_author_id: string
          p_title?: string
          p_content?: string
          p_tags?: string[]
          p_image_url?: string
        }
        Returns: Json
      }
      update_user_profile: {
        Args: {
          p_user_id: string
          p_username?: string
          p_bio?: string
          p_avatar_url?: string
          p_banner_url?: string
          p_avatar_position?: Json
          p_banner_position?: Json
        }
        Returns: {
          article_count: number | null
          avatar_position: Json | null
          avatar_url: string | null
          banner_position: Json | null
          banner_url: string | null
          bio: string | null
          created_at: string
          email: string | null
          followers: string[] | null
          followers_count: number | null
          following_count: number | null
          full_name: string | null
          id: number
          is_following: string[] | null
          points: number | null
          updated_at: string | null
          user_id: string
          username: string | null
        }
      }
      update_voice_status: {
        Args: {
          p_discord_id: string
          p_is_muted: boolean
          p_is_deafened: boolean
        }
        Returns: undefined
      }
      update_voice_xp: {
        Args: {
          p_discord_id: string
          p_xp_amount?: number
        }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never
