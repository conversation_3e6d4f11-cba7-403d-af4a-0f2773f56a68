// types/articleTypes.ts
import { Article, UserProfile } from '@/types';

export const TIME_RANGES = {
  all: "All Time",
  today: "Today",
  week: "This Week",
  month: "This Month",
  year: "This Year",
} as const;

export type TimeRange = keyof typeof TIME_RANGES;

export interface EnhancedArticle extends Article {
  is_liked?: boolean;
  is_bookmarked?: boolean;
  interaction_count?: number;
  last_interaction?: string | null;
}

export interface ArticleSort {
  field: 'timestamp' | 'likes' | 'comments_count';
  direction: 'asc' | 'desc';
}

export interface ArticleFilter {
  tags?: string[];
  author?: string;
  timeRange?: TimeRange;
}

export interface ArticleTimelineState {
  sort: ArticleSort;
  filter: ArticleFilter;
  page: number;
  hasMore: boolean;
}

export interface ArticleTimelineProps {
  articles: Article[];
  userProfile: UserProfile;
  errorMessage: string | null;
  rpcFunction?: string;
  title?: string;
}

export interface ArticleCardProps {
  article: EnhancedArticle;
  userProfile: UserProfile;
  onLike?: (articleId: number, isLiked: boolean, userId: string) => Promise<void>;
  onBookmark?: (articleId: number) => Promise<void>;
  onShare?: (article: EnhancedArticle) => Promise<void>;
  onTagClick?: (tag: string) => void; // Add this
  onError?: (error: Error) => void;
  className?: string;
}

export interface AuthorProfileDialogProps {
  authorId: string;
  isOpen: boolean;
  onClose: () => void;
  currentUserId?: string;
}

export interface InteractionResponse {
  is_liked: boolean;
  is_bookmarked: boolean;
  likes_count: number;
  interaction_count: number;
}