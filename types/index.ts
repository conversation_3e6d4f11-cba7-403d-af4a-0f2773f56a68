// Import Supabase generated types
import type { Database } from '../supabase_types';

// Re-export Database type
export type { Database };

// Helper type for accessing table rows - simplified
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];

// Basic interfaces without complex nested types
export interface NewsArticle {
  source: {
    id: string | null;
    name: string;
  };
  author: string | null;
  title: string;
  description: string;
  url: string;
  urlToImage?: string;
  publishedAt: string;
  content: string;
}

export interface NewsProps {
  newsArticles: NewsArticle[];
}

export interface ImagePosition {
  x: number;
  y: number;
  scale: number;
}

// Award/Badge interface
export interface UserAward {
  id: string;
  name: string;
  description: string;
  icon: string;
  earned_at: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

// Simplified user profile type
export type UserProfile = {
  id: number;
  user_id: string;
  username: string | null;
  full_name?: string;
  bio: string | null;
  avatar_url: string | null;
  banner_url: string | null;
  points: number;
  created_at: string;
  updated_at: string;
  avatar_position?: ImagePosition;
  banner_position?: ImagePosition;
  article_count?: number;
  is_followed?: boolean;
  email?: string;
  followers_count?: number;
  following_count?: number;
  // New stats fields
  premium_tier?: 'tier0' | 'tier1' | 'tier2' | 'tier3' | 'tier4';
  account_level?: number;
  account_exp?: number;
  discord_level?: number;
  total_exp_boost?: number;
  likes_count?: number;
  comments_count?: number;
  shares_count?: number;
  research_papers_count?: number;
  reputation_score?: number;
  awards?: UserAward[];
};

// Simplified article type
export type Article = {
  id: number;
  title: string;
  content: string;
  author_id: string;
  author?: string;
  author_avatar?: string;
  created_at: string;
  updated_at: string;
  timestamp?: string;
  image_url?: string;
  tags?: string[];
  likes?: number;
  comments_count?: number;
  is_liked?: boolean;
  is_bookmarked?: boolean;
  interaction_count?: number;
};


// Simplified comment type  
export type Comment = {
  id: number;
  article_id: number;
  author_id: string;
  content: string;
  created_at: string;
  comment_timestamp?: string;
};

// Simple interfaces without inheritance
export interface UserArticleInteraction {
  id: number;
  user_id: string;
  article_id: number;
  is_liked: boolean | null;
  is_bookmarked: boolean | null;
  interaction_count: number | null;
  interaction_timestamp: string | null;
  created_at: string;
}

export interface NestedComment {
  id: number;
  article_id: number;
  author_id: string;
  content: string;
  created_at: string;
  author: string;
  avatar_url: string;
  level: number;
}


// Subscription related types
export interface Subscription {
  id: string;
  user_id: string;
  status: string | null;
  price_id: string | null;
  quantity: number | null;
  created: string;
  current_period_start: string;
  current_period_end: string;
  ended_at: string | null;
  cancel_at: string | null;
  canceled_at: string | null;
  trial_start: string | null;
  trial_end: string | null;
  cancel_at_period_end: boolean | null;
  metadata: Json | null;
}

export interface Price {
  id: string;
  product_id: string;
  active: boolean | null;
  description: string | null;
  unit_amount: number | null;
  currency: string | null;
  type: string | null;
  interval: string | null;
  interval_count: number | null;
  trial_period_days: number | null;
  metadata: Json | null;
}

export interface Product {
  id: string;
  active: boolean | null;
  name: string | null;
  description: string | null;
  image: string | null;
  metadata: Json | null;
}

// Supabase table type aliases for convenience
export type User = Tables<'users'>;
export type SupabaseComment = Tables<'comments'>;

// Experience and tier system types
export interface UserExperienceData {
  user_id: string;
  tier: string;
  experience_points: number;
  level: number;
  next_level_exp: number;
  progress_percentage: number;
  daily_claim_streak: number;
  consecutive_claim_boost: number;
  experience_active: boolean;
  experience_active_until: string | null;
  can_claim_daily: boolean;
  time_until_next_claim: string | null;
  reputation_score: number;
}

export interface TierAccess {
  page_path: string;
  required_tier: string;
  access_type: 'view' | 'interact' | 'create';
  description?: string;
}

// Payment system types (to be replaced with Tables<> once SQL is applied)
export interface SubscriptionPlan {
  id: string;
  name: string;
  display_name: string;
  description: string | null;
  price_monthly: number;
  price_yearly: number;
  features: Json;
  sort_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  subscription_plan_id: string;
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
  status: string;
  current_period_start: string | null;
  current_period_end: string | null;
  cancel_at_period_end: boolean;
  canceled_at: string | null;
  trial_start: string | null;
  trial_end: string | null;
  payment_method: string;
  created_at: string;
  updated_at: string;
}

export interface PaymentHistory {
  id: string;
  user_id: string;
  subscription_id: string | null;
  amount: number;
  currency: string;
  payment_method: string;
  stripe_payment_intent_id: string | null;
  stripe_invoice_id: string | null;
  solana_transaction_id: string | null;
  ctt_amount: number | null;
  status: string;
  failure_reason: string | null;
  metadata: Json;
  processed_at: string | null;
  created_at: string;
}

export interface CTTTransaction {
  id: string;
  user_id: string;
  transaction_type: string;
  amount: number;
  balance_after: number;
  reference_id: string | null;
  solana_transaction_id: string | null;
  from_wallet: string | null;
  to_wallet: string | null;
  status: string;
  description: string | null;
  created_at: string;
}

// Utility types
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export interface DatabaseError {
  message: string;
  code?: string;
  details?: string;
}

// Response types
export interface ApiResponse<T> {
  data?: T;
  error?: DatabaseError;
  status: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Type aliases for compatibility
export type Profile = UserProfile;
