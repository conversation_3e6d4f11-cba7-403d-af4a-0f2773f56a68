{"private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "test:live-data": "bun run scripts/test-live-data.ts", "test:news-cache": "bun run scripts/test-cache-news.ts", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@solana/pay": "^0.2.5", "@solana/web3.js": "^1.98.2", "@stripe/stripe-js": "^7.6.1", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.52.1", "@tailwindcss/line-clamp": "^0.4.4", "ccxt": "^4.4.96", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.7", "lightweight-charts": "^5.0.8", "lucide-react": "^0.525.0", "next": "^15.4.3", "next-themes": "^0.4.6", "pino-pretty": "^13.0.0", "prettier": "^3.6.2", "qrcode-generator": "^2.0.2", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "recharts": "^3.1.0", "sonner": "^2.0.6", "stripe": "^18.3.0", "zod": "^4.0.5", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "19.1.6", "postcss": "^8.5.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "5.8.3"}}