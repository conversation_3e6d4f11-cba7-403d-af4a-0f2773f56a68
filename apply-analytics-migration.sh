#!/bin/bash

# <PERSON>ript to apply analytics functions to Supabase
# Make sure you have the Supabase CLI installed and logged in

echo "Applying analytics functions migration..."

# Check if supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo "Supabase CLI not found. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Apply the migration
echo "Running migration..."
supabase db push

echo "Migration completed. The analytics functions should now be available in your Supabase database."
echo ""
echo "If you're running this locally, make sure to:"
echo "1. Start supabase locally: supabase start"
echo "2. Or connect to your remote project: supabase link --project-ref YOUR_PROJECT_REF"
