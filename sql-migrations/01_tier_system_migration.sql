-- Tier System Migration: Update from free/basic/pro/enterprise to tier1/tier2/tier3/tier4
-- This migration updates the subscription system to use the new tier naming convention

-- Update users table premium_tier constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_premium_tier_check;
ALTER TABLE users ADD CONSTRAINT users_premium_tier_check 
  CHECK (premium_tier IN ('tier1', 'tier2', 'tier3', 'tier4'));

-- Update existing user premium_tier values
UPDATE users SET premium_tier = 'tier1' WHERE premium_tier = 'free';
UPDATE users SET premium_tier = 'tier2' WHERE premium_tier = 'basic';
UPDATE users SET premium_tier = 'tier3' WHERE premium_tier = 'pro';
UPDATE users SET premium_tier = 'tier4' WHERE premium_tier = 'enterprise';

-- Update subscription_plans table if it exists
UPDATE subscription_plans SET plan_name = 'tier1' WHERE plan_name = 'free';
UPDATE subscription_plans SET plan_name = 'tier2' WHERE plan_name = 'basic';
UPDATE subscription_plans SET plan_name = 'tier3' WHERE plan_name = 'pro';
UPDATE subscription_plans SET plan_name = 'tier4' WHERE plan_name = 'enterprise';

-- Update subscription_plans display names
UPDATE subscription_plans SET display_name = 'Tier 1' WHERE plan_name = 'tier1';
UPDATE subscription_plans SET display_name = 'Tier 2' WHERE plan_name = 'tier2';
UPDATE subscription_plans SET display_name = 'Tier 3' WHERE plan_name = 'tier3';
UPDATE subscription_plans SET display_name = 'Tier 4' WHERE plan_name = 'tier4';

-- Update user_subscriptions table references
UPDATE user_subscriptions SET plan_name = 'tier1' WHERE plan_name = 'free';
UPDATE user_subscriptions SET plan_name = 'tier2' WHERE plan_name = 'basic';
UPDATE user_subscriptions SET plan_name = 'tier3' WHERE plan_name = 'pro';
UPDATE user_subscriptions SET plan_name = 'tier4' WHERE plan_name = 'enterprise';

-- Update any functions that reference the old tier names
CREATE OR REPLACE FUNCTION check_tier_access(user_id uuid, required_tier text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_tier text;
  tier_level int;
  required_level int;
BEGIN
  -- Get user's current tier
  SELECT premium_tier INTO user_tier
  FROM users
  WHERE users.user_id = check_tier_access.user_id;
  
  -- Convert tier names to numeric levels for comparison
  tier_level := CASE user_tier
    WHEN 'tier1' THEN 1
    WHEN 'tier2' THEN 2
    WHEN 'tier3' THEN 3
    WHEN 'tier4' THEN 4
    ELSE 0
  END;
  
  required_level := CASE required_tier
    WHEN 'tier1' THEN 1
    WHEN 'tier2' THEN 2
    WHEN 'tier3' THEN 3
    WHEN 'tier4' THEN 4
    ELSE 0
  END;
  
  RETURN tier_level >= required_level;
END;
$$;

-- Update tier_access_controls table if it exists
UPDATE tier_access_controls SET required_tier = 'tier1' WHERE required_tier = 'free';
UPDATE tier_access_controls SET required_tier = 'tier2' WHERE required_tier = 'basic';
UPDATE tier_access_controls SET required_tier = 'tier3' WHERE required_tier = 'pro';
UPDATE tier_access_controls SET required_tier = 'tier4' WHERE required_tier = 'enterprise';

-- Insert default subscription plans if they don't exist
INSERT INTO subscription_plans (plan_name, display_name, price_monthly, price_yearly, features, max_articles_per_day, max_articles_per_month, max_media_storage_mb, created_at, updated_at)
VALUES 
  ('tier1', 'Tier 1', 0, 0, '["Basic access", "5 articles per day", "Basic analytics"]', 5, 150, 100, NOW(), NOW()),
  ('tier2', 'Tier 2', 9.99, 99.99, '["Enhanced access", "20 articles per day", "Advanced analytics", "Priority support"]', 20, 600, 500, NOW(), NOW()),
  ('tier3', 'Tier 3', 19.99, 199.99, '["Premium access", "50 articles per day", "Premium analytics", "Custom hashtags"]', 50, 1500, 2000, NOW(), NOW()),
  ('tier4', 'Tier 4', 49.99, 499.99, '["Enterprise access", "Unlimited articles", "Full analytics suite", "API access"]', -1, -1, 10000, NOW(), NOW())
ON CONFLICT (plan_name) DO UPDATE SET
  display_name = EXCLUDED.display_name,
  price_monthly = EXCLUDED.price_monthly,
  price_yearly = EXCLUDED.price_yearly,
  features = EXCLUDED.features,
  max_articles_per_day = EXCLUDED.max_articles_per_day,
  max_articles_per_month = EXCLUDED.max_articles_per_month,
  max_media_storage_mb = EXCLUDED.max_media_storage_mb,
  updated_at = NOW();

-- Update RLS policies that reference tier names
DROP POLICY IF EXISTS "Users can view tier-appropriate content" ON articles;
CREATE POLICY "Users can view tier-appropriate content" ON articles
  FOR SELECT USING (
    CASE 
      WHEN required_tier IS NULL THEN true
      WHEN auth.uid() IS NULL THEN required_tier = 'tier1'
      ELSE check_tier_access(auth.uid(), required_tier)
    END
  );

COMMENT ON MIGRATION IS 'Tier system migration from free/basic/pro/enterprise to tier1/tier2/tier3/tier4';
