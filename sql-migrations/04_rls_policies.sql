-- Row Level Security (RLS) Policies
-- Comprehensive RLS policies for all tables to ensure proper data access control

-- Enable RLS on all tables
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_drafts ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_hashtags ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_mentions ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_hashtags ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_mentions ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE ctt_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_claims ENABLE ROW LEVEL SECURITY;
ALTER TABLE experience_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE hashtags ENABLE ROW LEVEL SECURITY;
ALTER TABLE level_thresholds ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE tier_access_controls ENABLE ROW LEVEL SECURITY;
ALTER TABLE typing_indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_article_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- Analytics Events policies
DROP POLICY IF EXISTS "Users can view their own analytics events" ON analytics_events;
CREATE POLICY "Users can view their own analytics events" ON analytics_events
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own analytics events" ON analytics_events;
CREATE POLICY "Users can insert their own analytics events" ON analytics_events
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Article Drafts policies
DROP POLICY IF EXISTS "Users can manage their own drafts" ON article_drafts;
CREATE POLICY "Users can manage their own drafts" ON article_drafts
  FOR ALL USING (auth.uid() = author_id);

-- Article Hashtags policies
DROP POLICY IF EXISTS "Anyone can view article hashtags" ON article_hashtags;
CREATE POLICY "Anyone can view article hashtags" ON article_hashtags
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Article authors can manage hashtags" ON article_hashtags;
CREATE POLICY "Article authors can manage hashtags" ON article_hashtags
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM articles 
      WHERE articles.article_id = article_hashtags.article_id 
      AND articles.author_id = auth.uid()
    )
  );

-- Article Mentions policies
DROP POLICY IF EXISTS "Anyone can view article mentions" ON article_mentions;
CREATE POLICY "Anyone can view article mentions" ON article_mentions
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Article authors can create mentions" ON article_mentions;
CREATE POLICY "Article authors can create mentions" ON article_mentions
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM articles 
      WHERE articles.article_id = article_mentions.article_id 
      AND articles.author_id = auth.uid()
    )
  );

-- Article Views policies
DROP POLICY IF EXISTS "Users can view article views" ON article_views;
CREATE POLICY "Users can view article views" ON article_views
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Anyone can record article views" ON article_views;
CREATE POLICY "Anyone can record article views" ON article_views
  FOR INSERT WITH CHECK (true);

-- Comment Hashtags policies
DROP POLICY IF EXISTS "Anyone can view comment hashtags" ON comment_hashtags;
CREATE POLICY "Anyone can view comment hashtags" ON comment_hashtags
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Comment authors can manage hashtags" ON comment_hashtags;
CREATE POLICY "Comment authors can manage hashtags" ON comment_hashtags
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM comments 
      WHERE comments.comment_id = comment_hashtags.comment_id 
      AND comments.author_id = auth.uid()
    )
  );

-- Comment Likes policies
DROP POLICY IF EXISTS "Anyone can view comment likes" ON comment_likes;
CREATE POLICY "Anyone can view comment likes" ON comment_likes
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can manage their own comment likes" ON comment_likes;
CREATE POLICY "Users can manage their own comment likes" ON comment_likes
  FOR ALL USING (auth.uid() = user_id);

-- Comment Mentions policies
DROP POLICY IF EXISTS "Anyone can view comment mentions" ON comment_mentions;
CREATE POLICY "Anyone can view comment mentions" ON comment_mentions
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Comment authors can create mentions" ON comment_mentions;
CREATE POLICY "Comment authors can create mentions" ON comment_mentions
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM comments 
      WHERE comments.comment_id = comment_mentions.comment_id 
      AND comments.author_id = auth.uid()
    )
  );

-- Content Flags policies
DROP POLICY IF EXISTS "Users can view flags they created" ON content_flags;
CREATE POLICY "Users can view flags they created" ON content_flags
  FOR SELECT USING (auth.uid() = reporter_id);

DROP POLICY IF EXISTS "Users can create content flags" ON content_flags;
CREATE POLICY "Users can create content flags" ON content_flags
  FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- CTT Transactions policies
DROP POLICY IF EXISTS "Users can view their own transactions" ON ctt_transactions;
CREATE POLICY "Users can view their own transactions" ON ctt_transactions
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own transactions" ON ctt_transactions;
CREATE POLICY "Users can insert their own transactions" ON ctt_transactions
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Daily Claims policies
DROP POLICY IF EXISTS "Users can view their own claims" ON daily_claims;
CREATE POLICY "Users can view their own claims" ON daily_claims
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can create their own claims" ON daily_claims;
CREATE POLICY "Users can create their own claims" ON daily_claims
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Experience Transactions policies
DROP POLICY IF EXISTS "Users can view their own experience transactions" ON experience_transactions;
CREATE POLICY "Users can view their own experience transactions" ON experience_transactions
  FOR SELECT USING (auth.uid() = user_id);

-- Hashtags policies
DROP POLICY IF EXISTS "Anyone can view hashtags" ON hashtags;
CREATE POLICY "Anyone can view hashtags" ON hashtags
  FOR SELECT USING (true);

-- Level Thresholds policies
DROP POLICY IF EXISTS "Anyone can view level thresholds" ON level_thresholds;
CREATE POLICY "Anyone can view level thresholds" ON level_thresholds
  FOR SELECT USING (true);

-- Notifications policies
DROP POLICY IF EXISTS "Users can manage their own notifications" ON notifications;
CREATE POLICY "Users can manage their own notifications" ON notifications
  FOR ALL USING (auth.uid() = user_id);

-- Subscription Plans policies
DROP POLICY IF EXISTS "Anyone can view subscription plans" ON subscription_plans;
CREATE POLICY "Anyone can view subscription plans" ON subscription_plans
  FOR SELECT USING (true);

-- Tier Access Controls policies
DROP POLICY IF EXISTS "Anyone can view tier access controls" ON tier_access_controls;
CREATE POLICY "Anyone can view tier access controls" ON tier_access_controls
  FOR SELECT USING (true);

-- Typing Indicators policies
DROP POLICY IF EXISTS "Users can manage their own typing indicators" ON typing_indicators;
CREATE POLICY "Users can manage their own typing indicators" ON typing_indicators
  FOR ALL USING (auth.uid() = user_id);

-- User Article Interactions policies
DROP POLICY IF EXISTS "Users can manage their own interactions" ON user_article_interactions;
CREATE POLICY "Users can manage their own interactions" ON user_article_interactions
  FOR ALL USING (auth.uid() = user_id);

-- User Subscriptions policies
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON user_subscriptions;
CREATE POLICY "Users can view their own subscriptions" ON user_subscriptions
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own subscriptions" ON user_subscriptions;
CREATE POLICY "Users can update their own subscriptions" ON user_subscriptions
  FOR UPDATE USING (auth.uid() = user_id);

COMMENT ON MIGRATION IS 'Comprehensive Row Level Security policies for all tables';
