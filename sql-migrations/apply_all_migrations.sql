-- Master Migration Script
-- Apply all SQL updates to bring database up to date with supabase_types.ts

-- Migration 1: Tier System Migration
\echo 'Applying Tier System Migration...'
\i 01_tier_system_migration.sql

-- Migration 2: Analytics Functions
\echo 'Applying Analytics Functions...'
\i 02_analytics_functions.sql

-- Migration 3: Schema Validation
\echo 'Applying Schema Validation...'
\i 03_schema_validation.sql

-- Migration 4: RLS Policies
\echo 'Applying RLS Policies...'
\i 04_rls_policies.sql

-- Insert default data if needed
\echo 'Inserting default data...'

-- Insert level thresholds
INSERT INTO level_thresholds (level, experience_required, title, benefits) VALUES
  (1, 0, 'Newcomer', '{"Basic access", "Can post 5 articles per day"}'),
  (2, 100, 'Regular', '{"Increased daily posts", "Basic analytics"}'),
  (3, 300, 'Contributor', '{"Enhanced features", "Priority support"}'),
  (4, 600, 'Expert', '{"Advanced analytics", "Custom hashtags"}'),
  (5, 1000, 'Veteran', '{"Premium features", "Moderation tools"}'),
  (10, 5000, 'Master', '{"All features", "API access"}')
ON CONFLICT (level) DO NOTHING;

-- Insert tier access controls
INSERT INTO tier_access_controls (feature_name, required_tier, description) VALUES
  ('advanced_analytics', 'tier2', 'Access to advanced analytics dashboard'),
  ('custom_hashtags', 'tier3', 'Ability to create custom hashtags'),
  ('api_access', 'tier4', 'Full API access for integrations'),
  ('priority_support', 'tier2', 'Priority customer support'),
  ('unlimited_articles', 'tier4', 'Unlimited article publishing'),
  ('premium_themes', 'tier3', 'Access to premium themes and customization')
ON CONFLICT (feature_name) DO NOTHING;

-- Insert default hashtags
INSERT INTO hashtags (name, usage_count, trending_score) VALUES
  ('bitcoin', 150, 95),
  ('ethereum', 120, 88),
  ('defi', 100, 82),
  ('nft', 80, 75),
  ('trading', 200, 90),
  ('blockchain', 180, 85),
  ('crypto', 300, 98),
  ('altcoins', 90, 70),
  ('web3', 110, 80),
  ('solana', 85, 78)
ON CONFLICT (name) DO NOTHING;

\echo 'All migrations completed successfully!'
\echo 'Database is now up to date with supabase_types.ts schema.'
