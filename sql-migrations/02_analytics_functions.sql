-- Analytics Functions Migration
-- Creates all analytics functions referenced in the codebase

-- Function to get platform analytics
CREATE OR REPLACE FUNCTION get_platform_analytics(
  p_start_date text DEFAULT NULL,
  p_end_date text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  start_date timestamp;
  end_date timestamp;
BEGIN
  -- Parse dates or use defaults
  start_date := COALESCE(p_start_date::timestamp, NOW() - INTERVAL '30 days');
  end_date := COALESCE(p_end_date::timestamp, NOW());
  
  SELECT json_build_object(
    'total_users', (SELECT COUNT(*) FROM users),
    'active_users', (SELECT COUNT(DISTINCT user_id) FROM analytics_events 
                    WHERE created_at BETWEEN start_date AND end_date),
    'total_articles', (SELECT COUNT(*) FROM articles),
    'articles_this_period', (SELECT COUNT(*) FROM articles 
                           WH<PERSON><PERSON> created_at BETWEEN start_date AND end_date),
    'total_comments', (SELECT COUNT(*) FROM comments),
    'comments_this_period', (SELECT COUNT(*) FROM comments 
                           WHERE created_at BETWEEN start_date AND end_date),
    'engagement_rate', (SELECT ROUND(AVG(likes + COALESCE(comment_count, 0))::numeric, 2) 
                       FROM articles WHERE created_at BETWEEN start_date AND end_date)
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Function to search articles
CREATE OR REPLACE FUNCTION search_articles(search_query text)
RETURNS TABLE(
  article_id uuid,
  title text,
  content text,
  author_id uuid,
  created_at timestamp with time zone,
  likes integer,
  comment_count integer
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.article_id,
    a.title,
    a.content,
    a.author_id,
    a.created_at,
    a.likes,
    a.comment_count
  FROM articles a
  WHERE 
    a.title ILIKE '%' || search_query || '%' 
    OR a.content ILIKE '%' || search_query || '%'
    OR EXISTS (
      SELECT 1 FROM article_hashtags ah 
      JOIN hashtags h ON ah.hashtag_id = h.hashtag_id 
      WHERE ah.article_id = a.article_id 
      AND h.name ILIKE '%' || search_query || '%'
    )
  ORDER BY a.created_at DESC;
END;
$$;

-- Function to search hashtags
CREATE OR REPLACE FUNCTION search_hashtags(p_query text, p_limit integer DEFAULT 20)
RETURNS TABLE(
  hashtag_id uuid,
  name text,
  usage_count integer,
  trending_score integer
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    h.hashtag_id,
    h.name,
    h.usage_count,
    h.trending_score
  FROM hashtags h
  WHERE h.name ILIKE '%' || p_query || '%'
  ORDER BY h.trending_score DESC, h.usage_count DESC
  LIMIT p_limit;
END;
$$;

-- Function to search users for mentions
CREATE OR REPLACE FUNCTION search_users_for_mentions(p_query text, p_limit integer DEFAULT 20)
RETURNS TABLE(
  user_id uuid,
  username text,
  display_name text,
  avatar_url text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.user_id,
    u.username,
    u.display_name,
    u.avatar_url
  FROM users u
  WHERE 
    u.username ILIKE '%' || p_query || '%' 
    OR u.display_name ILIKE '%' || p_query || '%'
  ORDER BY u.username
  LIMIT p_limit;
END;
$$;

-- Function to cleanup expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count integer;
BEGIN
  DELETE FROM notifications 
  WHERE created_at < NOW() - INTERVAL '30 days'
  AND read_at IS NOT NULL;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$;

-- Function to cleanup expired typing indicators
CREATE OR REPLACE FUNCTION cleanup_expired_typing_indicators()
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count integer;
BEGIN
  DELETE FROM typing_indicators 
  WHERE last_typed_at < NOW() - INTERVAL '10 seconds';
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$;

-- Function to get article analytics
CREATE OR REPLACE FUNCTION get_article_analytics(p_article_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_build_object(
    'views', COALESCE((SELECT COUNT(*) FROM article_views WHERE article_id = p_article_id), 0),
    'likes', COALESCE((SELECT likes FROM articles WHERE article_id = p_article_id), 0),
    'comments', COALESCE((SELECT comment_count FROM articles WHERE article_id = p_article_id), 0),
    'shares', COALESCE((SELECT COUNT(*) FROM analytics_events 
                       WHERE event_type = 'article_shared' 
                       AND metadata->>'article_id' = p_article_id::text), 0),
    'engagement_score', COALESCE((SELECT 
      (likes * 1.0 + comment_count * 2.0 + 
       (SELECT COUNT(*) FROM article_views WHERE article_id = p_article_id) * 0.1)
      FROM articles WHERE article_id = p_article_id), 0)
  ) INTO result;
  
  RETURN result;
END;
$$;

-- Function to get experience leaderboard
CREATE OR REPLACE FUNCTION get_experience_leaderboard(p_limit integer DEFAULT 10)
RETURNS TABLE(
  user_id uuid,
  username text,
  display_name text,
  avatar_url text,
  total_experience integer,
  level integer,
  rank bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    u.user_id,
    u.username,
    u.display_name,
    u.avatar_url,
    u.total_experience,
    u.level,
    ROW_NUMBER() OVER (ORDER BY u.total_experience DESC) as rank
  FROM users u
  WHERE u.total_experience > 0
  ORDER BY u.total_experience DESC
  LIMIT p_limit;
END;
$$;

-- Function to process daily claims
CREATE OR REPLACE FUNCTION process_daily_claim(p_user_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  claim_record daily_claims%ROWTYPE;
  experience_reward integer := 50;
  result json;
BEGIN
  -- Check if user already claimed today
  SELECT * INTO claim_record
  FROM daily_claims
  WHERE user_id = p_user_id
  AND claim_date = CURRENT_DATE;

  IF FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Already claimed today',
      'next_claim_at', claim_record.claim_date + INTERVAL '1 day'
    );
  END IF;

  -- Create claim record
  INSERT INTO daily_claims (user_id, claim_date, experience_earned)
  VALUES (p_user_id, CURRENT_DATE, experience_reward)
  RETURNING * INTO claim_record;

  -- Award experience
  UPDATE users
  SET total_experience = total_experience + experience_reward,
      level = FLOOR(SQRT(total_experience + experience_reward) / 10) + 1
  WHERE user_id = p_user_id;

  RETURN json_build_object(
    'success', true,
    'experience_earned', experience_reward,
    'next_claim_at', CURRENT_DATE + INTERVAL '1 day'
  );
END;
$$;

-- Function to award experience for interactions
CREATE OR REPLACE FUNCTION award_interaction_experience(
  p_user_id uuid,
  p_interaction_type text,
  p_target_id uuid DEFAULT NULL
)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  experience_amount integer;
  new_level integer;
BEGIN
  -- Determine experience amount based on interaction type
  experience_amount := CASE p_interaction_type
    WHEN 'article_like' THEN 2
    WHEN 'article_comment' THEN 5
    WHEN 'article_share' THEN 3
    WHEN 'comment_like' THEN 1
    WHEN 'follow_user' THEN 1
    WHEN 'article_publish' THEN 10
    ELSE 1
  END;

  -- Update user experience
  UPDATE users
  SET total_experience = total_experience + experience_amount,
      level = FLOOR(SQRT(total_experience + experience_amount) / 10) + 1
  WHERE user_id = p_user_id
  RETURNING level INTO new_level;

  -- Log the experience transaction
  INSERT INTO experience_transactions (
    user_id,
    experience_change,
    transaction_type,
    description,
    created_at
  ) VALUES (
    p_user_id,
    experience_amount,
    p_interaction_type,
    'Experience earned from ' || p_interaction_type,
    NOW()
  );

  RETURN experience_amount;
END;
$$;

COMMENT ON FUNCTION get_platform_analytics IS 'Returns comprehensive platform analytics for dashboard';
COMMENT ON FUNCTION search_articles IS 'Full-text search across articles with hashtag support';
COMMENT ON FUNCTION search_hashtags IS 'Search hashtags by name with trending score ordering';
COMMENT ON FUNCTION search_users_for_mentions IS 'Search users for mention functionality';
COMMENT ON FUNCTION cleanup_expired_notifications IS 'Cleanup old read notifications';
COMMENT ON FUNCTION cleanup_expired_typing_indicators IS 'Cleanup expired typing indicators';
COMMENT ON FUNCTION get_article_analytics IS 'Get comprehensive analytics for a specific article';
COMMENT ON FUNCTION get_experience_leaderboard IS 'Get top users by experience points';
COMMENT ON FUNCTION process_daily_claim IS 'Process daily experience claim for users';
COMMENT ON FUNCTION award_interaction_experience IS 'Award experience points for user interactions';
