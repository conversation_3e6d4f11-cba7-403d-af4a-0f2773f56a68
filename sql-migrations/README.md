# SQL Migrations for Crypto Talks Network

This directory contains SQL migration files to bring your Supabase database up to date with the current `supabase_types.ts` schema.

## Migration Files

### 1. `01_tier_system_migration.sql`
Updates the subscription tier system from the old naming convention (free/basic/pro/enterprise) to the new tier system (tier1/tier2/tier3/tier4).

**Changes:**
- Updates `users.premium_tier` constraint
- Migrates existing user tier data
- Updates subscription plans and user subscriptions
- Updates the `check_tier_access` function
- Creates default subscription plans

### 2. `02_analytics_functions.sql`
Creates all analytics and utility functions referenced in the codebase.

**Functions created:**
- `get_platform_analytics()` - Platform-wide analytics
- `search_articles()` - Full-text article search
- `search_hashtags()` - Hashtag search functionality
- `search_users_for_mentions()` - User search for mentions
- `cleanup_expired_notifications()` - Cleanup old notifications
- `cleanup_expired_typing_indicators()` - Cleanup typing indicators
- `get_article_analytics()` - Individual article analytics
- `get_experience_leaderboard()` - User experience rankings
- `process_daily_claim()` - Daily experience claims
- `award_interaction_experience()` - Experience point system

### 3. `03_schema_validation.sql`
Creates any missing tables and ensures the database schema matches `supabase_types.ts`.

**Tables created (if missing):**
- `analytics_events` - User interaction tracking
- `article_drafts` - Draft article storage
- `article_hashtags` - Article-hashtag relationships
- `article_mentions` - User mentions in articles
- `article_views` - Article view tracking
- `comment_hashtags` - Comment-hashtag relationships
- `comment_likes` - Comment like system
- `comment_mentions` - User mentions in comments
- `content_flags` - Content moderation flags
- `ctt_transactions` - CTT token transactions
- `daily_claims` - Daily experience claims
- `experience_transactions` - Experience point history
- `hashtags` - Hashtag management
- `level_thresholds` - User level system
- `notifications` - User notifications
- `subscription_plans` - Subscription plan definitions
- `tier_access_controls` - Feature access control
- `typing_indicators` - Real-time typing indicators
- `user_article_interactions` - User-article interactions
- `user_subscriptions` - User subscription tracking

### 4. `04_rls_policies.sql`
Implements comprehensive Row Level Security (RLS) policies for all tables.

**Security features:**
- Users can only access their own data
- Public data is accessible to all users
- Proper access controls for content creation and modification
- Secure content flagging and moderation

### 5. `apply_all_migrations.sql`
Master script that applies all migrations in the correct order and inserts default data.

## How to Apply Migrations

### Option 1: Using Supabase CLI (Recommended)

1. **Install Supabase CLI** (if not already installed):
   ```bash
   npm install -g supabase
   ```

2. **Link to your Supabase project**:
   ```bash
   supabase link --project-ref YOUR_PROJECT_REF
   ```

3. **Apply all migrations**:
   ```bash
   supabase db push
   ```

   Or apply the master script:
   ```bash
   psql -h YOUR_DB_HOST -U postgres -d postgres -f sql-migrations/apply_all_migrations.sql
   ```

### Option 2: Manual Application

Apply each migration file individually in order:

1. `01_tier_system_migration.sql`
2. `02_analytics_functions.sql`
3. `03_schema_validation.sql`
4. `04_rls_policies.sql`

### Option 3: Supabase Dashboard

Copy and paste the contents of each migration file into the Supabase SQL Editor and execute them in order.

## Verification

After applying the migrations, verify that:

1. **Tier system is updated**: Check that users have tier1-tier4 values
2. **Functions are available**: Test analytics functions in your application
3. **Tables exist**: Verify all tables from `supabase_types.ts` are present
4. **RLS is working**: Test that users can only access their own data

## Rollback

If you need to rollback changes:

1. **Tier system**: Update tier values back to old naming
2. **Functions**: Drop the created functions
3. **Tables**: Drop newly created tables (be careful with data loss)
4. **RLS**: Disable RLS policies if needed

## Notes

- These migrations are designed to be idempotent (safe to run multiple times)
- Always backup your database before applying migrations
- Test migrations on a development environment first
- Some migrations may take time on large datasets

## Support

If you encounter issues:

1. Check Supabase logs for error details
2. Verify your database permissions
3. Ensure all referenced tables exist
4. Check for naming conflicts with existing objects
