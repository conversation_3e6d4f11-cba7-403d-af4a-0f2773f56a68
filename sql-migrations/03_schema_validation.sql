-- Schema Validation and Missing Tables/Functions Creation
-- Ensures all tables and functions from supabase_types.ts exist in the database

-- Create missing tables if they don't exist

-- Analytics Events table
CREATE TABLE IF NOT EXISTS analytics_events (
  event_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(user_id) ON DELETE CASCADE,
  event_type text NOT NULL,
  metadata jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT NOW()
);

-- Article Drafts table
CREATE TABLE IF NOT EXISTS article_drafts (
  draft_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  author_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  title text,
  content text,
  tags text[],
  metadata jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT NOW(),
  updated_at timestamp with time zone DEFAULT NOW()
);

-- Article Hashtags junction table
CREATE TABLE IF NOT EXISTS article_hashtags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid NOT NULL REFERENCES articles(article_id) ON DELETE CASCADE,
  hashtag_id uuid NOT NULL REFERENCES hashtags(hashtag_id) ON DELETE CASCADE,
  article_tags text[],
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(article_id, hashtag_id)
);

-- Article Mentions table
CREATE TABLE IF NOT EXISTS article_mentions (
  mention_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid NOT NULL REFERENCES articles(article_id) ON DELETE CASCADE,
  mentioned_user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(article_id, mentioned_user_id)
);

-- Article Views table
CREATE TABLE IF NOT EXISTS article_views (
  view_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid NOT NULL REFERENCES articles(article_id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(user_id) ON DELETE CASCADE,
  ip_address inet,
  user_agent text,
  viewed_at timestamp with time zone DEFAULT NOW()
);

-- Comment Hashtags table
CREATE TABLE IF NOT EXISTS comment_hashtags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id uuid NOT NULL REFERENCES comments(comment_id) ON DELETE CASCADE,
  hashtag_id uuid NOT NULL REFERENCES hashtags(hashtag_id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(comment_id, hashtag_id)
);

-- Comment Likes table
CREATE TABLE IF NOT EXISTS comment_likes (
  like_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id uuid NOT NULL REFERENCES comments(comment_id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(comment_id, user_id)
);

-- Comment Mentions table
CREATE TABLE IF NOT EXISTS comment_mentions (
  mention_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id uuid NOT NULL REFERENCES comments(comment_id) ON DELETE CASCADE,
  mentioned_user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(comment_id, mentioned_user_id)
);

-- Content Flags table
CREATE TABLE IF NOT EXISTS content_flags (
  flag_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  content_type text NOT NULL CHECK (content_type IN ('article', 'comment', 'user')),
  content_id uuid NOT NULL,
  reporter_id uuid REFERENCES users(user_id) ON DELETE SET NULL,
  flag_type text NOT NULL,
  reason text,
  moderator_reviewed boolean DEFAULT false,
  moderator_id uuid REFERENCES users(user_id) ON DELETE SET NULL,
  moderator_action text,
  created_at timestamp with time zone DEFAULT NOW(),
  reviewed_at timestamp with time zone
);

-- CTT Transactions table
CREATE TABLE IF NOT EXISTS ctt_transactions (
  transaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  transaction_type text NOT NULL,
  amount numeric(20, 8) NOT NULL,
  description text,
  metadata jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT NOW()
);

-- Daily Claims table
CREATE TABLE IF NOT EXISTS daily_claims (
  claim_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  claim_date date NOT NULL,
  experience_earned integer DEFAULT 50,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(user_id, claim_date)
);

-- Experience Transactions table
CREATE TABLE IF NOT EXISTS experience_transactions (
  transaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  experience_change integer NOT NULL,
  transaction_type text NOT NULL,
  description text,
  created_at timestamp with time zone DEFAULT NOW()
);

-- Hashtags table
CREATE TABLE IF NOT EXISTS hashtags (
  hashtag_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text UNIQUE NOT NULL,
  usage_count integer DEFAULT 0,
  trending_score integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT NOW(),
  updated_at timestamp with time zone DEFAULT NOW()
);

-- Level Thresholds table
CREATE TABLE IF NOT EXISTS level_thresholds (
  level integer PRIMARY KEY,
  experience_required integer NOT NULL,
  title text,
  benefits text[],
  created_at timestamp with time zone DEFAULT NOW()
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
  notification_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  type text NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  metadata jsonb DEFAULT '{}',
  read_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT NOW()
);

-- Subscription Plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  plan_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  plan_name text UNIQUE NOT NULL,
  display_name text NOT NULL,
  price_monthly numeric(10, 2),
  price_yearly numeric(10, 2),
  features jsonb DEFAULT '[]',
  max_articles_per_day integer,
  max_articles_per_month integer,
  max_media_storage_mb integer,
  created_at timestamp with time zone DEFAULT NOW(),
  updated_at timestamp with time zone DEFAULT NOW()
);

-- Tier Access Controls table
CREATE TABLE IF NOT EXISTS tier_access_controls (
  control_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  feature_name text NOT NULL,
  required_tier text NOT NULL CHECK (required_tier IN ('tier1', 'tier2', 'tier3', 'tier4')),
  description text,
  created_at timestamp with time zone DEFAULT NOW()
);

-- Typing Indicators table
CREATE TABLE IF NOT EXISTS typing_indicators (
  indicator_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  article_id uuid REFERENCES articles(article_id) ON DELETE CASCADE,
  last_typed_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(user_id, article_id)
);

-- User Article Interactions table
CREATE TABLE IF NOT EXISTS user_article_interactions (
  interaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  article_id uuid NOT NULL REFERENCES articles(article_id) ON DELETE CASCADE,
  interaction_type text NOT NULL,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(user_id, article_id, interaction_type)
);

-- User Subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
  subscription_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  plan_name text NOT NULL,
  status text NOT NULL DEFAULT 'active',
  current_period_start timestamp with time zone,
  current_period_end timestamp with time zone,
  created_at timestamp with time zone DEFAULT NOW(),
  updated_at timestamp with time zone DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);
CREATE INDEX IF NOT EXISTS idx_article_views_article_id ON article_views(article_id);
CREATE INDEX IF NOT EXISTS idx_article_views_user_id ON article_views(user_id);
CREATE INDEX IF NOT EXISTS idx_hashtags_name ON hashtags(name);
CREATE INDEX IF NOT EXISTS idx_hashtags_trending_score ON hashtags(trending_score DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read_at ON notifications(read_at);
CREATE INDEX IF NOT EXISTS idx_daily_claims_user_date ON daily_claims(user_id, claim_date);

COMMENT ON MIGRATION IS 'Schema validation and creation of missing tables with proper indexes';
