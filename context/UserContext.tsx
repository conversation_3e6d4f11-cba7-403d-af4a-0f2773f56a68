"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
} from "react";
import { createClient } from "@/utils/supabase/client";
import { User } from "@supabase/supabase-js"; // If you're using Supabase types

interface UserData {
  user_id: string; // Make required fields explicit
  [key: string]: any; // Allow additional dynamic fields
}

interface UserContextProps {
  userData: UserData | null;
  isLoading: boolean;
  error: string | null;
  refreshUserData: () => Promise<void>;
  updateUserField: (field: string, value: any) => Promise<boolean>; // Return success status
  clearError: () => void; // Add error clearing functionality
}

const UserContext = createContext<UserContextProps | undefined>(undefined);

export const UserProvider = ({
  children,
  initialUserId,
  autoLoad = true,
}: {
  children: React.ReactNode;
  initialUserId: string | null;
  autoLoad?: boolean;
}) => {
  const supabase = createClient();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use useCallback for memoization
  const fetchUserData = useCallback(async () => {
    if (!initialUserId) {
      setIsLoading(false);
      return;
    }

    try {
      const { data, error: fetchError } = await (supabase as any)
        .from("users")
        .select("*")
        .eq("user_id", initialUserId)
        .single();

      if (fetchError) throw fetchError;

      setUserData(data);
      setError(null); // Clear any existing errors on success
    } catch (fetchError) {
      setError("Failed to load user data.");
      console.error("Error fetching user data:", fetchError);
    } finally {
      setIsLoading(false);
    }
  }, [initialUserId, supabase]);

  useEffect(() => {
    if (autoLoad) {
      fetchUserData();
    }
  }, [fetchUserData, autoLoad]);

  const refreshUserData = useCallback(async () => {
    setIsLoading(true);
    await fetchUserData();
  }, [fetchUserData]);

  const updateUserField = async (
    field: string,
    value: any
  ): Promise<boolean> => {
    if (!initialUserId || !userData) return false;

    try {
      const { error: updateError } = await (supabase as any)
        .from("users")
        .update({ [field]: value })
        .eq("user_id", initialUserId);

      if (updateError) throw updateError;

      setUserData((prev) => (prev ? { ...prev, [field]: value } : null));
      setError(null);
      return true;
    } catch (updateError) {
      setError(`Failed to update ${field}`);
      console.error(`Error updating ${field}:`, updateError);
      return false;
    }
  };

  const clearError = () => setError(null);

  return (
    <UserContext.Provider
      value={{
        userData,
        isLoading,
        error,
        refreshUserData,
        updateUserField,
        clearError,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

// Custom hook with better error message
export const useUserContext = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error(
      "useUserContext must be used within a UserProvider. " +
        "Make sure you have wrapped your component tree with UserProvider."
    );
  }
  return context;
};
