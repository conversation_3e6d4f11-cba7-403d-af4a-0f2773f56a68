# Data Sources Mapping

This document maps each data point required for the crypto tools to its optimal data source, including API endpoints, rate limits, and fallback strategies.

## 📊 **Market Data Sources**

### **CoinMarketCap (Primary Market Data)**
| Data Point | Source | API Endpoint | Rate Limit | Notes |
|------------|--------|--------------|------------|-------|
| **Current Price** | CoinMarketCap | `/v1/cryptocurrency/quotes/latest` | 333 calls/day (free) | Real-time pricing |
| **Market Cap** | CoinMarketCap | `/v1/cryptocurrency/quotes/latest` | 333 calls/day (free) | Fully diluted market cap |
| **Circulating Supply** | CoinMarketCap | `/v1/cryptocurrency/quotes/latest` | 333 calls/day (free) | Current circulating supply |
| **Max Supply** | CoinMarketCap | `/v1/cryptocurrency/quotes/latest` | 333 calls/day (free) | Maximum possible supply |
| **24h Volume** | CoinMarketCap | `/v1/cryptocurrency/quotes/latest` | 333 calls/day (free) | 24-hour trading volume |
| **24h Price Change %** | CoinMarketCap | `/v1/cryptocurrency/quotes/latest` | 333 calls/day (free) | Percentage change |
| **7d Price Change %** | CoinMarketCap | `/v1/cryptocurrency/quotes/latest` | 333 calls/day (free) | 7-day percentage change |
| **Market Rank** | CoinMarketCap | `/v1/cryptocurrency/quotes/latest` | 333 calls/day (free) | Market cap ranking |

**Fallback:** CoinGecko API (`/api/v3/simple/price`)

---

## 🔗 **On-Chain Data Sources**

### **Dune Analytics (Primary On-Chain Data)**
| Data Point | Source | Query Type | Rate Limit | Notes |
|------------|--------|------------|------------|-------|
| **Daily Active Addresses** | Dune Analytics | Custom SQL Query | Free tier available | Most accurate DAA data |
| **Daily Transaction Count** | Dune Analytics | Custom SQL Query | Free tier available | All transaction types |
| **Daily Fees (USD)** | Dune Analytics | Custom SQL Query | Free tier available | Network fee revenue |
| **New Addresses (7d avg)** | Dune Analytics | Custom SQL Query | Free tier available | Growth metric |

**Sample Dune Queries:**
```sql
-- Daily Active Addresses (Ethereum)
SELECT 
    date_trunc('day', block_time) as date,
    count(distinct from_address) as daily_active_addresses
FROM ethereum.transactions 
WHERE block_time >= now() - interval '30 days'
GROUP BY 1 ORDER BY 1 DESC

-- Daily Transaction Count
SELECT 
    date_trunc('day', block_time) as date,
    count(*) as daily_transactions
FROM ethereum.transactions
WHERE block_time >= now() - interval '30 days'
GROUP BY 1 ORDER BY 1 DESC
```

**Fallback:** Flipside Crypto API

### **Flipside Crypto (Secondary On-Chain Data)**
| Data Point | Source | API Access | Rate Limit | Notes |
|------------|--------|------------|------------|-------|
| **Daily Active Addresses** | Flipside | shroomDK API | 500 query seconds/month (free) | Multi-chain support |
| **Daily Transaction Count** | Flipside | shroomDK API | 500 query seconds/month (free) | Comprehensive data |
| **Transaction Volume** | Flipside | shroomDK API | 500 query seconds/month (free) | USD value of transactions |

---

## 🏦 **DeFi Data Sources**

### **DeFiLlama (Primary DeFi Data)**
| Data Point | Source | API Endpoint | Rate Limit | Notes |
|------------|--------|--------------|------------|-------|
| **Total Value Locked (TVL)** | DeFiLlama | `/api/v1/tvl/{protocol}` | No rate limit | Free, no API key |
| **Chain TVL** | DeFiLlama | `/api/v1/chains` | No rate limit | All chains aggregated |
| **Protocol TVL** | DeFiLlama | `/api/v1/protocol/{slug}` | No rate limit | Individual protocols |

**API Examples:**
```javascript
// Get all chains TVL
fetch('https://api.llama.fi/chains')

// Get specific protocol TVL
fetch('https://api.llama.fi/protocol/uniswap')

// Get chain-specific TVL
fetch('https://api.llama.fi/v2/chains')
```

**Fallback:** Static estimates based on market cap ratios

---

## ⚡ **Technical Metrics Sources**

### **Blockchain Explorers (Chain-Specific Data)**

#### **Ethereum - Etherscan**
| Data Point | Source | API Endpoint | Rate Limit | Notes |
|------------|--------|--------------|------------|-------|
| **Average Gas Price** | Etherscan | `/api?module=gastracker&action=gasoracle` | 5 calls/sec (free) | Current gas prices |
| **Daily Transaction Count** | Etherscan | `/api?module=stats&action=dailytxncount` | 5 calls/sec (free) | Historical data |

#### **Solana - Solscan**
| Data Point | Source | API Endpoint | Rate Limit | Notes |
|------------|--------|--------------|------------|-------|
| **Current TPS** | Solscan | `/api/v1/network/stats` | Rate limited | Real-time TPS |
| **Average Fee** | Solscan | `/api/v1/network/stats` | Rate limited | Current fee levels |

#### **Polygon - Polygonscan**
| Data Point | Source | API Endpoint | Rate Limit | Notes |
|------------|--------|--------------|------------|-------|
| **Gas Tracker** | Polygonscan | `/api?module=gastracker&action=gasoracle` | 5 calls/sec (free) | Gas price data |

### **Static Technical Specifications**
| Data Point | Source | Type | Update Frequency | Notes |
|------------|--------|------|------------------|-------|
| **Max TPS** | Documentation | Static | Manual updates | Theoretical maximum |
| **Block Size** | Documentation | Static | Manual updates | Network specifications |
| **Finality Time** | Documentation | Static | Manual updates | Consensus mechanism |
| **Validator Count** | Staking APIs | Dynamic | Daily | Network decentralization |

---

## 👨‍💻 **Development & Ecosystem Data**

### **GitHub API (Development Activity)**
| Data Point | Source | API Endpoint | Rate Limit | Notes |
|------------|--------|--------------|------------|-------|
| **Active Developers** | GitHub API | `/repos/{org}/{repo}/stats/contributors` | 5000 req/hour | Monthly active devs |
| **Commit Activity** | GitHub API | `/repos/{org}/{repo}/stats/commit_activity` | 5000 req/hour | Development velocity |

### **Staking APIs (Network Security)**
| Data Point | Source | API Endpoint | Rate Limit | Notes |
|------------|--------|--------------|------------|-------|
| **Staked Supply** | Chain-specific APIs | Varies by chain | Varies | PoS networks only |
| **Validator Count** | Chain-specific APIs | Varies by chain | Varies | Active validator set |

---

## 🔄 **Data Refresh Strategy**

### **Update Frequencies**
| Data Category | Refresh Rate | Reason |
|---------------|--------------|--------|
| **Market Data** | Every 5 minutes | Price volatility |
| **On-Chain Metrics** | Every 1 hour | Block confirmation time |
| **TVL Data** | Every 30 minutes | DeFi activity |
| **Technical Specs** | Daily | Slow-changing data |
| **Development Data** | Weekly | Development cycles |

### **Caching Strategy**
```typescript
const CACHE_DURATIONS = {
  marketData: 5 * 60 * 1000,      // 5 minutes
  onchainData: 60 * 60 * 1000,    // 1 hour  
  tvlData: 30 * 60 * 1000,        // 30 minutes
  technicalData: 24 * 60 * 60 * 1000, // 24 hours
  developmentData: 7 * 24 * 60 * 60 * 1000 // 7 days
};
```

---

## 🚨 **Fallback Hierarchy**

### **Market Data Fallbacks**
1. **Primary:** CoinMarketCap API
2. **Secondary:** CoinGecko API  
3. **Tertiary:** Centralized mock data service (static)

### **On-Chain Data Fallbacks**
1. **Primary:** Dune Analytics
2. **Secondary:** Flipside Crypto
3. **Tertiary:** Blockchain Explorer APIs
4. **Final:** Static mock data with realistic estimates

### **DeFi Data Fallbacks**
1. **Primary:** DeFiLlama API
2. **Secondary:** Protocol-specific APIs
3. **Final:** Market cap ratio estimates

---

## 📋 **Implementation Checklist**

### **Phase 1: Market Data (Week 1)**
- [ ] Integrate CoinMarketCap API
- [ ] Add CoinGecko fallback
- [ ] Implement caching layer
- [ ] Test rate limiting

### **Phase 2: On-Chain Data (Week 2)**
- [ ] Set up Dune Analytics queries
- [ ] Integrate Flipside Crypto API
- [ ] Add blockchain explorer APIs
- [ ] Implement data validation

### **Phase 3: DeFi & Technical (Week 3)**
- [ ] Integrate DeFiLlama API
- [ ] Add technical specifications
- [ ] Implement GitHub API integration
- [ ] Set up monitoring

### **Phase 4: Optimization (Week 4)**
- [ ] Optimize caching strategies
- [ ] Add error handling
- [ ] Implement data quality checks
- [ ] Performance testing

---

## 🔑 **API Keys Required**

| Service | API Key Required | Free Tier | Upgrade Cost |
|---------|------------------|-----------|--------------|
| CoinMarketCap | ✅ Yes | 333 calls/day | $29/month |
| CoinGecko | ❌ No (optional) | 50 calls/min | $129/month |
| Etherscan | ✅ Yes | 5 calls/sec | $99/month |
| DeFiLlama | ❌ No | Unlimited | Free |
| Dune Analytics | ❌ No | Free tier | $390/month |
| Flipside Crypto | ❌ No | 500 query seconds | $99/month |
| GitHub | ✅ Yes | 5000 req/hour | Free |

---

## 💡 **Cost Optimization Tips**

1. **Start with free tiers** and monitor usage
2. **Cache aggressively** to reduce API calls
3. **Use DeFiLlama** for all TVL data (completely free)
4. **Batch requests** where possible
5. **Implement smart fallbacks** to avoid premium API costs
6. **Monitor rate limits** and implement exponential backoff

This mapping ensures your tools have access to realistic, current data while minimizing costs and maintaining reliability through comprehensive fallback strategies.
