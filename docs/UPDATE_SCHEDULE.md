# Blockchain Data Update Schedule

## Overview

This document outlines the comprehensive update schedule for blockchain metrics in the crypto-talks application. The system prioritizes **static data first**, then intelligently updates only stale data based on scheduled intervals. This approach optimizes resource usage while ensuring users have access to fresh, relevant data.

## 🎯 Smart Update Strategy

### Priority Order
1. **Static Data First** - Always start with comprehensive static fallback data
2. **Staleness Check** - Evaluate which metrics need updating based on timestamps
3. **Selective Updates** - Only update stale data categories (real-time, daily, weekly, monthly)
4. **Graceful Fallback** - If updates fail, keep existing data with updated timestamps

### Benefits
- **Instant Response** - Users always get immediate data from static sources
- **Efficient Updates** - Only refresh what's actually stale
- **Resource Optimization** - Minimize API calls and bandwidth usage
- **Reliability** - Never return empty data due to API failures

## Update Frequency Tiers

### 🔴 Real-Time/Hourly (Priority 1)
**Interval:** 1 hour  
**Rationale:** Critical for trading decisions and user experience

**Metrics:**
- `price` - Current market price
- `change24h` - 24-hour price change percentage
- `change7d` - 7-day price change percentage  
- `gasPrice` - Current network gas price
- `currentTPS` - Current transactions per second

**Data Sources:**
- CoinGecko API
- CoinMarketCap
- Blockchain explorers (for gas prices)

**Rate Limits:** 100 requests/minute  
**Fallback:** Use cached data if API fails

### 🟡 Daily (Priority 2)
**Interval:** 24 hours  
**Rationale:** Important for blockchain analysis but doesn't need constant updates

**Metrics:**
- `volume24h` - 24-hour trading volume
- `activeAddresses24h` - Active addresses in 24 hours
- `newAddresses24h` - New addresses created in 24 hours
- `transactions24h` - Transaction count in 24 hours
- `chainFees24h` - Total chain fees in 24 hours
- `dexVolume24h` - DEX trading volume in 24 hours
- `tvl` - Total Value Locked
- `marketCap` - Market capitalization

**Data Sources:**
- DeFiLlama
- Blockchain explorers
- DEX aggregators

**Rate Limits:** 50 requests/hour  
**Fallback:** Use previous day data

### 🟢 Weekly (Priority 3)
**Interval:** 7 days  
**Rationale:** Moderate importance, changes gradually

**Metrics:**
- `averageFee` - Average transaction fee
- `activeDevelopers` - Active developer count
- `commitActivity` - GitHub commit activity
- `rank` - Market cap ranking

**Data Sources:**
- GitHub API
- CoinGecko
- Development platforms

**Rate Limits:** 20 requests/hour  
**Fallback:** Use previous week data

### 🔵 Monthly (Priority 4)
**Interval:** 30 days  
**Rationale:** Technical specifications that rarely change

**Metrics:**
- `maxTPS` - Maximum transactions per second
- `blockSize` - Block size in MB
- `finalityTime` - Block finality time in seconds
- `validatorCount` - Number of validators
- `stakedSupply` - Total staked token supply

**Data Sources:**
- Official documentation
- Blockchain specifications
- Validator registries

**Rate Limits:** 10 requests/day  
**Fallback:** Use static configuration

## Implementation Details

### Data Flow
```
1. Load Static Data → 2. Check Staleness → 3. Update if Needed → 4. Return Fresh Data
     ↓                      ↓                    ↓                   ↓
  Always Available    Individual Timestamps   Selective Updates   Immediate Response
```

### Timestamp Tracking
Each coin data object includes individual timestamps:
- `priceLastUpdated` - For real-time metrics (price, gas, TPS)
- `metricsLastUpdated` - For daily metrics (volumes, addresses, transactions)
- `developmentLastUpdated` - For weekly metrics (dev activity, rankings)
- `technicalLastUpdated` - For monthly metrics (specs, validators)

### Smart Update Logic
```typescript
// 1. Start with static data
async getAllCoinsData(): Promise<CentralizedCoinData[]> {
  let coinsData = this.getStaticDataWithTimestamps();

  // 2. Check each coin for stale data
  const updatedCoinsData = await Promise.all(
    coinsData.map(async (coin) => {
      return await this.updateCoinIfStale(coin);
    })
  );

  return updatedCoinsData;
}

// 3. Update only stale categories
private async updateCoinIfStale(coin: CentralizedCoinData) {
  const updateNeeds = getMetricsToUpdate(coin);

  if (updateNeeds.realTime) await this.updateRealTimeData(coin);
  if (updateNeeds.daily) await this.updateDailyMetrics(coin);
  if (updateNeeds.weekly) await this.updateWeeklyMetrics(coin);
  if (updateNeeds.monthly) await this.updateMonthlyMetrics(coin);

  return updatedCoin;
}

// 4. Check staleness by category
function needsUpdate(lastUpdated: number | undefined, interval: number): boolean {
  if (!lastUpdated) return true;
  return Date.now() - lastUpdated > interval;
}
```

### Priority System
Updates are processed in priority order:
1. **Real-Time** - Most critical, updated first
2. **Daily** - Important analysis data
3. **Weekly** - Development trends
4. **Monthly** - Technical specifications

### Error Handling
- **API Failures:** Fall back to cached data
- **Rate Limiting:** Respect API limits with exponential backoff
- **Data Validation:** Verify data integrity before updating
- **Graceful Degradation:** Continue with partial updates if some sources fail

## Benefits

### Resource Optimization
- **Reduced API Calls:** Only update what's needed when it's needed
- **Lower Costs:** Minimize paid API usage
- **Better Performance:** Faster response times with cached data

### User Experience
- **Fresh Critical Data:** Prices and gas fees stay current
- **Reliable Service:** Fallback strategies prevent data staleness
- **Consistent Performance:** Predictable update patterns

### Scalability
- **Easy to Extend:** Add new metrics with appropriate schedules
- **Configurable:** Adjust intervals based on usage patterns
- **Maintainable:** Clear separation of concerns

## Configuration

The update schedule is defined in `utils/mock-data-service.ts`:

```typescript
export const UPDATE_SCHEDULE = {
  REAL_TIME: {
    interval: 60 * 60 * 1000, // 1 hour
    metrics: ['price', 'change24h', 'change7d', 'gasPrice', 'currentTPS'],
    priority: 1
  },
  DAILY: {
    interval: 24 * 60 * 60 * 1000, // 24 hours
    metrics: ['volume24h', 'activeAddresses24h', /* ... */],
    priority: 2
  },
  // ... etc
};
```

## Future Enhancements

### Adaptive Scheduling
- Adjust update frequencies based on market volatility
- Increase update frequency during high-activity periods
- Reduce frequency during stable periods

### Smart Caching
- Implement Redis for distributed caching
- Add cache warming strategies
- Implement cache invalidation policies

### Monitoring
- Add metrics for update success rates
- Monitor API response times
- Track data freshness across all metrics

### User Preferences
- Allow users to request more frequent updates for specific coins
- Implement premium tiers with higher update frequencies
- Add real-time subscriptions for critical metrics
