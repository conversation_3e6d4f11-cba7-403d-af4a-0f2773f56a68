# API Integration Guide

This guide covers the setup, configuration, and usage of all API integrations in the Crypto Tools project.

## Overview

The project integrates with multiple cryptocurrency and blockchain data APIs to provide real-time market data, news, and on-chain analytics. The system is designed with fallback mechanisms to ensure reliability.

## API Services Integrated

### 1. CoinGecko API (Primary Market Data)
- **Purpose**: Real-time cryptocurrency prices, market data, and trending coins
- **Free Tier**: 30 calls/min, 10,000 calls/month
- **Pro Tier**: $129/month, 500 calls/min, 500,000 calls/month
- **Documentation**: https://www.coingecko.com/en/api/documentation

### 2. CryptoNews API (Crypto-Specific News)
- **Purpose**: Cryptocurrency news and sentiment analysis
- **Features**: Crypto-focused news sources, sentiment scoring
- **Documentation**: https://cryptonews-api.com/

### 3. Binance WebSocket API (Real-Time Data)
- **Purpose**: Real-time price updates and market data streams
- **Cost**: Free
- **Features**: Live price feeds, 24hr ticker data
- **Documentation**: https://binance-docs.github.io/apidocs/spot/en/

### 4. DefiLlama API (TVL Data)
- **Purpose**: Total Value Locked (TVL) data for DeFi protocols
- **Cost**: Free
- **Documentation**: https://defillama.com/docs/api

### 5. NewsAPI (Backup News Source)
- **Purpose**: General news with crypto keyword filtering
- **Free Tier**: 1,000 requests/month
- **Documentation**: https://newsapi.org/docs

## Environment Configuration

### Required Environment Variables

Create a `.env` file in your project root with the following variables:

```bash
# CoinGecko API (Primary market data source)
COINGECKO_API_KEY=your_coingecko_api_key_here
COINGECKO_API_BASE_URL=https://api.coingecko.com/api/v3

# CryptoNews API (Crypto-specific news)
NEWSDATA_API_KEY=your_NEWSDATA_API_key_here

# NewsAPI (Backup news source)
NEWS_API_KEY=your_news_api_key_here

# CoinMarketCap API (Backup market data)
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here

# Binance API (Optional - for advanced features)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here

# Feature Flags
ENABLE_REAL_TIME_DATA=true
ENABLE_WEBSOCKET_CONNECTIONS=true
FALLBACK_TO_MOCK_DATA=true

# Rate Limiting
API_RATE_LIMIT_REQUESTS_PER_MINUTE=30
API_CACHE_TTL_SECONDS=300
```

### Getting API Keys

1. **CoinGecko API**:
   - Visit: https://www.coingecko.com/en/api/pricing
   - Sign up for a free account
   - Generate API key in your dashboard

2. **CryptoNews API**:
   - Visit: https://cryptonews-api.com/register
   - Create account and get API key

3. **NewsAPI**:
   - Visit: https://newsapi.org/register
   - Sign up and get free API key

4. **Binance API** (Optional):
   - Visit: https://www.binance.com/en/binance-api
   - Create account and generate API credentials

## Architecture Overview

### Core Components

1. **API Client (`utils/api-client.ts`)**:
   - Centralized HTTP client with rate limiting
   - Automatic retry logic and error handling
   - Response caching and request deduplication

2. **API Services (`utils/api-services.ts`)**:
   - Service classes for each API provider
   - Data transformation and normalization
   - Fallback to mock data when APIs fail

3. **WebSocket Service (`utils/websocket-service.ts`)**:
   - Real-time data streaming from Binance
   - Automatic reconnection and error recovery
   - Event-driven architecture for price updates

4. **Data Transformers (`utils/data-transformers.ts`)**:
   - Normalize data formats across different APIs
   - Validate and sanitize API responses
   - Merge data from multiple sources

5. **Real-Time Data Service (`utils/real-time-data.ts`)**:
   - Orchestrates data fetching from multiple sources
   - Combines API data with WebSocket streams
   - Provides React hooks for components

## Usage Examples

### Fetching Market Data

```typescript
import { coinGeckoService } from '@/utils/api-services';

// Get simple prices
const prices = await coinGeckoService.getSimplePrices(['bitcoin', 'ethereum']);

// Get detailed market data
const markets = await coinGeckoService.getCoinsMarkets('usd', 'market_cap_desc', 50);

// Get trending coins
const trending = await coinGeckoService.getTrending();
```

### Using Real-Time Data in React Components

```typescript
import { useMarketData, usePortfolioPrices } from '@/utils/real-time-data';

function MyComponent() {
  const { data: marketData, isLoading, error } = useMarketData();
  const { data: portfolioPrices } = usePortfolioPrices();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {marketData.map(coin => (
        <div key={coin.symbol}>
          {coin.symbol}: ${coin.price}
        </div>
      ))}
    </div>
  );
}
```

### WebSocket Integration

```typescript
import { cryptoWebSocketService } from '@/utils/websocket-service';

// Subscribe to price updates
cryptoWebSocketService.subscribe(['BTC', 'ETH', 'SOL']);

// Add event handler
const handler = (message) => {
  if (message.type === 'price_update') {
    console.log(`${message.data.symbol}: $${message.data.price}`);
  }
};

cryptoWebSocketService.addEventHandler(handler);
```

## Error Handling and Fallbacks

The system implements multiple layers of error handling:

1. **API-Level Fallbacks**:
   - CoinGecko → CoinMarketCap → Mock Data
   - CryptoNews → NewsAPI → Mock Data

2. **Rate Limiting**:
   - Automatic request throttling
   - Exponential backoff for retries
   - Request queuing and deduplication

3. **Caching**:
   - Response caching to reduce API calls
   - Configurable TTL per data type
   - Cache invalidation strategies

4. **Mock Data**:
   - Comprehensive mock data for development
   - Automatic fallback when APIs are unavailable
   - Configurable via `FALLBACK_TO_MOCK_DATA` flag

## Testing

### Running API Integration Tests

```bash
# Install dependencies
npm install

# Run the test script
npx tsx scripts/test-api-integrations.ts
```

The test script will:
- Check environment configuration
- Test each API service
- Verify WebSocket connections
- Test API endpoints
- Provide setup recommendations

### Manual Testing

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open browser and navigate to:
   - Market data: http://localhost:3000/ctn/tools
   - Portfolio calculator: http://localhost:3000/ctn/tools (scroll down)
   - On-chain analytics: http://localhost:3000/ctn/tools/indicators

3. Monitor browser console for API errors or warnings

## Performance Optimization

### Caching Strategy

- **Price Data**: 30 seconds TTL
- **Market Data**: 60 seconds TTL
- **News Data**: 5 minutes TTL
- **Historical Data**: 1 hour TTL
- **Static Data**: 24 hours TTL

### Rate Limiting

- Default: 30 requests per minute
- Configurable via `API_RATE_LIMIT_REQUESTS_PER_MINUTE`
- Automatic backoff when limits are exceeded

### WebSocket Optimization

- Connection pooling and reuse
- Automatic reconnection with exponential backoff
- Selective symbol subscription to reduce bandwidth

## Monitoring and Debugging

### Logging

The system provides comprehensive logging:

```typescript
// Enable debug logging
localStorage.setItem('debug', 'crypto-tools:*');

// Check API health
console.log(await coinGeckoService.ping());

// Monitor WebSocket status
console.log(cryptoWebSocketService.getConnectionStatus());
```

### Common Issues

1. **Rate Limiting**:
   - Symptoms: 429 errors, delayed responses
   - Solution: Reduce request frequency or upgrade API plan

2. **API Key Issues**:
   - Symptoms: 401/403 errors
   - Solution: Verify API keys in .env file

3. **WebSocket Connection Issues**:
   - Symptoms: No real-time updates
   - Solution: Check network connectivity and firewall settings

4. **CORS Issues**:
   - Symptoms: Browser console errors
   - Solution: API calls are made server-side to avoid CORS

## Deployment Considerations

### Production Environment

1. **API Keys**: Store in secure environment variables
2. **Rate Limits**: Monitor usage and upgrade plans as needed
3. **Caching**: Consider Redis for distributed caching
4. **Monitoring**: Set up alerts for API failures

### Scaling

- Implement request queuing for high traffic
- Use CDN for static data caching
- Consider API gateway for request routing
- Monitor and optimize database queries

## Troubleshooting

### Common Error Messages

- `Rate limit exceeded`: Reduce request frequency
- `API key invalid`: Check environment variables
- `Network error`: Check internet connectivity
- `Service unavailable`: API provider issues

### Debug Commands

```bash
# Test specific API
npx tsx -e "import('./utils/api-services').then(m => m.coinGeckoService.ping())"

# Check environment
npx tsx -e "console.log(process.env.COINGECKO_API_KEY ? 'API key set' : 'API key missing')"
```

## Support

For issues with specific APIs:
- CoinGecko: https://www.coingecko.com/en/api/documentation
- CryptoNews: https://cryptonews-api.com/contact
- Binance: https://binance-docs.github.io/apidocs/
- NewsAPI: https://newsapi.org/docs

For project-specific issues, check the console logs and ensure all environment variables are properly configured.
