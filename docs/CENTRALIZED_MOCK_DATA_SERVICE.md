# Centralized Mock Data Service

## Overview

The Centralized Mock Data Service consolidates all fallback data used throughout the application into a single, maintainable service. It provides realistic, up-to-date market data by fetching from CoinGecko API and falls back to static mock data when APIs are unavailable.

## Features

- **Single Source of Truth**: All mock/fallback data is managed in one place
- **Real Data Integration**: Fetches live market data from CoinGecko API
- **Multiple Fallback Layers**: API data → Cached data → Static mock data
- **BASE Coins Support**: Includes all cryptocurrencies from the supported trading pairs
- **Multiple Data Formats**: Supports different data structures for various components
- **Caching System**: Built-in caching with TTL to reduce API calls
- **Error Handling**: Comprehensive error handling with graceful degradation

## BASE Coins Included

The service includes data for all BASE coins extracted from the `supportedPairs` array:

- BTC (Bitcoin)
- ETH (Ethereum) 
- SOL (Solana)
- ADA (Cardano)
- XRP (Ripple)
- SUI (Sui)
- SEI (Sei)
- RAY (Raydium)
- JUP (Jupiter)
- BNB (Binance Coin)
- PYTH (Pyth Network)
- NATIX (Natix Network)
- RENDER (Render)
- ZEUS (Zeus Network)
- APE (ApeCoin)
- BONK (Bonk)
- DOGE (Dogecoin)
- FLOKI (FLOKI)
- PENGU (Pudgy Penguins)
- PEPE (Pepe)
- SHIB (Shiba Inu)
- WIF (dogwifhat)

## Usage

### Basic Usage

```typescript
import { 
  getAllCoinsData, 
  getCoinData, 
  getHeatmapData, 
  getTradingData 
} from '@/utils/mock-data-service';

// Get all coins data
const allCoins = await getAllCoinsData();

// Get specific coin data
const btcData = await getCoinData('BTC');

// Get data formatted for heatmap
const heatmapData = await getHeatmapData();

// Get data formatted for trading components
const tradingData = await getTradingData();
```

### Advanced Usage

```typescript
import { mockDataService } from '@/utils/mock-data-service';

// Check cache status
const cacheStatus = mockDataService.getCacheStatus();

// Clear cache to force refresh
mockDataService.clearCache();

// Get fresh data
const freshData = await mockDataService.getAllCoinsData();
```

### API Endpoint

The service also provides an API endpoint at `/api/mock-data`:

```bash
# Get all data in standard format
GET /api/mock-data

# Get data in heatmap format
GET /api/mock-data?format=heatmap

# Get data in trading format
GET /api/mock-data?format=trading

# Get specific coin data
GET /api/mock-data?symbol=BTC

# Force refresh cache
GET /api/mock-data?refresh=true

# Clear cache
POST /api/mock-data
{
  "action": "clearCache"
}

# Refresh data
POST /api/mock-data
{
  "action": "refreshData"
}
```

## Data Structures

### CentralizedCoinData

```typescript
interface CentralizedCoinData {
  symbol: string;        // e.g., "BTC"
  name: string;          // e.g., "Bitcoin"
  price: number;         // Current price in USD
  change24h: number;     // 24h price change percentage
  change7d: number;      // 7d price change percentage
  marketCap: number;     // Market capitalization
  volume24h: number;     // 24h trading volume
  rank: number;          // Market rank
  lastUpdated: number;   // Timestamp of last update
}
```

### HeatmapCoinData

```typescript
interface HeatmapCoinData {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  change7d: number;
  marketCap: number;
  volume24h: number;
  rank: number;
}
```

### TradingCoinData

```typescript
interface TradingCoinData {
  symbol: string;
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  lastUpdated: number;
}
```

## Fallback Strategy

The service implements a three-tier fallback strategy:

1. **Primary**: Fetch real data from CoinGecko API
2. **Secondary**: Use cached data if API fails
3. **Tertiary**: Use static mock data as final fallback

## Cache Management

- **TTL**: 5 minutes for cached data
- **Automatic Expiration**: Cache automatically expires and refreshes
- **Manual Control**: Cache can be manually cleared and refreshed
- **Status Tracking**: Cache status and source tracking

## Error Handling

The service includes comprehensive error handling:

- API failures gracefully fall back to cached or static data
- Network errors are logged but don't break the application
- Invalid data is filtered out and replaced with fallbacks
- All errors are logged with appropriate context

## Migration from Legacy Mock Data

The service replaces scattered mock data generation throughout the codebase:

### Files Updated

- `utils/onchain-data.ts` - generateMockMarketData()
- `utils/api-services.ts` - getMockMarketData(), getMockPrices()
- `app/api/market-data/route.ts` - getMockMarketData()
- `utils/real-time-data.ts` - fallback mock data
- `utils/lazy-real-time-data.ts` - fallback mock data
- `components/CryptoHeatmap.tsx` - generateMockHeatmapData()
- `app/api/trending/route.ts` - getMockTrendingData()
- `components/InteractiveMetricChart.tsx` - generateMockData()

### Legacy Compatibility

All updated files maintain backward compatibility during the migration:

- Legacy functions are preserved as emergency fallbacks
- Gradual migration allows for testing and validation
- Error handling ensures no breaking changes

## Testing

Run the test suite to verify functionality:

```bash
bun run utils/test-mock-data-service.ts
```

The test suite validates:

- Data retrieval for all formats
- Cache management
- Error handling
- Data structure validation
- BASE coins mapping

## Configuration

The service respects the existing feature flag system:

```typescript
// In utils/api-config.ts
export const FEATURE_FLAGS = {
  FALLBACK_TO_MOCK_DATA: process.env.FALLBACK_TO_MOCK_DATA === 'true',
};
```

When `FALLBACK_TO_MOCK_DATA` is disabled, the service will not attempt to fetch real data and will use static fallbacks only.

## Benefits

1. **Consistency**: All mock data uses the same realistic values
2. **Maintainability**: Single place to update mock data
3. **Performance**: Caching reduces API calls and improves response times
4. **Reliability**: Multiple fallback layers ensure the app always has data
5. **Realism**: Real API data provides more realistic testing scenarios
6. **Flexibility**: Multiple data formats support different component needs
