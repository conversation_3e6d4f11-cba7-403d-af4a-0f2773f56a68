# Live Data Integration Setup Guide

Your cryptocurrency tools now have comprehensive live data integration! This guide explains the setup and features.

## 🎯 Overview

We've implemented a robust, cost-effective live data system with multiple fallback layers:

### Data Sources Priority
1. **Binance API** (Primary) - FREE, 1,200 requests/minute
2. **CoinGecko API** (Secondary) - FREE, 30 requests/minute  
3. **DexScreener API** (DEX Data) - FREE, 300 requests/minute
4. **Messari API** (On-chain) - FREE, 20 requests/minute
5. **Mock Data** (Final fallback)

## 🚀 Quick Start

### 1. Environment Setup

Copy and configure your environment variables:

```bash
cp .env.example .env.local
```

### 2. API Keys (Optional but Recommended)

Most APIs work without keys, but keys provide higher rate limits:

```env
# CoinGecko (optional - higher rate limits with key)
COINGECKO_API_KEY=your_coingecko_api_key_here

# Messari (optional - for enhanced on-chain data)  
MESSARI_API_KEY=your_messari_api_key_here

# Binance (optional - only needed for private trading features)
BINANCE_API_KEY=your_binance_api_key_here

# News APIs (optional)
NEWSDATA_API_KEY=your_NEWSDATA_API_key_here
NEWS_API_KEY=your_news_api_key_here
```

### 3. Test the Integration

```bash
bun run test:live-data
```

## 📊 Available Endpoints

### Market Data
```bash
# Get market data with fallback hierarchy
GET /api/market-data?symbols=BTC,ETH,SOL&limit=50

# Source-specific requests
GET /api/market-data?source=binance
GET /api/market-data?source=coingecko
GET /api/market-data?source=unified
```

### Real-time Prices
```bash
# Get current prices
GET /api/prices?symbols=BTC,ETH,SOL

# Batch price updates
POST /api/prices { "action": "batch_prices", "symbols": ["BTC", "ETH"] }
```

### Trending Data
```bash
# Multi-source trending
GET /api/trending?limit=20

# Source-specific trending
GET /api/trending?source=coingecko
GET /api/trending?source=dexscreener
GET /api/trending?source=messari
```

### On-chain Analytics
```bash
# Enhanced on-chain metrics
GET /api/onchain-data?action=metrics&chain=bitcoin

# Multi-chain comparison
GET /api/onchain-data?action=compare&chains=bitcoin,ethereum,solana
```

### Crypto News
```bash
# Latest crypto news with fallbacks
GET /api/crypto-news?tickers=BTC,ETH&items=20
```

## 💾 Intelligent Caching

### Cache Strategy
- **Price data**: 30 seconds
- **Market data**: 60 seconds  
- **On-chain data**: 5 minutes
- **News**: 15 minutes
- **Static data**: 1 hour

### Cache Features
- LRU eviction
- Source-based invalidation
- Hit/miss analytics
- Memory usage monitoring
- Automatic cleanup

### Cache Management
```javascript
import { cacheService, logCachePerformance } from '@/utils/cache-service';

// Check cache stats
logCachePerformance();

// Clear cache by source
cacheService.invalidateBySource('binance');

// Manual cache warming
cacheService.preload([
  { key: 'btc-price', data: 45000, ttl: 60, source: 'manual' }
]);
```

## 🔧 Real-time Features

### WebSocket Connections
```javascript
import { cryptoWebSocketService } from '@/utils/websocket-service';

// Subscribe to real-time prices
cryptoWebSocketService.subscribe(['BTC', 'ETH', 'SOL']);
```

### React Hooks
```javascript
import { useMarketData, usePortfolioPrices } from '@/utils/real-time-data';

// In your component
const [marketData, isLoading, refresh] = useMarketData();
const [prices] = usePortfolioPrices();
```

## 🛡️ Rate Limiting & Error Handling

### Built-in Protection
- Automatic rate limiting per API
- Exponential backoff on failures
- Circuit breaker pattern
- Graceful degradation to fallbacks

### Error Handling
```javascript
try {
  const data = await binanceService.getPrices(['BTC', 'ETH']);
} catch (error) {
  if (error instanceof APIError) {
    switch (error.type) {
      case 'RATE_LIMIT_EXCEEDED':
        // Handle rate limiting
        break;
      case 'API_KEY_INVALID':
        // Handle auth issues
        break;
    }
  }
}
```

## 📈 Performance Optimization

### Free Tier Maximization
1. **Intelligent caching** reduces API calls by 70-80%
2. **Request batching** minimizes individual calls
3. **Fallback hierarchy** ensures data availability
4. **WebSocket connections** for real-time updates
5. **Source prioritization** uses free APIs first

### Monitoring
```bash
# Monitor cache performance
curl http://localhost:3000/api/market-data -X POST -d '{"action":"health"}'

# Check API health
curl http://localhost:3000/api/trending -X POST -d '{"action":"health"}'
```

## 🔀 Data Flow

```mermaid
graph TD
    A[Tool Request] --> B{Cache Hit?}
    B -->|Yes| C[Return Cached Data]
    B -->|No| D[Try Binance API]
    D -->|Success| E[Cache & Return]
    D -->|Fail| F[Try CoinGecko API]
    F -->|Success| E
    F -->|Fail| G[Try Other APIs]
    G -->|Success| E
    G -->|All Fail| H[Return Mock Data]
```

## 🎨 UI Integration

### Portfolio Calculator
- Real-time price updates via WebSocket
- Historical price charts from multiple sources
- Performance metrics with live data

### On-chain Analytics  
- Multi-source blockchain metrics
- Real-time transaction data
- TVL and DeFi analytics

### Market Discovery
- Trending cryptocurrencies
- Cross-platform aggregation
- Real-time market movements

## 🚨 Troubleshooting

### Common Issues

**No API keys configured**
- System automatically uses free tiers
- Falls back to mock data if all APIs fail
- No configuration required for basic functionality

**Rate limits exceeded**
- Automatic retry with exponential backoff
- Intelligent caching reduces API calls
- Multiple fallback sources prevent total failures

**Network connectivity issues**
- Request timeout handling
- Automatic retries with circuit breaker
- Graceful degradation to cached/mock data

### Debug Mode
```bash
# Enable debug logging
NODE_ENV=development ENABLE_REAL_TIME_DATA=true bun run dev
```

## 🎯 Cost Summary

### Free Tier Limits (Monthly)
- **Binance**: Unlimited (public endpoints)
- **CoinGecko**: ~45,000 requests (30/min × 30 days)  
- **DexScreener**: Unlimited (no rate limits)
- **Messari**: ~28,800 requests (20/min × 30 days)

### Paid Upgrades (Optional)
- **CoinGecko Pro**: $129/month → 500 calls/min
- **Messari Pro**: Contact for pricing → Higher limits
- **News APIs**: $29-99/month for higher volumes

## ✅ Success Criteria

Your live data integration is working when:

1. ✅ APIs respond with real data (or graceful fallbacks)
2. ✅ Cache hit rate > 50% (reduces API usage)
3. ✅ Tools display live cryptocurrency prices
4. ✅ On-chain metrics update automatically
5. ✅ News feeds refresh with latest articles
6. ✅ Real-time WebSocket connections established
7. ✅ Fallback system prevents data outages

## 🎉 What's Next

Your cryptocurrency tools now have enterprise-grade live data integration:

- **Portfolio Calculator**: Real-time portfolio valuations
- **On-chain Analytics**: Live blockchain metrics and comparisons  
- **Market Discovery**: Trending cryptocurrencies across platforms
- **Technical Indicators**: Live price feeds for analysis
- **News Integration**: Latest crypto news with sentiment analysis

The system automatically optimizes for cost-effectiveness while ensuring data reliability through intelligent caching and multi-source fallbacks.