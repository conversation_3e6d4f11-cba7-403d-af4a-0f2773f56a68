-- COMPLETE SUPABASE MIGRATION SCRIPT
-- Copy and paste this entire script into the Supabase SQL Editor

-- =====================================================
-- PART 1: TIER SYSTEM MIGRATION
-- =====================================================

-- Update users table premium_tier constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_premium_tier_check;
ALTER TABLE users ADD CONSTRAINT users_premium_tier_check 
  CHECK (premium_tier IN ('tier1', 'tier2', 'tier3', 'tier4'));

-- Update existing user premium_tier values
UPDATE users SET premium_tier = 'tier1' WHERE premium_tier = 'free';
UPDATE users SET premium_tier = 'tier2' WHERE premium_tier = 'basic';
UPDATE users SET premium_tier = 'tier3' WHERE premium_tier = 'pro';
UPDATE users SET premium_tier = 'tier4' WHERE premium_tier = 'enterprise';

-- Update subscription_plans table if it exists
UPDATE subscription_plans SET plan_name = 'tier1' WHERE plan_name = 'free';
UPDATE subscription_plans SET plan_name = 'tier2' WHERE plan_name = 'basic';
UPDATE subscription_plans SET plan_name = 'tier3' WHERE plan_name = 'pro';
UPDATE subscription_plans SET plan_name = 'tier4' WHERE plan_name = 'enterprise';

-- Update subscription_plans display names
UPDATE subscription_plans SET display_name = 'Tier 1' WHERE plan_name = 'tier1';
UPDATE subscription_plans SET display_name = 'Tier 2' WHERE plan_name = 'tier2';
UPDATE subscription_plans SET display_name = 'Tier 3' WHERE plan_name = 'tier3';
UPDATE subscription_plans SET display_name = 'Tier 4' WHERE plan_name = 'tier4';

-- Update user_subscriptions table references
UPDATE user_subscriptions SET plan_name = 'tier1' WHERE plan_name = 'free';
UPDATE user_subscriptions SET plan_name = 'tier2' WHERE plan_name = 'basic';
UPDATE user_subscriptions SET plan_name = 'tier3' WHERE plan_name = 'pro';
UPDATE user_subscriptions SET plan_name = 'tier4' WHERE plan_name = 'enterprise';

-- =====================================================
-- PART 2: CREATE MISSING TABLES
-- =====================================================

-- Analytics Events table
CREATE TABLE IF NOT EXISTS analytics_events (
  event_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES users(user_id) ON DELETE CASCADE,
  event_type text NOT NULL,
  metadata jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT NOW()
);

-- Article Drafts table
CREATE TABLE IF NOT EXISTS article_drafts (
  draft_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  author_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  title text,
  content text,
  tags text[],
  metadata jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT NOW(),
  updated_at timestamp with time zone DEFAULT NOW()
);

-- Article Hashtags junction table
CREATE TABLE IF NOT EXISTS article_hashtags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid NOT NULL REFERENCES articles(article_id) ON DELETE CASCADE,
  hashtag_id uuid NOT NULL REFERENCES hashtags(hashtag_id) ON DELETE CASCADE,
  article_tags text[],
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(article_id, hashtag_id)
);

-- Article Mentions table
CREATE TABLE IF NOT EXISTS article_mentions (
  mention_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid NOT NULL REFERENCES articles(article_id) ON DELETE CASCADE,
  mentioned_user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(article_id, mentioned_user_id)
);

-- Article Views table
CREATE TABLE IF NOT EXISTS article_views (
  view_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  article_id uuid NOT NULL REFERENCES articles(article_id) ON DELETE CASCADE,
  user_id uuid REFERENCES users(user_id) ON DELETE CASCADE,
  ip_address inet,
  user_agent text,
  viewed_at timestamp with time zone DEFAULT NOW()
);

-- Comment Hashtags table
CREATE TABLE IF NOT EXISTS comment_hashtags (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id uuid NOT NULL REFERENCES comments(comment_id) ON DELETE CASCADE,
  hashtag_id uuid NOT NULL REFERENCES hashtags(hashtag_id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(comment_id, hashtag_id)
);

-- Comment Likes table
CREATE TABLE IF NOT EXISTS comment_likes (
  like_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id uuid NOT NULL REFERENCES comments(comment_id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(comment_id, user_id)
);

-- Comment Mentions table
CREATE TABLE IF NOT EXISTS comment_mentions (
  mention_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  comment_id uuid NOT NULL REFERENCES comments(comment_id) ON DELETE CASCADE,
  mentioned_user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(comment_id, mentioned_user_id)
);

-- Content Flags table
CREATE TABLE IF NOT EXISTS content_flags (
  flag_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  content_type text NOT NULL CHECK (content_type IN ('article', 'comment', 'user')),
  content_id uuid NOT NULL,
  reporter_id uuid REFERENCES users(user_id) ON DELETE SET NULL,
  flag_type text NOT NULL,
  reason text,
  moderator_reviewed boolean DEFAULT false,
  moderator_id uuid REFERENCES users(user_id) ON DELETE SET NULL,
  moderator_action text,
  created_at timestamp with time zone DEFAULT NOW(),
  reviewed_at timestamp with time zone
);

-- CTT Transactions table
CREATE TABLE IF NOT EXISTS ctt_transactions (
  transaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  transaction_type text NOT NULL,
  amount numeric(20, 8) NOT NULL,
  description text,
  metadata jsonb DEFAULT '{}',
  created_at timestamp with time zone DEFAULT NOW()
);

-- Daily Claims table
CREATE TABLE IF NOT EXISTS daily_claims (
  claim_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  claim_date date NOT NULL,
  experience_earned integer DEFAULT 50,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(user_id, claim_date)
);

-- Experience Transactions table
CREATE TABLE IF NOT EXISTS experience_transactions (
  transaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  experience_change integer NOT NULL,
  transaction_type text NOT NULL,
  description text,
  created_at timestamp with time zone DEFAULT NOW()
);

-- Hashtags table
CREATE TABLE IF NOT EXISTS hashtags (
  hashtag_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text UNIQUE NOT NULL,
  usage_count integer DEFAULT 0,
  trending_score integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT NOW(),
  updated_at timestamp with time zone DEFAULT NOW()
);

-- Level Thresholds table
CREATE TABLE IF NOT EXISTS level_thresholds (
  level integer PRIMARY KEY,
  experience_required integer NOT NULL,
  title text,
  benefits text[],
  created_at timestamp with time zone DEFAULT NOW()
);

-- Notifications table
CREATE TABLE IF NOT EXISTS notifications (
  notification_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  type text NOT NULL,
  title text NOT NULL,
  message text NOT NULL,
  metadata jsonb DEFAULT '{}',
  read_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT NOW()
);

-- Subscription Plans table
CREATE TABLE IF NOT EXISTS subscription_plans (
  plan_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  plan_name text UNIQUE NOT NULL,
  display_name text NOT NULL,
  price_monthly numeric(10, 2),
  price_yearly numeric(10, 2),
  features jsonb DEFAULT '[]',
  max_articles_per_day integer,
  max_articles_per_month integer,
  max_media_storage_mb integer,
  created_at timestamp with time zone DEFAULT NOW(),
  updated_at timestamp with time zone DEFAULT NOW()
);

-- Tier Access Controls table
CREATE TABLE IF NOT EXISTS tier_access_controls (
  control_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  feature_name text NOT NULL,
  required_tier text NOT NULL CHECK (required_tier IN ('tier1', 'tier2', 'tier3', 'tier4')),
  description text,
  created_at timestamp with time zone DEFAULT NOW()
);

-- Typing Indicators table
CREATE TABLE IF NOT EXISTS typing_indicators (
  indicator_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  article_id uuid REFERENCES articles(article_id) ON DELETE CASCADE,
  last_typed_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(user_id, article_id)
);

-- User Article Interactions table
CREATE TABLE IF NOT EXISTS user_article_interactions (
  interaction_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  article_id uuid NOT NULL REFERENCES articles(article_id) ON DELETE CASCADE,
  interaction_type text NOT NULL,
  created_at timestamp with time zone DEFAULT NOW(),
  UNIQUE(user_id, article_id, interaction_type)
);

-- User Subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
  subscription_id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  plan_name text NOT NULL,
  status text NOT NULL DEFAULT 'active',
  current_period_start timestamp with time zone,
  current_period_end timestamp with time zone,
  created_at timestamp with time zone DEFAULT NOW(),
  updated_at timestamp with time zone DEFAULT NOW()
);

-- =====================================================
-- PART 3: CREATE INDEXES
-- =====================================================

CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_events_event_type ON analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_events_created_at ON analytics_events(created_at);
CREATE INDEX IF NOT EXISTS idx_article_views_article_id ON article_views(article_id);
CREATE INDEX IF NOT EXISTS idx_article_views_user_id ON article_views(user_id);
CREATE INDEX IF NOT EXISTS idx_hashtags_name ON hashtags(name);
CREATE INDEX IF NOT EXISTS idx_hashtags_trending_score ON hashtags(trending_score DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read_at ON notifications(read_at);
CREATE INDEX IF NOT EXISTS idx_daily_claims_user_date ON daily_claims(user_id, claim_date);

-- =====================================================
-- PART 4: CREATE FUNCTIONS
-- =====================================================

-- Function to check tier access
CREATE OR REPLACE FUNCTION check_tier_access(user_id uuid, required_tier text)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_tier text;
  tier_level int;
  required_level int;
BEGIN
  -- Get user's current tier
  SELECT premium_tier INTO user_tier
  FROM users
  WHERE users.user_id = check_tier_access.user_id;

  -- Convert tier names to numeric levels for comparison
  tier_level := CASE user_tier
    WHEN 'tier1' THEN 1
    WHEN 'tier2' THEN 2
    WHEN 'tier3' THEN 3
    WHEN 'tier4' THEN 4
    ELSE 0
  END;

  required_level := CASE required_tier
    WHEN 'tier1' THEN 1
    WHEN 'tier2' THEN 2
    WHEN 'tier3' THEN 3
    WHEN 'tier4' THEN 4
    ELSE 0
  END;

  RETURN tier_level >= required_level;
END;
$$;

-- Function to get platform analytics
CREATE OR REPLACE FUNCTION get_platform_analytics(
  p_start_date text DEFAULT NULL,
  p_end_date text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
  start_date timestamp;
  end_date timestamp;
BEGIN
  -- Parse dates or use defaults
  start_date := COALESCE(p_start_date::timestamp, NOW() - INTERVAL '30 days');
  end_date := COALESCE(p_end_date::timestamp, NOW());

  SELECT json_build_object(
    'total_users', (SELECT COUNT(*) FROM users),
    'active_users', (SELECT COUNT(DISTINCT user_id) FROM analytics_events
                    WHERE created_at BETWEEN start_date AND end_date),
    'total_articles', (SELECT COUNT(*) FROM articles),
    'articles_this_period', (SELECT COUNT(*) FROM articles
                           WHERE created_at BETWEEN start_date AND end_date),
    'total_comments', (SELECT COUNT(*) FROM comments),
    'comments_this_period', (SELECT COUNT(*) FROM comments
                           WHERE created_at BETWEEN start_date AND end_date),
    'engagement_rate', (SELECT ROUND(AVG(likes + COALESCE(comment_count, 0))::numeric, 2)
                       FROM articles WHERE created_at BETWEEN start_date AND end_date)
  ) INTO result;

  RETURN result;
END;
$$;

-- Function to search articles
CREATE OR REPLACE FUNCTION search_articles(search_query text)
RETURNS TABLE(
  article_id uuid,
  title text,
  content text,
  author_id uuid,
  created_at timestamp with time zone,
  likes integer,
  comment_count integer
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    a.article_id,
    a.title,
    a.content,
    a.author_id,
    a.created_at,
    a.likes,
    a.comment_count
  FROM articles a
  WHERE
    a.title ILIKE '%' || search_query || '%'
    OR a.content ILIKE '%' || search_query || '%'
    OR EXISTS (
      SELECT 1 FROM article_hashtags ah
      JOIN hashtags h ON ah.hashtag_id = h.hashtag_id
      WHERE ah.article_id = a.article_id
      AND h.name ILIKE '%' || search_query || '%'
    )
  ORDER BY a.created_at DESC;
END;
$$;

-- Function to search hashtags
CREATE OR REPLACE FUNCTION search_hashtags(p_query text, p_limit integer DEFAULT 20)
RETURNS TABLE(
  hashtag_id uuid,
  name text,
  usage_count integer,
  trending_score integer
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    h.hashtag_id,
    h.name,
    h.usage_count,
    h.trending_score
  FROM hashtags h
  WHERE h.name ILIKE '%' || p_query || '%'
  ORDER BY h.trending_score DESC, h.usage_count DESC
  LIMIT p_limit;
END;
$$;

-- Function to search users for mentions
CREATE OR REPLACE FUNCTION search_users_for_mentions(p_query text, p_limit integer DEFAULT 20)
RETURNS TABLE(
  user_id uuid,
  username text,
  display_name text,
  avatar_url text
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    u.user_id,
    u.username,
    u.display_name,
    u.avatar_url
  FROM users u
  WHERE
    u.username ILIKE '%' || p_query || '%'
    OR u.display_name ILIKE '%' || p_query || '%'
  ORDER BY u.username
  LIMIT p_limit;
END;
$$;

-- Function to cleanup expired notifications
CREATE OR REPLACE FUNCTION cleanup_expired_notifications()
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count integer;
BEGIN
  DELETE FROM notifications
  WHERE created_at < NOW() - INTERVAL '30 days'
  AND read_at IS NOT NULL;

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$;

-- Function to cleanup expired typing indicators
CREATE OR REPLACE FUNCTION cleanup_expired_typing_indicators()
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  deleted_count integer;
BEGIN
  DELETE FROM typing_indicators
  WHERE last_typed_at < NOW() - INTERVAL '10 seconds';

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$;

-- Function to get article analytics
CREATE OR REPLACE FUNCTION get_article_analytics(p_article_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result json;
BEGIN
  SELECT json_build_object(
    'views', COALESCE((SELECT COUNT(*) FROM article_views WHERE article_id = p_article_id), 0),
    'likes', COALESCE((SELECT likes FROM articles WHERE article_id = p_article_id), 0),
    'comments', COALESCE((SELECT comment_count FROM articles WHERE article_id = p_article_id), 0),
    'shares', COALESCE((SELECT COUNT(*) FROM analytics_events
                       WHERE event_type = 'article_shared'
                       AND metadata->>'article_id' = p_article_id::text), 0),
    'engagement_score', COALESCE((SELECT
      (likes * 1.0 + comment_count * 2.0 +
       (SELECT COUNT(*) FROM article_views WHERE article_id = p_article_id) * 0.1)
      FROM articles WHERE article_id = p_article_id), 0)
  ) INTO result;

  RETURN result;
END;
$$;

-- Function to get experience leaderboard
CREATE OR REPLACE FUNCTION get_experience_leaderboard(p_limit integer DEFAULT 10)
RETURNS TABLE(
  user_id uuid,
  username text,
  display_name text,
  avatar_url text,
  total_experience integer,
  level integer,
  rank bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT
    u.user_id,
    u.username,
    u.display_name,
    u.avatar_url,
    u.total_experience,
    u.level,
    ROW_NUMBER() OVER (ORDER BY u.total_experience DESC) as rank
  FROM users u
  WHERE u.total_experience > 0
  ORDER BY u.total_experience DESC
  LIMIT p_limit;
END;
$$;

-- Function to process daily claims
CREATE OR REPLACE FUNCTION process_daily_claim(p_user_id uuid)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  claim_record daily_claims%ROWTYPE;
  experience_reward integer := 50;
  result json;
BEGIN
  -- Check if user already claimed today
  SELECT * INTO claim_record
  FROM daily_claims
  WHERE user_id = p_user_id
  AND claim_date = CURRENT_DATE;

  IF FOUND THEN
    RETURN json_build_object(
      'success', false,
      'message', 'Already claimed today',
      'next_claim_at', claim_record.claim_date + INTERVAL '1 day'
    );
  END IF;

  -- Create claim record
  INSERT INTO daily_claims (user_id, claim_date, experience_earned)
  VALUES (p_user_id, CURRENT_DATE, experience_reward)
  RETURNING * INTO claim_record;

  -- Award experience
  UPDATE users
  SET total_experience = total_experience + experience_reward,
      level = FLOOR(SQRT(total_experience + experience_reward) / 10) + 1
  WHERE user_id = p_user_id;

  RETURN json_build_object(
    'success', true,
    'experience_earned', experience_reward,
    'next_claim_at', CURRENT_DATE + INTERVAL '1 day'
  );
END;
$$;

-- Function to award experience for interactions
CREATE OR REPLACE FUNCTION award_interaction_experience(
  p_user_id uuid,
  p_interaction_type text,
  p_target_id uuid DEFAULT NULL
)
RETURNS integer
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  experience_amount integer;
  new_level integer;
BEGIN
  -- Determine experience amount based on interaction type
  experience_amount := CASE p_interaction_type
    WHEN 'article_like' THEN 2
    WHEN 'article_comment' THEN 5
    WHEN 'article_share' THEN 3
    WHEN 'comment_like' THEN 1
    WHEN 'follow_user' THEN 1
    WHEN 'article_publish' THEN 10
    ELSE 1
  END;

  -- Update user experience
  UPDATE users
  SET total_experience = total_experience + experience_amount,
      level = FLOOR(SQRT(total_experience + experience_amount) / 10) + 1
  WHERE user_id = p_user_id
  RETURNING level INTO new_level;

  -- Log the experience transaction
  INSERT INTO experience_transactions (
    user_id,
    experience_change,
    transaction_type,
    description,
    created_at
  ) VALUES (
    p_user_id,
    experience_amount,
    p_interaction_type,
    'Experience earned from ' || p_interaction_type,
    NOW()
  );

  RETURN experience_amount;
END;
$$;

-- =====================================================
-- PART 5: ENABLE ROW LEVEL SECURITY
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_drafts ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_hashtags ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_mentions ENABLE ROW LEVEL SECURITY;
ALTER TABLE article_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_hashtags ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE comment_mentions ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE ctt_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_claims ENABLE ROW LEVEL SECURITY;
ALTER TABLE experience_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE hashtags ENABLE ROW LEVEL SECURITY;
ALTER TABLE level_thresholds ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE tier_access_controls ENABLE ROW LEVEL SECURITY;
ALTER TABLE typing_indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_article_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- PART 6: CREATE RLS POLICIES
-- =====================================================

-- Analytics Events policies
DROP POLICY IF EXISTS "Users can view their own analytics events" ON analytics_events;
CREATE POLICY "Users can view their own analytics events" ON analytics_events
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own analytics events" ON analytics_events;
CREATE POLICY "Users can insert their own analytics events" ON analytics_events
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Article Drafts policies
DROP POLICY IF EXISTS "Users can manage their own drafts" ON article_drafts;
CREATE POLICY "Users can manage their own drafts" ON article_drafts
  FOR ALL USING (auth.uid() = author_id);

-- Article Hashtags policies
DROP POLICY IF EXISTS "Anyone can view article hashtags" ON article_hashtags;
CREATE POLICY "Anyone can view article hashtags" ON article_hashtags
  FOR SELECT USING (true);

-- Article Views policies
DROP POLICY IF EXISTS "Anyone can record article views" ON article_views;
CREATE POLICY "Anyone can record article views" ON article_views
  FOR INSERT WITH CHECK (true);

-- Comment Likes policies
DROP POLICY IF EXISTS "Anyone can view comment likes" ON comment_likes;
CREATE POLICY "Anyone can view comment likes" ON comment_likes
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can manage their own comment likes" ON comment_likes;
CREATE POLICY "Users can manage their own comment likes" ON comment_likes
  FOR ALL USING (auth.uid() = user_id);

-- Content Flags policies
DROP POLICY IF EXISTS "Users can view flags they created" ON content_flags;
CREATE POLICY "Users can view flags they created" ON content_flags
  FOR SELECT USING (auth.uid() = reporter_id);

DROP POLICY IF EXISTS "Users can create content flags" ON content_flags;
CREATE POLICY "Users can create content flags" ON content_flags
  FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- CTT Transactions policies
DROP POLICY IF EXISTS "Users can view their own transactions" ON ctt_transactions;
CREATE POLICY "Users can view their own transactions" ON ctt_transactions
  FOR SELECT USING (auth.uid() = user_id);

-- Daily Claims policies
DROP POLICY IF EXISTS "Users can view their own claims" ON daily_claims;
CREATE POLICY "Users can view their own claims" ON daily_claims
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can create their own claims" ON daily_claims;
CREATE POLICY "Users can create their own claims" ON daily_claims
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Experience Transactions policies
DROP POLICY IF EXISTS "Users can view their own experience transactions" ON experience_transactions;
CREATE POLICY "Users can view their own experience transactions" ON experience_transactions
  FOR SELECT USING (auth.uid() = user_id);

-- Hashtags policies
DROP POLICY IF EXISTS "Anyone can view hashtags" ON hashtags;
CREATE POLICY "Anyone can view hashtags" ON hashtags
  FOR SELECT USING (true);

-- Level Thresholds policies
DROP POLICY IF EXISTS "Anyone can view level thresholds" ON level_thresholds;
CREATE POLICY "Anyone can view level thresholds" ON level_thresholds
  FOR SELECT USING (true);

-- Notifications policies
DROP POLICY IF EXISTS "Users can manage their own notifications" ON notifications;
CREATE POLICY "Users can manage their own notifications" ON notifications
  FOR ALL USING (auth.uid() = user_id);

-- Subscription Plans policies
DROP POLICY IF EXISTS "Anyone can view subscription plans" ON subscription_plans;
CREATE POLICY "Anyone can view subscription plans" ON subscription_plans
  FOR SELECT USING (true);

-- Tier Access Controls policies
DROP POLICY IF EXISTS "Anyone can view tier access controls" ON tier_access_controls;
CREATE POLICY "Anyone can view tier access controls" ON tier_access_controls
  FOR SELECT USING (true);

-- Typing Indicators policies
DROP POLICY IF EXISTS "Users can manage their own typing indicators" ON typing_indicators;
CREATE POLICY "Users can manage their own typing indicators" ON typing_indicators
  FOR ALL USING (auth.uid() = user_id);

-- User Article Interactions policies
DROP POLICY IF EXISTS "Users can manage their own interactions" ON user_article_interactions;
CREATE POLICY "Users can manage their own interactions" ON user_article_interactions
  FOR ALL USING (auth.uid() = user_id);

-- User Subscriptions policies
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON user_subscriptions;
CREATE POLICY "Users can view their own subscriptions" ON user_subscriptions
  FOR SELECT USING (auth.uid() = user_id);

-- Update RLS policies that reference tier names
DROP POLICY IF EXISTS "Users can view tier-appropriate content" ON articles;
CREATE POLICY "Users can view tier-appropriate content" ON articles
  FOR SELECT USING (
    CASE
      WHEN required_tier IS NULL THEN true
      WHEN auth.uid() IS NULL THEN required_tier = 'tier1'
      ELSE check_tier_access(auth.uid(), required_tier)
    END
  );

-- =====================================================
-- PART 7: INSERT DEFAULT DATA
-- =====================================================

-- Insert default subscription plans
INSERT INTO subscription_plans (plan_name, display_name, price_monthly, price_yearly, features, max_articles_per_day, max_articles_per_month, max_media_storage_mb, created_at, updated_at)
VALUES
  ('tier1', 'Tier 1', 0, 0, '["Basic access", "5 articles per day", "Basic analytics"]', 5, 150, 100, NOW(), NOW()),
  ('tier2', 'Tier 2', 9.99, 99.99, '["Enhanced access", "20 articles per day", "Advanced analytics", "Priority support"]', 20, 600, 500, NOW(), NOW()),
  ('tier3', 'Tier 3', 19.99, 199.99, '["Premium access", "50 articles per day", "Premium analytics", "Custom hashtags"]', 50, 1500, 2000, NOW(), NOW()),
  ('tier4', 'Tier 4', 49.99, 499.99, '["Enterprise access", "Unlimited articles", "Full analytics suite", "API access"]', -1, -1, 10000, NOW(), NOW())
ON CONFLICT (plan_name) DO UPDATE SET
  display_name = EXCLUDED.display_name,
  price_monthly = EXCLUDED.price_monthly,
  price_yearly = EXCLUDED.price_yearly,
  features = EXCLUDED.features,
  max_articles_per_day = EXCLUDED.max_articles_per_day,
  max_articles_per_month = EXCLUDED.max_articles_per_month,
  max_media_storage_mb = EXCLUDED.max_media_storage_mb,
  updated_at = NOW();

-- Insert level thresholds
INSERT INTO level_thresholds (level, experience_required, title, benefits) VALUES
  (1, 0, 'Newcomer', '{"Basic access", "Can post 5 articles per day"}'),
  (2, 100, 'Regular', '{"Increased daily posts", "Basic analytics"}'),
  (3, 300, 'Contributor', '{"Enhanced features", "Priority support"}'),
  (4, 600, 'Expert', '{"Advanced analytics", "Custom hashtags"}'),
  (5, 1000, 'Veteran', '{"Premium features", "Moderation tools"}'),
  (10, 5000, 'Master', '{"All features", "API access"}')
ON CONFLICT (level) DO NOTHING;

-- Insert tier access controls
INSERT INTO tier_access_controls (feature_name, required_tier, description) VALUES
  ('advanced_analytics', 'tier2', 'Access to advanced analytics dashboard'),
  ('custom_hashtags', 'tier3', 'Ability to create custom hashtags'),
  ('api_access', 'tier4', 'Full API access for integrations'),
  ('priority_support', 'tier2', 'Priority customer support'),
  ('unlimited_articles', 'tier4', 'Unlimited article publishing'),
  ('premium_themes', 'tier3', 'Access to premium themes and customization')
ON CONFLICT (feature_name) DO NOTHING;

-- Insert default hashtags
INSERT INTO hashtags (name, usage_count, trending_score) VALUES
  ('bitcoin', 150, 95),
  ('ethereum', 120, 88),
  ('defi', 100, 82),
  ('nft', 80, 75),
  ('trading', 200, 90),
  ('blockchain', 180, 85),
  ('crypto', 300, 98),
  ('altcoins', 90, 70),
  ('web3', 110, 80),
  ('solana', 85, 78)
ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- MIGRATION COMPLETE
-- =====================================================

-- Update tier_access_controls table if it exists
UPDATE tier_access_controls SET required_tier = 'tier1' WHERE required_tier = 'free';
UPDATE tier_access_controls SET required_tier = 'tier2' WHERE required_tier = 'basic';
UPDATE tier_access_controls SET required_tier = 'tier3' WHERE required_tier = 'pro';
UPDATE tier_access_controls SET required_tier = 'tier4' WHERE required_tier = 'enterprise';
