"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";

export default function ConfirmSignupPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState<string | null>(null);

  useEffect(() => {
    const checkUser = async () => {
      const supabase = createClient();

      // Fetch the current user after confirmation
      const { data, error } = await (supabase as any).auth.getUser();

      if (error || !data?.user) {
        setError(
          "Your email is confirmed, but we couldn't log you in automatically. Please sign in manually."
        );
        setLoading(false);
        return;
      }

      // If user exists, they are authenticated
      setMessage("Your email has been confirmed! Redirecting...");
      setTimeout(() => {
        router.push("/ctn");
      }, 2000);
    };

    checkUser();
  }, [router]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background text-center">
      <div className="max-w-md p-4 bg-white rounded-lg shadow-md">
        {loading ? (
          <p className="text-lg">Confirming your email...</p>
        ) : error ? (
          <div className="text-red-600">
            <p className="text-lg">{error}</p>
          </div>
        ) : (
          <div className="text-green-600">
            <p className="text-lg">{message}</p>
          </div>
        )}
      </div>
    </div>
  );
}
