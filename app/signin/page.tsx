// app/signin/page.tsx
import { AuthForm } from "@/components/AuthForm";
import { signInAction } from "../actions";

interface SigninPageProps {
  searchParams: Promise<{ success?: string }>; // Keep it as Promise
}

export default async function SigninPage({ searchParams }: SigninPageProps) {
  // Await the searchParams
  const params = await searchParams;
  const successMessage = params.success
    ? decodeURIComponent(params.success)
    : undefined;

  return (
    <div className="min-h-screen flex items-center justify-center p-4 auth-container">
      <div className="w-full max-w-lg animate-scale-in">
        <AuthForm signin={signInAction} successMessage={successMessage} />
      </div>
    </div>
  );
}
