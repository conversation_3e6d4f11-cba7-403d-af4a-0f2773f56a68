"use server";

import { encodedRedirect, getURL } from "@/lib/utils";
import { createClient } from "@/utils/supabase/server";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { revalidatePath } from "next/cache";
import { Provider } from '@supabase/supabase-js';


// Function to validate email format
const isValidEmail = (email: string): boolean => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email regex
  return emailPattern.test(email);
};

// SignUp Action
export async function signUpAction(formData: FormData) {
  const supabase = await createClient();

  const email = formData.get('email') as string;
  const password = formData.get('password') as string;
  const username = formData.get('username') as string;

  if (!email || !password || !username) {
    return encodedRedirect("error", "/signup", "All fields are required");
  }

  const { error } = await (supabase as any).auth.signUp({
    email,
    password,
    options: {
      data: {
        user_name: username,
      }
    }
  });

  if (error) {
    console.error("SignUp error:", error.message);
    return encodedRedirect("error", "/signup", error.message);
  }

  return encodedRedirect(
    "success",
    "/signin",
    "Please check your email to verify your account before signing in."
  );
}

// SignIn with OAuth (Provider)
export async function signInWithDiscord() {
  const supabase = await createClient();
  const { data, error } = await (supabase as any).auth.signInWithOAuth({
    provider: "discord",
    options: {
      redirectTo: getURL("/auth/callback"),
    },
  });

  if (data.url) {
    redirect(data.url);
  }
}



// Main sign-in action function
export async function signInAction(formData: FormData) {
  const supabase = await createClient()

  const data = {
    email: formData.get('email') as string,
    password: formData.get('password') as string,
  }

  const { error } = await (supabase as any).auth.signInWithPassword(data)

  if (error) {
    return encodedRedirect("error", "/signin", error.message);
  }

  revalidatePath('/', 'layout')
  redirect('/ctn')
}

// Forgot Password Action
export const forgotPasswordAction = async (formData: FormData) => {
  const email = formData.get("email")?.toString();
  const supabase = await createClient();
  const origin = (await headers()).get("origin");
  const callbackUrl = formData.get("callbackUrl")?.toString();

  if (!email) {
    return encodedRedirect("error", "/forgot-password", "Email is required");
  }

  const { error } = await (supabase as any).auth.resetPasswordForEmail(email, {
    redirectTo: `${origin}/auth/callback?redirect_to=/ctn/reset-password`,
  });

  if (error) {
    console.error(error.message);
    return encodedRedirect("error", "/forgot-password", "Could not reset password");
  }

  if (callbackUrl) {
    return redirect(callbackUrl);
  }

  return encodedRedirect("success", "/forgot-password", "Check your email for a link to reset your password.");
};

// Reset Password Action
export const resetPasswordAction = async (formData: FormData) => {
  const supabase = await createClient();

  const password = formData.get("password") as string;
  const confirmPassword = formData.get("confirmPassword") as string;

  if (!password || !confirmPassword) {
    return encodedRedirect("error", "/ctn/reset-password", "Password and confirm password are required");
  }

  if (password !== confirmPassword) {
    return encodedRedirect("error", "/ctn/reset-password", "Passwords do not match");
  }

  const { error } = await (supabase as any).auth.updateUser({
    password,
  });

  if (error) {
    return encodedRedirect("error", "/ctn/reset-password", "Password update failed");
  }

  return encodedRedirect("success", "/ctn/reset-password", "Password updated");
};

// SignOut Action
export const signOutAction = async () => {
  const supabase = await createClient();
  await (supabase as any).auth.signOut();
  return redirect("/");
};

// Get Settings
export async function getSettings() {
  const supabase = await createClient();

  const { data, error } = await (supabase as any)
    .from('user_settings')
    .select('*')
    .single();

  if (error) throw error;

  return data;
}

// Update Settings
export async function updateSettings(settings: any) {
  const supabase = await createClient();

  const { data, error } = await (supabase as any)
    .from('user_settings')
    .update(settings)
    .select();

  if (error) throw error;

  revalidatePath('/ctn/settings');
  return data;
}
