'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ExperienceLeaderboard } from '@/components/ExperienceLeaderboard';
import { ExperienceDisplay } from '@/components/ExperienceSystem';
import { 
  Trophy, 
  TrendingUp, 
  Users, 
  Star,
  Award,
  Target
} from 'lucide-react';

export default function LeaderboardsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Leaderboards</h1>
          <p className="text-gray-600 mt-2">
            See how you rank against other community members
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Main Leaderboards */}
        <div className="lg:col-span-3">
          <Tabs defaultValue="experience" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="experience">Experience</TabsTrigger>
              <TabsTrigger value="reputation">Reputation</TabsTrigger>
              <TabsTrigger value="articles">Top Articles</TabsTrigger>
              <TabsTrigger value="weekly">Weekly</TabsTrigger>
            </TabsList>

            <TabsContent value="experience">
              <ExperienceLeaderboard 
                limit={50}
                showCurrentUser={true}
                compact={false}
              />
            </TabsContent>

            <TabsContent value="reputation">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Star className="h-5 w-5 text-yellow-500" />
                    Reputation Leaders
                  </CardTitle>
                  <CardDescription>
                    Top contributors ranked by community reputation
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Reputation leaderboard coming soon</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="articles">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                    Top Articles
                  </CardTitle>
                  <CardDescription>
                    Most popular articles by engagement
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Article leaderboard coming soon</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="weekly">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="h-5 w-5 text-purple-500" />
                    Weekly Champions
                  </CardTitle>
                  <CardDescription>
                    Top performers this week
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Award className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">Weekly leaderboard coming soon</p>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* User's Experience */}
          <ExperienceDisplay 
            showDailyClaim={true}
            showProgress={true}
            showStats={false}
            compact={true}
          />

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="h-4 w-4 text-blue-500" />
                Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <Trophy className="h-5 w-5 text-blue-500 mx-auto mb-1" />
                  <div className="text-sm font-medium">Your Rank</div>
                  <div className="text-lg font-bold text-blue-600">#--</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <Users className="h-5 w-5 text-green-500 mx-auto mb-1" />
                  <div className="text-sm font-medium">Total Users</div>
                  <div className="text-lg font-bold text-green-600">1,247</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Top 5 Compact */}
          <ExperienceLeaderboard 
            limit={5}
            showCurrentUser={false}
            compact={true}
          />

          {/* Achievements Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Award className="h-4 w-4 text-purple-500" />
                Achievements
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                  <Trophy className="h-4 w-4 text-gold-500" />
                  <span className="text-sm">First Article</span>
                </div>
                <div className="flex items-center gap-2 p-2 bg-gray-50 rounded opacity-50">
                  <Star className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">100 Likes</span>
                </div>
                <div className="flex items-center gap-2 p-2 bg-gray-50 rounded opacity-50">
                  <Users className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">50 Followers</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
