import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import AutomatedModeration from "@/components/AutomatedModeration";

export default async function AutomatedModerationPage() {
  const supabase = await createClient();

  // Get authenticated user
  const {
    data: { user },
    error: userError,
  } = await (supabase as any).auth.getUser();

  if (!user || userError) {
    redirect("/signin");
  }

  // Fetch user profile and check if they're a moderator
  const { data: userProfile, error: profileError } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (profileError || !userProfile) {
    console.error("Error fetching user profile:", profileError);
    redirect("/error?message=profile-fetch-failed");
  }

  // Check if user is a moderator or admin
  if (!userProfile.is_moderator && !userProfile.is_admin) {
    redirect("/ctn?message=access-denied");
  }

  return (
    <div className="min-h-screen bg-background">
      <AutomatedModeration userProfile={userProfile} />
    </div>
  );
}
