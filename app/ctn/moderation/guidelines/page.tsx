import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import CommunityGuidelines from "@/components/CommunityGuidelines";

export default async function CommunityGuidelinesPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await (supabase as any).auth.getUser();

  if (!user) {
    return redirect("/signin");
  }

  // Get user profile and check admin/moderator status
  const { data: userProfile } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (!userProfile) {
    return redirect("/ctn");
  }

  // Check if user is admin or moderator
  if (!userProfile.is_admin && !userProfile.is_moderator) {
    return redirect("/ctn");
  }

  return (
    <div className="container mx-auto py-6">
      <CommunityGuidelines userProfile={userProfile} />
    </div>
  );
}
