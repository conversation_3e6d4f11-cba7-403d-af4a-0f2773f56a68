import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import NotificationsComponent from "@/components/NotificationsComponent";

export default async function NotificationsPage() {
  const supabase = await createClient();

  // Check if user is authenticated
  const {
    data: { user },
    error: userError,
  } = await (supabase as any).auth.getUser();

  if (!user || userError) {
    redirect("/signin");
  }

  // Fetch user profile for notifications settings
  const { data: profile, error: profileError } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (!profile || profileError) {
    console.error("Profile fetch error:", profileError?.message);
    redirect("/error?message=profile-fetch-failed");
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Notifications</h1>
        <NotificationsComponent userProfile={profile} />
      </div>
    </div>
  );
}
