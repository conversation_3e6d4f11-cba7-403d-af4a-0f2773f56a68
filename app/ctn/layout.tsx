import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import { ReactNode, Suspense } from "react";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/app-sidebar";
import { RightSidebar } from "@/components/right-sidebar";
import { Toaster } from "@/components/ui/toaster";
import { UserProfile as Profile } from "@/types";

// Force dynamic rendering for all authenticated pages in /ctn
export const dynamic = 'force-dynamic';

interface LayoutProps {
  children: ReactNode;
}

export default async function CtnLayout({ children }: LayoutProps) {
  const supabase = await createClient();

  // 🔹 Fetch User
  const {
    data: { user },
    error: userError,
  } = await (supabase as any).auth.getUser();

  if (!user || userError) {
    console.error("Auth error:", userError?.message);
    redirect("/signin");
  }

  // 🔹 Fetch Profile
  const { data: profile, error: profileError } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (!profile || profileError) {
    console.error("Profile fetch error:", profileError?.message);
    redirect("/error?message=profile-fetch-failed");
  }

  // 🔹 Fetch Counts Efficiently
  const [
    { count: followersCount },
    { count: followingCount },
    { count: articlesCount },
  ] = await Promise.all([
    (supabase as any)
      .from("follows")
      .select("", { count: "exact" })
      .eq("following_id", user.id),
    (supabase as any)
      .from("follows")
      .select("", { count: "exact" })
      .eq("follower_id", user.id),
    (supabase as any)
      .from("articles")
      .select("", { count: "exact" })
      .eq("user_id", user.id),
  ]);

  // 🔹 Merge Profile Data
  const enrichedProfile: Profile = {
    ...profile,
    followers_count: followersCount || 0,
    following_count: followingCount || 0,
    article_count: articlesCount || 0,
  };

  return (
    <>
      <div className="flex flex-row w-full">
        <SidebarProvider>
          <Suspense fallback={<Loading />}>
            <AppSidebar user={user} profile={enrichedProfile} />
            <main className="flex-1 overflow-auto">{children}</main>
            <RightSidebar userProfile={enrichedProfile} />
          </Suspense>
        </SidebarProvider>
      </div>
      <Toaster />
    </>
  );
}

// 🔹 Loading Component
export function Loading() {
  return (
    <div className="flex items-center justify-center min-h-screen bg-background">
      <div className="relative">
        <div className="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-primary"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <img src="/ct_logoclr.png" alt="Loading" className="w-16 h-16" />
        </div>
      </div>
    </div>
  );
}

// 🔹 Metadata
const SITE_URL = process.env.NEXT_PUBLIC_SITE_URL!;

export const metadata = {
  title: "Crypto Talks Network",
  description: "Your Crypto Talks Network Hub",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: SITE_URL,
    siteName: "CTN Hub",
    images: [
      {
        url: "/opengraph-image.png",
        width: 1200,
        height: 630,
        alt: "CTN Hub",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "CTN Dashboard",
    description: "Your CTN dashboard and content management system",
    images: ["/twitter-image.png"],
  },
};
