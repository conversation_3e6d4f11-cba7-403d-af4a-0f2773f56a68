'use client';

import { CachedTierProtection } from '@/components/CachedTierProtection';
import { ExperienceDisplay, ExperienceBadge } from '@/components/ExperienceSystem';
import { ExperienceLeaderboard } from '@/components/ExperienceLeaderboard';
import { ArticleInteraction } from '@/components/ArticleInteractionWithXP';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';

export default function ComponentTestPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold mb-4">Component Test Page</h1>
          <p className="text-gray-600">
            This page tests all tier-based access and experience components
          </p>
        </div>

        {/* Experience System Test */}
        <Card>
          <CardHeader>
            <CardTitle>Experience System</CardTitle>
            <CardDescription>Test experience display and badge components</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Experience Badge</h3>
              <ExperienceBadge />
            </div>
            
            <Separator />
            
            <div>
              <h3 className="text-lg font-semibold mb-2">Experience Display</h3>
              <ExperienceDisplay compact={false} />
            </div>
          </CardContent>
        </Card>

        {/* Leaderboard Test */}
        <Card>
          <CardHeader>
            <CardTitle>Experience Leaderboard</CardTitle>
            <CardDescription>Test the experience leaderboard component</CardDescription>
          </CardHeader>
          <CardContent>
            <ExperienceLeaderboard 
              limit={10}
              showCurrentUser={true}
              compact={true}
            />
          </CardContent>
        </Card>

        {/* Article Interaction Test */}
        <Card>
          <CardHeader>
            <CardTitle>Article Interaction</CardTitle>
            <CardDescription>Test the new XP-enabled article interaction component</CardDescription>
          </CardHeader>
          <CardContent>
            <ArticleInteraction
              articleId="test-article"
              authorId="test-author"
              initialLikes={42}
              initialComments={8}
              isLiked={false}
            />
          </CardContent>
        </Card>

        {/* Tier Protection Test */}
        <Card>
          <CardHeader>
            <CardTitle>Tier Protection Test</CardTitle>
            <CardDescription>Test tier-based access protection</CardDescription>
          </CardHeader>
          <CardContent>
            <CachedTierProtection pagePath="/test">
              <div className="p-4 bg-green-100 dark:bg-green-900 rounded-lg">
                <p className="text-green-800 dark:text-green-200">
                  ✅ You have access to this content!
                </p>
              </div>
            </CachedTierProtection>
          </CardContent>
        </Card>

        {/* Tier Badges */}
        <Card>
          <CardHeader>
            <CardTitle>Tier Badges</CardTitle>
            <CardDescription>Visual representation of different tiers</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex gap-2">
              <Badge variant="outline">Free</Badge>
              <Badge variant="outline" className="border-yellow-500 text-yellow-600">Tier 1</Badge>
              <Badge variant="outline" className="border-purple-500 text-purple-600">Tier 2</Badge>
              <Badge variant="outline" className="border-blue-500 text-blue-600">Tier 3</Badge>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
