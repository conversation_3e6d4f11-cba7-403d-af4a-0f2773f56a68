import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import ArticlePublisher from "@/components/Articles/ArticlePublisher";
import { Suspense } from "react";

export default async function PublishPage() {
  const supabase = await createClient();

  // Get authenticated user
  const {
    data: { user },
    error: userError,
  } = await (supabase as any).auth.getUser();

  if (!user || userError) {
    redirect("/signin");
  }

  // Fetch user profile including points
  const { data: userProfile, error: profileError } = await (supabase as any)
    .from("users")
    .select("user_id, points, username, avatar_url")
    .eq("user_id", user.id)
    .single();

  if (profileError || !userProfile) {
    console.error("Error fetching user profile:", profileError);
    redirect("/error?message=profile-fetch-failed");
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        <Suspense fallback={<PublishPageSkeleton />}>
          <ArticlePublisher
            userId={userProfile.user_id}
            userPoints={userProfile.points || 0}
          />
        </Suspense>
      </div>
    </div>
  );
}

// Loading skeleton component
function PublishPageSkeleton() {
  return (
    <div className="max-w-6xl p-6 mx-auto">
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded-sm w-1/4 mb-6"></div>
        <div className="space-y-4">
          <div className="h-12 bg-gray-200 rounded-sm"></div>
          <div className="h-64 bg-gray-200 rounded-sm"></div>
          <div className="h-32 bg-gray-200 rounded-sm"></div>
        </div>
      </div>
    </div>
  );
}
