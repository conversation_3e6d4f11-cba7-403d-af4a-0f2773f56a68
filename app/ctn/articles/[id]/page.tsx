// app/ctn/articles/[id]/page.tsx
import { Metadata } from "next";
import { createClient } from "@/utils/supabase/server";
import { notFound, redirect } from "next/navigation";
import Article from "@/components/Articles/Article";
import { Suspense } from "react";

type Props = {
  params: Promise<{ id: string }>;
};

export default async function ArticlePage(props: Props) {
  const { params } = props;
  const { id } = await params; // Await `params`
  const supabase = await createClient();

  // Get current user
  const {
    data: { user },
    error: userError,
  } = await (supabase as any).auth.getUser();

  if (!user || userError) {
    redirect("/signin");
  }

  // Fetch user profile
  const { data: userProfile, error: profileError } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (profileError) {
    console.error("Error fetching user profile:", profileError);
    redirect("/error?message=profile-fetch-failed");
  }

  // Fetch article with interaction data
  const { data: article, error: articleError } = await (supabase as any)
    .from("articles")
    .select(
      `
      *,
      user_article_interactions!left (
        is_liked,
        is_bookmarked
      )
    `
    )
    .eq("id", id)
    .single();

  if (articleError) {
    console.error("Error fetching article:", articleError);
    notFound();
  }

  // Fetch user's interaction with this article
  const { data: interaction, error: interactionError } = await (supabase as any)
    .from("user_article_interactions")
    .select("is_liked, is_bookmarked")
    .eq("article_id", id)
    .eq("user_id", user.id)
    .single();

  // Combine article with interaction data
  const enrichedArticle = {
    ...article,
    is_liked: interaction?.is_liked || false,
    is_bookmarked: interaction?.is_bookmarked || false,
  };

  return (
    <div className="min-h-screen bg-background px-4">
      <Suspense fallback={<ArticleSkeleton />}>
        <Article article={enrichedArticle} currentUser={userProfile} />
      </Suspense>
    </div>
  );
}

function ArticleSkeleton() {
  return (
    <div className="max-w-4xl mx-auto p-6 my-8 animate-pulse">
      <div className="flex items-center mb-6">
        <div className="h-12 w-12 rounded-full bg-gray-200" />
        <div className="ml-4">
          <div className="h-4 w-32 bg-gray-200 rounded-sm" />
          <div className="h-3 w-24 bg-gray-200 rounded-sm mt-2" />
        </div>
      </div>
      <div className="h-8 w-3/4 bg-gray-200 rounded-sm mb-4" />
      <div className="space-y-4">
        <div className="h-4 w-full bg-gray-200 rounded-sm" />
        <div className="h-4 w-full bg-gray-200 rounded-sm" />
        <div className="h-4 w-2/3 bg-gray-200 rounded-sm" />
      </div>
    </div>
  );
}

export async function generateMetadata(props: Props): Promise<Metadata> {
  const { params } = props;
  const { id } = await params; // Await `params`
  const supabase = await createClient();

  const { data: article } = await (supabase as any)
    .from("articles")
    .select("title")
    .eq("id", id)
    .single();

  return {
    title: article?.title || "Article",
    description: article?.title || "Read this article on CTN",
  };
}
