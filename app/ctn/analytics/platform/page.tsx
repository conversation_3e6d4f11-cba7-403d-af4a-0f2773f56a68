import { redirect } from "next/navigation";
import { createClient } from "@/utils/supabase/server";
import PlatformAnalytics from "@/components/PlatformAnalytics";
import { CachedTierProtection } from "@/components/CachedTierProtection";

export default async function PlatformAnalyticsPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await (supabase as any).auth.getUser();

  if (!user) {
    return redirect("/signin");
  }

  // Get user profile
  const { data: userProfile } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (!userProfile) {
    return redirect("/ctn");
  }

  return (
    <div className="container mx-auto py-6">
      <CachedTierProtection pagePath="/ctn/analytics/platform">
        <PlatformAnalytics userProfile={userProfile} />
      </CachedTierProtection>
    </div>
  );
}
