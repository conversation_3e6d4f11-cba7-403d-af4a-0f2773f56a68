import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import AnalyticsDashboard from "@/components/AnalyticsDashboard";
import { UserProfile as Profile } from "@/types";

export default async function AnalyticsPage() {
  const supabase = await createClient();

  // Check if user is authenticated
  const {
    data: { user },
    error: userError,
  } = await (supabase as any).auth.getUser();

  if (!user || userError) {
    redirect("/signin");
  }

  // Fetch user profile
  const { data: profile, error: profileError } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (!profile || profileError) {
    console.error("Profile fetch error:", profileError?.message);
    redirect("/error?message=profile-fetch-failed");
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <AnalyticsDashboard userProfile={profile as Profile} />
    </div>
  );
}
