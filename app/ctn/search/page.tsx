import { createClient } from "@/utils/supabase/server";
import SearchComponent from "@/components/SearchComponent";
import { redirect } from "next/navigation";
import { UserProfile as Profile } from "@/types";
import { Suspense } from "react";

async function getUserProfile(): Promise<Profile> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await (supabase as any).auth.getUser();

  if (!user || authError) {
    redirect("/signin");
  }

  const { data: userProfile, error: profileError } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (profileError) {
    console.error("Profile fetch error:", profileError);
    throw new Error("Failed to fetch user profile");
  }

  return userProfile as Profile;
}

export default async function SearchPage() {
  try {
    const userProfile = await getUserProfile();

    return (
      <div className="min-h-screen bg-background">
        <Suspense fallback={<div className="flex items-center justify-center h-screen">Loading...</div>}>
          <SearchComponent userProfile={userProfile} />
        </Suspense>
      </div>
    );
  } catch (error) {
    console.error("Error loading search page:", error);
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-red-500">
          Failed to load search page. Please try again later.
        </p>
      </div>
    );
  }
}
