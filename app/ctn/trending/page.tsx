import { createClient } from "@/utils/supabase/server";
import ArticleTimeline from "@/components/Articles/ArticleTimeline";
import { redirect } from "next/navigation";
import { Article, UserProfile as Profile } from "@/types";
import { Suspense } from "react";
import { ArticlesSkeleton } from "@/components/Skeletons/ArticlesSkeleton";

async function getTrendingArticlesData(): Promise<{
  articles: Article[];
  userProfile: Profile;
}> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await (supabase as any).auth.getUser();

  if (!user || authError) {
    redirect("/signin");
  }

  // Fetch user profile and trending articles in parallel
  const [profileResponse, articlesResponse] = await Promise.all([
    (supabase as any).from("users").select("*").eq("user_id", user.id).single(),
    (supabase as any).rpc("get_trending_articles", {
      p_user_id: user.id,
      p_limit: 10,
      p_time_range: "week",
      p_tags: null,
    }),
  ]);

  if (profileResponse.error) {
    console.error("Profile fetch error:", profileResponse.error);
    throw new Error("Failed to fetch user profile");
  }

  if (articlesResponse.error) {
    console.error("Trending articles fetch error:", articlesResponse.error);
    throw new Error("Failed to fetch trending articles");
  }

  // The function returns articles directly
  const articles = Array.isArray(articlesResponse.data) ? articlesResponse.data : [];

  return {
    articles: articles.map((article: any) => ({
      ...article,
      user_id: article.author_id, // Ensure compatibility
    })),
    userProfile: profileResponse.data as Profile,
  };
}

export default async function TrendingPage() {
  try {
    const { articles, userProfile } = await getTrendingArticlesData();

    return (
      <div className="flex bg-background">
        <main className="flex-1 flex flex-col">
          <Suspense fallback={<ArticlesSkeleton />}>
            {articles.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-12">
                <div className="text-center">
                  <h2 className="text-lg font-semibold mb-2">No trending articles</h2>
                  <p className="text-muted-foreground mb-4">
                    Check back later for popular content.
                  </p>
                  <a
                    href="/ctn"
                    className="text-primary hover:underline"
                  >
                    Explore all articles →
                  </a>
                </div>
              </div>
            ) : (
              <ArticleTimeline
                articles={articles}
                userProfile={userProfile}
                errorMessage={null}
                rpcFunction="get_trending_articles"
                title="🔥 Trending"
              />
            )}
          </Suspense>
        </main>
      </div>
    );
  } catch (error) {
    console.error("Error loading trending page:", error);
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-red-500">
          Failed to load trending articles. Please try again later.
        </p>
      </div>
    );
  }
}
