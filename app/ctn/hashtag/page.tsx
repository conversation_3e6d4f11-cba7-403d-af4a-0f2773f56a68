import { createClient } from "@/utils/supabase/server";
import { redirect } from "next/navigation";
import HashtagExplorer from "@/components/HashtagExplorer";
import { UserProfile as Profile } from "@/types";

interface HashtagPageProps {
  params: Promise<{
    hashtag?: string;
  }>;
  searchParams: Promise<{
    tag?: string;
  }>;
}

export default async function HashtagPage({ params, searchParams }: HashtagPageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const supabase = await createClient();

  // Check if user is authenticated
  const {
    data: { user },
    error: userError,
  } = await (supabase as any).auth.getUser();

  if (!user || userError) {
    redirect("/signin");
  }

  // Fetch user profile
  const { data: profile, error: profileError } = await (supabase as any)
    .from("users")
    .select("*")
    .eq("user_id", user.id)
    .single();

  if (!profile || profileError) {
    console.error("Profile fetch error:", profileError?.message);
    redirect("/error?message=profile-fetch-failed");
  }

  const initialHashtag = resolvedParams.hashtag || resolvedSearchParams.tag;

  return (
    <HashtagExplorer 
      userProfile={profile as Profile} 
      initialHashtag={initialHashtag}
    />
  );
}
