'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  RefreshCw,
  Percent,
  TrendingUp,
  Calendar,
  BarChart3,
  Activity,
  Info,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { InflationCharts } from '@/components/InflationCharts';
import { ModernContainer, ModernSection, ModernGrid } from '@/components/modern-layout';
import { getAllCoinsData, type CentralizedCoinData } from '@/utils/mock-data-service';

// Define chain data with inflation metrics using static data
interface ChainData {
  id: string;
  name: string;
  symbol: string;
  inflation: {
    mom: number;
    yoy: number;
  };
  price: number;
  marketCap: number;
  change24h: number;
  circulatingSupply: number;
}

// Calculate inflation metrics from static data
function calculateInflationMetrics(coin: CentralizedCoinData): ChainData {
  // Base inflation rates based on coin characteristics
  const inflationProfiles = {
    'BTC': { mom: 0.04, yoy: 0.65 }, // Post-halving, very low inflation
    'ETH': { mom: -0.08, yoy: -0.2 }, // Deflationary due to EIP-1559
    'SOL': { mom: 0.31, yoy: 4.8 }, // Higher staking inflation
    'BNB': { mom: -0.05, yoy: -0.8 }, // Burns reduce supply
    'ADA': { mom: 0.19, yoy: 3.2 }, // Staking rewards
    'XRP': { mom: 0.0, yoy: 0.0 }, // Fixed supply
    'SUI': { mom: 0.25, yoy: 4.1 }, // New chain with higher inflation
    'DOGE': { mom: 0.33, yoy: 5.0 }, // High inflation
    'PEPE': { mom: 0.0, yoy: 0.0 }, // Meme coin, fixed supply
    'BONK': { mom: 0.0, yoy: 0.0 }, // Meme coin, fixed supply
    'SEI': { mom: 0.28, yoy: 4.2 }, // New chain
    'RAY': { mom: 0.12, yoy: 2.1 }, // DEX token
    'JUP': { mom: 0.08, yoy: 1.5 }, // DEX aggregator
    'PYTH': { mom: 0.15, yoy: 2.8 }, // Oracle token
    'NATIX': { mom: 0.35, yoy: 5.5 }, // High inflation new project
    'RENDER': { mom: 0.05, yoy: 0.8 }, // Utility token
    'ZEUS': { mom: 0.40, yoy: 6.2 }, // High inflation new project
    'APE': { mom: 0.10, yoy: 1.8 }, // Governance token
    'FLOKI': { mom: 0.02, yoy: 0.3 }, // Deflationary meme
    'PENGU': { mom: 0.01, yoy: 0.1 }, // Low inflation NFT token
    'WIF': { mom: 0.03, yoy: 0.4 }, // Low inflation meme
    'SHIB': { mom: 0.0, yoy: 0.0 }, // Fixed supply meme
  };
  
  const baseInflation = inflationProfiles[coin.symbol as keyof typeof inflationProfiles] || 
    { mom: 0.15, yoy: 2.5 }; // Default moderate inflation
  
  // Add small variation to make data more realistic
  const momVariation = (Math.random() - 0.5) * 0.05;
  const yoyVariation = (Math.random() - 0.5) * 0.3;
  
  return {
    id: coin.symbol.toLowerCase(),
    name: coin.name,
    symbol: coin.symbol,
    inflation: {
      mom: Math.max(-1, Math.min(2, baseInflation.mom + momVariation)),
      yoy: Math.max(-5, Math.min(15, baseInflation.yoy + yoyVariation))
    },
    price: coin.price,
    marketCap: coin.marketCap,
    change24h: coin.change24h,
    circulatingSupply: coin.marketCap / coin.price
  };
}

export default function InflationAnalysisPage() {
  const searchParams = useSearchParams();
  const [selectedChainId, setSelectedChainId] = useState('btc');
  const [selectedView, setSelectedView] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [chains, setChains] = useState<ChainData[]>([]);
  const [isLoadingChains, setIsLoadingChains] = useState(true);

  // Load chain data from centralized mock service
  useEffect(() => {
    const loadChainData = async () => {
      try {
        setIsLoadingChains(true);
        const coinsData = await getAllCoinsData();
        const chainData = coinsData.map(calculateInflationMetrics);
        setChains(chainData);
        
        // Set first chain as default if current selection not found
        if (!chainData.find(c => c.id === selectedChainId)) {
          setSelectedChainId(chainData[0]?.id || 'btc');
        }
      } catch (error) {
        console.error('Failed to load chain data:', error);
        setChains([]);
      } finally {
        setIsLoadingChains(false);
      }
    };

    loadChainData();
  }, []);

  // Handle URL parameters
  useEffect(() => {
    const chain = searchParams.get('chain');
    const view = searchParams.get('view');
    
    if (chain && chains.find(c => c.id === chain)) {
      setSelectedChainId(chain);
    }
    if (view && ['overview', 'mom', 'yoy'].includes(view)) {
      setSelectedView(view);
    }
  }, [searchParams, chains]);

  const selectedChain = chains.find(c => c.id === selectedChainId) || chains[0];

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate data refresh
    setTimeout(() => setIsLoading(false), 1500);
  };

  const getInflationLevel = (rate: number) => {
    if (rate < -1) return { level: 'Deflationary', color: 'text-green-600', bg: 'bg-green-100' };
    if (rate < 0) return { level: 'Low Deflation', color: 'text-green-500', bg: 'bg-green-50' };
    if (rate < 2) return { level: 'Low', color: 'text-blue-600', bg: 'bg-blue-100' };
    if (rate < 5) return { level: 'Moderate', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    if (rate < 10) return { level: 'High', color: 'text-orange-600', bg: 'bg-orange-100' };
    return { level: 'Very High', color: 'text-red-600', bg: 'bg-red-100' };
  };

  const views = [
    { 
      id: 'overview', 
      name: 'Overview', 
      description: 'Combined inflation analysis and projections',
      icon: BarChart3,
      color: 'text-blue-500'
    },
    { 
      id: 'mom', 
      name: 'Month-over-Month', 
      description: 'Monthly inflation rate analysis',
      icon: Calendar,
      color: 'text-green-500'
    },
    { 
      id: 'yoy', 
      name: 'Year-over-Year', 
      description: 'Annual inflation rate and supply impact',
      icon: TrendingUp,
      color: 'text-purple-500'
    }
  ];

  return (
    <ModernContainer>
      <ModernSection>
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Percent className="h-8 w-8 text-purple-500" />
              Inflation Analysis
              <Badge variant="outline" className="text-xs text-purple-600">Economic Metrics</Badge>
            </h1>
            <p className="text-muted-foreground mt-2">
              Comprehensive inflation analysis for blockchain networks and their economic impact
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              <Activity className="h-3 w-3 mr-1" />
              {chains.length} Chains Available
            </Badge>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Chain Selection & Controls */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Analysis Configuration
            </CardTitle>
            <CardDescription>
              Select blockchain and analysis view for detailed inflation metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Blockchain</label>
                <Select value={selectedChainId} onValueChange={setSelectedChainId}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {chains.map((chain) => (
                      <SelectItem key={chain.id} value={chain.id}>
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{chain.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {chain.symbol}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-1 ml-4">
                            {chain.inflation.yoy > 0 ? (
                              <ArrowUp className="h-3 w-3 text-red-500" />
                            ) : chain.inflation.yoy < 0 ? (
                              <ArrowDown className="h-3 w-3 text-green-500" />
                            ) : (
                              <Minus className="h-3 w-3 text-gray-500" />
                            )}
                            <span className="text-xs">{chain.inflation.yoy.toFixed(1)}%</span>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Analysis View</label>
                <Select value={selectedView} onValueChange={setSelectedView}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {views.map((view) => (
                      <SelectItem key={view.id} value={view.id}>
                        <div className="flex items-center gap-2">
                          <view.icon className={`h-4 w-4 ${view.color}`} />
                          <span>{view.name}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats Overview */}
        {selectedChain && !isLoadingChains && (
          <ModernGrid className="mb-8">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-blue-500" />
                  Monthly Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold flex items-center gap-2">
                  {selectedChain.inflation.mom > 0 ? (
                    <ArrowUp className="h-6 w-6 text-red-500" />
                  ) : selectedChain.inflation.mom < 0 ? (
                    <ArrowDown className="h-6 w-6 text-green-500" />
                  ) : (
                    <Minus className="h-6 w-6 text-gray-500" />
                  )}
                  <span className={selectedChain.inflation.mom > 0 ? 'text-red-500' : 'text-green-500'}>
                    {selectedChain.inflation.mom > 0 ? '+' : ''}{selectedChain.inflation.mom.toFixed(2)}%
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">Month-over-Month</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-purple-500" />
                  Annual Rate
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold flex items-center gap-2">
                  {selectedChain.inflation.yoy > 0 ? (
                    <ArrowUp className="h-6 w-6 text-red-500" />
                  ) : selectedChain.inflation.yoy < 0 ? (
                    <ArrowDown className="h-6 w-6 text-green-500" />
                  ) : (
                    <Minus className="h-6 w-6 text-gray-500" />
                  )}
                  <span className={selectedChain.inflation.yoy > 0 ? 'text-red-500' : 'text-green-500'}>
                    {selectedChain.inflation.yoy > 0 ? '+' : ''}{selectedChain.inflation.yoy.toFixed(2)}%
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">Year-over-Year</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Activity className="h-5 w-5 text-green-500" />
                  Inflation Level
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Badge className={`${getInflationLevel(selectedChain.inflation.yoy).color} ${getInflationLevel(selectedChain.inflation.yoy).bg} text-sm px-3 py-1`}>
                  {getInflationLevel(selectedChain.inflation.yoy).level}
                </Badge>
                <p className="text-sm text-muted-foreground mt-2">
                  Based on annual rate
                </p>
              </CardContent>
            </Card>
          </ModernGrid>
        )}

        {/* Loading State */}
        {isLoadingChains && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-muted-foreground">Loading inflation data...</p>
            </div>
          </div>
        )}

        {/* Analysis View Cards */}
        <ModernGrid className="mb-8">
          {views.map((view) => {
            const isSelected = view.id === selectedView;
            
            return (
              <Card 
                key={view.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected ? 'ring-2 ring-purple-200 dark:ring-purple-800' : ''
                }`}
                onClick={() => setSelectedView(view.id)}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <view.icon className={`h-5 w-5 ${view.color}`} />
                    {view.name}
                    {isSelected && (
                      <Badge variant="default" className="text-xs bg-purple-500">
                        Active
                      </Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground">
                    {view.description}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </ModernGrid>

        {/* Main Inflation Analysis Display */}
        {selectedChain && !isLoadingChains && (
          <InflationCharts 
            selectedChain={selectedChain}
            onRefresh={handleRefresh}
          />
        )}

        {/* Information Panel */}
        <Card className="mt-8 border-purple-200 bg-purple-50/50 dark:bg-purple-950/20">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Info className="h-5 w-5 text-purple-500" />
              Understanding Inflation Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-semibold mb-2">What is Crypto Inflation?</h4>
                <p className="text-muted-foreground">
                  Crypto inflation refers to the rate at which new tokens are created and added to 
                  the circulating supply. This affects token value, staking rewards, and overall 
                  economic dynamics of the blockchain network.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Key Metrics</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• <span className="font-semibold">MoM:</span> Short-term inflation trends</li>
                  <li>• <span className="font-semibold">YoY:</span> Annual supply growth rate</li>
                  <li>• <span className="font-semibold">Deflationary:</span> Negative inflation (supply decrease)</li>
                  <li>• <span className="font-semibold">Impact:</span> Price pressure and tokenomics</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </ModernSection>
    </ModernContainer>
  );
}
