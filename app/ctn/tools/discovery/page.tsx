'use client';

import { useState, useEffect } from 'react';
import { CachedTierProtection } from '@/components/CachedTierProtection';
import { CoinDiscovery } from '@/components/CoinDiscovery';
import { InteractiveMetricChart, MetricChartsGrid } from '@/components/InteractiveMetricChart';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getAllCoinsData, type CentralizedCoinData } from '@/utils/mock-data-service';
import { 
  Activity, 
  TrendingUp, 
  Calculator, 
  Zap, 
  Search, 
  BarChart3, 
  RefreshCw,
  DollarSign,
  Users,
  Globe
} from 'lucide-react';

// Generate discovery chart data
function generateDiscoveryChartData(coins: CentralizedCoinData[], metric: string, days: number = 30) {
  const data = [];
  const now = new Date();
  
  // Calculate aggregate metric from all coins
  let baseValue = 0;
  switch (metric) {
    case 'totalMarketCap':
      baseValue = coins.reduce((sum, coin) => sum + coin.marketCap, 0);
      break;
    case 'totalVolume':
      baseValue = coins.reduce((sum, coin) => sum + coin.volume24h, 0);
      break;
    case 'averageChange':
      baseValue = coins.reduce((sum, coin) => sum + coin.change24h, 0) / coins.length;
      break;
    case 'activeProjects':
      baseValue = coins.length;
      break;
    default:
      baseValue = coins.reduce((sum, coin) => sum + coin.marketCap, 0);
  }

  for (let i = days; i >= 0; i--) {
    const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
    
    // Add realistic variation
    const volatility = metric === 'averageChange' ? 0.3 : 0.05;
    const trend = Math.sin(i / 14) * 0.02; // Bi-weekly trend
    const randomChange = (Math.random() - 0.5) * volatility;
    const value = Math.max(0, baseValue * (1 + trend + randomChange));
    
    data.push({
      time: date.toISOString().split('T')[0],
      value: value
    });
  }
  
  return data;
}

export default function DiscoveryPage() {
  const [activeTab, setActiveTab] = useState('discovery');
  const [coinsData, setCoinsData] = useState<CentralizedCoinData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load coin data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const data = await getAllCoinsData();
        setCoinsData(data);
      } catch (error) {
        console.error('Failed to load coin data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1000);
  };

  // Discovery metrics for charts
  const discoveryMetrics = coinsData.length > 0 ? [
    {
      id: 'totalMarketCap',
      title: 'Total Market Cap',
      description: 'Combined market capitalization of all tracked cryptocurrencies',
      data: generateDiscoveryChartData(coinsData, 'totalMarketCap'),
      color: '#3b82f6',
      icon: <DollarSign className="h-5 w-5" />,
      unit: '$',
      change24h: 2.3
    },
    {
      id: 'totalVolume',
      title: 'Total Trading Volume',
      description: 'Combined 24-hour trading volume across all markets',
      data: generateDiscoveryChartData(coinsData, 'totalVolume'),
      color: '#8b5cf6',
      icon: <BarChart3 className="h-5 w-5" />,
      unit: '$',
      change24h: 15.7
    },
    {
      id: 'averageChange',
      title: 'Market Sentiment',
      description: 'Average 24-hour price change across all cryptocurrencies',
      data: generateDiscoveryChartData(coinsData, 'averageChange'),
      color: '#10b981',
      icon: <TrendingUp className="h-5 w-5" />,
      unit: '%',
      change24h: 1.2
    },
    {
      id: 'activeProjects',
      title: 'Active Projects',
      description: 'Number of actively tracked cryptocurrency projects',
      data: generateDiscoveryChartData(coinsData, 'activeProjects'),
      color: '#f59e0b',
      icon: <Activity className="h-5 w-5" />,
      unit: '',
      change24h: 0.8
    }
  ] : [];

  if (isLoading) {
    return (
      <CachedTierProtection pagePath="/ctn/tools/discovery">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-muted-foreground">Loading discovery data...</p>
            </div>
          </div>
        </div>
      </CachedTierProtection>
    );
  }

  return (
    <CachedTierProtection pagePath="/ctn/tools/discovery">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Coin Discovery</h1>
            <p className="text-gray-600 mt-2">
              Discover new cryptocurrencies and trending tokens with advanced filtering and analytics
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-blue-600">
              Tier 2+ Feature
            </Badge>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Discovery Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="discovery">Coin Discovery</TabsTrigger>
            <TabsTrigger value="analytics">Market Analytics</TabsTrigger>
            <TabsTrigger value="trends">Trending Analysis</TabsTrigger>
          </TabsList>

          <TabsContent value="discovery" className="space-y-6">
            <CoinDiscovery 
              onCoinSelect={(coin) => {
                console.log('Selected coin:', coin);
                // Navigate to coin details or show modal
                // Could integrate with a dedicated coin details page
              }}
              onWatchlistToggle={(coinId) => {
                console.log('Toggled watchlist for:', coinId);
                // Integrate with user's watchlist system
                // Could sync with Supabase user preferences
              }}
              loading={false}
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Market Analytics Dashboard
                </CardTitle>
                <CardDescription>
                  Comprehensive market metrics and trends across all tracked cryptocurrencies
                </CardDescription>
              </CardHeader>
            </Card>

            <MetricChartsGrid
              metrics={discoveryMetrics}
              onTimeRangeChange={(metricId, range) => console.log('Time range changed:', metricId, range)}
              onRefresh={(metricId) => console.log('Refresh metric:', metricId)}
              onExport={(metricId) => console.log('Export metric:', metricId)}
            />
          </TabsContent>

          <TabsContent value="trends" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <InteractiveMetricChart
                title="Market Sentiment Trend"
                description="Average price change across all cryptocurrencies"
                data={generateDiscoveryChartData(coinsData, 'averageChange')}
                color="#10b981"
                icon={<TrendingUp className="h-5 w-5" />}
                unit="%"
                change24h={1.2}
                isActive={activeTab === 'trends'}
              />
              
              <InteractiveMetricChart
                title="Volume Trend Analysis"
                description="Trading volume trends across the market"
                data={generateDiscoveryChartData(coinsData, 'totalVolume')}
                color="#8b5cf6"
                icon={<BarChart3 className="h-5 w-5" />}
                unit="$"
                change24h={15.7}
                isActive={activeTab === 'trends'}
              />

              <InteractiveMetricChart
                title="Market Capitalization"
                description="Total market cap evolution over time"
                data={generateDiscoveryChartData(coinsData, 'totalMarketCap')}
                color="#3b82f6"
                icon={<DollarSign className="h-5 w-5" />}
                unit="$"
                change24h={2.3}
                isActive={activeTab === 'trends'}
              />

              <InteractiveMetricChart
                title="Project Activity"
                description="Number of active cryptocurrency projects"
                data={generateDiscoveryChartData(coinsData, 'activeProjects')}
                color="#f59e0b"
                icon={<Activity className="h-5 w-5" />}
                unit=""
                change24h={0.8}
                isActive={activeTab === 'trends'}
              />
            </div>

            {/* Market Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-8">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Projects</p>
                      <p className="text-2xl font-bold">{coinsData.length}</p>
                    </div>
                    <Globe className="h-8 w-8 text-blue-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Market Cap</p>
                      <p className="text-2xl font-bold">
                        ${(coinsData.reduce((sum, coin) => sum + coin.marketCap, 0) / 1e12).toFixed(1)}T
                      </p>
                    </div>
                    <DollarSign className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">24h Volume</p>
                      <p className="text-2xl font-bold">
                        ${(coinsData.reduce((sum, coin) => sum + coin.volume24h, 0) / 1e9).toFixed(0)}B
                      </p>
                    </div>
                    <BarChart3 className="h-8 w-8 text-purple-500" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Avg Change</p>
                      <p className="text-2xl font-bold text-green-600">
                        +{(coinsData.reduce((sum, coin) => sum + coin.change24h, 0) / coinsData.length).toFixed(1)}%
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </CachedTierProtection>
  );
}