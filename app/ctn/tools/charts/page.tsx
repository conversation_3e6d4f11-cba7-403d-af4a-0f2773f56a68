'use client';

import { useState, useEffect } from 'react';
import { CachedTierProtection } from '@/components/CachedTierProtection';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { InteractiveMetricChart, MetricChartsGrid } from '@/components/InteractiveMetricChart';
import { getAllCoinsData, type CentralizedCoinData } from '@/utils/mock-data-service';
import { 
  TrendingUp, 
  BarChart3, 
  Activity, 
  DollarSign, 
  Users, 
  Zap, 
  RefreshCw,
  Globe
} from 'lucide-react';

// Generate chart data from coin metrics
function generateChartDataFromCoin(coin: CentralizedCoinData, metric: string, days: number = 30) {
  const data = [];
  const now = new Date();
  
  // Get base value for the metric
  let baseValue = 0;
  switch (metric) {
    case 'price':
      baseValue = coin.price;
      break;
    case 'volume':
      baseValue = coin.volume24h;
      break;
    case 'marketCap':
      baseValue = coin.marketCap;
      break;
    case 'activeAddresses':
      baseValue = coin.activeAddresses24h || 100000;
      break;
    case 'transactions':
      baseValue = coin.transactions24h || 1000000;
      break;
    case 'fees':
      baseValue = coin.chainFees24h || 10000;
      break;
    case 'tps':
      baseValue = coin.currentTPS || 100;
      break;
    case 'tvl':
      baseValue = coin.tvl || 1000000000;
      break;
    default:
      baseValue = coin.price;
  }

  for (let i = days; i >= 0; i--) {
    const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
    
    // Add realistic variation based on the metric type
    const volatility = metric === 'price' ? 0.05 : metric === 'volume' ? 0.3 : 0.1;
    const trend = Math.sin(i / 7) * 0.02; // Weekly trend
    const randomChange = (Math.random() - 0.5) * volatility;
    const value = Math.max(0, baseValue * (1 + trend + randomChange));
    
    data.push({
      time: date.toISOString().split('T')[0],
      value: value
    });
  }
  
  return data;
}

export default function OnChainChartsPage() {
  const [selectedCoin, setSelectedCoin] = useState('BTC');
  const [activeTab, setActiveTab] = useState('overview');
  const [coinsData, setCoinsData] = useState<CentralizedCoinData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load coin data
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        const data = await getAllCoinsData();
        setCoinsData(data);
      } catch (error) {
        console.error('Failed to load coin data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const selectedCoinData = coinsData.find(coin => coin.symbol === selectedCoin) || coinsData[0];

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1000);
  };

  // Price and Market Metrics
  const priceMetrics = selectedCoinData ? [
    {
      id: 'price',
      title: 'Price Trends',
      description: `${selectedCoinData.name} price movement over time`,
      data: generateChartDataFromCoin(selectedCoinData, 'price'),
      color: '#10b981',
      icon: <TrendingUp className="h-5 w-5" />,
      unit: '$',
      change24h: selectedCoinData.change24h
    },
    {
      id: 'marketCap',
      title: 'Market Cap',
      description: 'Total market capitalization trends',
      data: generateChartDataFromCoin(selectedCoinData, 'marketCap'),
      color: '#3b82f6',
      icon: <DollarSign className="h-5 w-5" />,
      unit: '$',
      change24h: selectedCoinData.change24h * 0.8
    },
    {
      id: 'volume',
      title: 'Trading Volume',
      description: '24-hour trading volume analysis',
      data: generateChartDataFromCoin(selectedCoinData, 'volume'),
      color: '#8b5cf6',
      icon: <BarChart3 className="h-5 w-5" />,
      unit: '$',
      change24h: selectedCoinData.change24h * 1.5
    },
    {
      id: 'tvl',
      title: 'Total Value Locked',
      description: 'TVL across DeFi protocols',
      data: generateChartDataFromCoin(selectedCoinData, 'tvl'),
      color: '#f59e0b',
      icon: <Activity className="h-5 w-5" />,
      unit: '$',
      change24h: selectedCoinData.change24h * 0.6
    }
  ] : [];

  // Network Activity Metrics
  const networkMetrics = selectedCoinData ? [
    {
      id: 'activeAddresses',
      title: 'Active Addresses',
      description: 'Daily active addresses on the network',
      data: generateChartDataFromCoin(selectedCoinData, 'activeAddresses'),
      color: '#06b6d4',
      icon: <Users className="h-5 w-5" />,
      unit: '',
      change24h: 2.5
    },
    {
      id: 'transactions',
      title: 'Transaction Count',
      description: 'Daily transaction volume',
      data: generateChartDataFromCoin(selectedCoinData, 'transactions'),
      color: '#84cc16',
      icon: <Activity className="h-5 w-5" />,
      unit: '',
      change24h: 1.8
    },
    {
      id: 'fees',
      title: 'Network Fees',
      description: 'Daily fees paid to validators',
      data: generateChartDataFromCoin(selectedCoinData, 'fees'),
      color: '#ef4444',
      icon: <DollarSign className="h-5 w-5" />,
      unit: '$',
      change24h: -0.5
    },
    {
      id: 'tps',
      title: 'Transactions Per Second',
      description: 'Network throughput and performance',
      data: generateChartDataFromCoin(selectedCoinData, 'tps'),
      color: '#f97316',
      icon: <Zap className="h-5 w-5" />,
      unit: ' TPS',
      change24h: 0.2
    }
  ] : [];

  if (isLoading) {
    return (
      <CachedTierProtection pagePath="/ctn/tools/charts">
        <div className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-muted-foreground">Loading chart data...</p>
            </div>
          </div>
        </div>
      </CachedTierProtection>
    );
  }

  return (
    <CachedTierProtection pagePath="/ctn/tools/charts">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">OnChain Charts</h1>
            <p className="text-gray-600 mt-2">
              Advanced cryptocurrency analytics and on-chain data visualization
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-blue-600">
              Tier 1+ Feature
            </Badge>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Coin Selection */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Select Cryptocurrency
            </CardTitle>
            <CardDescription>
              Choose a cryptocurrency to analyze on-chain metrics and charts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Select value={selectedCoin} onValueChange={setSelectedCoin}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {coinsData.map((coin) => (
                      <SelectItem key={coin.symbol} value={coin.symbol}>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{coin.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {coin.symbol}
                          </Badge>
                          <span className={`text-xs ${coin.change24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {coin.change24h >= 0 ? '+' : ''}{coin.change24h.toFixed(2)}%
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {selectedCoinData && (
                <div className="text-right">
                  <div className="text-lg font-bold">
                    ${selectedCoinData.price.toLocaleString(undefined, { maximumFractionDigits: 2 })}
                  </div>
                  <div className={`text-sm ${selectedCoinData.change24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {selectedCoinData.change24h >= 0 ? '+' : ''}{selectedCoinData.change24h.toFixed(2)}% (24h)
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Chart Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="price">Price & Market</TabsTrigger>
            <TabsTrigger value="network">Network Activity</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {selectedCoinData && (
                <>
                  <InteractiveMetricChart
                    title="Price Trends"
                    description={`${selectedCoinData.name} price movement`}
                    data={generateChartDataFromCoin(selectedCoinData, 'price')}
                    color="#10b981"
                    icon={<TrendingUp className="h-5 w-5" />}
                    unit="$"
                    change24h={selectedCoinData.change24h}
                    isActive={true}
                  />
                  <InteractiveMetricChart
                    title="Trading Volume"
                    description="24-hour trading volume"
                    data={generateChartDataFromCoin(selectedCoinData, 'volume')}
                    color="#8b5cf6"
                    icon={<BarChart3 className="h-5 w-5" />}
                    unit="$"
                    change24h={selectedCoinData.change24h * 1.5}
                    isActive={true}
                  />
                  <InteractiveMetricChart
                    title="Active Addresses"
                    description="Daily active addresses"
                    data={generateChartDataFromCoin(selectedCoinData, 'activeAddresses')}
                    color="#06b6d4"
                    icon={<Users className="h-5 w-5" />}
                    unit=""
                    change24h={2.5}
                    isActive={true}
                  />
                  <InteractiveMetricChart
                    title="Network Fees"
                    description="Daily fees paid to validators"
                    data={generateChartDataFromCoin(selectedCoinData, 'fees')}
                    color="#ef4444"
                    icon={<DollarSign className="h-5 w-5" />}
                    unit="$"
                    change24h={-0.5}
                    isActive={true}
                  />
                </>
              )}
            </div>
          </TabsContent>

          <TabsContent value="price" className="space-y-6">
            <MetricChartsGrid
              metrics={priceMetrics}
              onTimeRangeChange={(metricId, range) => console.log('Time range changed:', metricId, range)}
              onRefresh={(metricId) => console.log('Refresh metric:', metricId)}
              onExport={(metricId) => console.log('Export metric:', metricId)}
            />
          </TabsContent>

          <TabsContent value="network" className="space-y-6">
            <MetricChartsGrid
              metrics={networkMetrics}
              onTimeRangeChange={(metricId, range) => console.log('Time range changed:', metricId, range)}
              onRefresh={(metricId) => console.log('Refresh metric:', metricId)}
              onExport={(metricId) => console.log('Export metric:', metricId)}
            />
          </TabsContent>
        </Tabs>
      </div>
    </CachedTierProtection>
  );
}
