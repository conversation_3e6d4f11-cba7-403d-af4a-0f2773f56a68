'use client';

import { useState } from 'react';
import { CachedTierProtection } from '@/components/CachedTierProtection';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Activity,
  TrendingUp,
  Users,
  DollarSign,
  Clock,
  BarChart3,
  Target,
  Zap
} from 'lucide-react';
import { CryptoHeatmap } from '@/components/CryptoHeatmap';

export default function DataHeatmapPage() {
  const [activeTab, setActiveTab] = useState('heatmap');

  return (
    <CachedTierProtection pagePath="/ctn/tools/heatmap">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">DATA Heatmap</h1>
            <p className="text-gray-600 mt-2">
              Interactive cryptocurrency market heatmap and sentiment analysis
            </p>
          </div>
          <Badge variant="outline" className="text-blue-600">
            Tier 1+ Feature
          </Badge>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="heatmap">Market Heatmap</TabsTrigger>
            <TabsTrigger value="sentiment">Sentiment</TabsTrigger>
            <TabsTrigger value="volume">Volume</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="heatmap" className="space-y-6">
            <CryptoHeatmap
              isActive={activeTab === 'heatmap'}
              onRefresh={() => {
                console.log('Refreshing heatmap data...');
                // Implement real data refresh
              }}
              onCoinClick={(coin) => {
                console.log('Coin clicked:', coin);
                // Navigate to coin details or show modal
              }}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    Top Gainers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex justify-between items-center p-2 bg-green-50 rounded">
                        <span className="font-medium">BTC</span>
                        <span className="text-green-600 font-bold">+{i * 2}.5%</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-500" />
                    Most Active
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex justify-between items-center p-2 bg-blue-50 rounded">
                        <span className="font-medium">ETH</span>
                        <span className="text-blue-600 font-bold">{i * 100}K</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-purple-500" />
                    Volume Leaders
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex justify-between items-center p-2 bg-purple-50 rounded">
                        <span className="font-medium">ADA</span>
                        <span className="text-purple-600 font-bold">${i * 50}M</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="sentiment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-orange-500" />
                  Market Sentiment Analysis
                </CardTitle>
                <CardDescription>
                  Real-time sentiment tracking across social media and news
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500">Sentiment Analysis Component</span>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="volume" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-500" />
                  Volume Analysis
                </CardTitle>
                <CardDescription>
                  Trading volume patterns and market depth
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
                  <span className="text-gray-500">Volume Analysis Component</span>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-500" />
                  Advanced Analytics
                </CardTitle>
                <CardDescription>
                  Contribute to the heatmap and earn experience points
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <h3 className="font-semibold mb-2">Contribute Data</h3>
                    <p className="text-sm text-gray-600 mb-4">
                      Share market insights and data points to help improve the heatmap accuracy
                    </p>
                    <Button className="bg-yellow-500 hover:bg-yellow-600">
                      <Zap className="h-4 w-4 mr-2" />
                      Contribute & Earn XP
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </CachedTierProtection>
  );
}
