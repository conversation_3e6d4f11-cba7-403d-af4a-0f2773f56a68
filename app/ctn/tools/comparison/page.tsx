'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  RefreshCw, 
  BarChart3, 
  Calculator, 
  Crown,
  TrendingUp,
  Activity,
  Zap
} from 'lucide-react';
import { AutomatedComparisonTool } from '@/components/AutomatedComparisonTool';
import { ManualComparisonTool } from '@/components/ManualComparisonTool';
import { ModernContainer, ModernSection, ModernGrid } from '@/components/modern-layout';

export default function ComparisonPage() {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('automated');
  const [isLoading, setIsLoading] = useState(false);

  // Handle URL tab parameter
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['automated', 'manual'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Mock available chains count for display
  const availableChainsCount = 15;

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate refresh
    setTimeout(() => setIsLoading(false), 1000);
  };

  return (
    <ModernContainer>
      <ModernSection>
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-blue-500" />
              Blockchain Comparison Tools
            </h1>
            <p className="text-muted-foreground mt-2">
              Compare multiple blockchain networks with automated analysis or manual data input
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              <Activity className="h-3 w-3 mr-1" />
              {availableChainsCount} Chains Available
            </Badge>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Feature Overview Cards */}
        <ModernGrid className="mb-8">
          <Card className="border-blue-200 bg-blue-50/50 dark:bg-blue-950/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Crown className="h-5 w-5 text-yellow-500" />
                Automated Analysis
                <Badge variant="outline" className="text-xs text-yellow-600">Tier 3+</Badge>
              </CardTitle>
              <CardDescription>
                AI-powered comparison with cumulative scoring and automated data collection
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-blue-500" />
                  <span>Select up to 5 blockchains</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span>Automated scoring algorithm</span>
                </div>
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-purple-500" />
                  <span>Interactive charts & rankings</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-green-50/50 dark:bg-green-950/20">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Calculator className="h-5 w-5 text-blue-500" />
                Manual Input
                <Badge variant="outline" className="text-xs text-blue-600">Tier 1+</Badge>
              </CardTitle>
              <CardDescription>
                Custom data input for detailed manual comparison analysis
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Calculator className="h-4 w-4 text-blue-500" />
                  <span>Custom metric input</span>
                </div>
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                  <span>Derived metrics calculation</span>
                </div>
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-purple-500" />
                  <span>CSV export functionality</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </ModernGrid>

        {/* Main Comparison Tools */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="automated" className="flex items-center gap-2">
              <Crown className="h-4 w-4" />
              Automated Analysis
            </TabsTrigger>
            <TabsTrigger value="manual" className="flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              Manual Input
            </TabsTrigger>
          </TabsList>

          {/* Automated Comparison Tool */}
          <TabsContent value="automated" className="space-y-6">
            <AutomatedComparisonTool
              onRefresh={handleRefresh}
            />
          </TabsContent>

          {/* Manual Comparison Tool */}
          <TabsContent value="manual" className="space-y-6">
            <ManualComparisonTool onRefresh={handleRefresh} />
          </TabsContent>
        </Tabs>

        {/* Additional Information */}
        <Card className="mt-8 border-amber-200 bg-amber-50/50 dark:bg-amber-950/20">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5 text-amber-500" />
              How It Works
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Crown className="h-4 w-4 text-yellow-500" />
                  Automated Analysis
                </h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• Select 2-5 blockchain networks to compare</li>
                  <li>• Choose analysis type (Technical, Economic, Comprehensive)</li>
                  <li>• Adjust custom weights for different metrics</li>
                  <li>• View cumulative scores and detailed rankings</li>
                  <li>• Export results as charts or data</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2 flex items-center gap-2">
                  <Calculator className="h-4 w-4 text-blue-500" />
                  Manual Input
                </h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• Input custom metrics for each blockchain</li>
                  <li>• Add or remove chains as needed</li>
                  <li>• View automatically calculated derived metrics</li>
                  <li>• Compare side-by-side in table format</li>
                  <li>• Export complete analysis to CSV</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </ModernSection>
    </ModernContainer>
  );
}
