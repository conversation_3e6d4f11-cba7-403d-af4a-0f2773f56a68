'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSearchParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  RefreshCw, 
  Activity, 
  TrendingUp,
  Eye,
  BarChart3,
  DollarSign,
  Wallet,
  Crown,
  Info
} from 'lucide-react';
import { OnChainModels } from '@/components/OnChainModels';
import { ModernContainer, ModernSection, ModernGrid } from '@/components/modern-layout';
import { getAllCoinsData, type CentralizedCoinData } from '@/utils/mock-data-service';

// Utility function to calculate onchain metrics from static data
function calculateOnChainMetrics(coin: CentralizedCoinData) {
  // Calculate MVRV ratio (Market Value to Realized Value)
  // Using price volatility as a proxy for realized value differential
  const volatility = Math.abs(coin.change24h) / 100;
  const mvrv = 1 + (volatility * 2); // Simplified MVRV calculation
  
  // Calculate Network Value to Transactions ratio
  const nvt = coin.transactions24h ? coin.marketCap / (coin.transactions24h * 365) : 0;
  
  // Calculate Puell Multiple (proxy using volume and market cap)
  const puellMultiple = coin.volume24h / (coin.marketCap * 0.0001); // Simplified
  
  // Calculate additional metrics
  const addressGrowth = coin.newAddresses24h && coin.activeAddresses24h ? 
    (coin.newAddresses24h / coin.activeAddresses24h) * 100 : 0;
  
  const feeToMarketCapRatio = coin.chainFees24h ? 
    (coin.chainFees24h * 365) / coin.marketCap * 100 : 0;
  
  return {
    mvrv: Math.max(0.1, Math.min(5, mvrv)), // Clamp between realistic bounds
    nvt: Math.max(0, Math.min(1000, nvt)),
    puellMultiple: Math.max(0.1, Math.min(10, puellMultiple)),
    addressGrowth: Math.max(0, Math.min(50, addressGrowth)),
    feeToMarketCapRatio: Math.max(0, Math.min(10, feeToMarketCapRatio)),
    circulatingSupply: coin.marketCap / coin.price,
    timestamp: Date.now()
  };
}

export default function OnChainModelsPage() {
  const searchParams = useSearchParams();
  const [selectedChainId, setSelectedChainId] = useState('BTC');
  const [selectedModel, setSelectedModel] = useState('mvrv');
  const [isLoading, setIsLoading] = useState(false);
  const [chainData, setChainData] = useState<CentralizedCoinData[]>([]);

  // Load dynamic chain data from centralized service
  useEffect(() => {
    const loadChainData = async () => {
      setIsLoading(true);
      try {
        console.log('📊 Loading onchain models data from centralized service...');
        const coinsData = await getAllCoinsData();
        
        // Filter to main chains that have comprehensive onchain data
        const mainChains = coinsData.filter(coin => 
          ['BTC', 'ETH', 'SOL', 'BNB', 'ADA'].includes(coin.symbol)
        );
        
        setChainData(mainChains);
        console.log(`✅ Loaded ${mainChains.length} chains for onchain analysis`);
      } catch (error) {
        console.error('❌ Failed to load chain data:', error);
        setChainData([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadChainData();
  }, []);

  // Handle URL parameters
  useEffect(() => {
    const chain = searchParams.get('chain');
    const model = searchParams.get('model');
    
    if (chain && chainData.find(c => c.symbol === chain.toUpperCase())) {
      setSelectedChainId(chain.toUpperCase());
    }
    if (model) {
      setSelectedModel(model);
    }
  }, [searchParams, chainData]);

  // Calculate onchain metrics for all chains
  const chainsWithMetrics = useMemo(() => {
    return chainData.map(coin => ({
      id: coin.symbol.toLowerCase(),
      name: coin.name,
      symbol: coin.symbol,
      price: coin.price,
      marketCap: coin.marketCap,
      ...calculateOnChainMetrics(coin)
    }));
  }, [chainData]);

  const selectedChain = chainsWithMetrics.find(c => c.symbol === selectedChainId) || chainsWithMetrics[0];

  const handleRefresh = () => {
    setIsLoading(true);
    // Simulate data refresh
    setTimeout(() => setIsLoading(false), 1500);
  };

  const models = [
    { 
      id: 'rainbow', 
      name: 'Bitcoin Rainbow Chart', 
      description: 'Long-term price prediction model based on logarithmic regression',
      btcOnly: true,
      icon: Activity,
      color: 'text-orange-500'
    },
    { 
      id: 'mvrv', 
      name: 'MVRV & MVRV-Z Score', 
      description: 'Market value to realized value ratio and Z-score analysis',
      icon: TrendingUp,
      color: 'text-blue-500'
    },
    { 
      id: 'realized_cap', 
      name: 'Realized Cap vs Market Cap', 
      description: 'Compare market cap with realized cap for valuation insights',
      icon: BarChart3,
      color: 'text-green-500'
    },
    { 
      id: 'nrpl', 
      name: 'Net Realized Profit/Loss', 
      description: 'Track profit taking and loss realization patterns',
      icon: DollarSign,
      color: 'text-yellow-500'
    },
    { 
      id: 'nupl', 
      name: 'Net Unrealized Profit/Loss', 
      description: 'Market sentiment indicator based on unrealized gains/losses',
      icon: Wallet,
      color: 'text-purple-500'
    }
  ];

  return (
    <ModernContainer>
      <ModernSection>
        {/* Page Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-3">
              <Eye className="h-8 w-8 text-blue-500" />
              On-Chain Models
              <Badge variant="outline" className="text-xs text-blue-600">Advanced Analytics</Badge>
            </h1>
            <p className="text-muted-foreground mt-2">
              Advanced on-chain analysis models for blockchain valuation and market insights
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              <Activity className="h-3 w-3 mr-1" />
              {models.length} Models Available
            </Badge>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {/* Chain Selection & Controls */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Analysis Configuration
            </CardTitle>
            <CardDescription>
              Select blockchain and model for detailed on-chain analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Blockchain</label>
                <Select value={selectedChainId} onValueChange={setSelectedChainId}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {chainsWithMetrics.map((chain) => (
                      <SelectItem key={chain.symbol} value={chain.symbol}>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{chain.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {chain.symbol}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">Model</label>
                <Select value={selectedModel} onValueChange={setSelectedModel}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {models.map((model) => (
                      <SelectItem 
                        key={model.id} 
                        value={model.id}
                        disabled={model.btcOnly && selectedChain.symbol !== 'BTC'}
                      >
                        <div className="flex items-center gap-2">
                          <model.icon className={`h-4 w-4 ${model.color}`} />
                          <span>{model.name}</span>
                          {model.btcOnly && (
                            <Badge variant="outline" className="text-xs text-orange-600">
                              BTC Only
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Model Overview Cards */}
        <ModernGrid className="mb-8">
          {models.map((model) => {
            const isSelected = model.id === selectedModel;
            const isDisabled = model.btcOnly && selectedChain.symbol !== 'BTC';
            
            return (
              <Card 
                key={model.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected ? 'ring-2 ring-blue-200 dark:ring-blue-800' : ''
                } ${isDisabled ? 'opacity-50' : ''}`}
                onClick={() => !isDisabled && setSelectedModel(model.id)}
              >
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <model.icon className={`h-5 w-5 ${model.color}`} />
                    {model.name}
                    {model.btcOnly && (
                      <Badge variant="outline" className="text-xs text-orange-600">
                        BTC Only
                      </Badge>
                    )}
                    {isSelected && (
                      <Badge variant="default" className="text-xs bg-blue-500">
                        Active
                      </Badge>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <p className="text-sm text-muted-foreground">
                    {model.description}
                  </p>
                  {isDisabled && (
                    <div className="mt-2 text-xs text-amber-600">
                      Only available for Bitcoin
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </ModernGrid>

        {/* Main Model Display */}
        <OnChainModels 
          selectedChain={selectedChain}
          onRefresh={handleRefresh}
        />

        {/* Information Panel */}
        <Card className="mt-8 border-blue-200 bg-blue-50/50 dark:bg-blue-950/20">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Info className="h-5 w-5 text-blue-500" />
              About On-Chain Models
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-semibold mb-2">What are On-Chain Models?</h4>
                <p className="text-muted-foreground">
                  On-chain models analyze blockchain data to provide insights into market valuation, 
                  investor behavior, and price trends. These models use metrics like realized cap, 
                  profit/loss ratios, and historical patterns.
                </p>
              </div>
              <div>
                <h4 className="font-semibold mb-2">How to Use</h4>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• Select a blockchain to analyze</li>
                  <li>• Choose from 5 different analytical models</li>
                  <li>• View interactive charts and current metrics</li>
                  <li>• Use insights for investment decisions</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </ModernSection>
    </ModernContainer>
  );
}
