"use client";

import { CachedTierProtection } from "@/components/CachedTierProtection";
import { PortfolioCalculator } from "@/components/PortfolioCalculator";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { Activity, Calculator, Search, TrendingUp, Zap } from "lucide-react";

export default function CalculatorPage() {
  return (
    <CachedTierProtection pagePath="/ctn/tools/calculator">
      <div className="container mx-auto px-4 py-8">
        <PortfolioCalculator
          onSave={(holdings) => {
            // Save to localStorage or backend
            localStorage.setItem(
              "portfolio-holdings",
              JSON.stringify(holdings)
            );
            console.log("Portfolio saved:", holdings);
          }}
          onLoad={() => {
            // Load from localStorage or backend
            const saved = localStorage.getItem("portfolio-holdings");
            return saved ? JSON.parse(saved) : [];
          }}
        />
      </div>
    </CachedTierProtection>
  );
}
