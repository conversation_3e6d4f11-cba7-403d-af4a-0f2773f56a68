// components/settings/privacy/PrivacySettings.tsx
"use client";

import { useEffect } from "react";
import { useSettingsStore } from "@/stores/settingsStore";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

export function PrivacySettings() {
  const { settings, loading, error, fetchSettings, updateSettings } =
    useSettingsStore();
  const { toast } = useToast();

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  const handlePrivacyChange = async (key: string, value: any) => {
    if (!settings) return;

    try {
      await updateSettings({
        privacy: {
          ...settings.privacy,
          [key]: value,
        },
      });

      toast({
        title: "Settings updated",
        description: "Your privacy settings have been saved.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update settings.",
        variant: "destructive",
      });
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading settings</div>;
  if (!settings) return <div>No settings found</div>;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Privacy Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Post Visibility</Label>
            <Select
              value={settings.privacy.postVisibility}
              onValueChange={(value) =>
                handlePrivacyChange("postVisibility", value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="everyone">Everyone</SelectItem>
                <SelectItem value="followers">Followers only</SelectItem>
                <SelectItem value="nobody">Nobody</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label>Private Account</Label>
              <p className="text-sm text-muted-foreground">
                Only approved followers can see your content
              </p>
            </div>
            <Switch
              checked={settings.privacy.accountPrivate}
              onCheckedChange={(checked) =>
                handlePrivacyChange("accountPrivate", checked)
              }
            />
          </div>

          {/* Add other privacy settings */}
        </CardContent>
      </Card>
    </div>
  );
}
