// components/settings/account/AccountSettings.tsx
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"

export function AccountSettings() {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">Account Settings</h3>
        <p className="text-sm text-muted-foreground">
          Manage your account preferences and settings
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Account Access</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Two-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">
                Add an extra layer of security to your account
              </p>
            </div>
            <Button variant="outline">Configure</Button>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <Label>Connected Accounts</Label>
              <p className="text-sm text-muted-foreground">
                Manage your connected social accounts
              </p>
            </div>
            <Button variant="outline">Manage</Button>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <Label>Account Deactivation</Label>
              <p className="text-sm text-muted-foreground">
                Temporarily disable your account
              </p>
            </div>
            <Button variant="destructive">Deactivate</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}