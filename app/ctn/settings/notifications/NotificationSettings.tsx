// components/settings/notifications/NotificationSettings.tsx
"use client";

import { useEffect } from "react";
import { useSettingsStore } from "@/stores/settingsStore";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

export function NotificationSettings() {
  const { settings, loading, error, fetchSettings, updateSettings } =
    useSettingsStore();
  const { toast } = useToast();

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  const handleToggle = async (key: string, value: boolean) => {
    if (!settings) return;

    try {
      await updateSettings({
        notifications: {
          ...settings.notifications,
          [key]: value,
        },
      });

      toast({
        title: "Settings updated",
        description: "Your notification preferences have been saved.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update settings.",
        variant: "destructive",
      });
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error loading settings</div>;
  if (!settings) return <div>No settings found</div>;

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Push Notifications</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Push Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Receive push notifications
              </p>
            </div>
            <Switch
              checked={settings.notifications.pushEnabled}
              onCheckedChange={(checked) =>
                handleToggle("pushEnabled", checked)
              }
            />
          </div>
          {/* Other notification settings */}
        </CardContent>
      </Card>
    </div>
  );
}
