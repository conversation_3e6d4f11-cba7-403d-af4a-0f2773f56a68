'use client';

import { CachedTierProtection } from '@/components/CachedTierProtection';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Download, 
  BookOpen, 
  Star, 
  Clock,
  Tag,
  User,
  Eye,
  MessageCircle,
  Heart
} from 'lucide-react';

const sampleResearchPapers = [
  {
    id: 1,
    title: "Bitcoin Network Security Analysis: A Comprehensive Study of Proof-of-Work Mechanisms",
    author: "Dr. <PERSON>",
    date: "2024-12-15",
    category: "Security",
    readTime: "15 min",
    views: 2341,
    likes: 89,
    comments: 12,
    description: "An in-depth analysis of Bitcoin's security model and potential vulnerabilities in the current proof-of-work consensus mechanism."
  },
  {
    id: 2,
    title: "DeFi Liquidity Pools: Mathematical Models and Risk Assessment",
    author: "Prof<PERSON> <PERSON>",
    date: "2024-12-10",
    category: "DeFi",
    readTime: "22 min",
    views: 1876,
    likes: 145,
    comments: 28,
    description: "Mathematical modeling of automated market makers and risk analysis of impermanent loss in liquidity provision."
  },
  {
    id: 3,
    title: "Layer 2 Scaling Solutions: Performance Comparison and Future Outlook",
    author: "Alex Thompson",
    date: "2024-12-08",
    category: "Scaling",
    readTime: "18 min",
    views: 3210,
    likes: 201,
    comments: 45,
    description: "Comprehensive comparison of various Layer 2 solutions including rollups, state channels, and sidechains."
  }
];

export default function ResearchPapersPage() {
  return (
    <CachedTierProtection pagePath="/ctn/research">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Research Papers</h1>
            <p className="text-gray-600 mt-2">
              Access peer-reviewed research and academic publications on cryptocurrency and blockchain technology
            </p>
          </div>
          <Badge variant="outline" className="text-blue-600">
            Tier 1+ Feature
          </Badge>
        </div>

        <Tabs defaultValue="all" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="all">All Papers</TabsTrigger>
            <TabsTrigger value="security">Security</TabsTrigger>
            <TabsTrigger value="defi">DeFi</TabsTrigger>
            <TabsTrigger value="scaling">Scaling</TabsTrigger>
            <TabsTrigger value="economics">Economics</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-6">
            <div className="grid gap-6">
              {sampleResearchPapers.map((paper) => (
                <Card key={paper.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-xl leading-tight mb-2">
                          {paper.title}
                        </CardTitle>
                        <CardDescription className="text-base">
                          {paper.description}
                        </CardDescription>
                      </div>
                      <Badge variant="secondary" className="ml-4">
                        {paper.category}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600 mt-4">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {paper.author}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {paper.readTime}
                      </div>
                      <div className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        {paper.views.toLocaleString()}
                      </div>
                      <div className="flex items-center gap-1">
                        <Heart className="h-4 w-4" />
                        {paper.likes}
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="h-4 w-4" />
                        {paper.comments}
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-500">
                        Published: {new Date(paper.date).toLocaleDateString()}
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <BookOpen className="h-4 w-4 mr-2" />
                          Read
                        </Button>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Download PDF
                        </Button>
                        <CachedTierProtection pagePath="/ctn/articles">
                          <Button variant="outline" size="sm">
                            <Heart className="h-4 w-4 mr-2" />
                            Like
                          </Button>
                        </CachedTierProtection>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="security" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-red-500" />
                  Security Research
                </CardTitle>
                <CardDescription>
                  Research papers focused on blockchain security, cryptography, and attack vectors
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sampleResearchPapers
                    .filter(paper => paper.category === "Security")
                    .map((paper) => (
                      <div key={paper.id} className="p-4 border rounded-lg">
                        <h3 className="font-semibold">{paper.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{paper.description}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-2">
                          <span>{paper.author}</span>
                          <span>{paper.readTime}</span>
                          <span>{paper.views} views</span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="defi" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-500" />
                  DeFi Research
                </CardTitle>
                <CardDescription>
                  Academic papers on decentralized finance, liquidity, and automated market makers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sampleResearchPapers
                    .filter(paper => paper.category === "DeFi")
                    .map((paper) => (
                      <div key={paper.id} className="p-4 border rounded-lg">
                        <h3 className="font-semibold">{paper.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{paper.description}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-2">
                          <span>{paper.author}</span>
                          <span>{paper.readTime}</span>
                          <span>{paper.views} views</span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="scaling" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-green-500" />
                  Scaling Research
                </CardTitle>
                <CardDescription>
                  Research on blockchain scalability solutions, Layer 2 protocols, and performance optimization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {sampleResearchPapers
                    .filter(paper => paper.category === "Scaling")
                    .map((paper) => (
                      <div key={paper.id} className="p-4 border rounded-lg">
                        <h3 className="font-semibold">{paper.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">{paper.description}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500 mt-2">
                          <span>{paper.author}</span>
                          <span>{paper.readTime}</span>
                          <span>{paper.views} views</span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="economics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-purple-500" />
                  Economics Research
                </CardTitle>
                <CardDescription>
                  Economic analysis, tokenomics, and market dynamics research papers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No economics papers available yet</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </CachedTierProtection>
  );
}
