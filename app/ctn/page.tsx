import { createClient } from "@/utils/supabase/server";
import ArticleTimeline from "@/components/Articles/ArticleTimeline";
import { redirect } from "next/navigation";
import { Article, UserProfile as Profile } from "@/types";
import { Suspense } from "react";
import { ArticlesSkeleton } from "@/components/Skeletons/ArticlesSkeleton";

async function getArticlesData(): Promise<{
  articles: Article[];
  userProfile: Profile;
}> {
  const supabase = await createClient();

  const {
    data: { user },
    error: authError,
  } = await (supabase as any).auth.getUser();

  if (!user || authError) {
    redirect("/signin");
  }

  // Fetch user profile and articles in parallel
  const [profileResponse, articlesResponse] = await Promise.all([
    (supabase as any).from("users").select("*").eq("user_id", user.id).single(),
    (supabase as any).rpc("get_articles_with_interactions", {
      p_user_id: user.id,
      p_last_timestamp: null, // Get from the beginning
      p_limit: 10,
      p_time_range: "all",
      p_sort_field: "created_at",
      p_sort_direction: "desc",
    }),
  ]);

  if (profileResponse.error) {
    console.error("Profile fetch error:", profileResponse.error);
    throw new Error("Failed to fetch user profile");
  }

  if (articlesResponse.error) {
    console.error("Articles fetch error:", articlesResponse.error);
    throw new Error("Failed to fetch articles");
  }

  const articles = articlesResponse.data || [];

  return {
    articles,
    userProfile: profileResponse.data as Profile,
  };
}

export default async function ArticlesPage() {
  try {
    const { articles, userProfile } = await getArticlesData();

    return (
      <div className="flex">
        <main className="flex-1 flex flex-col">
          <Suspense fallback={<ArticlesSkeleton />}>
            <ArticleTimeline
              articles={articles}
              userProfile={userProfile}
              errorMessage={null} // You can handle error messages here if needed
            />
          </Suspense>
        </main>
      </div>
    );
  } catch (error) {
    console.error("Error loading articles page:", error);
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-red-500">
          Failed to load articles. Please try again later.
        </p>
      </div>
    );
  }
}
