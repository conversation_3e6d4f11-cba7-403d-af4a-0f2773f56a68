'use client';

import { CachedTierProtection } from '@/components/CachedTierProtection';
import { ExperienceDisplay } from '@/components/ExperienceSystem';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Gift, 
  Calendar, 
  Star, 
  Clock, 
  TrendingUp,
  Zap,
  Trophy,
  Target
} from 'lucide-react';

const claimBenefits = [
  {
    icon: Gift,
    title: "Base Experience",
    description: "Earn 10 XP every day",
    color: "text-blue-500"
  },
  {
    icon: Star,
    title: "Streak Bonus",
    description: "Up to 10% bonus for consecutive days",
    color: "text-yellow-500"
  },
  {
    icon: Zap,
    title: "24h XP Activation",
    description: "Enables experience gain from interactions",
    color: "text-green-500"
  },
  {
    icon: TrendingUp,
    title: "Tier Boost",
    description: "10% additional XP based on your tier",
    color: "text-purple-500"
  }
];

export default function DailyClaimPage() {
  return (
    <CachedTierProtection pagePath="/ctn/daily-claim">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Daily Claim</h1>
            <p className="text-gray-600 mt-2">
              Claim your daily experience reward and activate XP gain for 24 hours
            </p>
          </div>
          <Badge variant="outline" className="text-blue-600">
            Tier 1+ Feature
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Experience Display */}
          <div className="lg:col-span-2">
            <ExperienceDisplay 
              showDailyClaim={true}
              showProgress={true}
              showStats={true}
            />
          </div>

          {/* Benefits Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-gold-500" />
                  Daily Benefits
                </CardTitle>
                <CardDescription>
                  What you get from claiming daily rewards
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {claimBenefits.map((benefit, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <benefit.icon className={`h-4 w-4 ${benefit.color}`} />
                    </div>
                    <div>
                      <h3 className="font-medium text-sm">{benefit.title}</h3>
                      <p className="text-xs text-gray-600">{benefit.description}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-orange-500" />
                  How to Earn XP
                </CardTitle>
                <CardDescription>
                  Ways to gain experience points
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Article likes received</span>
                    <span className="font-medium">+5 XP</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Article comments received</span>
                    <span className="font-medium">+5 XP</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Comment likes received</span>
                    <span className="font-medium">+5 XP</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Heatmap contributions</span>
                    <span className="font-medium">+10 XP</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Daily claim</span>
                    <span className="font-medium">+10-20 XP</span>
                  </div>
                </div>
                <Separator />
                <div className="text-xs text-gray-500">
                  * XP gain requires active 24h period from daily claim
                  <br />
                  * Each user can only grant you XP twice per article
                  <br />
                  * Tier 1+ subscribers get 10% XP boost
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-blue-500" />
                  Streak Rewards
                </CardTitle>
                <CardDescription>
                  Consecutive day bonuses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>1 day</span>
                    <span className="text-green-600">+0% bonus</span>
                  </div>
                  <div className="flex justify-between">
                    <span>2 days</span>
                    <span className="text-green-600">+1% bonus</span>
                  </div>
                  <div className="flex justify-between">
                    <span>3 days</span>
                    <span className="text-green-600">+2% bonus</span>
                  </div>
                  <div className="text-center text-xs text-gray-500 pt-2">
                    ...up to 10% at 10 days
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-purple-500" />
                Important Notes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h3 className="font-medium mb-2">Claiming Requirements:</h3>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Must have Tier 1+ subscription</li>
                    <li>• Can only claim once per day</li>
                    <li>• Resets at midnight UTC</li>
                    <li>• Missing a day resets streak</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-medium mb-2">Experience Activation:</h3>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Activates XP gain for 24 hours</li>
                    <li>• Only subscribers can give you XP</li>
                    <li>• Each user limited to 2 XP grants per article</li>
                    <li>• No XP from self-interactions</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </CachedTierProtection>
  );
}
