import { NextResponse } from 'next/server';
import { 
  checkAPIConfiguration, 
  getConfigurationSummary, 
  getAPIConfigurationForClient,
  checkProductionReadiness 
} from '@/utils/api-config-checker';

export async function GET() {
  try {
    const configStatus = getAPIConfigurationForClient();
    const summary = getConfigurationSummary();
    const productionReadiness = checkProductionReadiness();
    
    return NextResponse.json({
      services: configStatus,
      summary,
      productionReadiness,
      timestamp: Date.now(),
    });
  } catch (error) {
    console.error('Failed to get configuration status:', error);
    return NextResponse.json(
      { error: 'Failed to get configuration status' },
      { status: 500 }
    );
  }
}
