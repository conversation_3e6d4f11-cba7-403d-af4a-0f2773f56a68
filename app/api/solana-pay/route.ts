import { NextRequest, NextResponse } from 'next/server';
import { Connection, PublicKey } from '@solana/web3.js';
import { createClient } from '@/utils/supabase/server';

// Solana RPC endpoint (you can use a custom RPC for better performance)
const SOLANA_RPC = process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com';

export async function POST(request: NextRequest) {
  try {
    const { signature, reference } = await request.json();

    if (!signature) {
      return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
    }

    // Initialize Solana connection
    const connection = new Connection(SOLANA_RPC, 'confirmed');
    
    // Get transaction details
    const transaction = await connection.getTransaction(signature, {
      commitment: 'confirmed',
    });

    if (!transaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    // Verify transaction was successful
    if (transaction.meta?.err) {
      return NextResponse.json({ error: 'Transaction failed' }, { status: 400 });
    }

    // Extract memo from transaction (simplified approach)
    // For now, we'll generate memo from transaction signature and timing
    // In a production app, you'd want to properly decode the memo instruction
    const blockTime = transaction.blockTime || Date.now() / 1000;
    
    // For demo purposes, we'll extract payment info from the transaction logs
    // In practice, you'd want to store payment requests in your database
    // and match them with completed transactions
    
    // For now, we'll assume this is called from our frontend with payment details
    const searchParams = new URL(request.url).searchParams;
    const paymentType = searchParams.get('paymentType') || 'donation';
    const recipientUserId = searchParams.get('recipientUserId');
    
    if (!recipientUserId) {
      return NextResponse.json({ error: 'Missing recipient user ID' }, { status: 400 });
    }

    // Get transaction amount (in lamports)
    const preBalance = transaction.meta!.preBalances[0];
    const postBalance = transaction.meta!.postBalances[0];
    const amountLamports = preBalance - postBalance - (transaction.meta!.fee || 0);
    const amountSOL = amountLamports / 1e9;

    // Get sender and recipient addresses
    const fromWallet = transaction.transaction.message.accountKeys[0].toString();
    const toWallet = transaction.transaction.message.accountKeys[1].toString();

    // Initialize Supabase client
    const supabase = createClient();

    // Get sender user ID from wallet address
    const { data: senderData } = await (supabase as any)
      .from('users')
      .select('user_id')
      .eq('solana_wallet_address', fromWallet)
      .single();

    if (!senderData) {
      return NextResponse.json({ error: 'Sender not found' }, { status: 404 });
    }

    const senderId = senderData.user_id;
    const description = `${paymentType} - ${signature}`;

    // Record transaction for sender
    await (supabase as any).rpc('process_ctt_transaction', {
      p_user_id: senderId,
      p_transaction_type: paymentType === 'donation' ? 'donation_sent' : 'subscription_payment',
      p_amount: amountSOL,
      p_reference_id: recipientUserId,
      p_solana_transaction_id: signature,
      p_from_wallet: fromWallet,
      p_to_wallet: toWallet,
      p_description: description,
    });

    // Record transaction for recipient
    await (supabase as any).rpc('process_ctt_transaction', {
      p_user_id: recipientUserId,
      p_transaction_type: paymentType === 'donation' ? 'donation_received' : 'subscription_payment',
      p_amount: amountSOL,
      p_reference_id: senderId,
      p_solana_transaction_id: signature,
      p_from_wallet: fromWallet,
      p_to_wallet: toWallet,
      p_description: description,
    });

    // If it's a subscription, create/update subscription record
    if (paymentType === 'subscription') {
      await (supabase as any)
        .from('user_subscriptions_ctt')
        .upsert({
          subscriber_id: senderId,
          creator_id: recipientUserId,
          amount: amountSOL,
          solana_transaction_id: signature,
          status: 'active',
          next_payment_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        });
    }

    return NextResponse.json({ 
      success: true, 
      transactionId: signature,
      amount: amountSOL,
      paymentType 
    });

  } catch (error) {
    console.error('Solana Pay webhook error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Handle GET requests for payment verification
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const signature = searchParams.get('signature');

  if (!signature) {
    return NextResponse.json({ error: 'Missing signature' }, { status: 400 });
  }

  try {
    const connection = new Connection(SOLANA_RPC, 'confirmed');
    const transaction = await connection.getTransaction(signature, {
      commitment: 'confirmed',
    });

    if (!transaction) {
      return NextResponse.json({ confirmed: false });
    }

    return NextResponse.json({ 
      confirmed: !transaction.meta?.err,
      signature,
      slot: transaction.slot 
    });

  } catch (error) {
    console.error('Transaction verification error:', error);
    return NextResponse.json({ confirmed: false });
  }
}
