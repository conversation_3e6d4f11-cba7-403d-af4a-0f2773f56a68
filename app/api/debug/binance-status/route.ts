import { NextRequest, NextResponse } from 'next/server';
import { binanceService } from '@/utils/binance-service';

export async function GET() {
  try {
    const status = binanceService.getServiceStatus();
    return NextResponse.json({
      status: 'success',
      binanceStatus: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Failed to get Binance status', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'reset') {
      binanceService.resetFailures();
      const status = binanceService.getServiceStatus();
      
      return NextResponse.json({
        status: 'success',
        message: 'Binance service failures reset',
        binanceStatus: status,
        timestamp: new Date().toISOString()
      });
    }
    
    return NextResponse.json(
      { error: 'Invalid action. Use "reset" to reset failures.' },
      { status: 400 }
    );
  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Failed to reset Binance status', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    );
  }
}