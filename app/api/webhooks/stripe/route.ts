import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@/utils/supabase/server';

function getStripe() {
  if (!process.env.STRIPE_SECRET_KEY) {
    throw new Error('STRIPE_SECRET_KEY is not configured');
  }
  return new Stripe(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2025-06-30.basil',
  });
}

function getWebhookSecret() {
  if (!process.env.STRIPE_WEBHOOK_SECRET) {
    throw new Error('STRIPE_WEBHOOK_SECRET is not configured');
  }
  return process.env.STRIPE_WEBHOOK_SECRET;
}

export async function POST(req: NextRequest) {
  const body = await req.text();
  const signature = req.headers.get('stripe-signature')!;

  let event: Stripe.Event;

  try {
    const stripe = getStripe();
    const webhookSecret = getWebhookSecret();
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json({ error: 'Webhook signature verification failed' }, { status: 400 });
  }

  const supabase = await createClient();

  try {
    switch (event.type) {
      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionChange(supabase, subscription);
        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        await handleSubscriptionCancellation(supabase, subscription);
        break;
      }

      case 'invoice.payment_succeeded': {
        const invoice = event.data.object as Stripe.Invoice;
        await handlePaymentSuccess(supabase, invoice);
        break;
      }

      case 'invoice.payment_failed': {
        const invoice = event.data.object as Stripe.Invoice;
        await handlePaymentFailure(supabase, invoice);
        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function handleSubscriptionChange(supabase: any, subscription: Stripe.Subscription) {
  const stripe = getStripe();
  const customerId = subscription.customer as string;
  const subscriptionId = subscription.id;

  // Get the price to determine the plan
  const priceId = subscription.items.data[0]?.price.id;
  const planName = await getPlanNameFromPriceId(priceId);

  if (!planName) {
    console.error('Unknown price ID:', priceId);
    return;
  }

  // Get user by Stripe customer ID
  const { data: userData, error: userError } = await (supabase as any)
    .from('user_subscriptions')
    .select('user_id')
    .eq('stripe_customer_id', customerId)
    .single();

  let userId: string;

  if (userError || !userData) {
    // Try to find user by email from Stripe customer
    const customer = await stripe.customers.retrieve(customerId) as Stripe.Customer;
    if (customer.email) {
      const { data: authUser, error: authError } = await (supabase as any)
        .from('users')
        .select('user_id')
        .eq('email', customer.email)
        .single();

      if (authError || !authUser) {
        console.error('Could not find user for customer:', customerId);
        return;
      }
      userId = authUser.user_id;
    } else {
      console.error('No email found for customer:', customerId);
      return;
    }
  } else {
    userId = userData.user_id;
  }

  // Process subscription update
  const { error } = await (supabase as any).rpc('process_subscription_update', {
    p_user_id: userId,
    p_plan_name: planName,
    p_stripe_subscription_id: subscriptionId,
    p_stripe_customer_id: customerId,
    p_status: subscription.status,
    p_current_period_start: new Date().toISOString(),
    p_current_period_end: null,
  });

  if (error) {
    console.error('Error updating subscription:', error);
  }
}

async function handleSubscriptionCancellation(supabase: any, subscription: Stripe.Subscription) {
  const { error } = await (supabase as any)
    .from('user_subscriptions')
    .update({
      status: 'canceled',
      canceled_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('stripe_subscription_id', subscription.id);

  if (error) {
    console.error('Error canceling subscription:', error);
    return;
  }

  // Update user tier to tier0
  const { data: subData } = await (supabase as any)
    .from('user_subscriptions')
    .select('user_id')
    .eq('stripe_subscription_id', subscription.id)
    .single();

  if (subData?.user_id) {
    await (supabase as any)
      .from('users')
      .update({ premium_tier: 'tier0' })
      .eq('user_id', subData.user_id);
  }
}

async function handlePaymentSuccess(supabase: any, invoice: any) {
  if (!invoice.subscription) return;
  
  const subscriptionId = invoice.subscription as string;
  const amount = invoice.amount_paid / 100; // Convert from cents
  const currency = invoice.currency.toUpperCase();

  // Get subscription data
  const { data: subData } = await (supabase as any)
    .from('user_subscriptions')
    .select('user_id, id')
    .eq('stripe_subscription_id', subscriptionId)
    .single();

  if (!subData) {
    console.error('Subscription not found for invoice:', invoice.id);
    return;
  }

  // Record payment in history
  const { error } = await (supabase as any)
    .from('payment_history')
    .insert({
      user_id: subData.user_id,
      subscription_id: subData.id,
      amount,
      currency,
      payment_method: 'stripe',
      stripe_payment_intent_id: invoice.payment_intent || null,
      stripe_invoice_id: invoice.id,
      status: 'succeeded',
      processed_at: new Date().toISOString(),
    });

  if (error) {
    console.error('Error recording payment:', error);
  }
}

async function handlePaymentFailure(supabase: any, invoice: any) {
  if (!invoice.subscription) return;
  
  const subscriptionId = invoice.subscription as string;
  const amount = invoice.amount_due / 100; // Convert from cents
  const currency = invoice.currency.toUpperCase();

  // Get subscription data
  const { data: subData } = await (supabase as any)
    .from('user_subscriptions')
    .select('user_id, id')
    .eq('stripe_subscription_id', subscriptionId)
    .single();

  if (!subData) {
    console.error('Subscription not found for failed invoice:', invoice.id);
    return;
  }

  // Record failed payment
  const { error } = await (supabase as any)
    .from('payment_history')
    .insert({
      user_id: subData.user_id,
      subscription_id: subData.id,
      amount,
      currency,
      payment_method: 'stripe',
      stripe_invoice_id: invoice.id,
      status: 'failed',
      failure_reason: invoice.last_finalization_error?.message || 'Payment failed',
      processed_at: new Date().toISOString(),
    });

  if (error) {
    console.error('Error recording failed payment:', error);
  }
}

async function getPlanNameFromPriceId(priceId: string): Promise<string | null> {
  // This mapping should be stored in your database or environment variables
  // For now, using a simple mapping - you'll need to update these with your actual Stripe price IDs
  const priceMapping: Record<string, string> = {
    'price_tier1_monthly': 'tier1',
    'price_tier1_yearly': 'tier1',
    'price_tier2_monthly': 'tier2',
    'price_tier2_yearly': 'tier2',
    'price_tier3_monthly': 'tier3',
    'price_tier3_yearly': 'tier3',
    'price_tier4_monthly': 'tier4',
    'price_tier4_yearly': 'tier4',
  };

  return priceMapping[priceId] || null;
}
