import { NextRequest, NextResponse } from 'next/server';
import { binanceService } from '@/utils/binance-service';
import { coinGeckoService, convertSymbolToCoinGeckoId } from '@/utils/api-services';
import { FEATURE_FLAGS } from '@/utils/api-config';
import { transformSimplePricesToPortfolio } from '@/utils/data-transformers';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const symbolsParam = searchParams.get('symbols');
    const source = searchParams.get('source') || 'unified'; // 'binance', 'coingecko', 'unified'
    
    if (!symbolsParam) {
      return NextResponse.json({ error: 'Symbols parameter required' }, { status: 400 });
    }

    const requestedSymbols = symbolsParam.split(',').map(s => s.trim().toUpperCase());

    // Filter to only include BASE coins
    const { BASE_COINS } = await import('@/utils/mock-data-service');
    const baseSymbols = BASE_COINS.map(coin => coin.symbol);
    const symbols = requestedSymbols.filter(symbol => baseSymbols.includes(symbol));

    if (symbols.length !== requestedSymbols.length) {
      const filtered = requestedSymbols.filter(symbol => !baseSymbols.includes(symbol));
      console.warn(`⚠️ Filtered out non-BASE coins: ${filtered.join(',')}`);
    }

    console.log(`💰 Price request: symbols=${symbols.join(',')}, source=${source}`);

    switch (source) {
      case 'binance':
        return await getBinancePrices(symbols);
      
      case 'coingecko':
        return await getCoinGeckoPrices(symbols);
      
      case 'unified':
      default:
        return await getUnifiedPrices(symbols);
    }
  } catch (error) {
    console.error('Prices API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch prices', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Get prices from Binance
async function getBinancePrices(symbols: string[]) {
  try {
    console.log('🚀 Fetching prices from Binance...');
    const binancePrices = await binanceService.getPrices(symbols);
    const prices = binanceService.transformToPortfolioPrices(binancePrices);
    
    console.log(`✅ Binance: Got prices for ${Object.keys(prices).length} symbols`);
    
    return NextResponse.json({
      prices,
      source: 'binance',
      timestamp: Date.now(),
      count: Object.keys(prices).length,
    });
  } catch (error) {
    console.error('Binance prices failed:', error);
    throw error;
  }
}

// Get prices from CoinGecko
async function getCoinGeckoPrices(symbols: string[]) {
  try {
    console.log('🔄 Fetching prices from CoinGecko...');
    const coinIds = symbols.map(symbol => convertSymbolToCoinGeckoId(symbol));
    const coinGeckoPrices = await coinGeckoService.getSimplePrices(coinIds);
    const prices = transformSimplePricesToPortfolio(coinGeckoPrices, symbols);
    
    console.log(`✅ CoinGecko: Got prices for ${Object.keys(prices).length} symbols`);
    
    return NextResponse.json({
      prices,
      source: 'coingecko',
      timestamp: Date.now(),
      count: Object.keys(prices).length,
    });
  } catch (error) {
    console.error('CoinGecko prices failed:', error);
    throw error;
  }
}

// Get unified prices (Binance → CoinGecko → Mock)
async function getUnifiedPrices(symbols: string[]) {
  let prices: Record<string, number> = {};
  let sources: string[] = [];

  // Step 1: Try Binance first
  try {
    console.log('🚀 Trying Binance for prices...');
    const binancePrices = await binanceService.getPrices(symbols);
    const binanceResult = binanceService.transformToPortfolioPrices(binancePrices);
    
    if (Object.keys(binanceResult).length > 0) {
      prices = { ...prices, ...binanceResult };
      sources.push('binance');
      console.log(`✅ Binance: Got ${Object.keys(binanceResult).length} prices`);
    }
  } catch (binanceError) {
    console.warn('⚠️ Binance prices failed:', binanceError);
  }

  // Step 2: Fill gaps with CoinGecko
  const missingSymbols = symbols.filter(symbol => !prices[symbol]);
  if (missingSymbols.length > 0) {
    try {
      console.log(`🔄 Getting missing symbols from CoinGecko: ${missingSymbols.join(',')}`);
      const coinIds = missingSymbols.map(symbol => convertSymbolToCoinGeckoId(symbol));
      const coinGeckoPrices = await coinGeckoService.getSimplePrices(coinIds);
      const coinGeckoResult = transformSimplePricesToPortfolio(coinGeckoPrices, missingSymbols);
      
      if (Object.keys(coinGeckoResult).length > 0) {
        prices = { ...prices, ...coinGeckoResult };
        sources.push('coingecko');
        console.log(`✅ CoinGecko: Got ${Object.keys(coinGeckoResult).length} additional prices`);
      }
    } catch (coinGeckoError) {
      console.warn('⚠️ CoinGecko prices failed:', coinGeckoError);
    }
  }

  // Step 3: Final fallback to mock data for any remaining missing symbols
  const stillMissingSymbols = symbols.filter(symbol => !prices[symbol]);
  if (stillMissingSymbols.length > 0 && FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
    console.log(`📦 Using mock prices for: ${stillMissingSymbols.join(',')}`);
    stillMissingSymbols.forEach(symbol => {
      prices[symbol] = Math.random() * 50000 + 100;
    });
    sources.push('mock');
  }

  console.log(`✅ Final result: ${Object.keys(prices).length}/${symbols.length} prices from [${sources.join(', ')}]`);

  return NextResponse.json({
    prices,
    sources,
    timestamp: Date.now(),
    count: Object.keys(prices).length,
    requested: symbols.length,
  });
}

// Batch price updates for real-time data
export async function POST(request: NextRequest) {
  try {
    const { action, symbols } = await request.json();
    
    if (action === 'batch_prices' && symbols && Array.isArray(symbols)) {
      console.log(`🔄 Batch price update for ${symbols.length} symbols`);
      
      // Use unified approach for batch updates
      const response = await getUnifiedPrices(symbols);
      const data = await response.json();
      
      return NextResponse.json(data);
    }

    return NextResponse.json({ error: 'Invalid action or missing symbols' }, { status: 400 });
  } catch (error) {
    console.error('Batch prices error:', error);
    return NextResponse.json(
      { error: 'Batch price update failed' },
      { status: 500 }
    );
  }
}
