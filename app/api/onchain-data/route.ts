import { NextRequest, NextResponse } from 'next/server';
import {
  fetchOnChainMetrics,
  generate<PERSON>hain<PERSON>omparison,
  calculateDerivedMetrics,
  ChainMetrics,
  fetchMarketData,
  fetchTVLData
} from '@/utils/onchain-data';
import { FEATURE_FLAGS, CACHE_DURATIONS } from '@/utils/api-config';
import { cacheService, createCacheKey } from '@/utils/cache-service';
import { dexScreenerService } from '@/utils/dexscreener-service';
import { coinGeckoService } from '@/utils/api-services';

// Enhanced function to merge mock data with real API data
async function enhanceChainDataWithRealAPI(chainId: string, mockData: ChainMetrics): Promise<ChainMetrics> {
  try {
    // Fetch real market data
    const marketData = await fetchMarketData(chainId);

    // Fetch real TVL data (for chains that support DeFi)
    let tvlData = 0;
    if (['ethereum', 'solana', 'cardano', 'polkadot', 'avalanche'].includes(chainId)) {
      tvlData = await fetchTVLData(chainId);
    }

    // Merge real data with mock data
    return {
      ...mockData,
      ...marketData, // Override with real market data
      tvl: tvlData || mockData.tvl, // Use real TVL if available
      timestamp: Date.now(),
    };
  } catch (error) {
    console.error(`Failed to enhance ${chainId} data with real API:`, error);
    return mockData; // Return mock data if API fails
  }
}

// Mock data for development - enhanced with real API calls when available
const MOCK_CHAIN_DATA: Record<string, ChainMetrics> = {
  bitcoin: {
    name: 'Bitcoin',
    symbol: 'BTC',
    logo: '₿',
    dailyActiveAddresses: 980000,
    dailyTransactions: 280000,
    tvl: 0,
    fees: 1250000,
    stakedSupply: 0,
    newAddresses: 12000,
    transactionVolume: 2800000000,
    fdmc: 867000000000,
    marketCap: 867000000000,
    inflation: { mom: 0.08, yoy: 1.8 },
    circulatingSupply: 19700000,
    maxTps: 7,
    realTimeTps: 4.2,
    theoreticalTps: 7,
    blockSize: 1000000,
    avgFee: 4.5,
    finalityTime: 600,
    energyPerTx: 741000000,
    validators: 15000,
    activeDevelopers: 847,
    price: 44000,
    timestamp: Date.now(),
  },
  ethereum: {
    name: 'Ethereum',
    symbol: 'ETH',
    logo: 'Ξ',
    dailyActiveAddresses: 450000,
    dailyTransactions: 1200000,
    tvl: 28500000000,
    fees: 3400000,
    stakedSupply: 32000000,
    newAddresses: 25000,
    transactionVolume: 8900000000,
    fdmc: 298000000000,
    marketCap: 298000000000,
    inflation: { mom: -0.2, yoy: -0.8 },
    circulatingSupply: 120300000,
    maxTps: 15,
    realTimeTps: 12.8,
    theoreticalTps: 15,
    blockSize: 30000,
    avgFee: 2.8,
    finalityTime: 12.8,
    energyPerTx: 62000,
    validators: 900000,
    activeDevelopers: 2847,
    price: 2500,
    timestamp: Date.now(),
  },
  solana: {
    name: 'Solana',
    symbol: 'SOL',
    logo: '◎',
    dailyActiveAddresses: 180000,
    dailyTransactions: 28000000,
    tvl: 1200000000,
    fees: 45000,
    stakedSupply: 385000000,
    newAddresses: 8500,
    transactionVolume: 420000000,
    fdmc: 51000000000,
    marketCap: 51000000000,
    inflation: { mom: 0.5, yoy: 6.2 },
    circulatingSupply: 467000000,
    maxTps: 65000,
    realTimeTps: 2400,
    theoreticalTps: 65000,
    blockSize: 1232,
    avgFee: 0.00025,
    finalityTime: 0.4,
    energyPerTx: 1837,
    validators: 3200,
    activeDevelopers: 1247,
    price: 110,
    timestamp: Date.now(),
  },
  cardano: {
    name: 'Cardano',
    symbol: 'ADA',
    logo: '₳',
    dailyActiveAddresses: 85000,
    dailyTransactions: 95000,
    tvl: 145000000,
    fees: 8500,
    stakedSupply: 24500000000,
    newAddresses: 3200,
    transactionVolume: 125000000,
    fdmc: 15800000000,
    marketCap: 15800000000,
    inflation: { mom: 0.3, yoy: 4.1 },
    circulatingSupply: 35000000000,
    maxTps: 250,
    realTimeTps: 7.8,
    theoreticalTps: 250,
    blockSize: 65536,
    avgFee: 0.17,
    finalityTime: 300,
    energyPerTx: 6000,
    validators: 2800,
    activeDevelopers: 456,
    price: 0.45,
    timestamp: Date.now(),
  },
  polkadot: {
    name: 'Polkadot',
    symbol: 'DOT',
    logo: '●',
    dailyActiveAddresses: 42000,
    dailyTransactions: 180000,
    tvl: 890000000,
    fees: 18000,
    stakedSupply: 685000000,
    newAddresses: 1800,
    transactionVolume: 290000000,
    fdmc: 7200000000,
    marketCap: 7200000000,
    inflation: { mom: 0.4, yoy: 8.7 },
    circulatingSupply: 1400000000,
    maxTps: 1500,
    realTimeTps: 166,
    theoreticalTps: 1500,
    blockSize: 5242880,
    avgFee: 0.1,
    finalityTime: 6,
    energyPerTx: 126000,
    validators: 1200,
    activeDevelopers: 623,
    price: 5.15,
    timestamp: Date.now(),
  },
};

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const chain = searchParams.get('chain');
    const chains = searchParams.get('chains')?.split(',') || [];
    const analysisType = searchParams.get('analysisType') as 'technical' | 'economic' | 'comprehensive' || 'comprehensive';
    const refresh = searchParams.get('refresh') === 'true';
    const type = searchParams.get('type') || 'general';
    const tokens = searchParams.get('tokens')?.split(',') || [];
    const source = searchParams.get('source');

    switch (action) {
      case 'metrics':
        if (!chain) {
          return NextResponse.json({ error: 'Chain parameter required' }, { status: 400 });
        }

        // Get mock data first
        const mockChainData = MOCK_CHAIN_DATA[chain.toLowerCase()];
        if (!mockChainData) {
          return NextResponse.json({ error: 'Chain not found' }, { status: 404 });
        }

        // Enhance with real API data
        const enhancedChainData = await enhanceChainDataWithRealAPI(chain.toLowerCase(), mockChainData);
        const derivedMetrics = calculateDerivedMetrics(enhancedChainData);

        return NextResponse.json({
          chain: enhancedChainData,
          derivedMetrics,
          timestamp: Date.now(),
          dataSource: 'enhanced', // Indicate this is enhanced with real data
        });

      case 'compare':
        if (chains.length < 2) {
          return NextResponse.json({ error: 'At least 2 chains required for comparison' }, { status: 400 });
        }

        // In production, this would call generateChainComparison(chains, analysisType)
        const comparisonData = [];
        const insights = [];

        // Enhance each chain's data with real API data
        for (const chainId of chains) {
          const mockData = MOCK_CHAIN_DATA[chainId.toLowerCase()];
          if (mockData) {
            const enhancedData = await enhanceChainDataWithRealAPI(chainId.toLowerCase(), mockData);
            const derived = calculateDerivedMetrics(enhancedData);

            // Simple scoring algorithm for demo
            const score = Math.round(
              (enhancedData.realTimeTps / 1000) * 20 +
              (1 / Math.max(enhancedData.avgFee, 0.001)) * 10 +
              (enhancedData.validators / 100) * 15 +
              (enhancedData.activeDevelopers / 100) * 20 +
              Math.random() * 35 // Add some randomness for demo
            );

            comparisonData.push({
              ...enhancedData,
              derivedMetrics: derived,
              score: Math.min(score, 100),
            });
          }
        }

        // Sort by score
        comparisonData.sort((a, b) => b.score - a.score);

        // Generate insights
        if (comparisonData.length > 0) {
          const topChain = comparisonData[0];
          insights.push(`${topChain.name} leads with a score of ${topChain.score}/100`);
          
          if (topChain.realTimeTps > 1000) {
            insights.push(`${topChain.name} excels in transaction throughput with ${topChain.realTimeTps.toLocaleString()} TPS`);
          }
          
          if (topChain.avgFee < 0.01) {
            insights.push(`${topChain.name} offers extremely low transaction fees (${topChain.avgFee} USD)`);
          }
          
          if (topChain.validators > 1000) {
            insights.push(`${topChain.name} demonstrates strong decentralization with ${topChain.validators.toLocaleString()} validators`);
          }
        }

        return NextResponse.json({
          chains: comparisonData,
          insights,
          analysisType,
          timestamp: Date.now(),
        });

      case 'list':
        // Return available chains
        const availableChains = Object.values(MOCK_CHAIN_DATA).map(chain => ({
          id: chain.name.toLowerCase(),
          name: chain.name,
          symbol: chain.symbol,
          logo: chain.logo,
        }));

        return NextResponse.json({
          chains: availableChains,
          timestamp: Date.now(),
        });

      case 'dexscreener':
        // Handle DexScreener data refresh
        const cacheKey = createCacheKey('onchain-metrics', 'dexscreener', `${type}-${refresh}`);
        
        if (!refresh) {
          const cachedData = cacheService.get(cacheKey);
          if (cachedData) {
            return NextResponse.json({
              ...cachedData,
              cached: true,
              timestamp: Date.now(),
            });
          }
        }

        try {
          // Fetch fresh DexScreener data
          const tokenProfiles = await dexScreenerService.getTokenProfiles();
          const trendingPairs = await dexScreenerService.getTrendingPairs();
          
          const dexData = {
            tokenProfiles: tokenProfiles.slice(0, 50), // Limit for performance
            trendingPairs: trendingPairs.pairs.slice(0, 20),
            tokensCount: tokenProfiles.length || 0,
            source: 'dexscreener',
            lastUpdated: Date.now(),
          };

          // Cache the data
          cacheService.set(cacheKey, dexData, CACHE_DURATIONS.MARKET_DATA);

          return NextResponse.json({
            ...dexData,
            cached: false,
            timestamp: Date.now(),
          });
        } catch (error) {
          console.error('Failed to fetch DexScreener data:', error);
          return NextResponse.json({ error: 'Failed to fetch DexScreener data' }, { status: 500 });
        }

      case 'refresh':
        // Handle scheduled cache refresh
        const refreshType = type || 'general';
        const refreshCacheKey = createCacheKey('onchain-metrics', refreshType, tokens.join(','));
        
        try {
          let refreshedData;
          
          if (tokens.length > 0) {
            // Fetch specific tokens data
            const coinGeckoData = await coinGeckoService.getSimplePrices(tokens);
            refreshedData = {
              prices: coinGeckoData,
              tokens: tokens,
              type: refreshType,
              source: 'coingecko',
              tokensCount: tokens.length,
              lastUpdated: Date.now(),
            };
          } else {
            // Fetch trending/general data
            const trendingCoins = await coinGeckoService.getTrending();
            const marketData = await coinGeckoService.getCoinsMarkets('usd', 'market_cap_desc', 50);
            
            refreshedData = {
              trending: trendingCoins.coins,
              marketData: marketData,
              type: refreshType,
              source: 'coingecko',
              tokensCount: marketData.length,
              lastUpdated: Date.now(),
            };
          }

          // Cache the refreshed data
          cacheService.set(refreshCacheKey, refreshedData, CACHE_DURATIONS.MARKET_DATA);

          return NextResponse.json({
            ...refreshedData,
            cached: false,
            timestamp: Date.now(),
          });
        } catch (error) {
          console.error('Failed to refresh on-chain metrics:', error);
          return NextResponse.json({ error: 'Failed to refresh metrics data' }, { status: 500 });
        }

      default:
        // If no specific action, return cached or fresh data based on parameters
        if (source === 'dexscreener') {
          const dexCacheKey = createCacheKey('onchain-metrics', 'dexscreener', refresh.toString());
          
          if (!refresh) {
            const cachedDexData = cacheService.get(dexCacheKey);
            if (cachedDexData) {
              return NextResponse.json({
                ...cachedDexData,
                cached: true,
                timestamp: Date.now(),
              });
            }
          }

          // Fetch fresh DexScreener data
          try {
            const tokenProfiles = await dexScreenerService.getTokenProfiles();
            const searchResults = await dexScreenerService.searchPairs('ETH');
            
            const freshDexData = {
              tokenProfiles: tokenProfiles.slice(0, 30),
              searchResults: searchResults.pairs.slice(0, 20),
              tokensCount: tokenProfiles.length || searchResults.pairs.length,
              source: 'dexscreener',
              lastUpdated: Date.now(),
            };

            cacheService.set(dexCacheKey, freshDexData, CACHE_DURATIONS.MARKET_DATA);
            
            return NextResponse.json({
              ...freshDexData,
              cached: false,
              timestamp: Date.now(),
            });
          } catch (error) {
            console.error('DexScreener fetch error:', error);
            return NextResponse.json({ error: 'Failed to fetch DexScreener data' }, { status: 500 });
          }
        }

        return NextResponse.json({ error: 'Invalid action parameter' }, { status: 400 });
    }
  } catch (error) {
    console.error('OnChain API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, chains, analysisType, customWeights } = body;

    if (action === 'automated-comparison') {
      if (!chains || chains.length < 2) {
        return NextResponse.json({ error: 'At least 2 chains required' }, { status: 400 });
      }

      // In production, this would use real API calls and advanced scoring
      const comparisonResult: any[] = [];
      
      for (const chainId of chains) {
        const data = MOCK_CHAIN_DATA[chainId.toLowerCase()];
        if (data) {
          const derived = calculateDerivedMetrics(data);
          
          // Advanced scoring with custom weights
          let score = 0;
          const weights = customWeights || {
            technical: 0.3,
            economic: 0.3,
            adoption: 0.2,
            security: 0.2,
          };

          // Technical score
          const technicalScore = (
            (data.realTimeTps / data.theoreticalTps) * 25 +
            Math.max(0, 100 - data.finalityTime * 2) * 0.25 +
            Math.max(0, 100 - Math.log10(data.energyPerTx)) * 0.5
          );

          // Economic score
          const economicScore = (
            Math.min(derived.mc_tvl || 0, 100) * 0.3 +
            Math.max(0, 100 - data.avgFee * 100) * 0.4 +
            Math.min(data.transactionVolume / 1e9 * 10, 100) * 0.3
          );

          // Adoption score
          const adoptionScore = (
            Math.min(data.dailyActiveAddresses / 10000, 100) * 0.4 +
            Math.min(data.activeDevelopers / 50, 100) * 0.6
          );

          // Security score
          const securityScore = (
            Math.min(data.validators / 100, 100) * 0.6 +
            Math.min(derived.staked_circulating || 0, 100) * 0.4
          );

          score = 
            technicalScore * weights.technical +
            economicScore * weights.economic +
            adoptionScore * weights.adoption +
            securityScore * weights.security;

          comparisonResult.push({
            ...data,
            derivedMetrics: derived,
            score: Math.round(Math.max(0, Math.min(100, score))),
            breakdown: {
              technical: Math.round(technicalScore),
              economic: Math.round(economicScore),
              adoption: Math.round(adoptionScore),
              security: Math.round(securityScore),
            },
          });
        }
      }

      // Sort by score
      comparisonResult.sort((a, b) => b.score - a.score);

      // Generate detailed insights
      const detailedInsights = [];
      if (comparisonResult.length > 0) {
        const winner = comparisonResult[0];
        detailedInsights.push({
          type: 'winner',
          text: `${winner.name} achieved the highest overall score of ${winner.score}/100`,
          data: winner.breakdown,
        });

        // Find category leaders
        const categories = ['technical', 'economic', 'adoption', 'security'];
        categories.forEach(category => {
          const leader = comparisonResult.reduce((prev, current) => 
            (current.breakdown[category] > prev.breakdown[category]) ? current : prev
          );
          if (leader.breakdown[category] > 70) {
            detailedInsights.push({
              type: 'category_leader',
              category,
              text: `${leader.name} leads in ${category} performance with ${leader.breakdown[category]}/100`,
              chain: leader.name,
            });
          }
        });
      }

      return NextResponse.json({
        results: comparisonResult,
        insights: detailedInsights,
        analysisType,
        weights: customWeights,
        timestamp: Date.now(),
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('OnChain POST API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}