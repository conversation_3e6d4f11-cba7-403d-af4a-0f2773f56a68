import { NextResponse } from 'next/server';
import { cryptoNewsService } from '@/utils/api-services';
import { FEATURE_FLAGS } from '@/utils/api-config';
import { cacheService, createCacheKey, CACHE_CONFIGS } from '@/utils/cache-service';

// Cache news data for 12 hours (twice daily updates)
const NEWS_CACHE_TTL = 12 * 60 * 60; // 12 hours in seconds

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('query');
  const tickers = searchParams.get('tickers')?.split(',');
  const items = parseInt(searchParams.get('items') || '20');
  const forceRefresh = searchParams.get('refresh') === 'true';

  // Create cache key based on request parameters
  const cacheKey = createCacheKey('crypto-news', 
    tickers?.join('-') || 'general', 
    query || 'latest', 
    items.toString()
  );

  // Check cache first (unless force refresh requested)
  if (!forceRefresh) {
    const cachedNews = cacheService.get(cacheKey);
    if (cachedNews) {
      console.log(`🎯 Crypto news cache hit: ${cacheKey}`);
      return NextResponse.json({
        ...cachedNews,
        cached: true,
        cacheAge: Date.now() - ((cachedNews as any).timestamp || 0),
      });
    }
    console.log(`❌ Crypto news cache miss: ${cacheKey}`);
  }

  try {
    console.log(`📰 Fetching fresh crypto news: tickers=${tickers?.join(',') || 'all'}, items=${items}`);
    
    // Try to fetch from CryptoNews API first
    const newsData = await cryptoNewsService.getNews(tickers, items);

    const response = {
      status: 'ok',
      totalResults: newsData.data.length,
      articles: newsData.data,
      timestamp: Date.now(),
      source: 'cryptonews-api',
    };

    // Cache the successful response for 12 hours
    cacheService.set(cacheKey, response, NEWS_CACHE_TTL, 'crypto-news');
    console.log(`💾 Cached crypto news for 12 hours: ${cacheKey}`);

    return NextResponse.json(response);
  } catch (error) {
    console.error('CryptoNews API failed, trying fallback:', error);

    // Fallback to NewsAPI if available
    const NEWS_API_KEY = process.env.NEWS_API_KEY;

    if (NEWS_API_KEY && NEWS_API_KEY !== 'your_news_api_key_here') {
      try {
        const fallbackQuery = query || 'bitcoin cryptocurrency';
        const url = `https://newsapi.org/v2/everything?q=${encodeURIComponent(fallbackQuery)}&apiKey=${NEWS_API_KEY}&sortBy=publishedAt&pageSize=${items}`;

        const response = await fetch(url);
        if (!response.ok) throw new Error(`NewsAPI failed: ${response.statusText}`);

        const newsData = await response.json();

        const fallbackResponse = {
          status: newsData.status,
          totalResults: newsData.totalResults,
          articles: newsData.articles.map((article: any) => ({
            title: article.title,
            description: article.description,
            url: article.url,
            urlToImage: article.urlToImage,
            publishedAt: article.publishedAt,
            source: article.source,
          })),
          timestamp: Date.now(),
          source: 'newsapi-fallback',
        };

        // Cache fallback response for 12 hours too
        cacheService.set(cacheKey, fallbackResponse, NEWS_CACHE_TTL, 'crypto-news');
        console.log(`💾 Cached fallback news for 12 hours: ${cacheKey}`);

        return NextResponse.json(fallbackResponse);
      } catch (fallbackError) {
        console.error('NewsAPI fallback also failed:', fallbackError);
      }
    }

    // Final fallback to mock data
    if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
      console.warn('All news APIs failed, returning cached mock data');
      
      const mockResponse = {
        status: 'ok',
        totalResults: 3,
        articles: [
          {
            title: "Bitcoin Reaches New Heights",
            description: "Bitcoin continues its upward trajectory as institutional adoption grows.",
            url: "https://example.com/bitcoin-news",
            urlToImage: "https://via.placeholder.com/300x200",
            publishedAt: new Date().toISOString(),
            source: { name: "CryptoNews" },
          },
          {
            title: "Ethereum 2.0 Updates",
            description: "Latest developments in Ethereum's transition to proof-of-stake.",
            url: "https://example.com/ethereum-news",
            urlToImage: "https://via.placeholder.com/300x200",
            publishedAt: new Date(Date.now() - 3600000).toISOString(),
            source: { name: "CryptoNews" },
          },
          {
            title: "DeFi Market Analysis",
            description: "Comprehensive analysis of the current DeFi landscape and trends.",
            url: "https://example.com/defi-news",
            urlToImage: "https://via.placeholder.com/300x200",
            publishedAt: new Date(Date.now() - 7200000).toISOString(),
            source: { name: "CryptoNews" },
          },
        ],
        timestamp: Date.now(),
        source: 'mock-data',
      };

      // Cache mock data for 12 hours as well
      cacheService.set(cacheKey, mockResponse, NEWS_CACHE_TTL, 'crypto-news');
      console.log(`💾 Cached mock news for 12 hours: ${cacheKey}`);

      return NextResponse.json(mockResponse);
    }

    // If fallback is disabled, return error
    return NextResponse.json(
      { error: 'News service unavailable' },
      { status: 503 }
    );
  }
}


