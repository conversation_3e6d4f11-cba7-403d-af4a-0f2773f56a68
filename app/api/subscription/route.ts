import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@/utils/supabase/server';

function getStripe() {
  if (!process.env.STRIPE_SECRET_KEY) {
    throw new Error('STRIPE_SECRET_KEY is not configured');
  }
  return new Stripe(process.env.STRIPE_SECRET_KEY, {
    apiVersion: '2025-06-30.basil',
  });
}

export async function POST(req: NextRequest) {
  try {
    const { planName, billingCycle } = await req.json();
    
    if (!planName || !billingCycle) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 });
    }

    const supabase = await createClient();
    
    // Get current user
    const { data: { user }, error: authError } = await (supabase as any).auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user details
    const { data: userData, error: userError } = await (supabase as any)
      .from('users')
      .select('email, username')
      .eq('user_id', user.id)
      .single();

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get subscription plan
    const { data: plan, error: planError } = await (supabase as any)
      .from('subscription_plans')
      .select('*')
      .eq('name', planName)
      .eq('is_active', true)
      .single();

    if (planError || !plan) {
      return NextResponse.json({ error: 'Invalid subscription plan' }, { status: 400 });
    }

    // Create or get Stripe customer
    const stripe = getStripe();
    let customer: Stripe.Customer;
    const existingCustomers = await stripe.customers.list({
      email: userData.email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      customer = existingCustomers.data[0];
    } else {
      customer = await stripe.customers.create({
        email: userData.email,
        name: userData.username || undefined,
        metadata: {
          user_id: user.id,
        },
      });
    }

    // Get price for the plan and billing cycle
    const priceId = getPriceId(planName, billingCycle);
    if (!priceId) {
      return NextResponse.json({ error: 'Invalid billing cycle for plan' }, { status: 400 });
    }

    // Create checkout session
    const session = await stripe.checkout.sessions.create({
      customer: customer.id,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.NEXT_PUBLIC_SITE_URL}/ctn/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_SITE_URL}/ctn/subscription/plans`,
      metadata: {
        user_id: user.id,
        plan_name: planName,
      },
    });

    return NextResponse.json({ sessionId: session.id, url: session.url });
  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Get current user
    const { data: { user }, error: authError } = await (supabase as any).auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's subscription status using direct table query
    const { data: subscriptionData, error } = await (supabase as any)
      .from('user_subscriptions')
      .select(`
        *,
        subscription_plans (
          name,
          display_name,
          billing_cycle,
          features
        )
      `)
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (error) {
      // Return default tier status if no subscription found
      const defaultStatus = {
        status: 'tier1',
        plan_name: 'tier0',
        plan_display_name: 'Free Tier',
        features: [],
        is_premium: false
      };
      return NextResponse.json(defaultStatus);
    }

    // Format the subscription data
    const formattedData = {
      status: subscriptionData.status,
      plan_name: subscriptionData.subscription_plans.name,
      plan_display_name: subscriptionData.subscription_plans.display_name,
      billing_cycle: subscriptionData.subscription_plans.billing_cycle,
      features: subscriptionData.subscription_plans.features || [],
      is_premium: subscriptionData.status === 'active',
      current_period_end: subscriptionData.current_period_end,
      cancel_at_period_end: subscriptionData.cancel_at_period_end,
      trial_end_date: subscriptionData.trial_end_date
    };

    return NextResponse.json(formattedData);
  } catch (error) {
    console.error('Error fetching subscription:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const supabase = await createClient();
    
    // Get current user
    const { data: { user }, error: authError } = await (supabase as any).auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's subscription
    const { data: subscription, error: subError } = await (supabase as any)
      .from('user_subscriptions')
      .select('stripe_subscription_id')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    if (subError || !subscription) {
      return NextResponse.json({ error: 'No active subscription found' }, { status: 404 });
    }

    // Cancel the subscription in Stripe
    const stripe = getStripe();
    await stripe.subscriptions.update(subscription.stripe_subscription_id, {
      cancel_at_period_end: true,
    });

    // Update the subscription in our database
    const { error: updateError } = await (supabase as any)
      .from('user_subscriptions')
      .update({
        cancel_at_period_end: true,
        updated_at: new Date().toISOString(),
      })
      .eq('stripe_subscription_id', subscription.stripe_subscription_id);

    if (updateError) {
      console.error('Error updating subscription:', updateError);
      return NextResponse.json({ error: 'Failed to update subscription' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Subscription will be canceled at the end of the current period' });
  } catch (error) {
    console.error('Error canceling subscription:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function getPriceId(planName: string, billingCycle: 'monthly' | 'yearly'): string | null {
  // These should be your actual Stripe price IDs
  // You'll need to create these in your Stripe dashboard and update these values
  const priceIds: Record<string, Record<string, string>> = {
    tier1: {
      monthly: 'price_tier1_monthly', // Replace with actual Stripe price ID
      yearly: 'price_tier1_yearly',   // Replace with actual Stripe price ID
    },
    tier2: {
      monthly: 'price_tier2_monthly', // Replace with actual Stripe price ID
      yearly: 'price_tier2_yearly',   // Replace with actual Stripe price ID
    },
    tier3: {
      monthly: 'price_tier3_monthly', // Replace with actual Stripe price ID
      yearly: 'price_tier3_yearly',   // Replace with actual Stripe price ID
    },
    tier4: {
      monthly: 'price_tier4_monthly', // Replace with actual Stripe price ID
      yearly: 'price_tier4_yearly',   // Replace with actual Stripe price ID
    },
  };

  return priceIds[planName]?.[billingCycle] || null;
}
