import { NextRequest, NextResponse } from 'next/server';
import { binanceService } from '@/utils/binance-service';
import { coinGeckoService } from '@/utils/api-services';
import { mergeMarketDataSources, transformCoinGeckoToMarketData } from '@/utils/data-transformers';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const requestedSymbols = searchParams.get('symbols')?.split(',');
    const limit = parseInt(searchParams.get('limit') || '50');
    const source = searchParams.get('source') || 'unified'; // 'binance', 'coingecko', 'unified'

    // Filter to only include BASE coins if symbols are specified
    let symbols = requestedSymbols;
    if (requestedSymbols) {
      const { BASE_COINS } = await import('@/utils/mock-data-service');
      const baseSymbols = BASE_COINS.map(coin => coin.symbol);
      symbols = requestedSymbols.filter(symbol => baseSymbols.includes(symbol.toUpperCase()));

      if (symbols.length !== requestedSymbols.length) {
        const filtered = requestedSymbols.filter(symbol => !baseSymbols.includes(symbol.toUpperCase()));
        console.warn(`⚠️ Filtered out non-BASE coins: ${filtered.join(',')}`);
      }
    }

    console.log(`📊 Market data request: symbols=${symbols?.join(',') || 'all'}, limit=${limit}, source=${source}`);

    switch (source) {
      case 'binance':
        return await getBinanceMarketData(symbols, limit);
      
      case 'coingecko':
        return await getCoinGeckoMarketData(symbols, limit);
      
      case 'unified':
      default:
        return await getUnifiedMarketData(symbols, limit);
    }
  } catch (error) {
    console.error('Market data API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch market data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Get market data from Binance (primary)
async function getBinanceMarketData(symbols?: string[], limit: number = 50) {
  try {
    console.log('🚀 Fetching from Binance API...');
    const tickers = await binanceService.get24hrTicker(symbols);
    const marketData = binanceService.transformToMarketData(tickers);
    
    console.log(`✅ Binance: Retrieved ${marketData.length} coins`);
    
    return NextResponse.json({
      data: marketData.slice(0, limit),
      source: 'binance',
      timestamp: Date.now(),
      count: marketData.length,
    });
  } catch (error) {
    console.error('Binance API failed:', error);
    throw error;
  }
}

// Get market data from CoinGecko (fallback)
async function getCoinGeckoMarketData(_symbols?: string[], limit: number = 50) {
  try {
    console.log('🔄 Fetching from CoinGecko API...');
    const coinsData = await coinGeckoService.getCoinsMarkets('usd', 'market_cap_desc', limit);
    const marketData = transformCoinGeckoToMarketData(coinsData);
    
    console.log(`✅ CoinGecko: Retrieved ${marketData.length} coins`);
    
    return NextResponse.json({
      data: marketData,
      source: 'coingecko',
      timestamp: Date.now(),
      count: marketData.length,
    });
  } catch (error) {
    console.error('CoinGecko API failed:', error);
    throw error;
  }
}

// Get unified market data (Binance → CoinGecko → Mock)
async function getUnifiedMarketData(symbols?: string[], limit: number = 50) {
  let binanceData: any[] = [];
  let coinGeckoData: any[] = [];
  let sources: string[] = [];

  // Step 1: Try Binance first
  try {
    console.log('🚀 Trying Binance first...');
    const tickers = await binanceService.get24hrTicker(symbols);
    binanceData = binanceService.transformToMarketData(tickers);
    sources.push('binance');
    console.log(`✅ Binance: Got ${binanceData.length} coins`);
  } catch (binanceError) {
    console.warn('⚠️ Binance failed:', binanceError);
  }

  // Step 2: Supplement with CoinGecko if needed
  if (binanceData.length < limit || (symbols && binanceData.length < symbols.length)) {
    try {
      console.log('🔄 Supplementing with CoinGecko...');
      const coinsData = await coinGeckoService.getCoinsMarkets('usd', 'market_cap_desc', limit);
      coinGeckoData = transformCoinGeckoToMarketData(coinsData);
      sources.push('coingecko');
      console.log(`✅ CoinGecko: Got ${coinGeckoData.length} coins`);
    } catch (coinGeckoError) {
      console.warn('⚠️ CoinGecko failed:', coinGeckoError);
    }
  }

  // Step 3: Merge data sources
  let finalData: any[] = [];
  
  if (binanceData.length > 0 || coinGeckoData.length > 0) {
    finalData = mergeMarketDataSources(binanceData, coinGeckoData);
    console.log(`✅ Merged: ${finalData.length} total coins`);
  } else {
    // Step 4: Always fallback to mock data when APIs fail
    console.log('📦 All APIs failed, using mock data as fallback...');
    finalData = await getMockMarketData(symbols, limit);
    sources.push('mock');
    console.log(`✅ Mock data: Generated ${finalData.length} coins`);
  }

  return NextResponse.json({
    data: finalData.slice(0, limit),
    sources,
    timestamp: Date.now(),
    count: finalData.length,
  });
}

// Mock data fallback - now using centralized service
async function getMockMarketData(symbols?: string[], limit: number = 50): Promise<any[]> {
  try {
    // Import centralized service dynamically to avoid circular dependencies
    const { getAllCoinsData } = await import('@/utils/mock-data-service');
    const centralizedData = await getAllCoinsData();

    // Filter by symbols if provided, otherwise return all
    let filteredData = centralizedData;
    if (symbols && symbols.length > 0) {
      filteredData = centralizedData.filter(coin =>
        symbols.some(symbol => symbol.toLowerCase() === coin.symbol.toLowerCase())
      );
    }

    return filteredData.slice(0, limit).map(coin => ({
      symbol: coin.symbol,
      price: coin.price,
      change24h: coin.change24h,
      change7d: coin.change7d,
      volume24h: coin.volume24h,
      marketCap: coin.marketCap,
      high24h: coin.price * 1.05, // Estimate high as 5% above current price
      low24h: coin.price * 0.95,  // Estimate low as 5% below current price
      lastUpdated: coin.lastUpdated,
    }));
  } catch (error) {
    console.warn('⚠️ Centralized mock service failed, using legacy fallback:', error);

    // Legacy fallback for emergency cases - only use BASE coins
    const { BASE_COINS } = await import('@/utils/mock-data-service');
    const mockSymbols = symbols || BASE_COINS.map(coin => coin.symbol);

    return mockSymbols.slice(0, limit).map((symbol) => ({
      symbol,
      price: Math.random() * 50000 + 100,
      change24h: (Math.random() - 0.5) * 20,
      change7d: (Math.random() - 0.5) * 40,
      volume24h: Math.random() * **********,
      marketCap: Math.random() * **********00,
      high24h: Math.random() * 55000 + 100,
      low24h: Math.random() * 45000 + 100,
      lastUpdated: Date.now(),
    }));
  }
}

// Health check endpoint
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'health') {
      console.log('🏥 Checking API health...');
      
      const [binanceHealth, coinGeckoHealth] = await Promise.allSettled([
        binanceService.ping(),
        coinGeckoService.ping(),
      ]);

      const health = {
        binance: binanceHealth.status === 'fulfilled' && binanceHealth.value,
        coingecko: coinGeckoHealth.status === 'fulfilled' && coinGeckoHealth.value,
        timestamp: Date.now(),
      };

      console.log('🏥 Health check results:', health);

      return NextResponse.json(health);
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      { error: 'Health check failed' },
      { status: 500 }
    );
  }
}
