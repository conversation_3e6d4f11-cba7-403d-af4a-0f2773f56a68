import { NextRequest, NextResponse } from 'next/server';
import { coinGeckoService } from '@/utils/api-services';
import { dexScreenerService } from '@/utils/dexscreener-service';
import { messariService } from '@/utils/messari-service';
import { FEATURE_FLAGS } from '@/utils/api-config';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const source = searchParams.get('source') || 'unified'; // 'coingecko', 'dexscreener', 'messari', 'unified'
    const limit = parseInt(searchParams.get('limit') || '20');

    console.log(`📈 Trending request: source=${source}, limit=${limit}`);

    switch (source) {
      case 'coingecko':
        return await getCoinGeckoTrending(limit);
      
      case 'dexscreener':
        return await getDexScreenerTrending(limit);
      
      case 'messari':
        return await getMessariTrending(limit);
      
      case 'unified':
      default:
        return await getUnifiedTrending(limit);
    }
  } catch (error) {
    console.error('Trending API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trending data', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// Get trending from CoinGecko
async function getCoinGeckoTrending(limit: number) {
  try {
    console.log('📈 Fetching trending from CoinGecko...');
    const trending = await coinGeckoService.getTrending();
    
    const trendingCoins = trending.coins.slice(0, limit).map(coin => ({
      id: coin.id,
      name: coin.name,
      symbol: coin.symbol,
      rank: coin.market_cap_rank,
      score: coin.score,
      image: coin.large,
      price_btc: coin.price_btc,
      source: 'coingecko',
    }));

    console.log(`✅ CoinGecko: Got ${trendingCoins.length} trending coins`);
    
    return NextResponse.json({
      data: trendingCoins,
      source: 'coingecko',
      timestamp: Date.now(),
      count: trendingCoins.length,
    });
  } catch (error) {
    console.error('CoinGecko trending failed:', error);
    throw error;
  }
}

// Get trending from DexScreener (most traded pairs)
async function getDexScreenerTrending(limit: number) {
  try {
    console.log('📈 Fetching trending from DexScreener...');
    const trending = await dexScreenerService.getTrendingPairs();
    
    const trendingTokens = trending.pairs.slice(0, limit).map((pair, index) => ({
      id: pair.baseToken.address,
      name: pair.baseToken.name,
      symbol: pair.baseToken.symbol,
      rank: index + 1,
      score: pair.volume.h24, // Use volume as score
      price_usd: parseFloat(pair.priceUsd),
      volume_24h: pair.volume.h24,
      price_change_24h: pair.priceChange.h24,
      liquidity_usd: pair.liquidity?.usd || 0,
      dex: pair.dexId,
      chain: pair.chainId,
      source: 'dexscreener',
    }));

    console.log(`✅ DexScreener: Got ${trendingTokens.length} trending tokens`);
    
    return NextResponse.json({
      data: trendingTokens,
      source: 'dexscreener',
      timestamp: Date.now(),
      count: trendingTokens.length,
    });
  } catch (error) {
    console.error('DexScreener trending failed:', error);
    throw error;
  }
}

// Get trending from Messari (recent news and high activity)
async function getMessariTrending(limit: number) {
  try {
    console.log('📈 Fetching trending from Messari...');
    
    // Get popular assets from Messari
    const assets = await messariService.getAllAssets(limit);
    const popularAssets = assets.data.slice(0, limit);
    
    // Get metrics for these assets
    const assetKeys = popularAssets.map(asset => asset.slug);
    const metrics = await messariService.getMarketData(assetKeys);
    
    const trendingAssets = metrics.data.map((asset, index) => ({
      id: asset.id,
      name: asset.name,
      symbol: asset.symbol,
      rank: asset.marketcap?.rank || index + 1,
      score: asset.market_data?.volume_last_24_hours || 0,
      price_usd: asset.market_data?.price_usd || 0,
      market_cap: asset.marketcap?.current_marketcap_usd || 0,
      price_change_24h: asset.market_data?.percent_change_usd_last_24_hours || 0,
      volume_24h: asset.market_data?.volume_last_24_hours || 0,
      source: 'messari',
    }));

    console.log(`✅ Messari: Got ${trendingAssets.length} trending assets`);
    
    return NextResponse.json({
      data: trendingAssets,
      source: 'messari',
      timestamp: Date.now(),
      count: trendingAssets.length,
    });
  } catch (error) {
    console.error('Messari trending failed:', error);
    throw error;
  }
}

// Get unified trending (combine all sources)
async function getUnifiedTrending(limit: number) {
  let allTrending: any[] = [];
  let sources: string[] = [];

  // Try to get data from all sources internally
  const dataPromises = [
    (async () => {
      try {
        const trending = await coinGeckoService.getTrending();
        const trendingCoins = trending.coins.slice(0, Math.ceil(limit / 3)).map(coin => ({
          id: coin.id,
          name: coin.name,
          symbol: coin.symbol,
          rank: coin.market_cap_rank,
          score: coin.score,
          image: coin.large,
          price_btc: coin.price_btc,
          source: 'coingecko',
        }));
        return { data: trendingCoins, source: 'coingecko' };
      } catch (error) {
        console.warn('⚠️ CoinGecko trending failed:', error);
        return null;
      }
    })(),
    (async () => {
      try {
        const trending = await dexScreenerService.getTrendingPairs();
        const trendingTokens = trending.pairs.slice(0, Math.ceil(limit / 3)).map((pair, index) => ({
          id: pair.baseToken.address,
          name: pair.baseToken.name,
          symbol: pair.baseToken.symbol,
          rank: index + 1,
          score: pair.volume.h24,
          price_usd: parseFloat(pair.priceUsd),
          volume_24h: pair.volume.h24,
          price_change_24h: pair.priceChange.h24,
          liquidity_usd: pair.liquidity?.usd || 0,
          dex: pair.dexId,
          chain: pair.chainId,
          source: 'dexscreener',
        }));
        return { data: trendingTokens, source: 'dexscreener' };
      } catch (error) {
        console.warn('⚠️ DexScreener trending failed:', error);
        return null;
      }
    })(),
    (async () => {
      try {
        const assets = await messariService.getAllAssets(Math.ceil(limit / 3));
        const popularAssets = assets.data.slice(0, Math.ceil(limit / 3));
        const assetKeys = popularAssets.map(asset => asset.slug);
        const metrics = await messariService.getMarketData(assetKeys);
        
        const trendingAssets = metrics.data.map((asset, index) => ({
          id: asset.id,
          name: asset.name,
          symbol: asset.symbol,
          rank: asset.marketcap?.rank || index + 1,
          score: asset.market_data?.volume_last_24_hours || 0,
          price_usd: asset.market_data?.price_usd || 0,
          market_cap: asset.marketcap?.current_marketcap_usd || 0,
          price_change_24h: asset.market_data?.percent_change_usd_last_24_hours || 0,
          volume_24h: asset.market_data?.volume_last_24_hours || 0,
          source: 'messari',
        }));
        return { data: trendingAssets, source: 'messari' };
      } catch (error) {
        console.warn('⚠️ Messari trending failed:', error);
        return null;
      }
    })(),
  ];

  const results = await Promise.allSettled(dataPromises);

  // Process results
  results.forEach((result) => {
    if (result.status === 'fulfilled' && result.value) {
      const { data, source } = result.value;
      allTrending.push(...data);
      sources.push(source);
    }
  });

  // If no sources worked, provide mock data
  if (allTrending.length === 0 && FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
    console.log('📦 Using mock trending data...');
    allTrending = await getMockTrendingData(limit);
    sources.push('mock');
  }

  // Remove duplicates and sort by score
  const uniqueTrending = Array.from(
    new Map(allTrending.map(item => [item.symbol, item])).values()
  );

  const sortedTrending = uniqueTrending
    .sort((a, b) => (b.score || 0) - (a.score || 0))
    .slice(0, limit);

  console.log(`✅ Unified trending: ${sortedTrending.length} items from [${sources.join(', ')}]`);

  return NextResponse.json({
    data: sortedTrending,
    sources,
    timestamp: Date.now(),
    count: sortedTrending.length,
  });
}

// Mock trending data - now using centralized service
async function getMockTrendingData(limit: number): Promise<any[]> {
  try {
    // Import centralized service dynamically to avoid circular dependencies
    const { getAllCoinsData } = await import('@/utils/mock-data-service');
    const centralizedData = await getAllCoinsData();

    return centralizedData.slice(0, limit).map((coin, index) => ({
      id: coin.symbol.toLowerCase(),
      name: coin.name,
      symbol: coin.symbol,
      rank: coin.rank,
      score: Math.random() * 100, // Trending score is still random
      price_usd: coin.price,
      price_change_24h: coin.change24h,
      volume_24h: coin.volume24h,
      market_cap: coin.marketCap,
      source: 'centralized-mock',
    }));
  } catch (error) {
    console.warn('⚠️ Centralized mock service failed for trending data, using legacy fallback:', error);

    // Legacy fallback for emergency cases
    // Use BASE coins for legacy fallback
    const { BASE_COINS } = await import('@/utils/mock-data-service');
    const mockCoins = BASE_COINS.map(coin => ({
      symbol: coin.symbol,
      name: coin.name,
      price: Math.random() * 50000 + 100,
      change: (Math.random() - 0.5) * 20
    }));

    return mockCoins.slice(0, limit).map((coin, index) => ({
      id: coin.symbol.toLowerCase(),
      name: coin.name,
      symbol: coin.symbol,
      rank: index + 1,
      score: Math.random() * 100,
      price_usd: coin.price + (Math.random() - 0.5) * coin.price * 0.1,
      price_change_24h: coin.change + (Math.random() - 0.5) * 5,
      volume_24h: Math.random() * **********,
      market_cap: coin.price * (Math.random() * **********),
      source: 'legacy-mock',
    }));
  }
}

// Health check for trending sources
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();
    
    if (action === 'health') {
      console.log('🏥 Checking trending sources health...');
      
      const [coinGeckoHealth, dexScreenerHealth, messariHealth] = await Promise.allSettled([
        coinGeckoService.ping(),
        dexScreenerService.ping(),
        messariService.ping(),
      ]);

      const health = {
        coingecko: coinGeckoHealth.status === 'fulfilled' && coinGeckoHealth.value,
        dexscreener: dexScreenerHealth.status === 'fulfilled' && dexScreenerHealth.value,
        messari: messariHealth.status === 'fulfilled' && messariHealth.value,
        timestamp: Date.now(),
      };

      console.log('🏥 Trending sources health:', health);

      return NextResponse.json(health);
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
  } catch (error) {
    console.error('Trending health check error:', error);
    return NextResponse.json(
      { error: 'Health check failed' },
      { status: 500 }
    );
  }
}