import { NextRequest, NextResponse } from 'next/server';
import { newsScheduler } from '@/utils/news-scheduler';

// Admin endpoint to manually control news cache refresh
export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'refresh':
        console.log('🔄 Manual news cache refresh triggered');
        await newsScheduler.refreshNewsCache();
        return NextResponse.json({ 
          success: true, 
          message: 'News cache refreshed successfully',
          timestamp: Date.now(),
        });

      case 'start':
        newsScheduler.startScheduler();
        return NextResponse.json({ 
          success: true, 
          message: 'News scheduler started',
          status: newsScheduler.getStatus(),
        });

      case 'stop':
        newsScheduler.stopScheduler();
        return NextResponse.json({ 
          success: true, 
          message: 'News scheduler stopped',
          status: newsScheduler.getStatus(),
        });

      case 'status':
        return NextResponse.json({
          success: true,
          status: newsScheduler.getStatus(),
          cacheStats: newsScheduler.getNewsCacheStats(),
        });

      case 'clear':
        const deletedCount = newsScheduler.clearNewsCache();
        return NextResponse.json({
          success: true,
          message: `Cleared ${deletedCount} news cache entries`,
          deletedCount,
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: refresh, start, stop, status, clear' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('News refresh admin error:', error);
    return NextResponse.json(
      { error: 'Admin operation failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// GET endpoint for status check
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      scheduler: newsScheduler.getStatus(),
      cache: newsScheduler.getNewsCacheStats(),
      info: {
        refreshFrequency: '12 hours',
        cacheStrategy: 'twice-daily',
        lastRefresh: 'Available in cache stats',
      },
    });
  } catch (error) {
    console.error('News refresh status error:', error);
    return NextResponse.json(
      { error: 'Status check failed' },
      { status: 500 }
    );
  }
}