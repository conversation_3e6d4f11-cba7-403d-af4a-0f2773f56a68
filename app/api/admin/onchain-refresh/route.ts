import { NextRequest, NextResponse } from 'next/server';
import { onChainMetricsScheduler } from '@/utils/onchain-metrics-scheduler';

export async function POST(request: NextRequest) {
  try {
    const { action } = await request.json();

    switch (action) {
      case 'refresh':
        await onChainMetricsScheduler.refreshOnChainMetricsCache();
        return NextResponse.json({ 
          message: 'On-chain metrics cache refresh triggered successfully',
          timestamp: Date.now()
        });

      case 'start':
        onChainMetricsScheduler.startScheduler();
        return NextResponse.json({ 
          message: 'On-chain metrics scheduler started',
          status: onChainMetricsScheduler.getStatus()
        });

      case 'stop':
        onChainMetricsScheduler.stopScheduler();
        return NextResponse.json({ 
          message: 'On-chain metrics scheduler stopped',
          status: onChainMetricsScheduler.getStatus()
        });

      case 'status':
        const status = onChainMetricsScheduler.getStatus();
        const cacheStats = onChainMetricsScheduler.getDetailedCacheStats();
        return NextResponse.json({ 
          schedulerStatus: status,
          cacheStats,
          timestamp: Date.now()
        });

      case 'clear':
        const deletedCount = onChainMetricsScheduler.clearOnChainMetricsCache();
        return NextResponse.json({ 
          message: `Cleared ${deletedCount} on-chain metrics cache entries`,
          deletedCount,
          timestamp: Date.now()
        });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('On-chain metrics admin API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const info = searchParams.get('info');

    if (info === 'status') {
      const status = onChainMetricsScheduler.getStatus();
      const cacheStats = onChainMetricsScheduler.getDetailedCacheStats();
      const basicStats = onChainMetricsScheduler.getOnChainMetricsCacheStats();

      return NextResponse.json({
        scheduler: {
          isRunning: status.isRunning,
          nextRefresh: status.nextRefresh ? new Date(status.nextRefresh).toISOString() : null,
        },
        cache: {
          basic: basicStats,
          detailed: cacheStats,
        },
        timestamp: Date.now(),
      });
    }

    return NextResponse.json({
      message: 'On-chain metrics scheduler admin endpoint',
      availableActions: ['refresh', 'start', 'stop', 'status', 'clear'],
      availableInfo: ['status'],
      timestamp: Date.now(),
    });
  } catch (error) {
    console.error('On-chain metrics admin GET error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}