import { NextRequest, NextResponse } from 'next/server';
import { mockDataService, BASE_COINS_MAPPING } from '@/utils/mock-data-service';
import { FEATURE_FLAGS } from '@/utils/api-config';
import { coinGeckoService } from '@/utils/api-services';

/**
 * Mock Data API Endpoint
 * 
 * This endpoint provides centralized mock/fallback data for all BASE coins.
 * It fetches real data from CoinGecko and falls back to static mock data.
 */

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const format = searchParams.get('format') || 'standard'; // standard, heatmap, trading
  const symbol = searchParams.get('symbol'); // Get specific coin
  const forceRefresh = searchParams.get('refresh') === 'true';

  try {
    console.log(`📊 Mock data API called - format: ${format}, symbol: ${symbol || 'all'}, refresh: ${forceRefresh}`);

    // Clear cache if force refresh is requested
    if (forceRefresh) {
      mockDataService.clearCache();
    }

    // Handle specific coin request
    if (symbol) {
      const coinData = await mockDataService.getCoinData(symbol);
      if (!coinData) {
        return NextResponse.json(
          { error: `Coin with symbol '${symbol}' not found` },
          { status: 404 }
        );
      }

      return NextResponse.json({
        data: coinData,
        format: 'single',
        source: 'centralized-mock-service',
        timestamp: Date.now(),
        cacheStatus: mockDataService.getCacheStatus(),
      });
    }

    // Handle different format requests
    let data;
    switch (format) {
      case 'heatmap':
        data = await mockDataService.getHeatmapData();
        break;
      case 'trading':
        data = await mockDataService.getTradingData();
        break;
      case 'standard':
      default:
        data = await mockDataService.getAllCoinsData();
        break;
    }

    const cacheStatus = mockDataService.getCacheStatus();

    return NextResponse.json({
      data,
      format,
      count: data.length,
      baseCoins: Object.keys(BASE_COINS_MAPPING),
      source: 'centralized-mock-service',
      timestamp: Date.now(),
      cacheStatus,
      metadata: {
        description: 'Centralized mock data service providing realistic fallback data',
        supportedFormats: ['standard', 'heatmap', 'trading'],
        dataSource: cacheStatus.source === 'api' ? 'CoinGecko API' : 'Static fallback',
        lastUpdated: cacheStatus.lastUpdated,
        cacheTTL: '5 minutes',
      },
    });

  } catch (error) {
    console.error('❌ Mock data API error:', error);

    // Return basic fallback data even if service fails
    const fallbackData = [
      {
        symbol: 'BTC',
        name: 'Bitcoin',
        price: 118953.53,
        change24h: 2.45,
        change7d: 8.12,
        marketCap: 2366967578877,
        volume24h: 45234567890,
        rank: 1,
        lastUpdated: Date.now(),
      },
      {
        symbol: 'ETH',
        name: 'Ethereum',
        price: 3737.52,
        change24h: 3.21,
        change7d: 12.45,
        marketCap: 451138108044,
        volume24h: 23456789012,
        rank: 2,
        lastUpdated: Date.now(),
      },
    ];

    return NextResponse.json({
      data: fallbackData,
      format,
      count: fallbackData.length,
      source: 'emergency-fallback',
      timestamp: Date.now(),
      error: 'Service temporarily unavailable, using emergency fallback data',
      cacheStatus: { hasCache: false, isExpired: true },
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'clearCache':
        mockDataService.clearCache();
        return NextResponse.json({
          success: true,
          message: 'Cache cleared successfully',
          timestamp: Date.now(),
        });

      case 'getCacheStatus':
        const status = mockDataService.getCacheStatus();
        return NextResponse.json({
          success: true,
          cacheStatus: status,
          timestamp: Date.now(),
        });

      case 'refreshData':
        // Force refresh by clearing cache and fetching new data
        mockDataService.clearCache();
        const refreshedData = await mockDataService.getAllCoinsData();
        return NextResponse.json({
          success: true,
          message: 'Data refreshed successfully',
          data: refreshedData,
          count: refreshedData.length,
          timestamp: Date.now(),
        });

      default:
        return NextResponse.json(
          { error: `Unknown action: ${action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('❌ Mock data API POST error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
