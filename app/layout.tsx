import { Gei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { ThemeProvider } from "next-themes";
import "./globals.css";
import { Toaster } from "@/components/ui/toaster";
import { Providers } from "@/components/providers";
import { GlobalTierProvider } from "@/components/GlobalTierProvider";

const defaultUrl = process.env.VERCEL_URL
  ? `https://${process.env.VERCEL_URL}`
  : "http://localhost:3000";

const title = "Crypto Talks Network, your source of all things crypto.";
const description = "All crypto, all talks, all the time.";

export const metadata = {
  metadataBase: new URL(defaultUrl),
  title,
  description,
  openGraph: {
    title,
    description,
  },
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title,
  },
  formatDetection: {
    telephone: false,
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: "cover",
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="application-name" content="Crypto Talks" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="Crypto Talks" />
        <meta name="description" content={description} />
        <meta name="format-detection" content="telephone=no" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="msapplication-config" content="/icons/browserconfig.xml" />
        <meta name="msapplication-TileColor" content="#000000" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="theme-color" content="#000000" />

        <link rel="apple-touch-icon" href="/ct_logo.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/ct_logo.png" />
        <link rel="icon" type="image/png" sizes="16x16" href="/ct_logo.png" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="mask-icon" href="/ct_logo.png" color="#000000" />
        <link rel="shortcut icon" href="/ct_logo.png" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased min-h-screen bg-gradient-to-br from-background via-background/95 to-muted/10 relative overflow-x-hidden`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <Providers>
            <GlobalTierProvider>
              <Toaster />
              {/* Children content */}
              <div className="flex flex-1 z-10 m-0">{children}</div>
            </GlobalTierProvider>
          </Providers>
        </ThemeProvider>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Cleanup existing service workers
              if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(function(registrations) {
                  for(let registration of registrations) {
                    registration.unregister();
                    console.log('Service Worker unregistered:', registration);
                  }
                });
                
                // Clear all caches
                if ('caches' in window) {
                  caches.keys().then(function(cacheNames) {
                    return Promise.all(
                      cacheNames.map(function(cacheName) {
                        console.log('Deleting cache:', cacheName);
                        return caches.delete(cacheName);
                      })
                    );
                  }).then(function() {
                    console.log('All caches cleared');
                  });
                }
              }
            `,
          }}
        />
      </body>
    </html>
  );
}
