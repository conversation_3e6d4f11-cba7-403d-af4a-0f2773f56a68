import Hero from "@/components/hero";
import <PERSON><PERSON><PERSON><PERSON> from "@/components/header-auth";
import { createClient } from "@/utils/supabase/server";
import { Tables } from "@/types";
import { redirect } from "next/navigation";
import ArticleTimeline from "@/components/Articles/ArticleTimeline";
import { Suspense } from "react";
import { ArticlesSkeleton } from "@/components/Skeletons/ArticlesSkeleton";
import { SignupOverlay } from "@/components/SignupOverlay";

// Use Supabase generated types
type Article = Tables<'articles'>;

// Function to get public articles for non-logged-in users
async function getPublicArticlesData(): Promise<{ articles: Article[] }> {
  const supabase = await createClient();

  const { data, error } = await (supabase as any)
    .from("articles")
    .select("*")
    .order("created_at", { ascending: false })
    .limit(20); // Get more articles for the timeline

  if (error) {
    console.error("Error fetching articles:", error);
    return { articles: [] };
  }

  return { articles: data || [] };
}

export default async function Page() {
  const supabase = await createClient();

  // Check if user is logged in
  let user = null;
  let authError = null;
  
  try {
    const { data, error } = await (supabase as any).auth.getUser();
    user = data?.user;
    authError = error;
    
    // Only log non-session-missing errors
    if (error && error.message !== 'Auth session missing!') {
      console.error('Unexpected auth error:', error);
    }
  } catch (error) {
    // Handle any unexpected errors
    console.debug('Auth check for guest user');
  }

  // If user is logged in, redirect to the app
  if (user && !authError) {
    redirect("/ctn");
  }

  // Get public articles for non-logged-in users
  const { articles } = await getPublicArticlesData();

  // Create a mock user profile for non-logged-in users (used by ArticleTimeline)
  const guestProfile = {
    id: 1,
    user_id: '00000000-0000-0000-0000-000000000000', // Valid UUID for guest
    username: 'Guest',
    email: '',
    created_at: '',
    updated_at: '',
    premium_tier: 'tier0' as const,
    experience_points: 0,
    experience_active: false,
    ctt_balance: 0,
    solana_wallet_address: null,
    avatar_url: null,
    banner_url: null,
    bio: null,
    points: 0,
  };

  // Transform articles to match the expected type
  const transformedArticles = articles.map(article => ({
    ...article,
    content: article.content || '',
    title: article.title || 'Untitled',
    author: article.author || 'Anonymous',
    author_id: article.author_id || 'anonymous',
    created_at: article.created_at || new Date().toISOString(),
  })) as any; // Use type assertion to bypass strict typing for guest users

  return (
    <SignupOverlay isGuest={true}>
      <div className="min-h-screen">
        {/* Header for non-logged-in users */}
        <nav className="w-full flex justify-center border-b border-b-foreground/10 h-16 sticky top-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 z-50">
          <div className="w-full max-w-5xl flex justify-between items-center p-3 px-5 text-sm">
            <HeaderAuth />
          </div>
        </nav>

        {/* Main content */}
        <div className="container mx-auto py-8">
          <Suspense fallback={<ArticlesSkeleton />}>

            <ArticleTimeline
              articles={transformedArticles}
              userProfile={guestProfile}
              errorMessage={null}
              title="Welcome to CryptoTalks - Latest Articles"
            />
          </Suspense>
        </div>

        {/* Footer */}
        <footer className="w-full flex items-center justify-center border-t mx-auto text-center text-xs gap-8 py-16">
          <p>&copy; 2025 CryptoTalks. Join the conversation.</p>
        </footer>
      </div>
    </SignupOverlay>
  );
}
