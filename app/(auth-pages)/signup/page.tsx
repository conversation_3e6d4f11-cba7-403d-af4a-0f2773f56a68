// pages/signup/page.tsx
import Signup from "@/components/Signup";
import { Message } from "@/components/form-message";

export default async function SignupPage(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;

  return (
    <div className="min-h-screen flex items-center justify-center p-4 auth-container">
      <div className="w-full max-w-md animate-scale-in">
        <Signup searchParams={searchParams} />
      </div>
    </div>
  );
}
