import { forgotPasswordAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import Link from "next/link";

export default async function ForgotPassword(props: {
  searchParams: Promise<Message>;
}) {
  const searchParams = await props.searchParams;
  return (
    <Card className="liquid-glass glass-shadow glass-shadow-hover w-full max-w-md mx-auto">
      <CardContent className="p-8">
        <form className="flex flex-col gap-6">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2">Reset Password</h1>
            <p className="text-sm text-muted-foreground">
              Remember your password?{" "}
              <Link className="text-primary font-medium hover:text-primary/80 underline transition-colors" href="/signin">
                Sign in
              </Link>
            </p>
          </div>
          
          <FormMessage message={searchParams} />
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="email" className="text-sm font-medium">Email Address</Label>
              <Input 
                name="email" 
                type="email"
                placeholder="<EMAIL>" 
                required 
                className="mt-1"
              />
            </div>
            <SubmitButton 
              formAction={forgotPasswordAction}
              className="w-full bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border-primary/30 hover:border-primary/50 text-foreground font-medium liquid-glass glass-shadow"
            >
              Send Reset Link
            </SubmitButton>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
