'use client';

import { useState, useEffect } from 'react';
import { clientAPIService } from '@/utils/client-api-service';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export default function TestAPIPage() {
  const [marketData, setMarketData] = useState<any[]>([]);
  const [prices, setPrices] = useState<Record<string, number>>({});
  const [health, setHealth] = useState<any>(null);
  const [configStatus, setConfigStatus] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testMarketData = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🧪 Testing market data API...');
      const response = await clientAPIService.getMarketData(['BTC', 'ETH', 'SOL'], 10);
      setMarketData(response.data);
      console.log('✅ Market data test successful:', response);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError(`Market data test failed: ${errorMsg}`);
      console.error('❌ Market data test failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const testPrices = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🧪 Testing prices API...');
      const response = await clientAPIService.getPrices(['BTC', 'ETH', 'SOL']);
      setPrices(response.prices);
      console.log('✅ Prices test successful:', response);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError(`Prices test failed: ${errorMsg}`);
      console.error('❌ Prices test failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const testHealth = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🧪 Testing health check...');
      const response = await clientAPIService.healthCheck();
      setHealth(response);
      console.log('✅ Health check successful:', response);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError(`Health check failed: ${errorMsg}`);
      console.error('❌ Health check failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const testConfigStatus = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🧪 Testing configuration status...');
      const response = await fetch('/api/config-status');
      if (response.ok) {
        const data = await response.json();
        setConfigStatus(data);
        console.log('✅ Configuration status loaded:', data);
      } else {
        throw new Error(`Config status failed: ${response.status}`);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setError(`Configuration status test failed: ${errorMsg}`);
      console.error('❌ Configuration status test failed:', err);
    } finally {
      setLoading(false);
    }
  };

  const clearCache = () => {
    clientAPIService.clearCache();
    console.log('🗑️ Cache cleared');
  };

  useEffect(() => {
    // Auto-run health check and config status on load
    testHealth();
    testConfigStatus();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">API Integration Test</h1>
        <p className="text-muted-foreground mt-2">
          Test the Binance-first API strategy with real data
        </p>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">❌ {error}</p>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Configuration Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🔧 API Configuration
              {configStatus && (
                <Badge variant={configStatus.summary.missingRequired === 0 ? "default" : "destructive"}>
                  {configStatus.summary.configuredServices}/{configStatus.summary.totalServices}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Check API key configuration
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testConfigStatus} disabled={loading} className="w-full mb-4">
              {loading ? "Checking..." : "Check Configuration"}
            </Button>
            {configStatus && (
              <div className="space-y-2 text-sm">
                {configStatus.services.slice(0, 3).map((service: any, index: number) => (
                  <div key={index} className="flex justify-between items-center">
                    <span className="font-medium">{service.service}</span>
                    <Badge variant={service.configured ? "default" : service.required ? "destructive" : "secondary"}>
                      {service.configured ? "✅" : service.required ? "❌" : "⚪"}
                    </Badge>
                  </div>
                ))}
                {configStatus.summary.recommendations.length > 0 && (
                  <div className="mt-2 p-2 bg-yellow-50 rounded text-xs">
                    {configStatus.summary.recommendations[0]}
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Health Check */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              🏥 Health Check
              {health && (
                <div className="flex gap-1">
                  <Badge variant={health.binance ? "default" : "destructive"}>
                    Binance: {health.binance ? "✅" : "❌"}
                  </Badge>
                  <Badge variant={health.coingecko ? "default" : "destructive"}>
                    CoinGecko: {health.coingecko ? "✅" : "❌"}
                  </Badge>
                </div>
              )}
            </CardTitle>
            <CardDescription>
              Check API connectivity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testHealth} disabled={loading} className="w-full">
              {loading ? "Testing..." : "Test Health"}
            </Button>
            {health && (
              <div className="mt-4 text-sm">
                <p>Last checked: {new Date(health.timestamp).toLocaleTimeString()}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Market Data */}
        <Card>
          <CardHeader>
            <CardTitle>📊 Market Data</CardTitle>
            <CardDescription>
              Test unified market data (Binance → CoinGecko)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testMarketData} disabled={loading} className="w-full">
              {loading ? "Testing..." : "Test Market Data"}
            </Button>
            {marketData.length > 0 && (
              <div className="mt-4 space-y-2">
                {marketData.slice(0, 3).map((coin, index) => (
                  <div key={index} className="flex justify-between text-sm">
                    <span className="font-medium">{coin.symbol}</span>
                    <span>${coin.price?.toFixed(2)}</span>
                    <span className={coin.change24h >= 0 ? "text-green-600" : "text-red-600"}>
                      {coin.change24h?.toFixed(2)}%
                    </span>
                  </div>
                ))}
                <p className="text-xs text-muted-foreground mt-2">
                  Showing {marketData.length} coins
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Prices */}
        <Card>
          <CardHeader>
            <CardTitle>💰 Simple Prices</CardTitle>
            <CardDescription>
              Test simple price fetching
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={testPrices} disabled={loading} className="w-full">
              {loading ? "Testing..." : "Test Prices"}
            </Button>
            {Object.keys(prices).length > 0 && (
              <div className="mt-4 space-y-2">
                {Object.entries(prices).map(([symbol, price]) => (
                  <div key={symbol} className="flex justify-between text-sm">
                    <span className="font-medium">{symbol}</span>
                    <span>${price.toFixed(2)}</span>
                  </div>
                ))}
                <p className="text-xs text-muted-foreground mt-2">
                  {Object.keys(prices).length} prices fetched
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Cache Management */}
      <Card>
        <CardHeader>
          <CardTitle>🗄️ Cache Management</CardTitle>
          <CardDescription>
            Manage client-side cache
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button onClick={clearCache} variant="outline">
              Clear Cache
            </Button>
            <Badge variant="secondary">
              Cache size: {clientAPIService.getCacheStats().size} items
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>📋 Test Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <p>1. <strong>Health Check</strong>: Verify API connectivity (should show Binance ✅)</p>
          <p>2. <strong>Market Data</strong>: Test comprehensive market data fetching</p>
          <p>3. <strong>Prices</strong>: Test simple price fetching for portfolio use</p>
          <p>4. Check browser console for detailed logs</p>
          <p>5. <strong>Expected behavior</strong>: Binance should be primary source (free, fast)</p>
        </CardContent>
      </Card>

      {/* Console Logs */}
      <Card>
        <CardHeader>
          <CardTitle>🔍 Debug Info</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Open browser console (F12) to see detailed API logs and data sources.
            Look for logs like "✅ Got market data from [binance]" to verify the Binance-first strategy is working.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
