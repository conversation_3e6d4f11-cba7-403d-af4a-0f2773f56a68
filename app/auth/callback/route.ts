import { NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function GET(request: Request) {
  const { searchParams, origin } = new URL(request.url)
  const code = searchParams.get('code')
  // if "next" is in param, use it as the redirect URL
  const next = searchParams.get('next') ?? '/ctn'

  if (code) {
    const supabase = await createClient()

    // Exchange code for session - the SSR client handles cookies automatically
    const { error } = await supabase.auth.exchangeCodeForSession(code)

    if (!error) {
      // Redirect to the next URL or default to /ctn
      return NextResponse.redirect(`${origin}${next}`)
    } else {
      // Handle the error if something went wrong
      console.error('Error during code exchange:', error)
      return NextResponse.redirect(`${origin}/auth/auth-code-error`)
    }
  }

  // Return the user to an error page with instructions
  return NextResponse.redirect(`${origin}/auth/auth-code-error`)
}
