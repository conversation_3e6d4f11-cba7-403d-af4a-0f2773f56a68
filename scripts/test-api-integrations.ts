#!/usr/bin/env tsx
// Test script for API integrations - Server-side testing with Binance-first approach!
import { coinGeckoService, cryptoNewsService } from '../utils/api-services';
import { binanceService } from '../utils/binance-service';
import { fetchMarketData, fetchTVLData } from '../utils/onchain-data';
import { API_CONFIG } from '../utils/api-config';
import { logConfigurationStatus } from '../utils/api-config-checker';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message: string, color: string = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message: string) {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message: string) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Test Binance API (Primary)
async function testBinanceAPI() {
  log('\n' + colors.bold + '🚀 Testing Binance API (Primary - FREE!)' + colors.reset);

  try {
    // Test ping
    logInfo('Testing Binance ping...');
    const pingResult = await binanceService.ping();
    if (pingResult) {
      logSuccess('Binance API is reachable');
    } else {
      logError('Binance ping failed');
    }

    // Test 24hr ticker
    logInfo('Testing Binance 24hr ticker...');
    const tickers = await binanceService.get24hrTicker(['BTC', 'ETH', 'SOL']);
    if (tickers && tickers.length > 0) {
      logSuccess(`Retrieved 24hr data for ${tickers.length} symbols`);
      tickers.forEach(ticker => {
        const symbol = ticker.symbol.replace('USDT', '');
        log(`  ${symbol}: $${parseFloat(ticker.lastPrice).toFixed(2)} (${parseFloat(ticker.priceChangePercent).toFixed(2)}%)`, colors.blue);
      });
    } else {
      logError('Failed to retrieve Binance 24hr ticker');
    }

    // Test simple prices
    logInfo('Testing Binance prices...');
    const prices = await binanceService.getPrices(['BTC', 'ETH', 'SOL']);
    if (prices && prices.length > 0) {
      logSuccess(`Retrieved prices for ${prices.length} symbols`);
      prices.forEach(price => {
        const symbol = price.symbol.replace('USDT', '');
        log(`  ${symbol}: $${parseFloat(price.price).toFixed(2)}`, colors.blue);
      });
    } else {
      logError('Failed to retrieve Binance prices');
    }

    // Test supported symbols
    const supportedSymbols = binanceService.getSupportedSymbols();
    logSuccess(`Binance supports ${supportedSymbols.length} trading pairs`);

  } catch (error) {
    logError(`Binance API test failed: ${error}`);
  }
}

// Test API Endpoints (Client-side testing)
async function testAPIEndpoints() {
  log('\n' + colors.bold + '🌐 Testing Next.js API Endpoints' + colors.reset);

  const baseUrl = 'http://localhost:3000'; // Adjust if needed

  try {
    // Test market data endpoint
    logInfo('Testing /api/market-data endpoint...');
    const marketResponse = await fetch(`${baseUrl}/api/market-data?symbols=BTC,ETH,SOL&limit=5`);
    if (marketResponse.ok) {
      const marketData = await marketResponse.json();
      logSuccess(`Market data API working: ${marketData.count} coins from [${marketData.sources?.join(', ') || marketData.source}]`);
      if (marketData.data && marketData.data.length > 0) {
        marketData.data.slice(0, 2).forEach((coin: any) => {
          log(`  ${coin.symbol}: $${coin.price} (${coin.change24h?.toFixed(2)}%)`, colors.blue);
        });
      }
    } else {
      logError(`Market data API failed: ${marketResponse.status}`);
    }

    // Test prices endpoint
    logInfo('Testing /api/prices endpoint...');
    const pricesResponse = await fetch(`${baseUrl}/api/prices?symbols=BTC,ETH,SOL`);
    if (pricesResponse.ok) {
      const pricesData = await pricesResponse.json();
      logSuccess(`Prices API working: ${pricesData.count} prices from [${pricesData.sources?.join(', ') || pricesData.source}]`);
      if (pricesData.prices) {
        Object.entries(pricesData.prices).slice(0, 3).forEach(([symbol, price]: [string, any]) => {
          log(`  ${symbol}: $${price}`, colors.blue);
        });
      }
    } else {
      logError(`Prices API failed: ${pricesResponse.status}`);
    }

    // Test health check
    logInfo('Testing API health check...');
    const healthResponse = await fetch(`${baseUrl}/api/market-data`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'health' }),
    });
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      logSuccess('Health check API working');
      log(`  Binance: ${healthData.binance ? '✅' : '❌'}`, colors.blue);
      log(`  CoinGecko: ${healthData.coingecko ? '✅' : '❌'}`, colors.blue);
    } else {
      logError(`Health check API failed: ${healthResponse.status}`);
    }

  } catch (error) {
    logWarning('API endpoint tests require the Next.js server to be running');
    logInfo('Run "npm run dev" to start the server and test endpoints');
    logInfo('Or visit http://localhost:3000/test-api for interactive testing');
  }
}

// Test CoinGecko API (Secondary)
async function testCoinGeckoAPI() {
  log('\n' + colors.bold + '🔍 Testing CoinGecko API (Secondary/Fallback)' + colors.reset);
  
  try {
    // Test ping
    logInfo('Testing CoinGecko ping...');
    const pingResult = await coinGeckoService.ping();
    if (pingResult) {
      logSuccess('CoinGecko API is reachable');
    } else {
      logWarning('CoinGecko ping failed, but API might still work');
    }

    // Test simple prices
    logInfo('Testing simple prices...');
    const prices = await coinGeckoService.getSimplePrices(['bitcoin', 'ethereum', 'solana']);
    if (prices && Object.keys(prices).length > 0) {
      logSuccess(`Retrieved prices for ${Object.keys(prices).length} coins`);
      Object.entries(prices).forEach(([coin, data]) => {
        log(`  ${coin}: $${data.usd}`, colors.blue);
      });
    } else {
      logError('Failed to retrieve simple prices');
    }

    // Test coins markets
    logInfo('Testing coins markets...');
    const markets = await coinGeckoService.getCoinsMarkets('usd', 'market_cap_desc', 10);
    if (markets && markets.length > 0) {
      logSuccess(`Retrieved market data for ${markets.length} coins`);
      markets.slice(0, 3).forEach(coin => {
        log(`  ${coin.symbol.toUpperCase()}: $${coin.current_price} (${coin.price_change_percentage_24h?.toFixed(2)}%)`, colors.blue);
      });
    } else {
      logError('Failed to retrieve coins markets');
    }

    // Test trending
    logInfo('Testing trending coins...');
    const trending = await coinGeckoService.getTrending();
    if (trending && trending.coins && trending.coins.length > 0) {
      logSuccess(`Retrieved ${trending.coins.length} trending coins`);
      trending.coins.slice(0, 3).forEach(coin => {
        log(`  ${coin.name} (${coin.symbol})`, colors.blue);
      });
    } else {
      logWarning('No trending coins data available');
    }

  } catch (error) {
    logError(`CoinGecko API test failed: ${error}`);
  }
}

// Test CryptoNews API
async function testCryptoNewsAPI() {
  log('\n' + colors.bold + '📰 Testing CryptoNews API' + colors.reset);
  
  try {
    logInfo('Testing crypto news...');
    const news = await cryptoNewsService.getNews(['BTC', 'ETH'], 5);
    if (news && news.data && news.data.length > 0) {
      logSuccess(`Retrieved ${news.data.length} news articles`);
      news.data.slice(0, 2).forEach(article => {
        log(`  ${article.title}`, colors.blue);
      });
    } else {
      logWarning('No news articles retrieved (might be using mock data)');
    }
  } catch (error) {
    logError(`CryptoNews API test failed: ${error}`);
  }
}

// Test On-chain Data APIs
async function testOnChainDataAPIs() {
  log('\n' + colors.bold + '⛓️  Testing On-chain Data APIs' + colors.reset);
  
  try {
    // Test market data fetch
    logInfo('Testing market data fetch...');
    const marketData = await fetchMarketData('bitcoin');
    if (marketData && Object.keys(marketData).length > 0) {
      logSuccess('Retrieved market data for Bitcoin');
      log(`  Price: $${marketData.price}`, colors.blue);
      log(`  Market Cap: $${marketData.marketCap?.toLocaleString()}`, colors.blue);
    } else {
      logWarning('Market data fetch returned empty result');
    }

    // Test TVL data fetch
    logInfo('Testing TVL data fetch...');
    const tvlData = await fetchTVLData('ethereum');
    if (tvlData > 0) {
      logSuccess(`Retrieved TVL data: $${tvlData.toLocaleString()}`);
    } else {
      logWarning('TVL data fetch returned zero or failed');
    }

  } catch (error) {
    logError(`On-chain data API test failed: ${error}`);
  }
}

// Note: WebSocket testing removed from server-side script
// WebSocket connections should be tested in the browser at /test-api

// Test API endpoints
async function testServerAPIEndpoints() {
  log('\n' + colors.bold + '🌐 Testing Server API Endpoints' + colors.reset);
  
  const baseUrl = 'http://localhost:3000'; // Adjust if needed
  
  try {
    // Test crypto news endpoint
    logInfo('Testing /api/crypto-news endpoint...');
    const newsResponse = await fetch(`${baseUrl}/api/crypto-news?query=bitcoin`);
    if (newsResponse.ok) {
      const newsData = await newsResponse.json();
      logSuccess(`News API endpoint working: ${newsData.totalResults} articles`);
    } else {
      logError(`News API endpoint failed: ${newsResponse.status}`);
    }

    // Test onchain data endpoint
    logInfo('Testing /api/onchain-data endpoint...');
    const onchainResponse = await fetch(`${baseUrl}/api/onchain-data?action=metrics&chain=bitcoin`);
    if (onchainResponse.ok) {
      const onchainData = await onchainResponse.json();
      logSuccess('OnChain API endpoint working');
      if (onchainData.dataSource === 'enhanced') {
        logSuccess('OnChain data is enhanced with real API data');
      }
    } else {
      logError(`OnChain API endpoint failed: ${onchainResponse.status}`);
    }

  } catch (error) {
    logWarning('API endpoint tests require the Next.js server to be running');
    logInfo('Run "npm run dev" to start the server and test endpoints');
  }
}

// Check environment configuration using the new configuration checker
function checkEnvironmentConfig() {
  logConfigurationStatus();
}

// Main test function
async function runTests() {
  log(colors.bold + '🚀 Starting Server-Side API Integration Tests (Binance-First Strategy!)' + colors.reset);

  checkEnvironmentConfig();

  await testBinanceAPI();
  await testCoinGeckoAPI();
  await testCryptoNewsAPI();
  await testOnChainDataAPIs();
  await testServerAPIEndpoints();
  
  log('\n' + colors.bold + '✨ Server-Side API Integration Tests Complete' + colors.reset);
  log('\nNext steps:');
  log('1. Configure missing API keys in your .env file');
  log('2. Start the Next.js development server: npm run dev');
  log('3. Visit http://localhost:3000/test-api for interactive testing');
  log('4. Test real-time features in the main application');
  log('5. Monitor browser console for client-side API logs');
  
  process.exit(0);
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(error => {
    logError(`Test execution failed: ${error}`);
    process.exit(1);
  });
}

export { runTests };
