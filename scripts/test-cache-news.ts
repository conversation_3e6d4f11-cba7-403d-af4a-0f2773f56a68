#!/usr/bin/env bun

// Test crypto news caching system
import { cacheService, createCacheKey, logCachePerformance } from '../utils/cache-service';
import { newsScheduler } from '../utils/news-scheduler';

console.log('📰 Testing Crypto News Caching System\n');

async function testNewsCaching() {
  console.log('🧪 Testing News Cache Functionality...');
  
  // Test cache key creation
  const cacheKey = createCacheKey('crypto-news', 'BTC-ETH', 'latest', '20');
  console.log(`  ✅ Cache key: ${cacheKey}`);

  // Test cache storage (simulating API response)
  const mockNewsData = {
    status: 'ok',
    totalResults: 5,
    articles: [
      {
        title: "Bitcoin Breaks $50,000",
        description: "Bitcoin reaches new milestone amid institutional adoption.",
        url: "https://example.com/bitcoin-50k",
        publishedAt: new Date().toISOString(),
        source: { name: "CryptoDaily" },
      },
      {
        title: "Ethereum Layer 2 Scaling Solutions",
        description: "Layer 2 solutions show promising growth in transaction volume.",
        url: "https://example.com/eth-l2",
        publishedAt: new Date(Date.now() - 1800000).toISOString(),
        source: { name: "EthNews" },
      },
    ],
    timestamp: Date.now(),
    source: 'test-data',
  };

  // Cache for 12 hours (43200 seconds)
  const TWELVE_HOURS = 12 * 60 * 60;
  cacheService.set(cacheKey, mockNewsData, TWELVE_HOURS, 'crypto-news');
  console.log('  ✅ Cached mock news data for 12 hours');

  // Test cache retrieval
  const cachedData = cacheService.get(cacheKey);
  if (cachedData) {
    console.log(`  ✅ Cache hit: Retrieved ${cachedData.totalResults} articles`);
    console.log(`  📅 Cache age: ${Math.floor((Date.now() - cachedData.timestamp) / 1000)}s`);
  } else {
    console.log('  ❌ Cache miss');
  }

  return true;
}

async function testNewsScheduler() {
  console.log('\n📅 Testing News Scheduler...');
  
  // Get scheduler status
  const status = newsScheduler.getStatus();
  console.log(`  📊 Scheduler running: ${status.isRunning}`);

  // Get cache stats
  const cacheStats = newsScheduler.getNewsCacheStats();
  console.log(`  📦 Cache entries: ${cacheStats.entries}`);
  console.log(`  📋 Cache sources: [${cacheStats.sources.join(', ')}]`);

  // Test manual refresh trigger (without actual API calls)
  console.log('  🔄 Testing manual cache operations...');
  
  // Add some test entries to demonstrate source-based operations
  const testKeys = [
    'crypto-news:BTC:latest:10',
    'crypto-news:ETH:latest:15',
    'crypto-news:general:latest:20'
  ];

  testKeys.forEach((key, index) => {
    cacheService.set(key, {
      articles: [`Test article ${index + 1}`],
      timestamp: Date.now(),
      source: 'test'
    }, 43200, 'crypto-news');
  });

  console.log(`  ✅ Added ${testKeys.length} test cache entries`);

  // Test cache clearing
  const deletedCount = newsScheduler.clearNewsCache();
  console.log(`  🗑️ Cleared ${deletedCount} news cache entries`);

  return true;
}

async function testCachePerformance() {
  console.log('\n⚡ Testing Cache Performance...');
  
  // Simulate realistic news cache usage
  const scenarios = [
    { tickers: ['BTC'], items: 20, category: 'bitcoin' },
    { tickers: ['ETH'], items: 15, category: 'ethereum' },
    { tickers: ['BTC', 'ETH'], items: 25, category: 'major' },
    { tickers: null, items: 30, category: 'general' }
  ];

  // Fill cache with different scenarios
  scenarios.forEach((scenario, index) => {
    const key = createCacheKey(
      'crypto-news',
      scenario.tickers?.join('-') || 'general',
      'latest',
      scenario.items.toString()
    );

    const mockData = {
      status: 'ok',
      totalResults: scenario.items,
      articles: Array(scenario.items).fill(null).map((_, i) => ({
        title: `${scenario.category} News Article ${i + 1}`,
        description: `Latest news about ${scenario.category}`,
        publishedAt: new Date(Date.now() - (i * 1800000)).toISOString(),
        source: { name: 'TestNews' }
      })),
      timestamp: Date.now(),
      source: 'test-cache'
    };

    cacheService.set(key, mockData, 43200, 'crypto-news');
  });

  console.log(`  ✅ Populated cache with ${scenarios.length} news scenarios`);

  // Test cache hits
  let hits = 0;
  let misses = 0;

  scenarios.forEach(scenario => {
    const key = createCacheKey(
      'crypto-news',
      scenario.tickers?.join('-') || 'general',
      'latest',
      scenario.items.toString()
    );

    const cached = cacheService.get(key);
    if (cached) {
      hits++;
      console.log(`  🎯 Cache hit for ${scenario.category}: ${cached.totalResults} articles`);
    } else {
      misses++;
      console.log(`  ❌ Cache miss for ${scenario.category}`);
    }
  });

  const hitRate = hits / (hits + misses) * 100;
  console.log(`  📊 Cache hit rate: ${hitRate.toFixed(1)}% (${hits}/${hits + misses})`);

  // Log overall cache performance
  logCachePerformance();

  return true;
}

async function testApiResponseFormat() {
  console.log('\n🔧 Testing API Response Format...');
  
  // Test the expected format for cached responses
  const expectedFields = ['status', 'totalResults', 'articles', 'timestamp', 'source'];
  
  const testResponse = {
    status: 'ok',
    totalResults: 3,
    articles: [
      {
        title: "Test Article",
        description: "Test description",
        url: "https://example.com/test",
        publishedAt: new Date().toISOString(),
        source: { name: "TestSource" }
      }
    ],
    timestamp: Date.now(),
    source: 'format-test'
  };

  // Validate response format
  const hasAllFields = expectedFields.every(field => field in testResponse);
  console.log(`  ✅ Response format valid: ${hasAllFields}`);

  expectedFields.forEach(field => {
    const hasField = field in testResponse;
    console.log(`    ${hasField ? '✅' : '❌'} ${field}: ${hasField ? 'present' : 'missing'}`);
  });

  // Test cache age calculation
  const cacheKey = 'test-format-key';
  cacheService.set(cacheKey, testResponse, 3600, 'test');
  
  // Wait a moment and check age
  setTimeout(() => {
    const cached = cacheService.get(cacheKey);
    if (cached) {
      const age = Date.now() - cached.timestamp;
      console.log(`  ⏱️ Cache age calculation: ${Math.floor(age / 1000)}s`);
    }
  }, 100);

  return true;
}

async function main() {
  console.log('🚀 Starting comprehensive news caching tests...\n');
  
  const tests = [
    { name: 'News Caching', fn: testNewsCaching },
    { name: 'News Scheduler', fn: testNewsScheduler },
    { name: 'Cache Performance', fn: testCachePerformance },
    { name: 'API Response Format', fn: testApiResponseFormat },
  ];

  let passed = 0;
  const startTime = Date.now();

  for (const test of tests) {
    try {
      await test.fn();
      passed++;
      console.log(`✅ ${test.name} test passed\n`);
    } catch (error) {
      console.log(`❌ ${test.name} test failed: ${error}\n`);
    }
  }

  const endTime = Date.now();
  const duration = endTime - startTime;

  console.log('📊 Test Summary:');
  console.log(`  Tests passed: ${passed}/${tests.length}`);
  console.log(`  Duration: ${duration}ms`);
  
  console.log('\n🎯 News Caching Features:');
  console.log('  ✅ 12-hour cache duration (twice daily refresh)');
  console.log('  ✅ Intelligent cache key generation');
  console.log('  ✅ Source-based cache management');
  console.log('  ✅ Automatic cache age calculation');
  console.log('  ✅ Fallback system with mock data');
  console.log('  ✅ Performance monitoring and analytics');
  
  console.log('\n🔧 For Production:');
  console.log('  • Add NEWSDATA_API_KEY to .env.local');
  console.log('  • Set ENABLE_NEWS_SCHEDULER=true');
  console.log('  • Monitor cache hit rates in logs');
  console.log('  • Use /api/admin/news-refresh for manual control');
  
  console.log('\n📱 User Benefits:');
  console.log('  • News loads instantly from cache');
  console.log('  • Minimal API usage (updates twice daily)');
  console.log('  • Always available with fallback data');
  console.log('  • Fresh content every 12 hours');

  if (passed === tests.length) {
    console.log('\n🎉 All tests passed! News caching system is ready.');
  }
}

main().catch(console.error);