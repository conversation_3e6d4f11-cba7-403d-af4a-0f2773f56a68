#!/usr/bin/env bun

// Diagnostic script to test each API individually
console.log('🔍 Diagnosing API Issues\n');

async function testCoinGecko() {
  console.log('🔄 Testing CoinGecko API...');
  const apiKey = process.env.COINGECKO_API_KEY;
  console.log(`  API Key: ${apiKey ? `${apiKey.substring(0, 8)}...` : 'Not set'}`);

  try {
    // Test ping first
    const pingUrl = 'https://api.coingecko.com/api/v3/ping';
    console.log(`  🔗 Testing: ${pingUrl}`);
    
    const pingResponse = await fetch(pingUrl);
    console.log(`    Status: ${pingResponse.status} ${pingResponse.statusText}`);
    
    if (pingResponse.ok) {
      const pingData = await pingResponse.json();
      console.log(`    Response: ${JSON.stringify(pingData)}`);
    } else {
      const errorText = await pingResponse.text();
      console.log(`    Error: ${errorText.substring(0, 200)}...`);
    }

    // Test simple price with key
    if (apiKey && apiKey !== 'your_coingecko_api_key_here') {
      const priceUrl = `https://api.coingecko.com/api/v3/simple/price?ids=bitcoin,ethereum&vs_currencies=usd`;
      console.log(`  🔗 Testing with API key: ${priceUrl}`);
      
      const headers: HeadersInit = {
        'accept': 'application/json',
      };
      
      // CoinGecko uses x-cg-demo-api-key for demo keys or x-cg-pro-api-key for pro
      if (apiKey.startsWith('CG-')) {
        headers['x-cg-demo-api-key'] = apiKey;
      }
      
      const priceResponse = await fetch(priceUrl, { headers });
      console.log(`    Status: ${priceResponse.status} ${priceResponse.statusText}`);
      
      if (priceResponse.ok) {
        const priceData = await priceResponse.json();
        console.log(`    ✅ Success: ${JSON.stringify(priceData)}`);
      } else {
        const errorText = await priceResponse.text();
        console.log(`    ❌ Error: ${errorText.substring(0, 200)}...`);
      }
    }
  } catch (error) {
    console.log(`  ❌ Network error: ${error}`);
  }
}

async function testBinance() {
  console.log('\n🚀 Testing Binance API...');
  
  try {
    // Test ping first
    const pingUrl = 'https://api.binance.com/api/v3/ping';
    console.log(`  🔗 Testing: ${pingUrl}`);
    
    const pingResponse = await fetch(pingUrl);
    console.log(`    Status: ${pingResponse.status} ${pingResponse.statusText}`);
    
    if (pingResponse.ok) {
      const pingData = await pingResponse.json();
      console.log(`    ✅ Success: ${JSON.stringify(pingData)}`);
    } else {
      const errorText = await pingResponse.text();
      console.log(`    ❌ Error: ${errorText.substring(0, 200)}...`);
    }

    // Test ticker price
    const tickerUrl = 'https://api.binance.com/api/v3/ticker/price?symbol=BTCUSDT';
    console.log(`  🔗 Testing: ${tickerUrl}`);
    
    const tickerResponse = await fetch(tickerUrl);
    console.log(`    Status: ${tickerResponse.status} ${tickerResponse.statusText}`);
    
    if (tickerResponse.ok) {
      const tickerData = await tickerResponse.json();
      console.log(`    ✅ Success: ${JSON.stringify(tickerData)}`);
    } else {
      const errorText = await tickerResponse.text();
      console.log(`    ❌ Error: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`  ❌ Network error: ${error}`);
  }
}

async function testDexScreener() {
  console.log('\n📊 Testing DexScreener API...');
  
  try {
    // Test with a simple search
    const searchUrl = 'https://api.dexscreener.com/latest/dex/search?q=WETH';
    console.log(`  🔗 Testing: ${searchUrl}`);
    
    const searchResponse = await fetch(searchUrl);
    console.log(`    Status: ${searchResponse.status} ${searchResponse.statusText}`);
    
    if (searchResponse.ok) {
      const searchData = await searchResponse.json();
      console.log(`    ✅ Success: Found ${searchData.pairs?.length || 0} pairs`);
    } else {
      const errorText = await searchResponse.text();
      console.log(`    ❌ Error: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`  ❌ Network error: ${error}`);
  }
}

async function testCryptoNews() {
  console.log('\n📰 Testing NewsData.io API...');
  const apiKey = process.env.NEWSDATA_API_KEY;
  console.log(`  API Key: ${apiKey ? `${apiKey.substring(0, 8)}...` : 'Not set'}`);
  
  if (!apiKey || apiKey === 'your_NEWSDATA_API_key_here') {
    console.log('  ⚠️ No valid API key configured');
    return;
  }
  
  try {
    const newsUrl = `https://newsdata.io/api/1/news?apikey=${apiKey}&language=en&category=business&q=bitcoin&size=3`;
    console.log(`  🔗 Testing: ${newsUrl.replace(apiKey, 'API_KEY')}`);
    
    const newsResponse = await fetch(newsUrl);
    console.log(`    Status: ${newsResponse.status} ${newsResponse.statusText}`);
    
    if (newsResponse.ok) {
      const newsData = await newsResponse.json();
      console.log(`    ✅ Success: Found ${newsData.results?.length || 0} articles`);
      console.log(`    📊 Status: ${newsData.status}, Next page: ${newsData.nextPage || 'none'}`);
    } else {
      const errorText = await newsResponse.text();
      console.log(`    ❌ Error: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`  ❌ Network error: ${error}`);
  }
}

async function testMessari() {
  console.log('\n📈 Testing Messari API...');
  const apiKey = process.env.MESSARI_API_KEY;
  console.log(`  API Key: ${apiKey ? `${apiKey.substring(0, 8)}...` : 'Not set'}`);
  
  try {
    // Test without API key first (should work for basic endpoints)
    const assetsUrl = 'https://data.messari.io/api/v1/assets?limit=3';
    console.log(`  🔗 Testing: ${assetsUrl}`);
    
    const headers: HeadersInit = {
      'accept': 'application/json',
    };
    
    if (apiKey && apiKey !== 'your_messari_api_key_here') {
      headers['x-messari-api-key'] = apiKey;
    }
    
    const assetsResponse = await fetch(assetsUrl, { headers });
    console.log(`    Status: ${assetsResponse.status} ${assetsResponse.statusText}`);
    
    if (assetsResponse.ok) {
      const assetsData = await assetsResponse.json();
      console.log(`    ✅ Success: Found ${assetsData.data?.length || 0} assets`);
    } else {
      const errorText = await assetsResponse.text();
      console.log(`    ❌ Error: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`  ❌ Network error: ${error}`);
  }
}

async function testNetworkConnectivity() {
  console.log('\n🌐 Testing Network Connectivity...');
  
  const testUrls = [
    'https://httpbin.org/get',
    'https://api.github.com',
    'https://www.google.com'
  ];
  
  for (const url of testUrls) {
    try {
      console.log(`  🔗 Testing: ${url}`);
      const response = await fetch(url, { 
        method: 'HEAD',
        signal: AbortSignal.timeout(5000)
      });
      console.log(`    Status: ${response.status} ${response.statusText}`);
    } catch (error) {
      console.log(`    ❌ Failed: ${error}`);
    }
  }
}

async function main() {
  console.log('🔍 Starting API Diagnostics...\n');
  
  // Test network connectivity first
  await testNetworkConnectivity();
  
  // Test each API
  await testBinance();
  await testCoinGecko();
  await testDexScreener();
  await testCryptoNews();
  await testMessari();
  
  console.log('\n📋 Diagnostic Summary:');
  console.log('  🔍 Check the status codes and error messages above');
  console.log('  ✅ 200 = Working correctly');
  console.log('  ❌ 401/403 = API key issues');
  console.log('  ❌ 404 = Endpoint not found (possibly changed)');
  console.log('  ❌ 429 = Rate limited');
  console.log('  ❌ Network error = Connectivity issues');
  
  console.log('\n🔧 Next Steps:');
  console.log('  1. Check if any APIs returned 200 status');
  console.log('  2. For 401/403 errors, verify API key format');
  console.log('  3. For 404 errors, API endpoints may have changed');
  console.log('  4. For network errors, check internet connection');
  console.log('  5. Remember: System works with fallbacks even if APIs fail');
}

main().catch(console.error);