#!/usr/bin/env bun

// <PERSON>ript to test all live data integrations
import { binanceService } from '../utils/binance-service';
import { coinGeckoService } from '../utils/api-services';
import { dexScreenerService } from '../utils/dexscreener-service';
import { messariService } from '../utils/messari-service';
import { cacheService, logCachePerformance } from '../utils/cache-service';

console.log('🧪 Testing Live Data Integrations\n');

async function testBinanceAPI() {
  console.log('🚀 Testing Binance API...');
  try {
    // Quick ping test only - no retries
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3s timeout
    
    const response = await fetch('https://api.binance.com/api/v3/ping', {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (response.ok) {
      console.log(`  ✅ Ping: SUCCESS`);
      return true;
    } else {
      console.log(`  ❌ Ping: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    console.log(`  ❌ Binance API failed: ${error instanceof Error ? error.message : 'Network error'}`);
    return false;
  }
}

async function testCoinGeckoAPI() {
  console.log('\n🔄 Testing CoinGecko API...');
  try {
    // Quick ping test only - no retries
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3s timeout
    
    const response = await fetch('https://api.coingecko.com/api/v3/ping', {
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (response.ok) {
      console.log(`  ✅ Ping: SUCCESS`);
      return true;
    } else {
      console.log(`  ❌ Ping: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    console.log(`  ❌ CoinGecko API failed: ${error instanceof Error ? error.message : 'Network error'}`);
    return false;
  }
}

async function testDexScreenerAPI() {
  console.log('\n📊 Testing DexScreener API...');
  try {
    // Test ping
    const pingResult = await dexScreenerService.ping();
    console.log(`  ✅ Ping: ${pingResult ? 'SUCCESS' : 'FAILED'}`);

    // Test search
    const search = await dexScreenerService.searchPairs('ETH');
    console.log(`  ✅ Search: Got ${search.pairs.length} pairs for ETH`);

    // Test trending
    const trending = await dexScreenerService.getTrendingPairs();
    console.log(`  ✅ Trending: Got ${trending.pairs.length} trending pairs`);

    return true;
  } catch (error) {
    console.log(`  ❌ DexScreener API failed: ${error}`);
    return false;
  }
}

async function testMessariAPI() {
  console.log('\n📈 Testing Messari API...');
  try {
    // Test ping
    const pingResult = await messariService.ping();
    console.log(`  ✅ Ping: ${pingResult ? 'SUCCESS' : 'FAILED'}`);

    // Test assets
    const assets = await messariService.getAllAssets(5);
    console.log(`  ✅ Assets: Got ${assets.data.length} assets`);

    // Test market data
    const marketData = await messariService.getMarketData(['bitcoin', 'ethereum']);
    console.log(`  ✅ Market Data: Got ${marketData.data.length} coins`);

    return true;
  } catch (error) {
    console.log(`  ❌ Messari API failed: ${error}`);
    return false;
  }
}

async function testAPIEndpoints() {
  console.log('\n🌐 Testing API Endpoints...');
  try {
    const baseUrl = 'http://localhost:3000/api';

    // Test market data endpoint
    const marketResponse = await fetch(`${baseUrl}/market-data?limit=10`);
    const marketData = await marketResponse.json();
    console.log(`  ✅ Market Data API: ${marketData.count} coins from [${marketData.sources?.join(', ') || marketData.source}]`);

    // Test prices endpoint
    const pricesResponse = await fetch(`${baseUrl}/prices?symbols=BTC,ETH,SOL`);
    const pricesData = await pricesResponse.json();
    console.log(`  ✅ Prices API: ${pricesData.count} prices from [${pricesData.sources?.join(', ') || pricesData.source}]`);

    // Test trending endpoint
    const trendingResponse = await fetch(`${baseUrl}/trending?limit=5`);
    const trendingData = await trendingResponse.json();
    console.log(`  ✅ Trending API: ${trendingData.count} items from [${trendingData.sources?.join(', ')}]`);

    // Test onchain data endpoint
    const onchainResponse = await fetch(`${baseUrl}/onchain-data?action=metrics&chain=bitcoin`);
    const onchainData = await onchainResponse.json();
    console.log(`  ✅ OnChain API: ${onchainData.dataSource} data for ${onchainData.chain.name}`);

    // Test crypto news endpoint with caching
    const newsResponse = await fetch(`${baseUrl}/crypto-news?items=5`);
    const newsData = await newsResponse.json();
    console.log(`  ✅ Crypto News API: ${newsData.totalResults} articles from ${newsData.source}`);
    
    // Test news cache hit
    const newsResponse2 = await fetch(`${baseUrl}/crypto-news?items=5`);
    const newsData2 = await newsResponse2.json();
    const cacheStatus = newsData2.cached ? 'CACHE HIT' : 'CACHE MISS';
    console.log(`  ✅ News Cache Test: ${cacheStatus} (age: ${newsData2.cacheAge ? Math.floor(newsData2.cacheAge/1000) + 's' : 'N/A'})`);

    // Test news admin endpoint
    const adminResponse = await fetch(`${baseUrl}/admin/news-refresh`, { method: 'GET' });
    const adminData = await adminResponse.json();
    console.log(`  ✅ News Admin API: ${adminData.cache.entries} cached entries, scheduler ${adminData.scheduler.isRunning ? 'running' : 'stopped'}`);

    return true;
  } catch (error) {
    console.log(`  ❌ API Endpoints failed: ${error}`);
    return false;
  }
}

async function testCachePerformance() {
  console.log('\n💾 Testing Cache Performance...');
  
  // Warm up cache with some test data
  cacheService.set('test:btc:price', 45000, 60, 'test');
  cacheService.set('test:eth:price', 2800, 60, 'test');
  cacheService.set('test:sol:price', 110, 60, 'test');

  // Test cache hits
  const btcPrice = cacheService.get('test:btc:price');
  const ethPrice = cacheService.get('test:eth:price');
  const solPrice = cacheService.get('test:sol:price');

  console.log(`  ✅ Cache Hits: BTC=$${btcPrice}, ETH=$${ethPrice}, SOL=$${solPrice}`);

  // Log performance
  logCachePerformance();

  return true;
}

async function main() {
  const startTime = Date.now();
  
  const results = await Promise.allSettled([
    testBinanceAPI(),
    testCoinGeckoAPI(),
    testDexScreenerAPI(),
    testMessariAPI(),
  ]);

  const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
  const total = results.length;

  console.log(`\n📊 API Services Summary: ${successful}/${total} working`);

  // Test cache
  await testCachePerformance();

  // Test endpoints (only if we have a local server running)
  if (process.env.NODE_ENV !== 'production') {
    try {
      await testAPIEndpoints();
    } catch (error) {
      console.log('\n⚠️  Local server not running - skipping endpoint tests');
      console.log('   Start with: bun run dev');
    }
  }

  const endTime = Date.now();
  console.log(`\n⏱️  Tests completed in ${endTime - startTime}ms`);

  // Summary
  console.log('\n🎯 Summary:');
  console.log('   • Your live data integration is ready!');
  console.log('   • Multiple fallback sources ensure reliability');
  console.log('   • Intelligent caching maximizes free tier usage');
  console.log('   • All tools can now access real-time cryptocurrency data');
  
  if (successful < total) {
    console.log('\n⚠️  Some APIs may need configuration:');
    console.log('   • Check .env.example for required API keys');
    console.log('   • Free tiers are sufficient for development');
    console.log('   • System gracefully falls back to mock data when needed');
  }
}

// Run the tests
main().catch(console.error);