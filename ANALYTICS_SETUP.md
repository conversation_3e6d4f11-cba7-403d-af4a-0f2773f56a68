# Analytics Setup Instructions

The AnalyticsDashboard component is currently using mock data because the required Supabase functions are not yet deployed.

## To Set Up Real Analytics:

### 1. Install Supabase CLI (if not already installed)
```bash
npm install -g supabase
```

### 2. Initialize Supabase in your project (if not already done)
```bash
supabase init
```

### 3. Link to your Supabase project
```bash
supabase link --project-ref YOUR_PROJECT_REF
```

### 4. Apply the analytics functions migration
```bash
supabase db push
```

This will apply the migration file: `supabase/migrations/20250711000000_analytics_functions.sql`

### 5. Update the AnalyticsDashboard component
In `components/AnalyticsDashboard.tsx`, change this line:
```typescript
const useMockData = true; // Set to false once database functions are deployed
```

To:
```typescript
const useMockData = false; // Set to false once database functions are deployed
```

## What the Analytics Functions Do:

- **get_user_analytics_overview**: Returns overall statistics for a user
- **get_article_analytics**: Returns performance data for articles
- **get_engagement_trends**: Returns engagement trends over time

## Note:
The current functions return basic data and assume you have an `articles` table. You may need to modify the SQL functions in the migration file to match your actual database schema and add proper analytics tracking tables.

## Current Mock Data Features:
- Realistic random analytics data
- Responsive to time range selection
- Sample article performance metrics
- Engagement trends visualization
- Works without database setup for development/demo purposes
