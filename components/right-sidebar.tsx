"use client";

import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { createClient } from "@/utils/supabase/client";
import { Sidebar } from "@/components/ui/sidebar";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useDebounce } from "@/hooks/use-debounce";
import { Sparkles, TrendingUp, Hash, Search } from "lucide-react";
import { UserProfile } from "@/types";
import { ExperienceDisplay } from "./ExperienceSystem";
import { rpcHelper } from "@/utils/supabase-rpc-helper";
import { seedHashtags } from "@/utils/hashtag-seeder";

interface Article {
  id: number;
  title: string;
  created_at: string;
}

interface Tag {
  id: number;
  name: string;
  article_count: number;
}

interface RecommendedArticle extends Article {
  recommendation_score: number;
}

interface RightSidebarProps {
  userProfile?: UserProfile;
}

export function RightSidebar({ userProfile }: RightSidebarProps) {
  const supabase = createClient();
  const [articles, setArticles] = useState<Article[]>([]);
  const [recommendations, setRecommendations] = useState<RecommendedArticle[]>([]);
  const [popularTags, setPopularTags] = useState<Tag[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Article[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"trending" | "foryou">("trending");

  const debouncedSearch = useDebounce(searchQuery, 300);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        
        // Use RPC helper for consistent data fetching with fallbacks
        console.log("Fetching real sidebar data from database...");
        
        // Fetch trending articles - try RPC first, then fallback to direct query
        try {
          const { data: trendingData, error: trendingError } = await supabase
            .rpc('get_trending_articles', { p_hours: 24 })
            .limit(5);

          if (trendingError) {
            console.warn("RPC get_trending_articles failed, using direct query:", trendingError);
            // Fallback to direct query of articles table
            const { data: fallbackData, error: fallbackError } = await supabase
              .from('articles')
              .select('id, title, created_at')
              .order('created_at', { ascending: false })
              .limit(5);
            
            if (fallbackError) throw fallbackError;
            
            const formattedData = fallbackData?.map(article => ({
              id: article.id,
              title: article.title,
              created_at: article.created_at
            })) || [];
            
            setArticles(formattedData);
          } else {
            setArticles(trendingData || []);
          }
        } catch (error) {
          console.error("Error fetching trending articles:", error);
          // Final fallback to recent articles
          const { data: recentData } = await supabase
            .from('articles')
            .select('id, title, created_at')
            .order('created_at', { ascending: false })
            .limit(5);
          
          setArticles(recentData || []);
        }

        // Fetch popular hashtags from multiple database sources
        try {
          console.log("Fetching trending hashtags from database...");
          
          // First, try to get hashtags from the dedicated hashtags table
          const { data: hashtagsData, error: hashtagsError } = await supabase
            .from('hashtags')
            .select('id, name, usage_count, trending_score')
            .order('usage_count', { ascending: false })
            .order('trending_score', { ascending: false })
            .limit(10);

          if (hashtagsData && hashtagsData.length > 0) {
            console.log("Found hashtags from hashtags table:", hashtagsData);
            const formattedHashtags = hashtagsData.map(hashtag => ({
              id: hashtag.id,
              name: hashtag.name,
              article_count: hashtag.usage_count || 0
            }));
            setPopularTags(formattedHashtags);
          } else {
            // Fallback: Extract tags from articles table
            console.log("No hashtags in hashtags table, extracting from articles...");
            
            const { data: articlesData, error: articlesError } = await supabase
              .from('articles')
              .select('tags')
              .not('tags', 'is', null)
              .limit(1000); // Get a good sample size

            if (articlesData && articlesData.length > 0) {
              // Count tag occurrences from articles
              const tagCounts = new Map<string, number>();
              
              articlesData.forEach(article => {
                if (article.tags && Array.isArray(article.tags)) {
                  article.tags.forEach((tag: string) => {
                    if (tag && tag.trim()) {
                      const normalizedTag = tag.trim().toLowerCase();
                      const count = tagCounts.get(normalizedTag) || 0;
                      tagCounts.set(normalizedTag, count + 1);
                    }
                  });
                }
              });

              console.log("Extracted tag counts:", Object.fromEntries(tagCounts));

              const formattedTags = Array.from(tagCounts.entries())
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10)
                .map(([name, count], index) => ({
                  id: index + 1,
                  name: name,
                  article_count: count
                }));

              setPopularTags(formattedTags);
            } else {
              // Final fallback: Try article_hashtags table
              console.log("No tags in articles, trying article_hashtags...");
              
              const { data: articleHashtagsData, error: articleHashtagsError } = await supabase
                .from('article_hashtags')
                .select('article_tags')
                .not('article_tags', 'is', null);

              if (articleHashtagsData && articleHashtagsData.length > 0) {
                const tagCounts = new Map<string, number>();
                
                articleHashtagsData.forEach(row => {
                  if (row.article_tags && Array.isArray(row.article_tags)) {
                    row.article_tags.forEach((tag: string) => {
                      if (tag && tag.trim()) {
                        const normalizedTag = tag.trim().toLowerCase();
                        const count = tagCounts.get(normalizedTag) || 0;
                        tagCounts.set(normalizedTag, count + 1);
                      }
                    });
                  }
                });

                const formattedTags = Array.from(tagCounts.entries())
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 10)
                  .map(([name, count], index) => ({
                    id: index + 1,
                    name: name,
                    article_count: count
                  }));

                setPopularTags(formattedTags);
              } else {
                console.log("No tags found in any table, using fallback hashtags...");
                await seedHashtags();
                setPopularTags([]);
              }
            }
          }
        } catch (error) {
          console.error("Error fetching popular hashtags:", error);
          setPopularTags([]);
        }

        // Fetch recommendations if user is logged in
        if (userProfile) {
          try {
            // Get recent popular articles as recommendations (exclude user's own articles)
            const { data: recData, error: recError } = await supabase
              .from('articles')
              .select('id, title, created_at, likes')
              .neq('author_id', userProfile.user_id)
              .order('likes', { ascending: false })
              .order('created_at', { ascending: false })
              .limit(5);

            if (recError) {
              console.debug("Could not fetch recommendations:", recError);
              setRecommendations([]);
            } else {
              const formattedRecs = recData?.map((article, index) => ({
                ...article,
                recommendation_score: 95 - (index * 5), // Simple scoring based on order
                created_at: article.created_at
              })) || [];
              
              setRecommendations(formattedRecs);
            }
          } catch (error) {
            console.debug("Recommendations not available:", error);
            setRecommendations([]);
          }
        }
      } catch (error) {
        console.error("Error in fetchData:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [userProfile]);

  // Handle search
  useEffect(() => {
    const performSearch = async () => {
      if (!debouncedSearch.trim()) {
        setSearchResults([]);
        setIsSearching(false);
        return;
      }

      setIsSearching(true);
      try {
        // Try RPC search first
        const { data, error } = await supabase
          .rpc("search_articles", {
            search_query: debouncedSearch,
          })
          .limit(5);

        if (error) {
          console.warn("RPC search_articles failed, using direct search:", error);
          // Fallback to simple text search
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('articles')
            .select('id, title, created_at')
            .or(`title.ilike.%${debouncedSearch}%,content.ilike.%${debouncedSearch}%`)
            .order('created_at', { ascending: false })
            .limit(5);
          
          if (fallbackError) throw fallbackError;
          setSearchResults(fallbackData || []);
        } else {
          setSearchResults(data || []);
        }
      } catch (error) {
        console.error("Search error:", error);
        setSearchResults([]);
      } finally {
        setIsSearching(false);
      }
    };

    performSearch();
  }, [debouncedSearch]);

  const handleTagClick = async (tagName: string) => {
    setSearchQuery(`#${tagName}`);
  };

  return (
    <Sidebar
      side="right"
      className="bg-secondary text-foreground border-r border-zinc-800"
    >
      <div className="p-4 space-y-6">
        <div className="relative">
          <Input
            type="search"
            placeholder="Search articles..."
            className="bg-secondary text-foreground w-full"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {isSearching && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
            </div>
          )}
        </div>

        {isLoading ? (
          <div className="space-y-4">
            <div className="h-4 bg-muted rounded-sm animate-pulse w-3/4" />
            <div className="h-4 bg-muted rounded-sm animate-pulse w-1/2" />
            <div className="h-4 bg-muted rounded-sm animate-pulse w-2/3" />
          </div>
        ) : searchQuery ? (
          <div>
            <h3 className="text-foreground font-semibold mb-3">
              Search Results
            </h3>
            <ul className="space-y-3">
              {searchResults.map((article) => (
                <li key={article.id}>
                  <Link
                    href={`/ctn/articles/${article.id}`}
                    className="text-accent hover:underline block"
                  >
                    <span className="text-sm line-clamp-2">
                      {article.title}
                    </span>
                  </Link>
                </li>
              ))}
              {searchResults.length === 0 && !isSearching && (
                <li className="text-sm text-muted-foreground">
                  No articles found
                </li>
              )}
            </ul>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Experience System */}
            {userProfile && (
              <div className="mb-6">
                <ExperienceDisplay compact={true} />
              </div>
            )}

            {/* Recommendation tabs - only show if user is logged in */}
            {userProfile && (
              <div className="flex space-x-1 bg-muted p-1 rounded-lg">
                <Button
                  variant={activeTab === "trending" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab("trending")}
                  className="flex-1 text-xs"
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  Trending
                </Button>
                <Button
                  variant={activeTab === "foryou" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab("foryou")}
                  className="flex-1 text-xs"
                >
                  <Sparkles className="h-3 w-3 mr-1" />
                  For You
                </Button>
              </div>
            )}

            {/* Popular Tags */}
            <div>
              <h3 className="text-foreground font-semibold mb-3 flex items-center">
                <Hash className="h-4 w-4 mr-2" />
                Popular Tags
              </h3>
              <div className="flex flex-wrap gap-2 mb-6">
                {popularTags.map((tag) => (
                  <div key={tag.id}>
                    <Badge
                      variant="secondary"
                      className="cursor-pointer hover:bg-accent text-xs"
                      onClick={() => handleTagClick(tag.name)}
                    >
                      {tag.name} ({tag.article_count})
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            {/* Content based on active tab */}
            {activeTab === "foryou" && userProfile && recommendations.length > 0 ? (
              <div>
                <h3 className="text-foreground font-semibold mb-3 flex items-center">
                  <Sparkles className="h-4 w-4 mr-2" />
                  For You
                </h3>
                <ul className="space-y-3">
                  {recommendations.map((article) => (
                    <li key={article.id}>
                      <Link
                        href={`/ctn/articles/${article.id}`}
                        className="text-accent hover:underline block"
                      >
                        <span className="text-sm line-clamp-2">
                          {article.title}
                        </span>
                        <div className="flex items-center mt-1">
                          <Badge variant="outline" className="text-xs">
                            {Math.round(article.recommendation_score)} pts
                          </Badge>
                        </div>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <div>
                <h3 className="text-foreground font-semibold mb-3 flex items-center">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Recent Articles
                </h3>
                <ul className="space-y-3">
                  {articles.map((article) => (
                    <li key={article.id}>
                      <Link
                        href={`/ctn/articles/${article.id}`}
                        className="text-accent hover:underline block"
                      >
                        <span className="text-sm line-clamp-2">
                          {article.title}
                        </span>
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </Sidebar>
  );
}