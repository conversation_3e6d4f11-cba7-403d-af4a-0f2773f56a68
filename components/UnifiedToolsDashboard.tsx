'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getAllCoinsData, type CentralizedCoinData } from '@/utils/mock-data-service';
import {
  Calculator,
  Activity,
  TrendingUp,
  Eye,
  Wallet,
  BarChart3,
  Zap,
  Search,
  RefreshCw,
  Crown,
  Globe
} from 'lucide-react';

// Import all the tool components
import { PortfolioCalculator } from '@/components/PortfolioCalculator';
import { CryptoHeatmap } from '@/components/CryptoHeatmap';
import { OnChainModels } from '@/components/OnChainModels';
import { WalletBalanceAnalysis } from '@/components/WalletBalanceAnalysis';
import { ManualComparisonTool } from '@/components/ManualComparisonTool';
import { AutomatedComparisonTool } from '@/components/AutomatedComparisonTool';
import { CachedTierProtection } from '@/components/CachedTierProtection';
import { ModernContainer, ModernSection } from '@/components/modern-layout';
import { InteractiveMetricChart, MetricChartsGrid } from '@/components/InteractiveMetricChart';
import { CoinDiscovery } from '@/components/CoinDiscovery';


interface UnifiedToolsDashboardProps {
  defaultTab?: string;
}

// Generate chart data from coin metrics
function generateChartData(coin: CentralizedCoinData, metric: string, days: number = 30) {
  const data = [];
  const now = new Date();
  
  let baseValue = 0;
  switch (metric) {
    case 'price':
      baseValue = coin.price;
      break;
    case 'volume':
      baseValue = coin.volume24h;
      break;
    case 'marketCap':
      baseValue = coin.marketCap;
      break;
    case 'activity':
      baseValue = coin.activeAddresses24h || 100000;
      break;
    default:
      baseValue = coin.price;
  }

  for (let i = days; i >= 0; i--) {
    const date = new Date(now.getTime() - (i * 24 * 60 * 60 * 1000));
    const volatility = metric === 'price' ? 0.05 : metric === 'volume' ? 0.3 : 0.1;
    const trend = Math.sin(i / 7) * 0.02;
    const randomChange = (Math.random() - 0.5) * volatility;
    const value = Math.max(0, baseValue * (1 + trend + randomChange));
    
    data.push({
      time: date.toISOString().split('T')[0],
      value: value
    });
  }
  
  return data;
}

export function UnifiedToolsDashboard({ defaultTab = 'portfolio' }: UnifiedToolsDashboardProps) {
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [isLoading, setIsLoading] = useState(false);
  const [coinsData, setCoinsData] = useState<CentralizedCoinData[]>([]);
  const [selectedCoin, setSelectedCoin] = useState('BTC');
  const [isLoadingData, setIsLoadingData] = useState(true);

  // Load coin data from centralized mock service
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoadingData(true);
        const data = await getAllCoinsData();
        setCoinsData(data);
      } catch (error) {
        console.error('Failed to load coin data:', error);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadData();
  }, []);

  // Get selected coin data for components that need it
  const selectedCoinData = coinsData.find(coin => coin.symbol === selectedCoin) || coinsData[0];
  
  // Convert to chain metrics format for compatibility
  const chainData = selectedCoinData ? {
    name: selectedCoinData.name,
    symbol: selectedCoinData.symbol,
    price: selectedCoinData.price,
    marketCap: selectedCoinData.marketCap,
    circulatingSupply: selectedCoinData.marketCap / selectedCoinData.price,
    timestamp: Date.now()
  } : {
    name: 'Bitcoin',
    symbol: 'BTC',
    price: 45000,
    marketCap: 850000000000,
    circulatingSupply: 19500000,
    timestamp: Date.now()
  };

  const handleRefresh = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 1500);
  };

  const tabs = [
    {
      id: 'portfolio',
      label: 'Portfolio',
      icon: Calculator,
      description: 'Track your investments',
      tierRequired: 'tier1'
    },
    {
      id: 'heatmap',
      label: 'Heatmap',
      icon: Activity,
      description: 'Market visualization',
      tierRequired: 'tier1'
    },
    {
      id: 'charts',
      label: 'Charts',
      icon: TrendingUp,
      description: 'Interactive analytics',
      tierRequired: 'tier1'
    },
    {
      id: 'models',
      label: 'Models',
      icon: Eye,
      description: 'Valuation models',
      tierRequired: 'tier1'
    },
    {
      id: 'wallets',
      label: 'Wallets',
      icon: Wallet,
      description: 'Balance analysis',
      tierRequired: 'tier1'
    },
    {
      id: 'comparison',
      label: 'Compare',
      icon: BarChart3,
      description: 'Chain comparison',
      tierRequired: 'tier1'
    },
    {
      id: 'indicators',
      label: 'Indicators',
      icon: Zap,
      description: 'Trading signals',
      tierRequired: 'tier1'
    },
    {
      id: 'discovery',
      label: 'Discovery',
      icon: Search,
      description: 'Find new coins',
      tierRequired: 'tier2'
    }
  ];

  return (
    <ModernContainer>
      <ModernSection>
        {/* Header */}
        <div className="flex items-center justify-between mb-6 p-6 bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-lg">
          <div>
            <h1 className="text-3xl font-bold">Crypto Tools Dashboard</h1>
            <p className="text-muted-foreground mt-2">
              Comprehensive cryptocurrency analysis and portfolio management
            </p>
          </div>
          <div className="flex items-center gap-2">
            {/* Coin Selector for models and analysis */}
            {(activeTab === 'models' || activeTab === 'wallets') && coinsData.length > 0 && (
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-muted-foreground" />
                <Select value={selectedCoin} onValueChange={setSelectedCoin}>
                  <SelectTrigger className="w-32 bg-white/10 dark:bg-black/10 backdrop-blur-md border-white/20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {coinsData.map((coin) => (
                      <SelectItem key={coin.symbol} value={coin.symbol}>
                        {coin.symbol}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            <Badge variant="outline" className="text-blue-600 bg-white/10 dark:bg-black/10 backdrop-blur-md border-white/20">
              <Crown className="h-3 w-3 mr-1" />
              Premium Tools
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isLoading || isLoadingData}
              className="bg-white/10 dark:bg-black/10 backdrop-blur-md border-white/20 hover:bg-white/20"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading || isLoadingData ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Main Dashboard */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8 h-auto p-1 bg-white/10 dark:bg-black/10 backdrop-blur-md border border-white/20 dark:border-white/10 rounded-lg">
            {tabs.map((tab) => (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className="flex flex-col items-center gap-1 p-3 rounded-md transition-all duration-200 data-[state=active]:bg-white/20 data-[state=active]:backdrop-blur-md data-[state=active]:border data-[state=active]:border-white/30 data-[state=active]:shadow-lg hover:bg-white/10 dark:hover:bg-white/5"
              >
                <tab.icon className="h-4 w-4" />
                <span className="text-xs font-medium">{tab.label}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Portfolio Tab */}
          <TabsContent value="portfolio" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/calculator" requiredTier="tier1">
              <PortfolioCalculator 
                onSave={(holdings) => {
                  localStorage.setItem('portfolio-holdings', JSON.stringify(holdings));
                }}
                onLoad={() => {
                  const saved = localStorage.getItem('portfolio-holdings');
                  return saved ? JSON.parse(saved) : [];
                }}
              />
            </CachedTierProtection>
          </TabsContent>

          {/* Heatmap Tab */}
          <TabsContent value="heatmap" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/heatmap" requiredTier="tier1">
              <Card className="bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/20 dark:border-white/10">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Activity className="h-5 w-5 text-blue-500" />
                    Market Heatmap
                  </CardTitle>
                  <CardDescription>
                    Interactive cryptocurrency market visualization
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <CryptoHeatmap
                    isActive={activeTab === 'heatmap'}
                    onRefresh={handleRefresh}
                    onCoinClick={(coin) => {
                      console.log('Coin clicked:', coin);
                    }}
                  />
                </CardContent>
              </Card>
            </CachedTierProtection>
          </TabsContent>

          {/* Charts Tab */}
          <TabsContent value="charts" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/charts" requiredTier="tier1">
              {isLoadingData ? (
                <Card className="bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/20 dark:border-white/10">
                  <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">Loading chart data...</p>
                    </div>
                  </CardContent>
                </Card>
              ) : selectedCoinData ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InteractiveMetricChart
                    title="Price Trends"
                    description={`${selectedCoinData.name} price movement`}
                    data={generateChartData(selectedCoinData, 'price')}
                    color="#10b981"
                    icon={<TrendingUp className="h-5 w-5" />}
                    unit="$"
                    change24h={selectedCoinData.change24h}
                    isActive={activeTab === 'charts'}
                  />
                  <InteractiveMetricChart
                    title="Trading Volume"
                    description="24-hour trading volume"
                    data={generateChartData(selectedCoinData, 'volume')}
                    color="#8b5cf6"
                    icon={<BarChart3 className="h-5 w-5" />}
                    unit="$"
                    change24h={selectedCoinData.change24h * 1.5}
                    isActive={activeTab === 'charts'}
                  />
                  <InteractiveMetricChart
                    title="Market Cap"
                    description="Total market capitalization"
                    data={generateChartData(selectedCoinData, 'marketCap')}
                    color="#3b82f6"
                    icon={<Activity className="h-5 w-5" />}
                    unit="$"
                    change24h={selectedCoinData.change24h * 0.8}
                    isActive={activeTab === 'charts'}
                  />
                  <InteractiveMetricChart
                    title="Network Activity"
                    description="Blockchain activity metrics"
                    data={generateChartData(selectedCoinData, 'activity')}
                    color="#f59e0b"
                    icon={<Zap className="h-5 w-5" />}
                    unit=""
                    change24h={2.3}
                    isActive={activeTab === 'charts'}
                  />
                </div>
              ) : null}
            </CachedTierProtection>
          </TabsContent>

          {/* Models Tab */}
          <TabsContent value="models" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain-models" requiredTier="tier1">
              {isLoadingData ? (
                <Card className="bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/20 dark:border-white/10">
                  <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">Loading coin data...</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <OnChainModels
                  selectedChain={chainData}
                  isActive={activeTab === 'models'}
                  onRefresh={handleRefresh}
                />
              )}
            </CachedTierProtection>
          </TabsContent>

          {/* Wallets Tab */}
          <TabsContent value="wallets" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain" requiredTier="tier1">
              {isLoadingData ? (
                <Card className="bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/20 dark:border-white/10">
                  <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">Loading wallet data...</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <WalletBalanceAnalysis
                  selectedChain={chainData}
                  isActive={activeTab === 'wallets'}
                  onRefresh={handleRefresh}
                />
              )}
            </CachedTierProtection>
          </TabsContent>

          {/* Comparison Tab */}
          <TabsContent value="comparison" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/comparison" requiredTier="tier1">
              <div className="space-y-6">
                <Card className="bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/20 dark:border-white/10">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-purple-500" />
                      Blockchain Comparison
                    </CardTitle>
                    <CardDescription>
                      Compare multiple blockchain networks with advanced analytics
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Tabs defaultValue="manual" className="space-y-4">
                      <TabsList className="grid w-full grid-cols-2 bg-white/10 dark:bg-black/10 backdrop-blur-md border border-white/20 dark:border-white/10">
                        <TabsTrigger value="manual" className="data-[state=active]:bg-white/20 data-[state=active]:backdrop-blur-md">Manual Input</TabsTrigger>
                        <TabsTrigger value="automated" className="data-[state=active]:bg-white/20 data-[state=active]:backdrop-blur-md">Automated Analysis</TabsTrigger>
                      </TabsList>
                      <TabsContent value="manual">
                        <ManualComparisonTool
                          isActive={activeTab === 'comparison'}
                          onRefresh={handleRefresh}
                        />
                      </TabsContent>
                      <TabsContent value="automated">
                        <CachedTierProtection pagePath="/ctn/tools/comparison?tab=automated" requiredTier="tier3">
                          <AutomatedComparisonTool
                            isActive={activeTab === 'comparison'}
                            onRefresh={handleRefresh}
                          />
                        </CachedTierProtection>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              </div>
            </CachedTierProtection>
          </TabsContent>

          {/* Indicators Tab */}
          <TabsContent value="indicators" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/indicators" requiredTier="tier1">
              <Card className="bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/20 dark:border-white/10">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-500" />
                    Trading Indicators
                  </CardTitle>
                  <CardDescription>
                    Advanced trading strategies and technical indicators
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-64 bg-gradient-to-br from-yellow-50/20 to-orange-50/20 dark:from-yellow-950/10 dark:to-orange-950/10 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/10">
                    <span className="text-muted-foreground">Trading Indicators Component</span>
                  </div>
                </CardContent>
              </Card>
            </CachedTierProtection>
          </TabsContent>

          {/* Discovery Tab */}
          <TabsContent value="discovery" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/discovery" requiredTier="tier2">
              {isLoadingData ? (
                <Card className="bg-white/5 dark:bg-black/5 backdrop-blur-md border border-white/20 dark:border-white/10">
                  <CardContent className="flex items-center justify-center py-12">
                    <div className="text-center">
                      <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
                      <p className="text-muted-foreground">Loading discovery data...</p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <CoinDiscovery 
                  onCoinSelect={(coin) => {
                    console.log('Selected coin:', coin);
                  }}
                  onWatchlistToggle={(coinId) => {
                    console.log('Toggled watchlist for:', coinId);
                  }}
                  loading={isLoading}
                />
              )}
            </CachedTierProtection>
          </TabsContent>
        </Tabs>
      </ModernSection>
    </ModernContainer>
  );
}
