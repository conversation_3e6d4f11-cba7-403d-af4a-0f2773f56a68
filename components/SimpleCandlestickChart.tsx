"use client";

import React, { useRef, useEffect, useState, useImperativeHandle, forwardRef } from "react";

export type CandlestickData = {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
};

export interface SimpleCandlestickChartProps {
  data: CandlestickData[];
  width?: number | string;
  height?: number;
  options?: Record<string, any>;
}

export interface ChartRef {
  updateData: (bar: CandlestickData) => void;
  setData: (data: CandlestickData[]) => void;
  chart: any;
  mainSeries: any;
}

const SimpleCandlestickChart = forwardRef<ChartRef, SimpleCandlestickChartProps>(
  ({ data, width = '100%', height = 520, options = {} }, ref) => {
    const chartContainerRef = useRef<HTMLDivElement>(null);
    const chartRef = useRef<any>(null);
    const mainSeriesRef = useRef<any>(null);
    const [ready, setReady] = useState(false);

    // Expose chart methods via ref
    useImperativeHandle(ref, () => ({
      updateData: (bar: CandlestickData) => {
        if (ready && mainSeriesRef.current && typeof mainSeriesRef.current.update === 'function') {
          mainSeriesRef.current.update(bar);
        }
      },
      setData: (newData: CandlestickData[]) => {
        if (ready && mainSeriesRef.current && typeof mainSeriesRef.current.setData === 'function') {
          const validData = newData
            .filter(d => d && typeof d.time === 'number' && !isNaN(d.time))
            .sort((a, b) => a.time - b.time);
          if (validData.length > 0) {
            mainSeriesRef.current.setData(validData);
          }
        }
      },
      chart: chartRef.current,
      mainSeries: mainSeriesRef.current,
    }), [ready]);

    useEffect(() => {
      let chart: any;
      let mainSeries: any;
      let isMounted = true;

      const initChart = async () => {
        try {
          if (!chartContainerRef.current || !isMounted) return;

          // Dynamic import to ensure client-side only
          const { createChart } = await import('lightweight-charts');
          
          if (!isMounted || !chartContainerRef.current) return;

          // Clear container
          chartContainerRef.current.innerHTML = '';

          // Get container dimensions
          const containerRect = chartContainerRef.current.getBoundingClientRect();
          const containerWidth = containerRect.width || chartContainerRef.current.offsetWidth || 800;
          const containerHeight = height || 520;

          // Dynamic import to get ColorType
          const { ColorType } = await import('lightweight-charts');

          // Create chart
          chart = createChart(chartContainerRef.current, {
            width: Math.floor(containerWidth),
            height: Math.floor(containerHeight),
            layout: {
              background: { type: ColorType.Solid, color: 'transparent' },
              textColor: '#888',
            },
            grid: {
              vertLines: { color: 'rgba(42, 46, 57, 0.1)' },
              horzLines: { color: 'rgba(42, 46, 57, 0.1)' },
            },
            crosshair: {
              mode: 1,
            },
            timeScale: {
              borderColor: 'rgba(42, 46, 57, 0.2)',
              timeVisible: true,
            },
            rightPriceScale: {
              borderColor: 'rgba(42, 46, 57, 0.2)',
            },
            ...options,
          });

          // Add candlestick series
          mainSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
          });

          chartRef.current = chart;
          mainSeriesRef.current = mainSeries;

          // Set data if provided
          if (Array.isArray(data) && data.length > 0) {
            const validData = data
              .filter(d => {
                return d && 
                  typeof d.time === 'number' && 
                  !isNaN(d.time) && 
                  d.time > 0 &&
                  typeof d.open === 'number' && 
                  typeof d.high === 'number' && 
                  typeof d.low === 'number' && 
                  typeof d.close === 'number' &&
                  !isNaN(d.open) && !isNaN(d.high) && !isNaN(d.low) && !isNaN(d.close) &&
                  d.open > 0 && d.high > 0 && d.low > 0 && d.close > 0 &&
                  d.high >= d.low &&
                  d.high >= Math.max(d.open, d.close) &&
                  d.low <= Math.min(d.open, d.close);
              })
              .sort((a, b) => a.time - b.time);

            if (validData.length > 0) {
              mainSeries.setData(validData);
            }
          }

          // Handle resize
          const handleResize = () => {
            if (chart && chartContainerRef.current) {
              const newWidth = chartContainerRef.current.offsetWidth;
              if (newWidth > 0) {
                chart.applyOptions({
                  width: Math.floor(newWidth),
                });
              }
            }
          };

          window.addEventListener('resize', handleResize);
          
          setReady(true);

          // Cleanup function
          return () => {
            window.removeEventListener('resize', handleResize);
            if (mainSeries && chart) {
              chart.removeSeries(mainSeries);
            }
            if (chart) {
              chart.remove();
            }
            chartRef.current = null;
            mainSeriesRef.current = null;
            setReady(false);
          };

        } catch (error) {
          console.error('Error initializing candlestick chart:', error);
          setReady(false);
        }
      };

      const cleanup = initChart();

      return () => {
        isMounted = false;
        if (cleanup && typeof cleanup.then === 'function') {
          cleanup.then(fn => fn && fn());
        }
      };
    }, [data, height, JSON.stringify(options)]);

    return (
      <div
        ref={chartContainerRef}
        style={{
          width: typeof width === 'string' ? width : `${width}px`,
          height: `${height}px`,
          minWidth: 320,
          maxWidth: "100%",
          position: "relative",
        }}
      />
    );
  }
);

SimpleCandlestickChart.displayName = 'SimpleCandlestickChart';

export default SimpleCandlestickChart;