'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@/utils/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  Legend
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  Eye, 
  Heart, 
  MessageCircle, 
  Bookmark,
  Download,
  Calendar,
  Target,
  Crown,
  BarChart3,
  Activity
} from 'lucide-react';
import { PremiumBadge, FeatureLockedIndicator } from './PremiumBadges';
import { premiumManager } from '@/utils/premiumManager';

interface PremiumAnalyticsProps {
  userId: string;
  articleId?: string;
}

interface AnalyticsData {
  likes: number;
  comments: number;
  bookmarks: number;
  followers: number;
  engagement_rate: number;
  trending_score: number;
}

interface TimeSeriesData {
  date: string;
  likes: number;
  comments: number;
  engagement: number;
}

interface AudienceData {
  name: string;
  value: number;
  color: string;
}

export function PremiumAnalyticsDashboard({ userId, articleId }: PremiumAnalyticsProps) {
  const [hasAnalyticsAccess, setHasAnalyticsAccess] = useState(false);
  const [hasAdvancedAnalytics, setHasAdvancedAnalytics] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [timeSeriesData, setTimeSeriesData] = useState<TimeSeriesData[]>([]);
  const [audienceData, setAudienceData] = useState<AudienceData[]>([]);
  const [loading, setLoading] = useState(true);

  const supabase = createClient();

  useEffect(() => {
    loadAnalyticsFeatures();
  }, [userId]);

  useEffect(() => {
    if (hasAnalyticsAccess) {
      loadAnalyticsData();
    }
  }, [timeRange, hasAnalyticsAccess]);

  const loadAnalyticsFeatures = async () => {
    try {
      const [enhancedCheck, advancedCheck] = await Promise.all([
        premiumManager.checkPremiumFeature(userId, 'enhanced_analytics'),
        premiumManager.checkPremiumFeature(userId, 'advanced_analytics')
      ]);

      setHasAnalyticsAccess(enhancedCheck.hasFeature);
      setHasAdvancedAnalytics(advancedCheck.hasFeature);
    } catch (error) {
      console.error('Error loading analytics features:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadAnalyticsData = async () => {
    try {
      // Load real analytics data from database functions
      let analyticsResult = null;
      
      if (articleId) {
        // Get specific article stats
        const { data: articleStats, error: articleError } = await supabase.rpc(
          "get_article_stats",
          { p_author_id: userId }
        );
        
        if (articleError) {
          console.error('Error loading article stats:', articleError);
        } else {
          console.log('Article stats loaded:', articleStats);
          analyticsResult = articleStats;
        }
      } else {
        // Get user analytics overview
        const { data: userStats, error: userError } = await supabase.rpc(
          "get_user_analytics_overview",
          {
            p_user_id: userId,
            p_time_range: timeRange + "d"
          }
        );
        
        if (userError) {
          console.error('Error loading user analytics:', userError);
        } else {
          console.log('User analytics loaded:', userStats);
          analyticsResult = userStats?.[0];
        }
      }

      // Set analytics data from real database or fallback to defaults
      if (analyticsResult) {
        setAnalyticsData({
          likes: analyticsResult.total_likes || 0,
          comments: analyticsResult.total_comments || 0,
          bookmarks: 0, // Not available in current schema
          followers: analyticsResult.total_followers || 0,
          engagement_rate: analyticsResult.avg_engagement_rate || 0,
          trending_score: 0 // Could be calculated from engagement
        });
      } else {
        // Fallback to zero values if no data
        setAnalyticsData({
          likes: 0,
          comments: 0,
          bookmarks: 0,
          followers: 0,
          engagement_rate: 0,
          trending_score: 0
        });
      }

      // Load engagement trends for time series data
      try {
        const { data: trendsData, error: trendsError } = await supabase.rpc(
          "get_engagement_trends",
          {
            p_user_id: userId,
            p_time_range: timeRange + "d"
          }
        );

        if (trendsError) {
          console.error('Error loading engagement trends:', trendsError);
          // Generate fallback sample data
          const days = parseInt(timeRange);
          const data: TimeSeriesData[] = [];
          for (let i = days; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            data.push({
              date: date.toISOString().split('T')[0],
              likes: 0,
              comments: 0,
              engagement: 0
            });
          }
          setTimeSeriesData(data);
        } else {
          console.log('Engagement trends loaded:', trendsData);
          // Convert trends data to TimeSeriesData format
          const timeSeriesData: TimeSeriesData[] = (trendsData || []).map((trend: any) => ({
            date: trend.date,
            likes: trend.likes || 0,
            comments: trend.comments || 0,
            engagement: ((trend.likes + trend.comments) / Math.max(trend.likes + trend.comments, 1)) * 100
          }));
          setTimeSeriesData(timeSeriesData);
        }
      } catch (trendsErr) {
        console.error('Error calling get_engagement_trends:', trendsErr);
        // Fallback to empty data
        setTimeSeriesData([]);
      }

      // Sample audience data (this would need to be tracked separately in analytics_events)
      setAudienceData([
        { name: 'Desktop', value: 65, color: '#8884d8' },
        { name: 'Mobile', value: 30, color: '#82ca9d' },
        { name: 'Tablet', value: 5, color: '#ffc658' }
      ]);
    } catch (error) {
      console.error('Error loading analytics data:', error);
      // Set empty/default data on error
      setAnalyticsData({
        likes: 0,
        comments: 0,
        bookmarks: 0,
        followers: 0,
        engagement_rate: 0,
        trending_score: 0
      });
      setTimeSeriesData([]);
    }
  };

  const exportAnalytics = async () => {
    if (!hasAdvancedAnalytics) return;
    
    const csvData = timeSeriesData
      .map(row => `${row.date},${row.likes},${row.comments},${row.engagement}`)
      .join('\n');
    
    const blob = new Blob([`Date,Likes,Comments,Engagement\n${csvData}`], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-${timeRange}.csv`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-32 bg-gray-100 rounded animate-pulse" />
        <div className="h-96 bg-gray-100 rounded animate-pulse" />
      </div>
    );
  }

  if (!hasAnalyticsAccess) {
    return (
      <FeatureLockedIndicator featureName="Enhanced Analytics" requiredPlan="Premium">
        <div className="space-y-6">
          {/* Mock Analytics Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { label: 'Total Views', value: '---', icon: Eye },
              { label: 'Likes', value: '---', icon: Heart },
              { label: 'Comments', value: '---', icon: MessageCircle },
              { label: 'Engagement Rate', value: '---%', icon: TrendingUp }
            ].map((stat, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                      <p className="text-2xl font-bold">{stat.value}</p>
                    </div>
                    <stat.icon className="w-8 h-8 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Mock Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 bg-gray-100 rounded flex items-center justify-center">
                <p className="text-gray-500">Analytics Chart Unavailable</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </FeatureLockedIndicator>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="w-6 h-6" />
            Analytics Dashboard
            <PremiumBadge type="premium" size="sm" />
          </h2>
          <p className="text-gray-600 mt-1">
            {articleId ? 'Article performance insights' : 'Profile performance insights'}
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          {hasAdvancedAnalytics && (
            <Button variant="outline" onClick={exportAnalytics}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          )}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Likes</p>
                <p className="text-2xl font-bold">{analyticsData?.likes.toLocaleString()}</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +12.5% from last period
                </p>
              </div>
              <Heart className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Likes</p>
                <p className="text-2xl font-bold">{analyticsData?.likes.toLocaleString()}</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +8.3% from last period
                </p>
              </div>
              <Heart className="w-8 h-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Comments</p>
                <p className="text-2xl font-bold">{analyticsData?.comments.toLocaleString()}</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +15.7% from last period
                </p>
              </div>
              <MessageCircle className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Engagement Rate</p>
                <p className="text-2xl font-bold">{analyticsData?.engagement_rate}%</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +2.1% from last period
                </p>
              </div>
              <Activity className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="audience">Audience</TabsTrigger>
          <TabsTrigger value="engagement">Engagement</TabsTrigger>
          <TabsTrigger value="growth">Growth</TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Over Time</CardTitle>
              <CardDescription>Views, likes, and engagement trends</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={timeSeriesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Area type="monotone" dataKey="views" stackId="1" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                  <Area type="monotone" dataKey="likes" stackId="1" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.6} />
                  <Area type="monotone" dataKey="comments" stackId="1" stroke="#ffc658" fill="#ffc658" fillOpacity={0.6} />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Daily Engagement</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={timeSeriesData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="engagement" stroke="#8884d8" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Content Performance Score</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Trending Score</span>
                  <Badge variant="secondary">{analyticsData?.trending_score}/100</Badge>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${analyticsData?.trending_score}%` }}
                  />
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Quality Score</p>
                    <p className="font-medium">92/100</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Virality Score</p>
                    <p className="font-medium">78/100</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="audience" className="space-y-4">
          {hasAdvancedAnalytics ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Device Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={200}>
                    <PieChart>
                      <Pie
                        data={audienceData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }: any) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {audienceData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Audience Demographics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Age 18-24</span>
                      <span>28%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '28%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Age 25-34</span>
                      <span>35%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '35%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Age 35-44</span>
                      <span>22%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '22%' }} />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Age 45+</span>
                      <span>15%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-600 h-2 rounded-full" style={{ width: '15%' }} />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <FeatureLockedIndicator featureName="Advanced Audience Analytics" requiredPlan="Tier 3">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Device Usage</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-48 bg-gray-100 rounded flex items-center justify-center">
                      <p className="text-gray-500">Advanced Analytics Required</p>
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Audience Demographics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="h-48 bg-gray-100 rounded flex items-center justify-center">
                      <p className="text-gray-500">Advanced Analytics Required</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </FeatureLockedIndicator>
          )}
        </TabsContent>

        <TabsContent value="engagement" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Engagement Breakdown</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={timeSeriesData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="likes" fill="#ff6b6b" name="Likes" />
                  <Bar dataKey="comments" fill="#4ecdc4" name="Comments" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="growth" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Growth Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded">
                  <Users className="w-8 h-8 mx-auto mb-2 text-blue-500" />
                  <p className="text-2xl font-bold">{analyticsData?.followers.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total Followers</p>
                  <p className="text-xs text-green-600 mt-1">+234 this week</p>
                </div>
                <div className="text-center p-4 border rounded">
                  <Target className="w-8 h-8 mx-auto mb-2 text-green-500" />
                  <p className="text-2xl font-bold">23.4%</p>
                  <p className="text-sm text-gray-600">Growth Rate</p>
                  <p className="text-xs text-green-600 mt-1">+2.1% vs last month</p>
                </div>
                <div className="text-center p-4 border rounded">
                  <Bookmark className="w-8 h-8 mx-auto mb-2 text-purple-500" />
                  <p className="text-2xl font-bold">{analyticsData?.bookmarks.toLocaleString()}</p>
                  <p className="text-sm text-gray-600">Total Bookmarks</p>
                  <p className="text-xs text-green-600 mt-1">+45 this week</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
