"use client";

import { useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";

interface PerformanceOptimizerProps {
  children: React.ReactNode;
  enablePreloading?: boolean;
}

// Cache keys for different data types
const CACHE_KEYS = {
  USER_PROFILE: 'user_profile',
  ARTICLES: 'articles',
  TRENDING: 'trending',
  RECOMMENDATIONS: 'recommendations',
  NOTIFICATIONS: 'notifications'
} as const;

// Cache expiry times (in milliseconds)
const CACHE_EXPIRY = {
  SHORT: 5 * 60 * 1000,    // 5 minutes
  MEDIUM: 15 * 60 * 1000,  // 15 minutes
  LONG: 60 * 60 * 1000,    // 1 hour
} as const;

class CacheManager {
  private cache = new Map<string, { data: any; expiry: number }>();

  set(key: string, data: any, expiry: number = CACHE_EXPIRY.MEDIUM) {
    this.cache.set(key, {
      data,
      expiry: Date.now() + expiry
    });
  }

  get(key: string) {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }

  clear() {
    this.cache.clear();
  }

  delete(key: string) {
    this.cache.delete(key);
  }

  // Preload critical data
  async preloadCriticalData(supabase: any, userId?: string) {
    try {
      const promises = [];

      // Preload user profile if authenticated
      if (userId) {
        promises.push(
          this.preloadUserProfile(supabase, userId),
          this.preloadUserNotifications(supabase, userId),
          this.preloadUserRecommendations(supabase, userId)
        );
      }

      // Preload trending content
      promises.push(
        this.preloadTrendingContent(supabase),
        this.preloadPopularHashtags(supabase)
      );

      await Promise.allSettled(promises);
    } catch (error) {
      console.error('Error preloading critical data:', error);
    }
  }

  private async preloadUserProfile(supabase: any, userId: string) {
    const cacheKey = `${CACHE_KEYS.USER_PROFILE}_${userId}`;
    if (this.get(cacheKey)) return;

    try {
      const { data } = await (supabase as any)
        .from('users')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (data) {
        this.set(cacheKey, data, CACHE_EXPIRY.LONG);
      }
    } catch (error) {
      console.error('Error preloading user profile:', error);
    }
  }

  private async preloadUserNotifications(supabase: any, userId: string) {
    const cacheKey = `${CACHE_KEYS.NOTIFICATIONS}_${userId}`;
    if (this.get(cacheKey)) return;

    try {
      const { data } = await (supabase as any)
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .eq('is_read', false)
        .order('created_at', { ascending: false })
        .limit(10);

      if (data) {
        this.set(cacheKey, data, CACHE_EXPIRY.SHORT);
      }
    } catch (error) {
      console.error('Error preloading notifications:', error);
    }
  }

  private async preloadUserRecommendations(supabase: any, userId: string) {
    const cacheKey = `${CACHE_KEYS.RECOMMENDATIONS}_${userId}`;
    if (this.get(cacheKey)) return;

    try {
      const { data } = await (supabase as any)
        .rpc('get_article_recommendations', {
          p_user_id: userId,
          p_limit: 10
        });

      if (data) {
        this.set(cacheKey, data, CACHE_EXPIRY.MEDIUM);
      }
    } catch (error) {
      console.error('Error preloading recommendations:', error);
    }
  }

  private async preloadTrendingContent(supabase: any) {
    const cacheKey = CACHE_KEYS.TRENDING;
    if (this.get(cacheKey)) return;

    try {
      const { data } = await (supabase as any)
        .rpc('get_trending_articles', {
          p_limit: 20
        });

      if (data) {
        this.set(cacheKey, data, CACHE_EXPIRY.MEDIUM);
      }
    } catch (error) {
      console.error('Error preloading trending content:', error);
    }
  }

  private async preloadPopularHashtags(supabase: any) {
    const cacheKey = 'popular_hashtags';
    if (this.get(cacheKey)) return;

    try {
      const { data } = await (supabase as any)
        .rpc('get_trending_tags', {
          p_limit: 20
        });

      if (data) {
        this.set(cacheKey, data, CACHE_EXPIRY.LONG);
      }
    } catch (error) {
      console.error('Error preloading hashtags:', error);
    }
  }
}

// Global cache instance
const cacheManager = new CacheManager();

// Intersection Observer for lazy loading
class LazyLoadManager {
  private observer: IntersectionObserver | null = null;

  init() {
    if (typeof window === 'undefined' || this.observer) return;

    this.observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const target = entry.target as HTMLElement;
            
            // Handle lazy loading of images
            if (target.tagName === 'IMG') {
              const img = target as HTMLImageElement;
              const src = img.dataset.src;
              if (src) {
                img.src = src;
                img.removeAttribute('data-src');
                this.observer?.unobserve(img);
              }
            }

            // Handle lazy loading of components
            if (target.dataset.lazyComponent) {
              target.dispatchEvent(new Event('lazy-load'));
              this.observer?.unobserve(target);
            }
          }
        });
      },
      {
        rootMargin: '50px 0px',
        threshold: 0.1
      }
    );
  }

  observe(element: Element) {
    this.observer?.observe(element);
  }

  unobserve(element: Element) {
    this.observer?.unobserve(element);
  }
}

const lazyLoadManager = new LazyLoadManager();

// Performance monitoring
class PerformanceMonitor {
  private metrics = new Map<string, number>();

  startTiming(key: string) {
    this.metrics.set(key, performance.now());
  }

  endTiming(key: string): number {
    const start = this.metrics.get(key);
    if (start) {
      const duration = performance.now() - start;
      this.metrics.delete(key);
      return duration;
    }
    return 0;
  }

  measureAsyncOperation<T>(key: string, operation: () => Promise<T>): Promise<T> {
    this.startTiming(key);
    return operation().finally(() => {
      const duration = this.endTiming(key);
      if (duration > 1000) {
        console.warn(`Slow operation detected: ${key} took ${duration.toFixed(2)}ms`);
      }
    });
  }

  // Core Web Vitals monitoring
  monitorWebVitals() {
    if (typeof window === 'undefined') return;

    // Monitor LCP (Largest Contentful Paint)
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lcpEntry = entries[entries.length - 1];
      console.log('LCP:', lcpEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // Monitor FID (First Input Delay)
    new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        console.log('FID:', (entry as any).processingStart - entry.startTime);
      });
    }).observe({ entryTypes: ['first-input'] });

    // Monitor CLS (Cumulative Layout Shift)
    let clsValue = 0;
    new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (!(entry as any).hadRecentInput) {
          clsValue += (entry as any).value;
        }
      });
      console.log('CLS:', clsValue);
    }).observe({ entryTypes: ['layout-shift'] });
  }
}

const performanceMonitor = new PerformanceMonitor();

// Resource prefetching
class ResourcePrefetcher {
  private prefetched = new Set<string>();

  prefetchRoute(href: string) {
    if (this.prefetched.has(href) || typeof window === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = href;
    document.head.appendChild(link);
    
    this.prefetched.add(href);
  }

  prefetchCriticalRoutes() {
    const criticalRoutes = [
      '/ctn',
      '/ctn/foryou',
      '/ctn/trending',
      '/ctn/search',
      '/ctn/profile'
    ];

    criticalRoutes.forEach(route => this.prefetchRoute(route));
  }

  preloadCriticalAssets() {
    const criticalAssets = [
      '/ct_logo.png',
      '/default-avatar.jpg'
    ];

    criticalAssets.forEach(asset => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = asset;
      link.as = 'image';
      document.head.appendChild(link);
    });
  }
}

const resourcePrefetcher = new ResourcePrefetcher();

// Main Performance Optimizer Component
export default function PerformanceOptimizer({ children, enablePreloading = false }: PerformanceOptimizerProps) {
  const supabase = createClient();

  const initializeOptimizations = useCallback(async () => {
    // Initialize lazy loading
    lazyLoadManager.init();

    // Start performance monitoring
    performanceMonitor.monitorWebVitals();

    // Prefetch critical routes and assets
    resourcePrefetcher.prefetchCriticalRoutes();
    resourcePrefetcher.preloadCriticalAssets();

    // Get current user and preload critical data (only if enabled)
    if (enablePreloading) {
      try {
        const { data: { user } } = await (supabase as any).auth.getUser();
        await cacheManager.preloadCriticalData(supabase, user?.id);
      } catch (error) {
        console.error('Error during initialization:', error);
      }
    }
  }, [supabase]);

  // Network quality adaptation
  const adaptToNetworkQuality = useCallback(() => {
    if (typeof window === 'undefined' || !(navigator as any).connection) return;

    const connection = (navigator as any).connection;
    const effectiveType = connection.effectiveType;

    // Adjust behavior based on network quality
    if (effectiveType === 'slow-2g' || effectiveType === '2g') {
      // Disable prefetching and reduce image quality
      console.log('Slow network detected, optimizing for performance');
      document.documentElement.classList.add('slow-network');
    } else if (effectiveType === '3g') {
      // Moderate optimizations
      document.documentElement.classList.add('medium-network');
    } else {
      // Fast network, enable all features
      document.documentElement.classList.add('fast-network');
    }
  }, []);

  useEffect(() => {
    // Initialize optimizations on mount
    initializeOptimizations();

    // Adapt to network quality
    adaptToNetworkQuality();

    // Listen for network changes
    if (typeof window !== 'undefined' && (navigator as any).connection) {
      const connection = (navigator as any).connection;
      connection.addEventListener('change', adaptToNetworkQuality);

      return () => {
        connection.removeEventListener('change', adaptToNetworkQuality);
      };
    }
  }, [initializeOptimizations, adaptToNetworkQuality]);

  // Provide cache manager and performance utilities to children
  useEffect(() => {
    // Make utilities available globally for other components
    (window as any).cacheManager = cacheManager;
    (window as any).performanceMonitor = performanceMonitor;
    (window as any).lazyLoadManager = lazyLoadManager;
  }, []);

  return <>{children}</>;
}

// Export utilities for use in other components
export { cacheManager, performanceMonitor, lazyLoadManager, CACHE_KEYS, CACHE_EXPIRY };
