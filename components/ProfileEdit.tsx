"use client";

import { useState, useCallback, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { toast } from "@/hooks/use-toast";
import { User, SupabaseClient } from "@supabase/supabase-js";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Info } from "lucide-react";
import { UserProfile as Profile } from "@/types";
import { ImagePositionAdjuster } from "./ImagePositionAdjuster";
import { Label } from "./ui/label";
import { useDebounce } from "@/hooks/use-debounce";
import { Dialog, DialogContent, DialogTitle, DialogFooter } from "./ui/dialog";

interface ProfileEditProps {
  profile: Profile;
  user: User;
  onCancel: () => void;
  onSave: (updatedProfile: Profile) => void;
}

interface UploadConfig {
  maxSize: number;
  allowedTypes: string[];
  dimensions?: {
    minWidth?: number;
    maxWidth?: number;
    minHeight?: number;
    maxHeight?: number;
  };
}

interface ProfileUpdateStatus {
  canUpdateUsername: boolean;
  hoursUntilNextUpdate: number;
}

interface ImagePosition {
  x: number;
  y: number;
  scale: number;
}

interface FormData extends Profile {
  avatar_position?: ImagePosition;
  banner_position?: ImagePosition;
}

const INITIAL_IMAGE_POSITION: ImagePosition = {
  x: 0,
  y: 0,
  scale: 1,
};

const UPLOAD_CONFIG: Record<"avatar" | "banner", UploadConfig> = {
  avatar: {
    maxSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ["image/jpeg", "image/png", "image/webp"],
    dimensions: {
      minWidth: 200,
      minHeight: 200,
      maxWidth: 1000,
      maxHeight: 1000,
    },
  },
  banner: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ["image/jpeg", "image/png", "image/webp"],
    dimensions: {
      minWidth: 1200,
      minHeight: 400,
    },
  },
};

const DEFAULT_AVATAR_URL = "https://mwtctaoyvflqfkzqlrzf.supabase.co/storage/v1/object/public/ctn//ct_logoclr.png";

export function ProfileEdit({
  profile,
  user,
  onCancel,
  onSave,
}: ProfileEditProps) {
  const supabase = createClient();
  const [isLoading, setIsLoading] = useState(true);
  const [formData, setFormData] = useState<FormData>({
    ...profile,
    avatar_position: profile.avatar_position || INITIAL_IMAGE_POSITION,
    banner_position: profile.banner_position || INITIAL_IMAGE_POSITION,
  });
  const [isUploading, setIsUploading] = useState(false);
  const [profileStatusLoading, setProfileStatusLoading] = useState(true);
  const [originalUsername] = useState(profile.username);
  const [usernameUpdateAllowed, setUsernameUpdateAllowed] = useState(true);
  const [hoursUntilNextUpdate, setHoursUntilNextUpdate] = useState(0);
  const [adjustingImage, setAdjustingImage] = useState<"avatar" | "banner" | null>(
    null
  );
  const [username, setUsername] = useState(profile.username || '');
  const [debouncedUsername] = useDebounce(username || '', 1000);
  const [usernameAvailability, setUsernameAvailability] = useState<boolean | null>(
    null
  );
  const [usernameChecking, setUsernameChecking] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  useEffect(() => {
    const checkForChanges = () => {
      const hasChanged =
        username !== originalUsername ||
        formData.bio !== profile.bio ||
        formData.avatar_url !== profile.avatar_url ||
        formData.banner_url !== profile.banner_url ||
        JSON.stringify(formData.avatar_position) !==
          JSON.stringify(profile.avatar_position) ||
        JSON.stringify(formData.banner_position) !==
          JSON.stringify(profile.banner_position);

      setHasUnsavedChanges(hasChanged);
    };

    checkForChanges();
  }, [formData, profile, username, originalUsername]);

  useEffect(() => {
    const loadProfileStatus = async () => {
      try {
        setProfileStatusLoading(true);
        const status = await getProfileUpdateStatus(profile.user_id, supabase);
        setUsernameUpdateAllowed(status.canUpdateUsername);
        setHoursUntilNextUpdate(status.hoursUntilNextUpdate);
      } catch (error) {
        console.error("Error loading profile status:", error);
        toast({
          title: "Error",
          description: "Failed to load profile update status",
          variant: "destructive",
        });
      } finally {
        setProfileStatusLoading(false);
      }
    };

    loadProfileStatus();
  }, [profile.user_id, supabase]);

  useEffect(() => {
    const checkAvailability = async () => {
      if (debouncedUsername && debouncedUsername !== originalUsername) {
        setUsernameChecking(true);
        try {
          const isAvailable = await checkUsername(
            debouncedUsername,
            profile.user_id
          );
          setUsernameAvailability(isAvailable);
        } catch (error) {
          console.error("Error checking username:", error);
          setUsernameAvailability(false);
          toast({
            title: "Error",
            description: "Failed to check username availability",
            variant: "destructive",
          });
        } finally {
          setUsernameChecking(false);
        }
      } else {
        setUsernameAvailability(null);
      }
    };

    checkAvailability();
  }, [debouncedUsername, profile.user_id, originalUsername]);

  // Cleanup effect for file uploads
  useEffect(() => {
    return () => {
      // Cleanup any uploaded files if component unmounts during upload
      if (isUploading) {
        // Implement cleanup logic here if needed
        console.log("Cleaning up incomplete uploads");
      }
    };
  }, [isUploading]);

  const getProfileUpdateStatus = async (
    userId: string,
    supabase: any
  ): Promise<ProfileUpdateStatus> => {
    try {
      const { data, error } = await (supabase as any).rpc("get_profile_update_status", {
        p_user_id: userId,
      });

      if (error) {
        console.error("RPC Error:", error);
        throw new Error(error.message || "Error getting profile update status");
      }
      return data as unknown as ProfileUpdateStatus;
    } catch (error: any) {
      console.error("Error in getProfileUpdateStatus:", error);
      throw error;
    }
  };
    const validateFile = async (
    file: File,
    config: UploadConfig
  ): Promise<boolean> => {
    if (file.size > config.maxSize) {
      toast({
        title: "File too large",
        description: `File must be less than ${config.maxSize / 1024 / 1024}MB`,
        variant: "destructive",
      });
      return false;
    }

    if (!config.allowedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: "Please upload a JPEG, PNG, or WebP image",
        variant: "destructive",
      });
      return false;
    }

    if (config.dimensions) {
      return new Promise((resolve) => {
        const img = new Image();
        const objectUrl = URL.createObjectURL(file);

        img.onload = () => {
          URL.revokeObjectURL(objectUrl);
          const { minWidth, maxWidth, minHeight, maxHeight } = config.dimensions!;

          const dimensionsValid =
            (!minWidth || img.width >= minWidth) &&
            (!maxWidth || img.width <= maxWidth) &&
            (!minHeight || img.height >= minHeight) &&
            (!maxHeight || img.height <= maxHeight);

          if (!dimensionsValid) {
            const dimensionsText = `${minWidth}x${minHeight}${
              maxWidth ? ` to ${maxWidth}x${maxHeight}` : ""
            }`;

            toast({
              title: "Invalid image dimensions",
              description: `Image dimensions must be ${dimensionsText} pixels`,
              variant: "destructive",
            });
            resolve(false);
          }
          resolve(true);
        };

        img.onerror = () => {
          URL.revokeObjectURL(objectUrl);
          toast({
            title: "Error",
            description: "Failed to load image for validation",
            variant: "destructive",
          });
          resolve(false);
        };

        img.src = objectUrl;
      });
    }

    return true;
  };

  const isDefaultImageUrl = (url: string): boolean => {
    return url.includes(DEFAULT_AVATAR_URL);
  };

  const deleteOldImage = useCallback(
    async (folder: "avatar" | "banner", currentImageUrl: string | null) => {
      if (!currentImageUrl || isDefaultImageUrl(currentImageUrl)) return;

      try {
        const storageBaseUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/ctn/`;
        const filePath = currentImageUrl.replace(storageBaseUrl, "");
        const expectedPrefix = `${profile.user_id}/account/${folder}/`;

        if (!filePath.startsWith(expectedPrefix)) {
          throw new Error(`Invalid file path: ${filePath}`);
        }

        const { error } = await (supabase as any).storage.from("ctn").remove([filePath]);

        if (error) {
          console.error(`Error deleting old ${folder} image:`, error);
          toast({
            title: "Warning",
            description: `Could not delete old ${folder} image. This might result in unused files.`,
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error(`Error deleting old ${folder} image:`, error);
        toast({
          title: "Warning",
          description: `Could not delete old ${folder} image. This might result in unused files.`,
          variant: "destructive",
        });
      }
    },
    [profile.user_id, supabase]
  );

  const uploadImage = async (
    file: File,
    type: "avatar" | "banner"
  ): Promise<string | null> => {
    try {
      const folder = type;
      const extension = file.type.split("/")[1] || "jpg";
      const fileName = `${type}.${extension}`;
      const filePath = `${profile.user_id}/account/${folder}/${fileName}`;

      const { error: uploadError, data } = await (supabase as any).storage
        .from("ctn")
        .upload(filePath, file, {
          contentType: file.type,
          upsert: true,
        });

      if (uploadError) {
        throw uploadError;
      }

      const publicUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/ctn/${data.path}`;
      return publicUrl;
    } catch (error: any) {
      console.error(`Error uploading ${type} image:`, error);
      toast({
        title: "Error",
        description: `Could not upload ${type} image: ${error.message}`,
        variant: "destructive",
      });
      return null;
    }
  };

   const handleFileChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "avatar" | "banner"
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsUploading(true);
      const isValid = await validateFile(file, UPLOAD_CONFIG[type]);
      if (!isValid) return;

      const folder = type;
      const currentUrl =
        type === "avatar" ? formData.avatar_url : formData.banner_url;

      if (currentUrl) {
        await deleteOldImage(folder, currentUrl);
      }

      const url = await uploadImage(file, type);

      if (url) {
        const timestampedUrl = `${url}?t=${Date.now()}`;
        setFormData((prev) => ({
          ...prev,
          [`${type}_url`]: timestampedUrl,
          [`${type}_position`]: INITIAL_IMAGE_POSITION,
        }));
        setAdjustingImage(type);
      }
    } catch (error) {
      console.error(`Error handling ${type} upload:`, error);
      toast({
        title: "Error",
        description: `Failed to upload ${type}`,
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
      // Reset the input value to allow uploading the same file again
      e.target.value = "";
    }
  };

  const handleImagePositionSave = (
    position: ImagePosition,
    type: "avatar" | "banner"
  ) => {
    setFormData((prev) => ({
      ...prev,
      [`${type}_position`]: position,
    }));
    setAdjustingImage(null);
  };

  const handleUsernameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newUsername = e.target.value.trim();

    if (!usernameUpdateAllowed && newUsername !== originalUsername) {
      toast({
        title: "Username Update Restricted",
        description: `You can update your username in ${hoursUntilNextUpdate} hour${
          hoursUntilNextUpdate !== 1 ? "s" : ""
        }`,
        variant: "destructive",
      });
      return;
    }

    setUsername(newUsername);
    setFormData((prev) => ({ ...prev, username: newUsername }));
  };

  const checkUsername = async (
    username: string,
    currentUserId: string
  ): Promise<boolean> => {
    try {
      const { data: isAvailable, error: availabilityError } = await (supabase as any).rpc(
        "check_username_availability",
        {
          p_username: username,
          p_current_user_id: currentUserId,
        }
      );

      if (availabilityError) {
        throw availabilityError;
      }

      return Boolean(isAvailable);
    } catch (error: any) {
      console.error("Error in checkUsername:", error);
      throw new Error(
        error.message || "Failed to check username availability"
      );
    }
  };

  const validateForm = (): boolean => {
    if (!formData.username?.trim()) {
      toast({
        title: "Validation Error",
        description: "Username is required",
        variant: "destructive",
      });
      return false;
    }

    if (
      username !== originalUsername &&
      usernameAvailability === false
    ) {
      toast({
        title: "Username Unavailable",
        description: "This username is already taken",
        variant: "destructive",
      });
      return false;
    }

    if (!usernameUpdateAllowed && username !== originalUsername) {
      toast({
        title: "Username Update Restricted",
        description: `You can update your username in ${hoursUntilNextUpdate} hour${
          hoursUntilNextUpdate !== 1 ? "s" : ""
        }`,
        variant: "destructive",
      });
      return false;
    }

    return true;
  };
   const updateUserProfile = async (formData: FormData): Promise<Profile> => {
    try {
      const { data, error } = await (supabase as any).rpc("update_user_profile", {
        p_user_id: profile.user_id,
        p_username: formData.username,
        p_bio: formData.bio,
        p_avatar_url: formData.avatar_url,
        p_banner_url: formData.banner_url,
        p_avatar_position: formData.avatar_position,
        p_banner_position: formData.banner_position,
      });

      if (error) {
        throw new Error(error.message || "Error updating profile");
      }

      return data as unknown as Profile;
    } catch (error: any) {
      console.error("Error in updateUserProfile:", error);
      throw error;
    }
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    const previousFormData = { ...formData };

    try {
      setIsUploading(true);
      // Optimistic update
      onSave({ ...formData, username });

      const updatedProfile = await updateUserProfile(formData);
      onSave(updatedProfile);

      setHasUnsavedChanges(false);
      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
    } catch (error: any) {
      // Revert optimistic update
      setFormData(previousFormData);
      console.error("Error saving profile:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to update profile",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleCancelClick = () => {
    if (hasUnsavedChanges) {
      setShowCancelDialog(true);
    } else {
      onCancel();
    }
  };

  const handleConfirmCancel = () => {
    setShowCancelDialog(false);
    onCancel();
  };

  return (
    <div className="space-y-6 py-4">
      <ImagePositionAdjuster
        open={adjustingImage !== null}
        imageUrl={
          adjustingImage === "avatar"
            ? formData.avatar_url || ""
            : formData.banner_url || ""
        }
        aspectRatio={adjustingImage === "avatar" ? 1 : 3}
        type={adjustingImage || "avatar"}
        onSave={(position) => handleImagePositionSave(position, adjustingImage!)}
        onClose={() => setAdjustingImage(null)}
      />

      <div className="space-y-4">
        <div className="space-y-2">
          <h3 className="font-medium flex items-center gap-2">
            <span>Banner Image</span>
            <Badge variant="secondary" className="text-xs">
              Recommended: 1200x400px
            </Badge>
          </h3>
          <p className="text-sm text-muted-foreground">
            Upload a banner image for your profile (3:1 aspect ratio recommended)
          </p>
        </div>
        
        <div className="relative w-full h-64 group">
          <Input
            type="file"
            onChange={(e) => handleFileChange(e, "banner")}
            accept={UPLOAD_CONFIG.banner.allowedTypes.join(",")}
            disabled={isUploading}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
            aria-label="Upload banner image"
          />
          
          {isUploading && (
            <div className="absolute inset-0 bg-black/60 flex items-center justify-center z-20">
              <div className="text-center text-white space-y-2">
                <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto" />
                <span>Uploading banner...</span>
              </div>
            </div>
          )}
          
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center z-10">
            <div className="text-center text-white space-y-2">
              <div className="text-lg font-medium">
                {formData.banner_url ? "Change Banner" : "Upload Banner"}
              </div>
              <div className="text-sm opacity-80">
                Click to {formData.banner_url ? "replace" : "upload"}
              </div>
            </div>
          </div>
          
          {formData.banner_url ? (
            <div className="relative w-full h-full overflow-hidden rounded-lg border">
              <img
                src={formData.banner_url}
                alt="Profile banner"
                className="w-full h-full object-cover"
                style={{
                  transform: `translate(${formData.banner_position?.x || 0}px, ${
                    formData.banner_position?.y || 0
                  }px) scale(${formData.banner_position?.scale || 1})`,
                  transformOrigin: "center",
                }}
              />
              <Button
                variant="secondary"
                size="sm"
                className="absolute z-10 bottom-2 right-2 gap-2"
                onClick={() => setAdjustingImage("banner")}
                aria-label="Adjust banner position"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                </svg>
                Adjust Position
              </Button>
            </div>
          ) : (
            <div className="w-full h-full bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg border-2 border-dashed border-border flex items-center justify-center">
              <div className="text-center space-y-2">
                <svg className="w-12 h-12 text-muted-foreground mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <div className="space-y-1">
                  <div className="text-muted-foreground font-medium">
                    Click to upload banner
                  </div>
                  <div className="text-xs text-muted-foreground">
                    JPEG, PNG, or WebP • Max 5MB
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
            <div className="grid gap-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="font-medium flex items-center gap-2">
              <span>Profile Picture</span>
              <Badge variant="secondary" className="text-xs">
                Recommended: 200x200px minimum
              </Badge>
            </h3>
            <p className="text-sm text-muted-foreground">
              Upload a square profile picture that represents you
            </p>
          </div>
          
          <div className="flex items-center gap-6">
            <div className="relative group">
              <Input
                type="file"
                onChange={(e) => handleFileChange(e, "avatar")}
                accept={UPLOAD_CONFIG.avatar.allowedTypes.join(",")}
                disabled={isUploading}
                className="hidden"
                id="avatar-upload"
                aria-label="Upload profile picture"
              />

              <Label
                htmlFor="avatar-upload"
                className="w-32 h-32 border-4 border-background rounded-full cursor-pointer flex items-center justify-center bg-muted relative overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                title="Click to upload a new profile picture"
              >
                {formData.avatar_url ? (
                  <div className="relative w-full h-full overflow-hidden">
                    <img
                      src={formData.avatar_url}
                      alt="Profile avatar"
                      className="w-full h-full object-cover"
                      style={{
                        transform: `translate(${formData.avatar_position?.x || 0}px, ${
                          formData.avatar_position?.y || 0
                        }px) scale(${formData.avatar_position?.scale || 1})`,
                        transformOrigin: "center",
                      }}
                    />
                    <Button
                      variant="secondary"
                      size="sm"
                      className="z-10 absolute -bottom-1 -right-1 rounded-full w-8 h-8 p-0 shadow-lg"
                      onClick={(e) => {
                        e.preventDefault();
                        setAdjustingImage("avatar");
                      }}
                      aria-label="Adjust avatar position"
                      title="Adjust position and zoom"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />
                      </svg>
                    </Button>
                  </div>
                ) : (
                  <div className="text-center space-y-2">
                    <svg className="w-8 h-8 text-muted-foreground mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span className="text-muted-foreground text-xs">Upload</span>
                  </div>
                )}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <div className="text-center text-white space-y-1">
                    <div className="text-xs font-medium">
                      {formData.avatar_url ? "Change" : "Upload"}
                    </div>
                    <div className="text-xs opacity-80">
                      Click here
                    </div>
                  </div>
                </div>
              </Label>

              {isUploading && (
                <div className="absolute inset-0 bg-black/60 rounded-full flex items-center justify-center">
                  <div className="text-center text-white space-y-1">
                    <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin mx-auto" />
                    <span className="text-xs">Uploading...</span>
                  </div>
                </div>
              )}
            </div>

            <div className="flex-1 space-y-3">
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">
                  • Use a clear, high-quality image
                </div>
                <div className="text-sm text-muted-foreground">
                  • Square format works best (1:1 aspect ratio)
                </div>
                <div className="text-sm text-muted-foreground">
                  • Minimum size: 200x200 pixels
                </div>
                <div className="text-sm text-muted-foreground">
                  • Supported formats: JPEG, PNG, WebP
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label htmlFor="username" className="text-sm font-medium">
              Username
            </Label>
            {!usernameUpdateAllowed && (
              <span className="text-xs text-muted-foreground">
                Can update in {hoursUntilNextUpdate} hour
                {hoursUntilNextUpdate !== 1 ? "s" : ""}
              </span>
            )}
          </div>
          <Input
            id="username"
            type="text"
            value={username || ''}
            onChange={handleUsernameChange}
            placeholder="Your username"
            disabled={!usernameUpdateAllowed || profileStatusLoading}
            className={`${!usernameUpdateAllowed ? "opacity-50" : ""} ${
              profileStatusLoading ? "cursor-not-allowed" : ""
            }`}
            aria-describedby="username-status"
          />
          <div id="username-status" className="text-sm">
            {usernameChecking && (
              <p className="text-muted-foreground">Checking availability...</p>
            )}
            {usernameAvailability === false && (
              <p className="text-red-500">Username is not available.</p>
            )}
            {usernameAvailability === true && username !== originalUsername && (
              <p className="text-green-500">Username is available.</p>
            )}
          </div>
          {!usernameUpdateAllowed && username !== originalUsername && (
            <Alert variant="destructive" className="mt-2">
              <Info className="h-4 w-4" />
              <AlertTitle>Username Update Restricted</AlertTitle>
              <AlertDescription>
                You can only update your username once per day. Next update
                available in {hoursUntilNextUpdate} hour
                {hoursUntilNextUpdate !== 1 ? "s" : ""}.
              </AlertDescription>
            </Alert>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="bio" className="text-sm font-medium">
            Bio
          </Label>
          <Textarea
            id="bio"
            value={formData.bio || ""}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, bio: e.target.value }))
            }
            placeholder="Tell us about yourself"
            className="min-h-[100px]"
            aria-describedby="bio-description"
          />
          <p id="bio-description" className="text-xs text-muted-foreground">
            Brief description for your profile.
          </p>
        </div>

        <div className="flex gap-4 pt-4">
          <Button
            onClick={handleSave}
            disabled={
              isUploading ||
              !formData.username?.trim() ||
              (formData.username !== originalUsername && !usernameUpdateAllowed)
            }
          >
            {isUploading ? "Saving..." : "Save Changes"}
          </Button>

          <Button
            onClick={handleCancelClick}
            variant="outline"
            disabled={isUploading}
          >
            Cancel
          </Button>
        </div>
      </div>

      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogTitle>Discard Changes?</DialogTitle>
          <p>Are you sure you want to discard your unsaved changes?</p>
          <DialogFooter>
            <Button variant="destructive" onClick={handleConfirmCancel}>
              Discard Changes
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowCancelDialog(false)}
            >
              Continue Editing
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}