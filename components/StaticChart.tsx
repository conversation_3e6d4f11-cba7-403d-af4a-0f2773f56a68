"use client";

import React from 'react';
import {
  Responsive<PERSON><PERSON>r,
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ReferenceLine,
} from 'recharts';

export type ChartType = 'line' | 'area' | 'bar';

export interface ChartDataPoint {
  time: string;
  value: number;
  [key: string]: any;
}

export interface StaticChartProps {
  data: ChartDataPoint[];
  type?: ChartType;
  color?: string;
  gradientColors?: [string, string];
  width?: number | string;
  height?: number;
  showGrid?: boolean;
  showTooltip?: boolean;
  showLegend?: boolean;
  xAxisKey?: string;
  yAxisKey?: string;
  formatValue?: (value: number) => string;
  formatDate?: (date: string) => string;
  referenceLines?: Array<{
    y?: number;
    x?: string;
    stroke?: string;
    strokeDasharray?: string;
    label?: string;
  }>;
  multipleLines?: Array<{
    key: string;
    color: string;
    name: string;
  }>;
}

export function StaticChart({
  data,
  type = 'line',
  color = '#3b82f6',
  gradientColors,
  width = '100%',
  height = 300,
  showGrid = true,
  showTooltip = true,
  showLegend = false,
  xAxisKey = 'time',
  yAxisKey = 'value',
  formatValue,
  formatDate,
  referenceLines = [],
  multipleLines = [],
}: StaticChartProps) {
  // Format data for Recharts
  const formattedData = data.map((point, index) => ({
    ...point,
    formattedTime: formatDate ? formatDate(point.time) : new Date(point.time).toLocaleDateString(),
    index,
  }));

  // Default value formatter
  const defaultFormatValue = (value: number) => {
    if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
    if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
    if (value >= 1e3) return `$${(value / 1e3).toFixed(2)}K`;
    return value.toFixed(2);
  };

  const valueFormatter = formatValue || defaultFormatValue;

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
          <p className="text-sm font-medium">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {`${entry.name}: ${valueFormatter(entry.value)}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Render different chart types
  const renderChart = () => {
    const commonProps = {
      data: formattedData,
      margin: { top: 5, right: 30, left: 20, bottom: 5 },
    };

    switch (type) {
      case 'area':
        return (
          <AreaChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" className="opacity-30" />}
            <XAxis 
              dataKey="formattedTime" 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
              tickFormatter={valueFormatter}
            />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend />}
            
            {referenceLines.map((line, index) => (
              <ReferenceLine
                key={index}
                {...line}
                stroke={line.stroke || '#666'}
                strokeDasharray={line.strokeDasharray || '5 5'}
              />
            ))}
            
            {multipleLines.length > 0 ? (
              multipleLines.map((line) => (
                <Area
                  key={line.key}
                  type="monotone"
                  dataKey={line.key}
                  stroke={line.color}
                  fill={line.color}
                  fillOpacity={0.3}
                  name={line.name}
                />
              ))
            ) : (
              <Area
                type="monotone"
                dataKey={yAxisKey}
                stroke={color}
                fill={gradientColors ? `url(#gradient-${color.replace('#', '')})` : color}
                fillOpacity={0.3}
              />
            )}
            
            {gradientColors && (
              <defs>
                <linearGradient id={`gradient-${color.replace('#', '')}`} x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={gradientColors[0]} stopOpacity={0.8} />
                  <stop offset="95%" stopColor={gradientColors[1]} stopOpacity={0.1} />
                </linearGradient>
              </defs>
            )}
          </AreaChart>
        );

      case 'bar':
        return (
          <BarChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" className="opacity-30" />}
            <XAxis 
              dataKey="formattedTime" 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
              tickFormatter={valueFormatter}
            />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend />}
            
            {referenceLines.map((line, index) => (
              <ReferenceLine
                key={index}
                {...line}
                stroke={line.stroke || '#666'}
                strokeDasharray={line.strokeDasharray || '5 5'}
              />
            ))}
            
            {multipleLines.length > 0 ? (
              multipleLines.map((line) => (
                <Bar
                  key={line.key}
                  dataKey={line.key}
                  fill={line.color}
                  name={line.name}
                />
              ))
            ) : (
              <Bar dataKey={yAxisKey} fill={color} />
            )}
          </BarChart>
        );

      default: // line
        return (
          <LineChart {...commonProps}>
            {showGrid && <CartesianGrid strokeDasharray="3 3" className="opacity-30" />}
            <XAxis 
              dataKey="formattedTime" 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
            />
            <YAxis 
              tick={{ fontSize: 12 }}
              className="text-muted-foreground"
              tickFormatter={valueFormatter}
            />
            {showTooltip && <Tooltip content={<CustomTooltip />} />}
            {showLegend && <Legend />}
            
            {referenceLines.map((line, index) => (
              <ReferenceLine
                key={index}
                {...line}
                stroke={line.stroke || '#666'}
                strokeDasharray={line.strokeDasharray || '5 5'}
              />
            ))}
            
            {multipleLines.length > 0 ? (
              multipleLines.map((line) => (
                <Line
                  key={line.key}
                  type="monotone"
                  dataKey={line.key}
                  stroke={line.color}
                  strokeWidth={2}
                  dot={false}
                  name={line.name}
                />
              ))
            ) : (
              <Line
                type="monotone"
                dataKey={yAxisKey}
                stroke={color}
                strokeWidth={2}
                dot={false}
              />
            )}
          </LineChart>
        );
    }
  };

  return (
    <ResponsiveContainer width={width} height={height}>
      {renderChart()}
    </ResponsiveContainer>
  );
}

// Specialized components for common use cases
export function PriceChart({ data, ...props }: Omit<StaticChartProps, 'type' | 'formatValue'>) {
  return (
    <StaticChart
      {...props}
      data={data}
      type="line"
      formatValue={(value) => `$${value.toLocaleString(undefined, { maximumFractionDigits: 2 })}`}
    />
  );
}

export function VolumeChart({ data, ...props }: Omit<StaticChartProps, 'type' | 'formatValue'>) {
  return (
    <StaticChart
      {...props}
      data={data}
      type="bar"
      formatValue={(value) => {
        if (value >= 1e9) return `$${(value / 1e9).toFixed(2)}B`;
        if (value >= 1e6) return `$${(value / 1e6).toFixed(2)}M`;
        return `$${value.toLocaleString()}`;
      }}
    />
  );
}

export function PercentageChart({ data, ...props }: Omit<StaticChartProps, 'formatValue'>) {
  return (
    <StaticChart
      {...props}
      data={data}
      formatValue={(value) => `${value.toFixed(2)}%`}
    />
  );
}