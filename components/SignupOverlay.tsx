'use client';

import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Heart, MessageCircle, Star, Zap, Gift, UserPlus, ArrowRight } from 'lucide-react';
import Link from 'next/link';

interface SignupOverlayProps {
  isGuest: boolean;
  children: React.ReactNode;
}

export function SignupOverlay({ isGuest, children }: SignupOverlayProps) {
  const [showSignupModal, setShowSignupModal] = useState(false);

  const handleOverlayClick = () => {
    if (isGuest) {
      setShowSignupModal(true);
    }
  };

  return (
    <div className="relative">
      {/* Main content */}
      {children}

      {/* Full-screen overlay for guests */}
      {isGuest && (
        <div 
          className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40 cursor-pointer"
          onClick={handleOverlayClick}
        >
          <div className="absolute inset-0 flex items-center justify-center p-4">
            <div className="text-center text-white space-y-4 max-w-md">
              <div className="space-y-2">
                <h2 className="text-2xl font-bold">Welcome to CryptoTalks!</h2>
                <p className="text-white/80">
                  Click anywhere to join the conversation and unlock all features
                </p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-md rounded-lg p-4 space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Heart className="h-4 w-4 text-red-400" />
                  <span>Like and interact with articles</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Zap className="h-4 w-4 text-yellow-400" />
                  <span>Earn experience points and level up</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Gift className="h-4 w-4 text-green-400" />
                  <span>Send tips with Solana Pay</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <MessageCircle className="h-4 w-4 text-blue-400" />
                  <span>Join discussions and comment</span>
                </div>
              </div>

              <div className="animate-bounce">
                <ArrowRight className="h-8 w-8 mx-auto text-white/60 rotate-90" />
              </div>
              
              <p className="text-sm text-white/60">
                Click anywhere to get started
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Signup Modal */}
      <Dialog open={showSignupModal} onOpenChange={setShowSignupModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">Join CryptoTalks</DialogTitle>
            <DialogDescription className="text-center">
              Sign up to unlock all features and join the crypto conversation!
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-6">
            <div className="text-center">
              <img
                src="/ct_logoclr.png"
                alt="CryptoTalks"
                className="w-20 h-20 mx-auto mb-4"
              />
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-sm p-3 bg-red-50 dark:bg-red-950/20 rounded-lg">
                <Heart className="h-5 w-5 text-red-500 flex-shrink-0" />
                <span>Like and interact with articles to support authors</span>
              </div>
              <div className="flex items-center gap-3 text-sm p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
                <Zap className="h-5 w-5 text-yellow-500 flex-shrink-0" />
                <span>Earn experience points and unlock higher tiers</span>
              </div>
              <div className="flex items-center gap-3 text-sm p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                <Gift className="h-5 w-5 text-green-500 flex-shrink-0" />
                <span>Send cryptocurrency tips with Solana Pay</span>
              </div>
              <div className="flex items-center gap-3 text-sm p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <MessageCircle className="h-5 w-5 text-blue-500 flex-shrink-0" />
                <span>Comment and join engaging discussions</span>
              </div>
              <div className="flex items-center gap-3 text-sm p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
                <Star className="h-5 w-5 text-purple-500 flex-shrink-0" />
                <span>Access premium features and analytics</span>
              </div>
            </div>

            <div className="space-y-3">
              <Button asChild className="w-full" size="lg">
                <Link href="/signup">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Create Free Account
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full">
                <Link href="/signin">
                  Already have an account? Sign In
                </Link>
              </Button>
            </div>

            <div className="text-center text-xs text-muted-foreground">
              <p>Free to join • No credit card required</p>
              <p>Join the CryptoTalks Network - Start talking about crypto</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
