// ZStrategyPanePrimitive.ts
// Implements a Lightweight Charts v5 Pane Primitive for ZStrategy overlays

export interface ZSignal {
  timestamp: number;
  stLine: number;
  vhma?: number;
  price?: number;
  buy?: boolean;
  sell?: boolean;
  filtersPass?: boolean;
}

export interface PaneViewAPI {
  timeToCoordinate: (time: number) => number | null;
  priceToCoordinate: (price: number) => number | null;
}

export interface PaneRenderingContext {
  mediaSize: { width: number; height: number };
  bitmapSize: { width: number; height: number };
  horizontalPixelRatio: number;
  verticalPixelRatio: number;
}

// Enhanced ZStrategy Pane Primitive for Lightweight Charts v5.0
export class ZStrategyPanePrimitive {
  private signals: ZSignal[];
  public zOrder: number;
  private _requestUpdate?: () => void;
  private _paneView: PaneViewAPI | null = null;

  constructor(signals: ZSignal[], zOrder: number = 1) {
    this.signals = signals || [];
    this.zOrder = zOrder; // Lower value to render behind candlesticks
  }

  // Update signals and request redraw
  updateSignals(newSignals: ZSignal[]) {
    this.signals = newSignals || [];
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }

  // Lightweight Charts v5.0 Pane Primitive interface
  attached(param: { chart: any; series: any; requestUpdate: () => void }) {
    console.log('ZStrategy primitive attached to chart');
    this._requestUpdate = param.requestUpdate;
    
    // Request initial update to trigger draw
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }

  detached() {
    console.log('ZStrategy primitive detached from chart');
    this._requestUpdate = undefined;
    this._paneView = null;
  }

  // Main render method called by Lightweight Charts
  paneViews(): Array<{
    zOrder: () => number;
    renderer: (height: number, width: number, addAnchors?: any) => {
      draw: (target: any, isHovered?: boolean, hitTestData?: any) => void;
    };
  }> {
    return [
      {
        zOrder: () => this.zOrder,
        renderer: (height: number, width: number) => ({
          draw: (target: any, isHovered: boolean = false) => {
            try {
              // Store pane view for coordinate mapping
              this._paneView = this.createPaneView(target, height, width);
              this.draw(target, isHovered);
            } catch (error) {
              console.error('Error in ZStrategy pane primitive draw:', error);
            }
          }
        })
      }
    ];
  }

  // Create pane view with proper coordinate mapping
  private createPaneView(_ctx: any, height: number, width: number): PaneViewAPI {
    // In v5.0, we need to get the coordinate mapping from the chart context
    // This is a simplified implementation that assumes the chart provides these methods
    return {
      timeToCoordinate: (time: number) => {
        // This would be provided by the chart's time scale
        // For now, we'll use a basic linear mapping
        if (this.signals.length < 2) return null;
        
        const minTime = Math.min(...this.signals.map(s => s.timestamp / 1000));
        const maxTime = Math.max(...this.signals.map(s => s.timestamp / 1000));
        const timeRange = maxTime - minTime;
        
        if (timeRange === 0) return width / 2;
        
        const normalized = (time - minTime) / timeRange;
        return Math.max(0, Math.min(width, normalized * width));
      },
      priceToCoordinate: (price: number) => {
        // This would be provided by the chart's price scale
        // For now, we'll use a basic linear mapping
        if (this.signals.length === 0) return null;
        
        const prices = this.signals.map(s => s.stLine).filter(p => !isNaN(p) && isFinite(p));
        if (prices.length === 0) return null;
        
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);
        const priceRange = maxPrice - minPrice;
        
        if (priceRange === 0) return height / 2;
        
        const normalized = 1 - (price - minPrice) / priceRange; // Inverted for canvas coordinates
        return Math.max(0, Math.min(height, normalized * height));
      }
    };
  }

  // Enhanced drawing method with better visual indicators
  private draw(ctx: any, isHovered: boolean) {
    // Validate that we have a proper canvas context
    if (!ctx || typeof ctx.save !== 'function' || typeof ctx.restore !== 'function') {
      console.warn('Invalid canvas context provided to ZStrategy primitive');
      return;
    }
    
    if (!this.signals || this.signals.length === 0) return;

    const paneView = this.getPaneView();
    if (!paneView) return;

    // Draw SuperTrend line with gradient effect
    this.drawSuperTrendLine(ctx, paneView);
    
    // Draw VHMA line if available
    this.drawVHMALine(ctx, paneView);
    
    // Draw buy/sell signals with enhanced styling
    this.drawTradingSignals(ctx, paneView, isHovered);
    
  }

  private getPaneView(): PaneViewAPI | null {
    return this._paneView;
  }

  private drawSuperTrendLine(ctx: any, paneView: PaneViewAPI) {
    if (this.signals.length < 2) return;
    
    // Additional safety check
    if (!ctx || typeof ctx.save !== 'function') {
      console.warn('Invalid canvas context in drawSuperTrendLine');
      return;
    }

    ctx.save();
    
    // Create gradient for SuperTrend line
    const validPoints = this.signals
      .map((sig) => ({
        x: paneView.timeToCoordinate(sig.timestamp / 1000),
        y: paneView.priceToCoordinate(sig.stLine),
        signal: sig
      }))
      .filter(point => point.x !== null && point.y !== null);

    if (validPoints.length < 2) {
      ctx.restore();
      return;
    }

    // Draw SuperTrend line with trend-based coloring (thinner to not obscure candles)
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    validPoints.forEach((point, i) => {
      if (i === 0) return;
      
      const prevPoint = validPoints[i - 1];
      const isUpTrend = point.signal.stLine < point.signal.price!;
      
      ctx.strokeStyle = isUpTrend ? '#10b981' : '#ef4444'; // Green for uptrend, red for downtrend
      ctx.beginPath();
      ctx.moveTo(prevPoint.x!, prevPoint.y!);
      ctx.lineTo(point.x!, point.y!);
      ctx.stroke();
    });
    
    ctx.restore();
  }

  private drawVHMALine(ctx: any, paneView: PaneViewAPI) {
    const vhmaPoints = this.signals
      .filter(sig => sig.vhma !== undefined)
      .map(sig => ({
        x: paneView.timeToCoordinate(sig.timestamp / 1000),
        y: paneView.priceToCoordinate(sig.vhma!),
        vhma: sig.vhma
      }))
      .filter(point => point.x !== null && point.y !== null);

    if (vhmaPoints.length < 2) return;

    ctx.save();
    ctx.strokeStyle = '#8b5cf6'; // Purple for VHMA
    ctx.lineWidth = 1.5;
    ctx.setLineDash([3, 3]); // Smaller dashes, thinner line
    ctx.lineCap = 'round';

    ctx.beginPath();
    vhmaPoints.forEach((point, i) => {
      if (i === 0) {
        ctx.moveTo(point.x!, point.y!);
      } else {
        ctx.lineTo(point.x!, point.y!);
      }
    });
    ctx.stroke();
    ctx.restore();
  }

  private drawTradingSignals(ctx: any, paneView: PaneViewAPI, isHovered: boolean) {
    this.signals.forEach((sig) => {
      const x = paneView.timeToCoordinate(sig.timestamp / 1000);
      const y = paneView.priceToCoordinate(sig.price || sig.stLine);
      
      if (x === null || y === null) return;

      // Enhanced buy signals
      if (sig.buy && sig.filtersPass) {
        this.drawSignalMarker(ctx, x, y, 'buy', isHovered);
        this.drawSignalLabel(ctx, x, y, 'BUY', 'buy');
      }

      // Enhanced sell signals
      if (sig.sell && sig.filtersPass) {
        this.drawSignalMarker(ctx, x, y, 'sell', isHovered);
        this.drawSignalLabel(ctx, x, y, 'SELL', 'sell');
      }

      // Draw filtered signals with different opacity
      if ((sig.buy || sig.sell) && !sig.filtersPass) {
        this.drawFilteredSignal(ctx, x, y, sig.buy ? 'buy' : 'sell');
      }
    });
  }

  private drawSignalMarker(ctx: any, x: number, y: number, type: 'buy' | 'sell', isHovered: boolean) {
    ctx.save();
    
    const size = isHovered ? 10 : 8;
    const yOffset = type === 'buy' ? -15 : 15;
    const markerY = y + yOffset;

    // Outer glow effect
    ctx.shadowColor = type === 'buy' ? '#10b981' : '#ef4444';
    ctx.shadowBlur = 8;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;

    // Draw marker background
    ctx.fillStyle = type === 'buy' ? '#10b981' : '#ef4444';
    ctx.beginPath();
    
    if (type === 'buy') {
      // Triangle pointing up for buy
      ctx.moveTo(x, markerY - size);
      ctx.lineTo(x - size, markerY + size);
      ctx.lineTo(x + size, markerY + size);
      ctx.closePath();
    } else {
      // Triangle pointing down for sell
      ctx.moveTo(x, markerY + size);
      ctx.lineTo(x - size, markerY - size);
      ctx.lineTo(x + size, markerY - size);
      ctx.closePath();
    }
    
    ctx.fill();

    // Draw border
    ctx.shadowBlur = 0;
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    ctx.restore();
  }

  private drawSignalLabel(ctx: any, x: number, y: number, text: string, type: 'buy' | 'sell') {
    ctx.save();
    
    const yOffset = type === 'buy' ? -35 : 35;
    const labelY = y + yOffset;
    
    ctx.font = 'bold 10px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Draw text background
    const textWidth = ctx.measureText(text).width;
    const padding = 4;
    ctx.fillStyle = type === 'buy' ? '#dcfce7' : '#fee2e2';
    ctx.strokeStyle = type === 'buy' ? '#10b981' : '#ef4444';
    ctx.lineWidth = 1;
    
    ctx.fillRect(x - textWidth/2 - padding, labelY - 8, textWidth + padding * 2, 16);
    ctx.strokeRect(x - textWidth/2 - padding, labelY - 8, textWidth + padding * 2, 16);
    
    // Draw text
    ctx.fillStyle = type === 'buy' ? '#065f46' : '#991b1b';
    ctx.fillText(text, x, labelY);
    
    ctx.restore();
  }

  private drawFilteredSignal(ctx: any, x: number, y: number, type: 'buy' | 'sell') {
    ctx.save();
    ctx.globalAlpha = 0.3; // Low opacity for filtered signals
    
    const size = 6;
    const yOffset = type === 'buy' ? -12 : 12;
    const markerY = y + yOffset;

    ctx.fillStyle = type === 'buy' ? '#10b981' : '#ef4444';
    ctx.beginPath();
    ctx.arc(x, markerY, size, 0, 2 * Math.PI);
    ctx.fill();
    
    ctx.restore();
  }


  // Additional utility methods for advanced features
  hitTest(): any {
    // Implement hit testing for interactive features
    // This would allow clicking on signals for more details
    return null;
  }

  autoscaleInfo(): { priceRange: { minValue: number; maxValue: number } } | null {
    if (this.signals.length === 0) return null;

    const prices = this.signals
      .map(sig => sig.stLine)
      .filter(price => !isNaN(price) && isFinite(price));

    if (prices.length === 0) return null;

    return {
      priceRange: {
        minValue: Math.min(...prices) * 0.99,
        maxValue: Math.max(...prices) * 1.01
      }
    };
  }
}
