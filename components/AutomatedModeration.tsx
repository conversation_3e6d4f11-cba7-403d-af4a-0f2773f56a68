"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  X, 
  Eye,
  TrendingUp,
  Users,
  Flag,
  Zap,
  Target
} from "lucide-react";
import { UserProfile } from "@/types";
import { toast } from "@/hooks/use-toast";

interface AutomatedModerationProps {
  userProfile: UserProfile;
}

interface ModerationFlag {
  flag_id: number;
  content_type: 'article' | 'comment';
  content_id: number;
  content_preview: string;
  author_username: string;
  flag_type: string;
  rule_name: string;
  severity: number;
  confidence_score: number;
  created_at: string;
  moderator_reviewed: boolean;
}

interface ModerationStats {
  total_flags: number;
  pending_review: number;
  auto_removed: number;
  false_positives: number;
  avg_confidence: number;
}

interface TrustScoreUser {
  user_id: string;
  username: string;
  trust_score: number;
  spam_reports: number;
  valid_contributions: number;
  last_violation: string | null;
}

export default function AutomatedModeration({ userProfile }: AutomatedModerationProps) {
  const [flags, setFlags] = useState<ModerationFlag[]>([]);
  const [stats, setStats] = useState<ModerationStats | null>(null);
  const [lowTrustUsers, setLowTrustUsers] = useState<TrustScoreUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFlag, setSelectedFlag] = useState<ModerationFlag | null>(null);
  const [moderatorNotes, setModeratorNotes] = useState("");
  const [filterSeverity, setFilterSeverity] = useState("1");
  
  const supabase = createClient();

  const loadModerationData = useCallback(async () => {
    setIsLoading(true);
    try {
      // Load moderation queue
      const { data: flagsData, error: flagsError } = await (supabase as any).rpc(
        "get_moderation_queue",
        {
          p_limit: 50,
          p_offset: 0,
          p_severity_min: parseInt(filterSeverity)
        }
      );

      if (flagsError) throw flagsError;
      setFlags(flagsData || []);

      // Load moderation statistics
      const { data: statsData, error: statsError } = await (supabase as any)
        .from("content_flags")
        .select("*");

      if (statsError) throw statsError;

      interface RawContentFlag {
        moderator_reviewed: boolean;
        moderator_action?: 'removed' | 'approved' | 'edited';
        confidence_score?: number;
      }

      const processedStats: ModerationStats = {
        total_flags: (statsData as RawContentFlag[])?.length || 0,
        pending_review: (statsData as RawContentFlag[])?.filter((f: RawContentFlag) => !f.moderator_reviewed)?.length || 0,
        auto_removed: (statsData as RawContentFlag[])?.filter((f: RawContentFlag) => f.moderator_action === 'removed')?.length || 0,
        false_positives: (statsData as RawContentFlag[])?.filter((f: RawContentFlag) => f.moderator_action === 'approved')?.length || 0,
        avg_confidence: (statsData as RawContentFlag[])?.reduce((sum: number, f: RawContentFlag) => sum + (f.confidence_score || 0), 0) / ((statsData as RawContentFlag[])?.length || 1) || 0
      };
      setStats(processedStats);

      // Load low trust score users
      const { data: trustData, error: trustError } = await (supabase as any)
        .from("user_trust_scores")
        .select(`
          user_id,
          trust_score,
          spam_reports,
          valid_contributions,
          last_violation,
          users!inner(username)
        `)
        .lt("trust_score", 30)
        .order("trust_score", { ascending: true })
        .limit(10);

      if (trustError) throw trustError;

      interface TrustDataItem {
        user_id: string;
        trust_score: number;
        spam_reports: number;
        valid_contributions: number;
        last_violation: string | null;
        users: {
          username: string;
        };
      }

      const formattedTrustData: TrustScoreUser[] = (trustData as TrustDataItem[])?.map((item: TrustDataItem) => ({
        user_id: item.user_id,
        username: item.users?.username || "Unknown",
        trust_score: item.trust_score,
        spam_reports: item.spam_reports,
        valid_contributions: item.valid_contributions,
        last_violation: item.last_violation
      })) || [];

      setLowTrustUsers(formattedTrustData);

    } catch (error) {
      console.error("Error loading moderation data:", error);
      toast({
        title: "Error",
        description: "Failed to load moderation data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [supabase, filterSeverity]);

  useEffect(() => {
    loadModerationData();
  }, [loadModerationData]);

  const handleFlagAction = async (flagId: number, action: 'approved' | 'removed' | 'edited') => {
    try {
      const { error } = await (supabase as any)
        .from("content_flags")
        .update({
          moderator_reviewed: true,
          moderator_action: action,
          moderator_id: userProfile.user_id,
          moderator_notes: moderatorNotes || null
        })
        .eq("id", flagId);

      if (error) throw error;

      // Update user trust score based on action
      if (selectedFlag) {
        if (action === 'removed') {
          // Decrease trust score for the content author
          await (supabase as any).rpc("update_user_trust_score", {
            p_user_id: selectedFlag.author_username, // This should be user_id, not username
            p_action: "violation",
            p_severity: selectedFlag.severity
          });
        } else if (action === 'approved') {
          // This was a false positive, might want to adjust the rule
          // Could also slightly increase the author's trust score
        }
      }

      await loadModerationData();
      setSelectedFlag(null);
      setModeratorNotes("");

      toast({
        title: "Success",
        description: `Flag ${action} successfully`,
      });
    } catch (error) {
      console.error("Error handling flag action:", error);
      toast({
        title: "Error",
        description: "Failed to process flag action",
        variant: "destructive",
      });
    }
  };

  const getSeverityColor = (severity: number) => {
    switch (severity) {
      case 1: return "bg-yellow-100 text-yellow-800";
      case 2: return "bg-orange-100 text-orange-800";
      case 3: return "bg-red-100 text-red-800";
      case 4: return "bg-red-200 text-red-900";
      case 5: return "bg-red-300 text-red-950";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getSeverityLabel = (severity: number) => {
    switch (severity) {
      case 1: return "Low";
      case 2: return "Medium";
      case 3: return "High";
      case 4: return "Critical";
      case 5: return "Severe";
      default: return "Unknown";
    }
  };

  if (isLoading) {
    return (
      <div className="container max-w-6xl mx-auto py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container max-w-6xl mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Shield className="w-8 h-8" />
            Automated Moderation
          </h1>
          <p className="text-muted-foreground">
            AI-powered content filtering and community safety tools
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Select value={filterSeverity} onValueChange={setFilterSeverity}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Min Severity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">All Severities</SelectItem>
              <SelectItem value="2">Medium+</SelectItem>
              <SelectItem value="3">High+</SelectItem>
              <SelectItem value="4">Critical+</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={loadModerationData} variant="outline">
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Flag className="h-4 w-4 text-muted-foreground" />
                <div className="ml-2">
                  <p className="text-2xl font-bold">{stats.total_flags}</p>
                  <p className="text-xs text-muted-foreground">Total Flags</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Eye className="h-4 w-4 text-yellow-600" />
                <div className="ml-2">
                  <p className="text-2xl font-bold">{stats.pending_review}</p>
                  <p className="text-xs text-muted-foreground">Pending Review</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <X className="h-4 w-4 text-red-600" />
                <div className="ml-2">
                  <p className="text-2xl font-bold">{stats.auto_removed}</p>
                  <p className="text-xs text-muted-foreground">Removed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <div className="ml-2">
                  <p className="text-2xl font-bold">{stats.false_positives}</p>
                  <p className="text-xs text-muted-foreground">False Positives</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center">
                <Target className="h-4 w-4 text-blue-600" />
                <div className="ml-2">
                  <p className="text-2xl font-bold">{(stats.avg_confidence * 100).toFixed(0)}%</p>
                  <p className="text-xs text-muted-foreground">Avg Confidence</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Moderation Queue */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Moderation Queue
              </CardTitle>
            </CardHeader>
            <CardContent>
              {flags.length > 0 ? (
                <div className="space-y-4">
                  {flags.map((flag) => (
                    <div
                      key={flag.flag_id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedFlag?.flag_id === flag.flag_id
                          ? "border-primary bg-primary/5"
                          : "hover:bg-muted/50"
                      }`}
                      onClick={() => setSelectedFlag(flag)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-2">
                            <Badge variant="outline">
                              {flag.content_type}
                            </Badge>
                            <Badge className={getSeverityColor(flag.severity)}>
                              {getSeverityLabel(flag.severity)}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              by @{flag.author_username}
                            </span>
                          </div>
                          <p className="text-sm mb-2 line-clamp-2">
                            {flag.content_preview}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>Rule: {flag.rule_name}</span>
                            <span>Confidence: {(flag.confidence_score * 100).toFixed(0)}%</span>
                            <span>{new Date(flag.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle className="w-12 h-12 mx-auto mb-4" />
                  <p>No flags pending review</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Sidebar */}
        <div className="space-y-6">
          {/* Selected Flag Actions */}
          {selectedFlag && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Review Flag</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Content Preview</h4>
                  <p className="text-sm bg-muted p-3 rounded">
                    {selectedFlag.content_preview}
                  </p>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Moderator Notes</h4>
                  <Textarea
                    value={moderatorNotes}
                    onChange={(e) => setModeratorNotes(e.target.value)}
                    placeholder="Add notes about your decision..."
                    rows={3}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Button
                    onClick={() => handleFlagAction(selectedFlag.flag_id, 'approved')}
                    variant="outline"
                    className="w-full"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Approve (False Positive)
                  </Button>
                  <Button
                    onClick={() => handleFlagAction(selectedFlag.flag_id, 'removed')}
                    variant="destructive"
                    className="w-full"
                  >
                    <X className="w-4 h-4 mr-2" />
                    Remove Content
                  </Button>
                  <Button
                    onClick={() => handleFlagAction(selectedFlag.flag_id, 'edited')}
                    variant="secondary"
                    className="w-full"
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Request Edit
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Low Trust Users */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Low Trust Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              {lowTrustUsers.length > 0 ? (
                <div className="space-y-3">
                  {lowTrustUsers.map((user) => (
                    <div key={user.user_id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">@{user.username}</p>
                        <p className="text-xs text-muted-foreground">
                          {user.spam_reports} reports • {user.valid_contributions} contributions
                        </p>
                      </div>
                      <Badge 
                        variant="outline"
                        className={user.trust_score < 10 ? "text-red-600" : "text-orange-600"}
                      >
                        {user.trust_score.toFixed(0)}
                      </Badge>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No low trust users found
                </p>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="w-5 h-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <TrendingUp className="w-4 h-4 mr-2" />
                View Trending Flags
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Users className="w-4 h-4 mr-2" />
                Export User Reports
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Shield className="w-4 h-4 mr-2" />
                Update Rules
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
