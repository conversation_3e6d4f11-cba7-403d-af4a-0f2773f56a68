"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { InteractiveMetricChart } from '@/components/InteractiveMetricChart';
import { getAllCoinsData, type CentralizedCoinData } from '@/utils/mock-data-service';
import { 
  Crown, 
  Zap, 
  TrendingUp, 
  BarChart3, 
  RefreshCw,
  Download,
  Play,
  Award,
  Target,
  Activity,
  Users,
  DollarSign,
  Shield,
  Code
} from 'lucide-react';

interface ChainMetrics {
  id: string;
  name: string;
  symbol: string;
  logo: string;
  dailyActiveAddresses: number;
  dailyTransactions: number;
  tvl: number;
  fees: number;
  stakedSupply: number;
  newAddresses: number;
  transactionVolume: number;
  fdmc: number;
  marketCap: number;
  inflation: { mom: number; yoy: number };
  circulatingSupply: number;
  maxTps: number;
  realTimeTps: number;
  theoreticalTps: number;
  blockSize: number;
  avgFee: number;
  finalityTime: number;
  energyPerTx: number;
  validators: number;
  activeDevelopers: number;
  price: number;
}

interface ComparisonResult extends ChainMetrics {
  score: number;
  breakdown: {
    technical: number;
    economic: number;
    adoption: number;
    security: number;
  };
  rank: number;
}

interface AutomatedComparisonToolProps {
  isActive?: boolean;
  onRefresh?: () => void;
}

// Mock data for available chains - Updated to current market conditions (July 2024)
const AVAILABLE_CHAINS: ChainMetrics[] = [
  {
    id: 'bitcoin',
    name: 'Bitcoin',
    symbol: 'BTC',
    logo: '₿',
    dailyActiveAddresses: 1200000, // Increased adoption
    dailyTransactions: 320000,
    tvl: 0, // Bitcoin doesn't have DeFi TVL
    fees: 2800000, // Higher fees due to increased activity
    stakedSupply: 0, // Bitcoin doesn't have staking
    newAddresses: 15000,
    transactionVolume: 4200000000, // Higher volume
    fdmc: 2366967578877, // Current market cap from CMC
    marketCap: 2366967578877,
    inflation: { mom: 0.05, yoy: 0.9 }, // Post-halving very low inflation
    circulatingSupply: 19890000,
    maxTps: 7,
    realTimeTps: 4.8, // Slightly improved
    theoreticalTps: 7,
    blockSize: 1000000,
    avgFee: 8.5, // Higher average fees
    finalityTime: 600,
    energyPerTx: 741000000,
    validators: 15000, // Mining pools
    activeDevelopers: 1200, // Growing developer ecosystem
    price: 118953.53
  },
  {
    id: 'ethereum',
    name: 'Ethereum',
    symbol: 'ETH',
    logo: 'Ξ',
    dailyActiveAddresses: 450000,
    dailyTransactions: 1200000,
    tvl: 28500000000,
    fees: 3400000,
    stakedSupply: 32000000,
    newAddresses: 25000,
    transactionVolume: 8900000000,
    fdmc: 451138108044,
    marketCap: 451138108044,
    inflation: { mom: -0.12, yoy: -0.3 },
    circulatingSupply: 120710000,
    maxTps: 15,
    realTimeTps: 12.8,
    theoreticalTps: 15,
    blockSize: 30000,
    avgFee: 2.8,
    finalityTime: 12.8,
    energyPerTx: 62000,
    validators: 900000,
    activeDevelopers: 2847,
    price: 3737.52
  },
  {
    id: 'solana',
    name: 'Solana',
    symbol: 'SOL',
    logo: '◎',
    dailyActiveAddresses: 180000,
    dailyTransactions: 28000000,
    tvl: 1200000000,
    fees: 45000,
    stakedSupply: 385000000,
    newAddresses: 8500,
    transactionVolume: 420000000,
    fdmc: 101408443821,
    marketCap: 101408443821,
    inflation: { mom: 0.35, yoy: 5.2 },
    circulatingSupply: 538190000,
    maxTps: 65000,
    realTimeTps: 2400,
    theoreticalTps: 65000,
    blockSize: 1232,
    avgFee: 0.00025,
    finalityTime: 0.4,
    energyPerTx: 1837,
    validators: 3200,
    activeDevelopers: 1247,
    price: 188.42
  },
  {
    id: 'cardano',
    name: 'Cardano',
    symbol: 'ADA',
    logo: '₳',
    dailyActiveAddresses: 85000,
    dailyTransactions: 95000,
    tvl: 145000000,
    fees: 8500,
    stakedSupply: 24500000000,
    newAddresses: 3200,
    transactionVolume: 125000000,
    fdmc: 15800000000,
    marketCap: 15800000000,
    inflation: { mom: 0.3, yoy: 4.1 },
    circulatingSupply: 35000000000,
    maxTps: 250,
    realTimeTps: 7.8,
    theoreticalTps: 250,
    blockSize: 65536,
    avgFee: 0.17,
    finalityTime: 300,
    energyPerTx: 6000,
    validators: 2800,
    activeDevelopers: 456,
    price: 0.45
  },
  {
    id: 'polkadot',
    name: 'Polkadot',
    symbol: 'DOT',
    logo: '●',
    dailyActiveAddresses: 42000,
    dailyTransactions: 180000,
    tvl: 890000000,
    fees: 18000,
    stakedSupply: 685000000,
    newAddresses: 1800,
    transactionVolume: 290000000,
    fdmc: 7200000000,
    marketCap: 7200000000,
    inflation: { mom: 0.4, yoy: 8.7 },
    circulatingSupply: 1400000000,
    maxTps: 1500,
    realTimeTps: 166,
    theoreticalTps: 1500,
    blockSize: 5242880,
    avgFee: 0.1,
    finalityTime: 6,
    energyPerTx: 126000,
    validators: 1200,
    activeDevelopers: 623,
    price: 5.15
  },
  {
    id: 'avalanche',
    name: 'Avalanche',
    symbol: 'AVAX',
    logo: '🔺',
    dailyActiveAddresses: 65000,
    dailyTransactions: 850000,
    tvl: 780000000,
    fees: 125000,
    stakedSupply: 245000000,
    newAddresses: 4200,
    transactionVolume: 380000000,
    fdmc: 12500000000,
    marketCap: 12500000000,
    inflation: { mom: 0.6, yoy: 7.2 },
    circulatingSupply: 365000000,
    maxTps: 4500,
    realTimeTps: 850,
    theoreticalTps: 4500,
    blockSize: 2000000,
    avgFee: 0.15,
    finalityTime: 1.5,
    energyPerTx: 47000,
    validators: 1800,
    activeDevelopers: 892,
    price: 34.25
  }
];

// Advanced scoring algorithm
const calculateAdvancedScore = (chain: ChainMetrics, weights: Record<string, number>) => {
  const scores = {
    technical: 0,
    economic: 0,
    adoption: 0,
    security: 0
  };

  // Technical Score (0-100)
  const tpsEfficiency = Math.min((chain.realTimeTps / chain.theoreticalTps) * 100, 100);
  const finalityScore = Math.max(100 - Math.log10(chain.finalityTime + 1) * 20, 0);
  const energyScore = Math.max(100 - Math.log10(chain.energyPerTx) * 10, 0);
  scores.technical = (tpsEfficiency * 0.4 + finalityScore * 0.3 + energyScore * 0.3);

  // Economic Score (0-100)
  const feeScore = Math.max(100 - chain.avgFee * 50, 0);
  const tvlScore = Math.min(Math.log10(Math.max(chain.tvl, 1)) * 10, 100);
  const volumeScore = Math.min(Math.log10(chain.transactionVolume) * 8, 100);
  scores.economic = (feeScore * 0.4 + tvlScore * 0.3 + volumeScore * 0.3);

  // Adoption Score (0-100)
  const daaScore = Math.min(Math.log10(chain.dailyActiveAddresses) * 15, 100);
  const txScore = Math.min(Math.log10(chain.dailyTransactions) * 12, 100);
  const devScore = Math.min(Math.log10(chain.activeDevelopers) * 20, 100);
  scores.adoption = (daaScore * 0.4 + txScore * 0.3 + devScore * 0.3);

  // Security Score (0-100)
  const validatorScore = Math.min(Math.log10(chain.validators) * 25, 100);
  const stakingScore = Math.min((chain.stakedSupply / chain.circulatingSupply) * 100, 100);
  const mcScore = Math.min(Math.log10(chain.marketCap / 1e9) * 20, 100);
  scores.security = (validatorScore * 0.4 + stakingScore * 0.3 + mcScore * 0.3);

  // Calculate weighted total
  const totalScore = 
    scores.technical * weights.technical +
    scores.economic * weights.economic +
    scores.adoption * weights.adoption +
    scores.security * weights.security;

  return {
    total: Math.round(totalScore),
    breakdown: {
      technical: Math.round(scores.technical),
      economic: Math.round(scores.economic),
      adoption: Math.round(scores.adoption),
      security: Math.round(scores.security)
    }
  };
};

export function AutomatedComparisonTool({ onRefresh }: AutomatedComparisonToolProps) {
  const [availableChains, setAvailableChains] = useState<ChainMetrics[]>([]);
  const [isLoadingChains, setIsLoadingChains] = useState(true);

  // Load chains from centralized service
  useEffect(() => {
    const loadChains = async () => {
      try {
        setIsLoadingChains(true);
        const coinsData = await getAllCoinsData();
        const supportedChains = convertSupportedCoinsToChains(coinsData);
        setAvailableChains(supportedChains);
      } catch (error) {
        console.error('Failed to load chains data:', error);
        // Fallback to static data if service fails
        setAvailableChains(AVAILABLE_CHAINS);
      } finally {
        setIsLoadingChains(false);
      }
    };
    loadChains();
  }, []);

  // Convert supported coins from centralized service to ChainMetrics format
  const convertSupportedCoinsToChains = (coins: CentralizedCoinData[]): ChainMetrics[] => {
    // Only include blockchains with comprehensive on-chain metrics
    const supportedBlockchains = ['BTC', 'ETH', 'XRP', 'BNB', 'SOL', 'DOGE', 'ADA', 'SUI', 'SEI'];
    
    return coins
      .filter(coin => supportedBlockchains.includes(coin.symbol))
      .map(coin => ({
        id: coin.symbol.toLowerCase(),
        name: coin.name,
        symbol: coin.symbol,
        logo: getChainLogo(coin.symbol),
        dailyActiveAddresses: coin.activeAddresses24h || 0,
        dailyTransactions: coin.transactions24h || 0,
        tvl: coin.tvl || 0,
        fees: coin.chainFees24h || 0,
        stakedSupply: coin.stakedSupply || 0,
        newAddresses: coin.newAddresses24h || 0,
        transactionVolume: coin.volume24h,
        fdmc: coin.marketCap,
        marketCap: coin.marketCap,
        inflation: { 
          mom: getInflationEstimate(coin.symbol, 'monthly'),
          yoy: getInflationEstimate(coin.symbol, 'yearly')
        },
        circulatingSupply: coin.marketCap / coin.price,
        maxTps: coin.maxTPS || 0,
        realTimeTps: coin.currentTPS || 0,
        theoreticalTps: coin.maxTPS || 0,
        blockSize: coin.blockSize || 1,
        avgFee: coin.averageFee || 0,
        finalityTime: coin.finalityTime || 60,
        energyPerTx: getEnergyEstimate(coin.symbol),
        validators: coin.validatorCount || 0,
        activeDevelopers: coin.activeDevelopers || 0,
        price: coin.price
      }));
  };

  // Get realistic inflation estimates
  const getInflationEstimate = (symbol: string, period: 'monthly' | 'yearly'): number => {
    const inflationData: Record<string, { monthly: number; yearly: number }> = {
      'BTC': { monthly: 0.05, yearly: 0.9 },
      'ETH': { monthly: -0.12, yearly: -0.3 },
      'XRP': { monthly: 0.0, yearly: 0.0 },
      'BNB': { monthly: -0.15, yearly: -2.0 },
      'SOL': { monthly: 0.35, yearly: 5.2 },
      'DOGE': { monthly: 0.4, yearly: 5.0 },
      'ADA': { monthly: 0.3, yearly: 4.1 },
      'SUI': { monthly: 0.25, yearly: 3.5 },
      'SEI': { monthly: 0.3, yearly: 4.0 }
    };
    return inflationData[symbol]?.[period] || 0;
  };

  // Get energy consumption estimates per transaction
  const getEnergyEstimate = (symbol: string): number => {
    const energyData: Record<string, number> = {
      'BTC': 741000000,  // Very high energy for Bitcoin
      'ETH': 62000,      // Much lower after PoS
      'XRP': 79000,      // Moderate energy usage
      'BNB': 15000,      // Efficient PoS variant
      'SOL': 1837,       // Very efficient
      'DOGE': 580000,    // High like Bitcoin but lower
      'ADA': 6000,       // Efficient PoS
      'SUI': 4500,       // Modern efficient design
      'SEI': 5200        // Cosmos-based efficiency
    };
    return energyData[symbol] || 100000;
  };

  // Updated chain logos
  const getChainLogo = (symbol: string): string => {
    const logos: Record<string, string> = {
      'BTC': '₿',
      'ETH': 'Ξ',
      'XRP': '💧',
      'BNB': '💎', 
      'SOL': '◎',
      'DOGE': '🐕',
      'ADA': '₳',
      'SUI': '🌊',
      'SEI': '⚡'
    };
    return logos[symbol] || '●';
  };
  const [selectedChains, setSelectedChains] = useState<string[]>([]);
  const [analysisType, setAnalysisType] = useState<'comprehensive' | 'technical' | 'economic'>('comprehensive');
  const [customWeights, setCustomWeights] = useState({
    technical: 0.25,
    economic: 0.25,
    adoption: 0.25,
    security: 0.25
  });
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [results, setResults] = useState<ComparisonResult[]>([]);
  const [showResults, setShowResults] = useState(false);

  const handleChainToggle = (chainId: string) => {
    setSelectedChains(prev => {
      if (prev.includes(chainId)) {
        return prev.filter(id => id !== chainId);
      } else if (prev.length < 5) {
        return [...prev, chainId];
      }
      return prev;
    });
  };

  const runAnalysis = async () => {
    if (selectedChains.length < 2) return;

    setIsAnalyzing(true);
    setShowResults(false);

    // Simulate analysis delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    const analysisResults: ComparisonResult[] = selectedChains.map(chainId => {
      const chain = availableChains.find(c => c.id === chainId)!;
      const scoreData = calculateAdvancedScore(chain, customWeights);
      
      return {
        ...chain,
        score: scoreData.total,
        breakdown: scoreData.breakdown,
        rank: 0 // Will be set after sorting
      };
    });

    // Sort by score and assign ranks
    analysisResults.sort((a, b) => b.score - a.score);
    analysisResults.forEach((result, index) => {
      result.rank = index + 1;
    });

    setResults(analysisResults);
    setIsAnalyzing(false);
    setShowResults(true);
  };

  const generateTimeSeriesData = () => {
    return results.map(chain => ({
      name: chain.name,
      data: Array.from({ length: 30 }, (_, i) => ({
        time: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: chain.score + (Math.random() - 0.5) * 10 // Add some variation
      })),
      color: getChainColor(chain.symbol)
    }));
  };

  const getChainColor = (symbol: string) => {
    const colors: Record<string, string> = {
      'BTC': '#f7931a',
      'ETH': '#627eea',
      'SOL': '#9945ff',
      'ADA': '#0033ad',
      'DOT': '#e6007a',
      'AVAX': '#e84142'
    };
    return colors[symbol] || '#6b7280';
  };

  const formatNumber = (num: number, type: 'currency' | 'number' | 'percentage' = 'number') => {
    if (type === 'currency') {
      return new Intl.NumberFormat('en-US', { 
        style: 'currency', 
        currency: 'USD', 
        notation: 'compact',
        maximumFractionDigits: 2
      }).format(num);
    }
    if (type === 'percentage') {
      return `${num.toFixed(1)}%`;
    }
    return new Intl.NumberFormat('en-US', { 
      notation: 'compact',
      maximumFractionDigits: 2
    }).format(num);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold flex items-center gap-2">
            <Crown className="h-5 w-5 text-yellow-500" />
            Automated Chain Comparison
            <Badge variant="outline" className="text-xs text-yellow-600">Tier 3+</Badge>
          </h3>
          <p className="text-sm text-muted-foreground">
            Select up to 5 blockchains for automated comprehensive analysis with cumulative scoring
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {!showResults ? (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Chain Selection */}
          <div className="lg:col-span-2 space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-500" />
                  Select Chains to Compare
                </CardTitle>
                <CardDescription>
                  Choose 2-5 blockchains for automated analysis ({selectedChains.length}/5 selected)
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoadingChains ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
                    <span className="ml-2 text-muted-foreground">Loading blockchains...</span>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {availableChains.map(chain => (
                      <div
                        key={chain.id}
                        className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                          selectedChains.includes(chain.id)
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                        onClick={() => handleChainToggle(chain.id)}
                      >
                        <Checkbox
                          checked={selectedChains.includes(chain.id)}
                          disabled={!selectedChains.includes(chain.id) && selectedChains.length >= 5}
                        />
                        <div className="flex items-center gap-2 flex-1">
                          <span className="text-lg">{chain.logo}</span>
                          <div>
                            <div className="font-medium">{chain.name}</div>
                            <div className="text-xs text-muted-foreground">{chain.symbol}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium">{formatNumber(chain.marketCap, 'currency')}</div>
                          <div className="text-xs text-muted-foreground">Market Cap</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Analysis Configuration */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Analysis Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Analysis Type</label>
                  <Select value={analysisType} onValueChange={(value: any) => setAnalysisType(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="comprehensive">Comprehensive</SelectItem>
                      <SelectItem value="technical">Technical Focus</SelectItem>
                      <SelectItem value="economic">Economic Focus</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-3">
                  <label className="text-sm font-medium">Custom Weights</label>
                  {Object.entries(customWeights).map(([key, value]) => (
                    <div key={key} className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span className="capitalize">{key}</span>
                        <span>{(value * 100).toFixed(0)}%</span>
                      </div>
                      <input
                        type="range"
                        min="0"
                        max="1"
                        step="0.05"
                        value={value}
                        onChange={(e) => setCustomWeights(prev => ({
                          ...prev,
                          [key]: parseFloat(e.target.value)
                        }))}
                        className="w-full"
                      />
                    </div>
                  ))}
                </div>

                <Button 
                  className="w-full" 
                  onClick={runAnalysis}
                  disabled={selectedChains.length < 2 || isAnalyzing}
                >
                  {isAnalyzing ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Run Analysis
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Results Header */}
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-semibold">Analysis Results</h4>
              <p className="text-sm text-muted-foreground">
                Comprehensive comparison of {results.length} blockchain networks
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setShowResults(false)}>
                New Analysis
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export Results
              </Button>
            </div>
          </div>

          {/* Cumulative Score Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-500" />
                Cumulative Score Comparison
              </CardTitle>
              <CardDescription>
                Overall performance scores across all metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <InteractiveMetricChart
                title="Cumulative Scores"
                description="Combined performance metrics over time"
                data={results.map(chain => ({
                  time: chain.name,
                  value: chain.score
                }))}
                color="#10b981"
                icon={<BarChart3 className="h-5 w-5" />}
                unit=""
              />
            </CardContent>
          </Card>

          {/* Detailed Results */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {results.map((chain, index) => (
              <Card key={chain.id} className={`${index === 0 ? 'border-yellow-300 bg-yellow-50 dark:bg-yellow-950/10' : ''}`}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {index === 0 && <Award className="h-5 w-5 text-yellow-500" />}
                      <span className="text-lg">{chain.logo}</span>
                      <div>
                        <CardTitle className="text-lg">{chain.name}</CardTitle>
                        <CardDescription>Rank #{chain.rank}</CardDescription>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">{chain.score}/100</div>
                      <div className="text-xs text-muted-foreground">Overall Score</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(chain.breakdown).map(([category, score]) => (
                      <div key={category} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="capitalize flex items-center gap-1">
                            {category === 'technical' && <Zap className="h-3 w-3" />}
                            {category === 'economic' && <DollarSign className="h-3 w-3" />}
                            {category === 'adoption' && <Users className="h-3 w-3" />}
                            {category === 'security' && <Shield className="h-3 w-3" />}
                            {category}
                          </span>
                          <span className="font-medium">{score}/100</span>
                        </div>
                        <Progress value={score} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
