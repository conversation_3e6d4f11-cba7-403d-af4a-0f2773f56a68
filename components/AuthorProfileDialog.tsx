"use client";

// components/AuthorProfileDialog.tsx
import { useState, useCallback, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import { AuthorProfileDialogProps } from "@/types/articleTypes";
import { Profile } from "@/types";
import { toast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import {
  UserPlus,
  UserMinus,
  ExternalLink,
  Calendar,
  Users,
  BookOpen,
} from "lucide-react";
import Link from "next/link";
import { formatTimeAgo } from "@/utils/articleUtils";

export function AuthorProfileDialog({
  authorId,
  isOpen,
  onClose,
  currentUserId,
}: AuthorProfileDialogProps) {
  const [authorProfile, setAuthorProfile] = useState<Profile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFollowing, setIsFollowing] = useState(false);
  const [isFollowingLoading, setIsFollowingLoading] = useState(false);
  const supabase = createClient();

  const loadAuthorProfile = useCallback(async () => {
    try {
      setIsLoading(true);

      // Fetch profile data and counts separately
      const [profileData, followData, followersCount, followingCount] =
        await Promise.all([
          (supabase as any).from("users").select("*").eq("user_id", authorId).single(),
          currentUserId
            ? (supabase as any)
                .from("follows")
                .select("*")
                .eq("follower_id", currentUserId)
                .eq("following_id", authorId)
                .maybeSingle()
            : Promise.resolve({ data: null }),
          (supabase as any)
            .from("follows")
            .select("*", { count: "exact" })
            .eq("following_id", authorId),
          (supabase as any)
            .from("follows")
            .select("*", { count: "exact" })
            .eq("follower_id", authorId),
        ]);

      if (profileData.error) throw profileData.error;

      setIsFollowing(!!followData.data);
      setAuthorProfile({
        ...profileData.data,
        followers_count: followersCount.count || 0,
        following_count: followingCount.count || 0,
        article_count: profileData.data.article_count || 0,
      } as Profile);
    } catch (error) {
      console.error("Error loading author profile:", error);
      toast({
        title: "Error",
        description: "Failed to load author profile",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [authorId, currentUserId, supabase]);

  useEffect(() => {
    if (isOpen && authorId) {
      loadAuthorProfile();
    }
  }, [isOpen, authorId, loadAuthorProfile]);

  const handleFollow = async () => {
    if (!currentUserId) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to follow users",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsFollowingLoading(true);
      if (isFollowing) {
        await (supabase as any)
          .from("follows")
          .delete()
          .eq("follower_id", currentUserId)
          .eq("following_id", authorId);
      } else {
        await (supabase as any).from("follows").insert({
          follower_id: currentUserId,
          following_id: authorId,
          created_at: new Date().toISOString(),
        });
      }

      setIsFollowing(!isFollowing);
      await loadAuthorProfile(); // Refresh counts

      toast({
        title: isFollowing ? "Unfollowed" : "Following",
        description: isFollowing
          ? `You have unfollowed this author`
          : `You are now following this author`,
      });
    } catch (error) {
      console.error("Error updating follow status:", error);
      toast({
        title: "Error",
        description: "Failed to update follow status",
        variant: "destructive",
      });
    } finally {
      setIsFollowingLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent aria-describedby={"user info"} className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Author Profile</DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-20 w-20 rounded-full mx-auto -mt-10" />
            <Skeleton className="h-6 w-32 mx-auto" />
            <div className="flex justify-center gap-4">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>
        ) : authorProfile ? (
          <div className="space-y-4">
            {/* Banner and Avatar */}
            <div className="relative h-32 w-full">
              <img
                src={authorProfile.banner_url || "/default-banner.jpg"}
                alt="Profile banner"
                className="w-full h-full object-cover rounded-t-lg"
              />
              <Avatar className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-20 h-20 border-4 border-background">
                <AvatarImage
                  src={authorProfile.avatar_url || "/default-avatar.jpg"}
                />
                <AvatarFallback>{authorProfile.username?.[0]}</AvatarFallback>
              </Avatar>
            </div>

            {/* Profile Info */}
            <div className="pt-12 text-center space-y-4">
              <div>
                <h3 className="font-semibold text-xl">
                  @{authorProfile.username}
                </h3>
                {authorProfile.full_name && (
                  <p className="text-sm text-muted-foreground">
                    {authorProfile.full_name}
                  </p>
                )}
              </div>

              {authorProfile.bio && (
                <p className="text-sm text-muted-foreground px-4">
                  {authorProfile.bio}
                </p>
              )}

              {/* Stats */}
              <div className="grid grid-cols-3 gap-4 px-4">
                <div className="flex flex-col items-center">
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    <span className="font-medium">
                      {authorProfile.followers_count}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">Followers</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="flex items-center gap-1">
                    <BookOpen className="w-4 h-4" />
                    <span className="font-medium">
                      {authorProfile.article_count}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">Articles</div>
                </div>
                <div className="flex flex-col items-center">
                  <div className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    <span className="font-medium">
                      {formatTimeAgo(authorProfile.created_at || "")}
                    </span>
                  </div>
                  <div className="text-xs text-muted-foreground">Joined</div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-center gap-2">
                {currentUserId && currentUserId !== authorId && (
                  <Button
                    onClick={handleFollow}
                    variant={isFollowing ? "outline" : "default"}
                    className="gap-2"
                    disabled={isFollowingLoading}
                  >
                    {isFollowing ? (
                      <>
                        <UserMinus className="w-4 h-4" />
                        Unfollow
                      </>
                    ) : (
                      <>
                        <UserPlus className="w-4 h-4" />
                        Follow
                      </>
                    )}
                  </Button>
                )}
                <Button asChild variant="outline">
                  <Link href={`/ctn/profile/${authorId}`}>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Full Profile
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-4 text-muted-foreground">
            Profile not found
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
