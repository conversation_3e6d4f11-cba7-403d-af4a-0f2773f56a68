// SimpleZStrategyPrimitive.ts
// ZStrategy Pane Primitive implementing IPanePrimitive interface for Lightweight Charts v5.0

export interface ZSignal {
  timestamp: number;
  stLine: number;
  vhma?: number;
  price?: number;
  buy?: boolean;
  sell?: boolean;
  filtersPass?: boolean;
}

export class SimpleZStrategyPrimitive {
  private signals: ZSignal[];
  private _requestUpdate?: () => void;

  constructor(signals: ZSignal[] = []) {
    this.signals = signals || [];
  }

  // Update signals and request redraw
  updateSignals(newSignals: ZSignal[]) {
    this.signals = newSignals || [];
    console.log('Updated ZStrategy signals:', this.signals.length);
    
    // Request redraw if attached
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }

  // IPanePrimitive interface - required for pane attachment
  attached(param: { pane: any; requestUpdate: () => void }) {
    console.log('ZStrategy pane primitive attached:', param);
    this._requestUpdate = param.requestUpdate;
    
    // Request initial update
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }

  detached() {
    console.log('ZStrategy pane primitive detached');
    this._requestUpdate = undefined;
  }

  // IPanePrimitive interface method - returns array of pane views
  paneViews() {
    console.log('SimpleZStrategyPrimitive paneViews called with', this.signals.length, 'signals');
    
    if (!this.signals || this.signals.length === 0) {
      console.log('No signals available, returning empty pane views');
      return [];
    }
    
    return [
      {
        zOrder: () => 1, // Low z-order to render behind candlesticks
        renderer: (height: number, width: number) => {
          console.log('Renderer factory called with dimensions:', { height, width });
          
          return {
            draw: (target: any, _isHovered: boolean = false) => {
              console.log('SimpleZStrategyPrimitive draw called:', { 
                target: target,
                targetType: typeof target,
                targetProps: target ? Object.keys(target) : 'null',
                hasCanvasRenderingContext2D: target instanceof CanvasRenderingContext2D,
                signalsCount: this.signals?.length || 0,
                height,
                width
              });

              // Try to extract the canvas context from different possible formats
              let ctx = target;
              
              // Method 1: Direct CanvasRenderingContext2D
              if (target && typeof target.save === 'function') {
                ctx = target;
                console.log('Using direct canvas context');
              }
              // Method 2: Wrapped in context property
              else if (target && target.context && typeof target.context.save === 'function') {
                ctx = target.context;
                console.log('Using wrapped context from target.context');
              }
              // Method 3: Wrapped in ctx property  
              else if (target && target.ctx && typeof target.ctx.save === 'function') {
                ctx = target.ctx;
                console.log('Using wrapped context from target.ctx');
              }
              // Method 4: Target might be a canvas element
              else if (target && target.getContext && typeof target.getContext === 'function') {
                ctx = target.getContext('2d');
                console.log('Extracted 2D context from canvas element');
              }
              
              // Validate final canvas context
              if (!ctx || typeof ctx.save !== 'function') {
                console.error('Invalid canvas context provided to ZStrategy:', {
                  originalTarget: target,
                  extractedCtx: ctx,
                  targetType: typeof target,
                  ctxType: typeof ctx,
                  hasSave: ctx ? typeof ctx.save : 'null',
                  targetProps: target ? Object.getOwnPropertyNames(target).slice(0, 10) : 'null',
                  ctxProps: ctx ? Object.getOwnPropertyNames(ctx).slice(0, 10) : 'null'
                });
                return;
              }
              
              console.log('✅ Valid canvas context found, proceeding with drawing');

              if (!this.signals || this.signals.length === 0) {
                console.log('No signals to draw in ZStrategy');
                return;
              }

              try {
                this.drawStrategy(ctx, height, width);
              } catch (error) {
                console.error('Error drawing ZStrategy:', error);
              }
            }
          };
        }
      }
    ];
  }

  private drawStrategy(ctx: any, height: number, width: number) {
    if (this.signals.length < 2) {
      console.log('Not enough signals to draw (need at least 2)');
      return;
    }

    console.log('Drawing ZStrategy with canvas dimensions:', { width, height });

    ctx.save();

    try {
      // Draw SuperTrend line
      this.drawSuperTrend(ctx, height, width);
      
      // Draw VHMA line
      this.drawVHMA(ctx, height, width);
      
      // Draw signals
      this.drawSignals(ctx, height, width);
    } finally {
      ctx.restore();
    }
  }

  private drawSuperTrend(ctx: any, height: number, width: number) {
    if (this.signals.length < 2) return;

    // Calculate coordinate mapping
    const timeMin = Math.min(...this.signals.map(s => s.timestamp));
    const timeMax = Math.max(...this.signals.map(s => s.timestamp));
    const timeRange = timeMax - timeMin;

    const prices = this.signals.map(s => s.stLine).filter(p => !isNaN(p) && isFinite(p));
    if (prices.length === 0) return;

    const priceMin = Math.min(...prices);
    const priceMax = Math.max(...prices);
    const priceRange = priceMax - priceMin;

    if (timeRange === 0 || priceRange === 0) return;

    // Draw SuperTrend line
    ctx.strokeStyle = '#a21caf';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.beginPath();

    let firstPoint = true;
    this.signals.forEach((signal) => {
      if (!isFinite(signal.stLine) || isNaN(signal.stLine)) return;

      const x = ((signal.timestamp - timeMin) / timeRange) * width;
      const y = height - ((signal.stLine - priceMin) / priceRange) * height;

      if (firstPoint) {
        ctx.moveTo(x, y);
        firstPoint = false;
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();
  }

  private drawVHMA(ctx: any, height: number, width: number) {
    const vhmaSignals = this.signals.filter(s => s.vhma && isFinite(s.vhma));
    if (vhmaSignals.length < 2) return;

    // Calculate coordinate mapping
    const timeMin = Math.min(...this.signals.map(s => s.timestamp));
    const timeMax = Math.max(...this.signals.map(s => s.timestamp));
    const timeRange = timeMax - timeMin;

    const vhmaPrices = vhmaSignals.map(s => s.vhma!);
    const priceMin = Math.min(...vhmaPrices);
    const priceMax = Math.max(...vhmaPrices);
    const priceRange = priceMax - priceMin;

    if (timeRange === 0 || priceRange === 0) return;

    // Draw VHMA line
    ctx.strokeStyle = '#8b5cf6';
    ctx.lineWidth = 1.5;
    ctx.setLineDash([3, 3]);
    ctx.lineCap = 'round';
    ctx.beginPath();

    let firstPoint = true;
    vhmaSignals.forEach((signal) => {
      const x = ((signal.timestamp - timeMin) / timeRange) * width;
      const y = height - ((signal.vhma! - priceMin) / priceRange) * height;

      if (firstPoint) {
        ctx.moveTo(x, y);
        firstPoint = false;
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();
    ctx.setLineDash([]); // Reset dash
  }

  private drawSignals(ctx: any, height: number, width: number) {

    // Calculate coordinate mapping
    const timeMin = Math.min(...this.signals.map(s => s.timestamp));
    const timeMax = Math.max(...this.signals.map(s => s.timestamp));
    const timeRange = timeMax - timeMin;

    const prices = this.signals.map(s => s.price || s.stLine).filter(p => !isNaN(p) && isFinite(p));
    if (prices.length === 0) return;

    const priceMin = Math.min(...prices);
    const priceMax = Math.max(...prices);
    const priceRange = priceMax - priceMin;

    if (timeRange === 0 || priceRange === 0) return;

    this.signals.forEach((signal) => {
      if (!signal.filtersPass || (!signal.buy && !signal.sell)) return;

      const price = signal.price || signal.stLine;
      const x = ((signal.timestamp - timeMin) / timeRange) * width;
      const y = height - ((price - priceMin) / priceRange) * height;

      // Draw signal markers
      if (signal.buy) {
        this.drawBuySignal(ctx, x, y);
      }
      if (signal.sell) {
        this.drawSellSignal(ctx, x, y);
      }
    });
  }

  private drawBuySignal(ctx: any, x: number, y: number) {
    const size = 8;
    const yOffset = -15;

    ctx.fillStyle = '#10b981';
    ctx.beginPath();
    // Triangle pointing up
    ctx.moveTo(x, y + yOffset - size);
    ctx.lineTo(x - size, y + yOffset + size);
    ctx.lineTo(x + size, y + yOffset + size);
    ctx.closePath();
    ctx.fill();

    // Border
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 1;
    ctx.stroke();
  }

  private drawSellSignal(ctx: any, x: number, y: number) {
    const size = 8;
    const yOffset = 15;

    ctx.fillStyle = '#ef4444';
    ctx.beginPath();
    // Triangle pointing down
    ctx.moveTo(x, y + yOffset + size);
    ctx.lineTo(x - size, y + yOffset - size);
    ctx.lineTo(x + size, y + yOffset - size);
    ctx.closePath();
    ctx.fill();

    // Border
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 1;
    ctx.stroke();
  }

  // Optional autoscale info
  autoscaleInfo() {
    if (this.signals.length === 0) return null;

    const prices = this.signals
      .map(sig => sig.stLine)
      .filter(price => !isNaN(price) && isFinite(price));

    if (prices.length === 0) return null;

    return {
      priceRange: {
        minValue: Math.min(...prices) * 0.99,
        maxValue: Math.max(...prices) * 1.01
      }
    };
  }

  // Add hitTest method for compatibility
  hitTest() {
    return null;
  }
}