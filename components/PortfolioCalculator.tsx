"use client";

import React, { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { getAllCoinsData, type CentralizedCoinData } from '@/utils/mock-data-service';
import { exportPortfolioData, exportAnalyticsSummary } from '@/utils/export-utils';
import { 
  Calculator, 
  Plus, 
  Trash2, 
  TrendingUp, 
  TrendingDown,
  PieChart,
  Download,
  Upload,
  RefreshCw,
  DollarSign,
  Target,
  AlertTriangle
} from 'lucide-react';

interface PortfolioHolding {
  id: string;
  symbol: string;
  name: string;
  amount: number;
  avgBuyPrice: number;
  currentPrice: number;
  lastUpdated: Date;
}

interface PortfolioStats {
  totalValue: number;
  totalInvested: number;
  totalPnL: number;
  totalPnLPercentage: number;
  bestPerformer: PortfolioHolding | null;
  worstPerformer: PortfolioHolding | null;
  diversityScore: number;
}

interface PortfolioCalculatorProps {
  onSave?: (holdings: PortfolioHolding[]) => void;
  onLoad?: () => PortfolioHolding[];
  initialHoldings?: PortfolioHolding[];
}

// Get supported coins from centralized service - will be populated after data loads

export function PortfolioCalculator({ 
  onSave, 
  onLoad, 
  initialHoldings = [] 
}: PortfolioCalculatorProps) {
  const [holdings, setHoldings] = useState<PortfolioHolding[]>(initialHoldings);
  const [newHolding, setNewHolding] = useState({
    symbol: '',
    amount: '',
    avgBuyPrice: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [availableCoins, setAvailableCoins] = useState<CentralizedCoinData[]>([]);
  const [isLoadingCoins, setIsLoadingCoins] = useState(true);

  // Load available coins from centralized service
  useEffect(() => {
    const loadCoins = async () => {
      try {
        setIsLoadingCoins(true);
        const coinsData = await getAllCoinsData();
        setAvailableCoins(coinsData);
      } catch (error) {
        console.error('Failed to load coins data:', error);
      } finally {
        setIsLoadingCoins(false);
      }
    };
    loadCoins();
  }, []);

  // Update holdings with current prices from static data
  useEffect(() => {
    if (availableCoins.length > 0 && holdings.length > 0) {
      setHoldings(prevHoldings =>
        prevHoldings.map(holding => {
          const coinData = availableCoins.find(coin => coin.symbol === holding.symbol);
          return {
            ...holding,
            currentPrice: coinData?.price || holding.currentPrice,
            lastUpdated: new Date(),
          };
        })
      );
    }
  }, [availableCoins]);

  // Calculate portfolio statistics
  const portfolioStats: PortfolioStats = useMemo(() => {
    if (holdings.length === 0) {
      return {
        totalValue: 0,
        totalInvested: 0,
        totalPnL: 0,
        totalPnLPercentage: 0,
        bestPerformer: null,
        worstPerformer: null,
        diversityScore: 0,
      };
    }

    let totalValue = 0;
    let totalInvested = 0;
    let bestPnL = -Infinity;
    let worstPnL = Infinity;
    let bestPerformer: PortfolioHolding | null = null;
    let worstPerformer: PortfolioHolding | null = null;

    holdings.forEach(holding => {
      const invested = holding.amount * holding.avgBuyPrice;
      const currentValue = holding.amount * holding.currentPrice;
      const pnlPercentage = ((currentValue - invested) / invested) * 100;

      totalValue += currentValue;
      totalInvested += invested;

      if (pnlPercentage > bestPnL) {
        bestPnL = pnlPercentage;
        bestPerformer = holding;
      }

      if (pnlPercentage < worstPnL) {
        worstPnL = pnlPercentage;
        worstPerformer = holding;
      }
    });

    const totalPnL = totalValue - totalInvested;
    const totalPnLPercentage = totalInvested > 0 ? (totalPnL / totalInvested) * 100 : 0;

    // Calculate diversity score (simple Herfindahl index)
    const weights = holdings.map(h => (h.amount * h.currentPrice) / totalValue);
    const herfindahl = weights.reduce((sum, weight) => sum + weight * weight, 0);
    const diversityScore = Math.round((1 - herfindahl) * 100);

    return {
      totalValue,
      totalInvested,
      totalPnL,
      totalPnLPercentage,
      bestPerformer,
      worstPerformer,
      diversityScore,
    };
  }, [holdings]);

  const addHolding = () => {
    if (!newHolding.symbol || !newHolding.amount || !newHolding.avgBuyPrice) {
      return;
    }

    const coinData = availableCoins.find(coin => coin.symbol === newHolding.symbol);
    if (!coinData) {
      alert('Symbol not found. Please use a supported symbol.');
      return;
    }

    const holding: PortfolioHolding = {
      id: Date.now().toString(),
      symbol: coinData.symbol,
      name: coinData.name,
      amount: parseFloat(newHolding.amount),
      avgBuyPrice: parseFloat(newHolding.avgBuyPrice),
      currentPrice: coinData.price,
      lastUpdated: new Date(),
    };

    setHoldings(prev => [...prev, holding]);
    setNewHolding({ symbol: '', amount: '', avgBuyPrice: '' });
  };

  const removeHolding = (id: string) => {
    setHoldings(prev => prev.filter(h => h.id !== id));
  };

  const updatePrices = async () => {
    setIsLoading(true);
    try {
      // Refresh data from centralized service
      const coinsData = await getAllCoinsData();
      setAvailableCoins(coinsData);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to refresh prices:', error);
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    const color = percentage >= 0 ? 'text-green-600' : 'text-red-600';
    const sign = percentage >= 0 ? '+' : '';
    return (
      <span className={color}>
        {sign}{percentage.toFixed(2)}%
      </span>
    );
  };

  return (
    <div className="space-y-6">
      {/* Portfolio Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5 text-blue-500" />
            Portfolio Calculator
          </CardTitle>
          <CardDescription>
            Track your cryptocurrency investments and performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Value</p>
                  <p className="text-2xl font-bold">{formatCurrency(portfolioStats.totalValue)}</p>
                </div>
                <DollarSign className="h-8 w-8 text-blue-500 opacity-60" />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Invested</p>
                  <p className="text-2xl font-bold">{formatCurrency(portfolioStats.totalInvested)}</p>
                </div>
                <Target className="h-8 w-8 text-purple-500 opacity-60" />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total P&L</p>
                  <p className="text-2xl font-bold">{formatCurrency(portfolioStats.totalPnL)}</p>
                  <p className="text-sm">{formatPercentage(portfolioStats.totalPnLPercentage)}</p>
                </div>
                {portfolioStats.totalPnL >= 0 ? (
                  <TrendingUp className="h-8 w-8 text-green-500 opacity-60" />
                ) : (
                  <TrendingDown className="h-8 w-8 text-red-500 opacity-60" />
                )}
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Diversity Score</p>
                  <p className="text-2xl font-bold">{portfolioStats.diversityScore}/100</p>
                  <p className="text-sm text-muted-foreground">
                    {portfolioStats.diversityScore > 70 ? 'Well diversified' : 
                     portfolioStats.diversityScore > 40 ? 'Moderately diversified' : 'Concentrated'}
                  </p>
                </div>
                <PieChart className="h-8 w-8 text-orange-500 opacity-60" />
              </div>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 mb-6">
            <Button 
              onClick={updatePrices}
              disabled={isLoading || isLoadingCoins}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${(isLoading || isLoadingCoins) ? 'animate-spin' : ''}`} />
              Update Prices
            </Button>
            
            {/* Data Status Indicator */}
            <div className="flex items-center gap-1 ml-2">
              {!isLoadingCoins ? (
                <div className="w-2 h-2 bg-green-500 rounded-full" title="Static data loaded" />
              ) : (
                <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" title="Loading data" />
              )}
              <span className="text-xs text-muted-foreground ml-1">
                {!isLoadingCoins ? "Ready" : "Loading"}
              </span>
            </div>
            <Button 
              variant="outline" 
              onClick={() => {
                exportPortfolioData(holdings);
                onSave?.(holdings);
              }}
              disabled={!holdings.length}
            >
              <Download className="h-4 w-4 mr-2" />
              Export Holdings
            </Button>
            <Button 
              variant="outline" 
              onClick={() => exportAnalyticsSummary({
                ...portfolioStats,
                holdingsCount: holdings.length,
              })}
              disabled={!holdings.length}
            >
              <Download className="h-4 w-4 mr-2" />
              Export Summary
            </Button>
            <Button variant="outline" onClick={() => onLoad?.()}>
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="holdings" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="holdings">Holdings</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="allocation">Allocation</TabsTrigger>
        </TabsList>

        {/* Holdings Tab */}
        <TabsContent value="holdings" className="space-y-4">
          {/* Add New Holding */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Add New Holding</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="symbol">Symbol</Label>
                  <Select 
                    value={newHolding.symbol} 
                    onValueChange={(value) => setNewHolding(prev => ({ ...prev, symbol: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select coin" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCoins.map((coin) => (
                        <SelectItem key={coin.symbol} value={coin.symbol}>
                          {coin.symbol} - {coin.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="amount">Amount</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="any"
                    placeholder="0.00"
                    value={newHolding.amount}
                    onChange={(e) => setNewHolding(prev => ({ ...prev, amount: e.target.value }))}
                  />
                </div>

                <div>
                  <Label htmlFor="avgBuyPrice">Avg Buy Price</Label>
                  <Input
                    id="avgBuyPrice"
                    type="number"
                    step="any"
                    placeholder="0.00"
                    value={newHolding.avgBuyPrice}
                    onChange={(e) => setNewHolding(prev => ({ ...prev, avgBuyPrice: e.target.value }))}
                  />
                </div>

                <div className="flex items-end">
                  <Button onClick={addHolding} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Holding
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Holdings List */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Current Holdings</CardTitle>
            </CardHeader>
            <CardContent>
              {holdings.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calculator className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No holdings added yet. Add your first holding above.</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {holdings.map((holding) => {
                    const invested = holding.amount * holding.avgBuyPrice;
                    const currentValue = holding.amount * holding.currentPrice;
                    const pnl = currentValue - invested;
                    const pnlPercentage = (pnl / invested) * 100;

                    return (
                      <div key={holding.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div>
                            <h3 className="font-semibold">{holding.symbol}</h3>
                            <p className="text-sm text-muted-foreground">{holding.name}</p>
                          </div>
                          <div className="text-sm">
                            <p>Amount: {holding.amount.toLocaleString()}</p>
                            <p>Avg Buy: {formatCurrency(holding.avgBuyPrice)}</p>
                          </div>
                          <div className="text-sm">
                            <p>Current: {formatCurrency(holding.currentPrice)}</p>
                            <p>Value: {formatCurrency(currentValue)}</p>
                          </div>
                          <div className="text-sm">
                            <p>P&L: {formatCurrency(pnl)}</p>
                            <p>{formatPercentage(pnlPercentage)}</p>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeHolding(holding.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Best/Worst Performers */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Performance Leaders</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {portfolioStats.bestPerformer && (
                  <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded border border-green-200 dark:border-green-800">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-700 dark:text-green-300">Best Performer</span>
                    </div>
                    <p className="font-semibold">{portfolioStats.bestPerformer.symbol}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatPercentage(
                        ((portfolioStats.bestPerformer.amount * portfolioStats.bestPerformer.currentPrice) - 
                         (portfolioStats.bestPerformer.amount * portfolioStats.bestPerformer.avgBuyPrice)) /
                        (portfolioStats.bestPerformer.amount * portfolioStats.bestPerformer.avgBuyPrice) * 100
                      )}
                    </p>
                  </div>
                )}

                {portfolioStats.worstPerformer && (
                  <div className="p-3 bg-red-50 dark:bg-red-950/20 rounded border border-red-200 dark:border-red-800">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingDown className="h-4 w-4 text-red-600" />
                      <span className="font-medium text-red-700 dark:text-red-300">Worst Performer</span>
                    </div>
                    <p className="font-semibold">{portfolioStats.worstPerformer.symbol}</p>
                    <p className="text-sm text-muted-foreground">
                      {formatPercentage(
                        ((portfolioStats.worstPerformer.amount * portfolioStats.worstPerformer.currentPrice) - 
                         (portfolioStats.worstPerformer.amount * portfolioStats.worstPerformer.avgBuyPrice)) /
                        (portfolioStats.worstPerformer.amount * portfolioStats.worstPerformer.avgBuyPrice) * 100
                      )}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Risk Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Risk Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Diversity Score</span>
                    <div className="flex items-center gap-2">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${portfolioStats.diversityScore}%` }}
                        ></div>
                      </div>
                      <span className="text-sm">{portfolioStats.diversityScore}/100</span>
                    </div>
                  </div>

                  {portfolioStats.diversityScore < 40 && (
                    <div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded border border-yellow-200 dark:border-yellow-800">
                      <div className="flex items-center gap-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        <span className="font-medium text-yellow-700 dark:text-yellow-300">High Concentration Risk</span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        Consider diversifying your portfolio across more assets.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Allocation Tab */}
        <TabsContent value="allocation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Portfolio Allocation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {holdings.map((holding) => {
                  const value = holding.amount * holding.currentPrice;
                  const percentage = portfolioStats.totalValue > 0 ? (value / portfolioStats.totalValue) * 100 : 0;
                  
                  return (
                    <div key={holding.id} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-4 h-4 bg-blue-500 rounded-full" style={{ 
                          backgroundColor: `hsl(${(holdings.indexOf(holding) * 360) / holdings.length}, 70%, 50%)` 
                        }}></div>
                        <span className="font-medium">{holding.symbol}</span>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="w-32 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ 
                              width: `${percentage}%`,
                              backgroundColor: `hsl(${(holdings.indexOf(holding) * 360) / holdings.length}, 70%, 50%)`
                            }}
                          ></div>
                        </div>
                        <div className="text-right min-w-[100px]">
                          <p className="font-medium">{percentage.toFixed(1)}%</p>
                          <p className="text-sm text-muted-foreground">{formatCurrency(value)}</p>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}