'use client';

import { useState, useEffect } from 'react';
import { useTierAccess } from '@/hooks/use-tier-access';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Zap, 
  Star, 
  Trophy, 
  Calendar, 
  Clock, 
  TrendingUp, 
  Gift,
  Crown,
  Shield,
  Award,
  Timer
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';

interface ExperienceDisplayProps {
  showDailyClaim?: boolean;
  showProgress?: boolean;
  showStats?: boolean;
  compact?: boolean;
  className?: string;
}

export function ExperienceDisplay({ 
  showDailyClaim = true,
  showProgress = true, 
  showStats = true,
  compact = false,
  className 
}: ExperienceDisplayProps) {
  const [claiming, setClaiming] = useState(false);
  const { userExperience, processDailyClaim, getTierName, loading } = useTierAccess();
  const { toast } = useToast();

  const handleDailyClaim = async () => {
    if (!userExperience?.can_claim_daily) return;
    
    setClaiming(true);
    try {
      const result = await processDailyClaim();
      
      if (result.success) {
        toast({
          title: "Daily Reward Claimed!",
          description: `You gained ${result.experience_gained} experience points!`,
          duration: 5000,
        });
      } else {
        // Show the specific error message from the backend
        toast({
          title: "Claim Failed",
          description: result.message || "Unable to claim daily reward",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error in handleDailyClaim:', error);
      // Show a more specific error message if available
      const errorMessage = error instanceof Error ? error.message : "Failed to claim daily reward. Please try again.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setClaiming(false);
    }
  };

  const formatTimeRemaining = (timeString: string) => {
    if (!timeString) return '';
    
    const interval = timeString.replace(/[^\d:.-]/g, '');
    const parts = interval.split(':');
    
    if (parts.length >= 2) {
      const hours = parseInt(parts[0]);
      const minutes = parseInt(parts[1]);
      
      if (hours > 0) {
        return `${hours}h ${minutes}m`;
      } else {
        return `${minutes}m`;
      }
    }
    
    return interval;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!userExperience) {
    return (
      <Card className={cn("w-full", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            Experience System
          </CardTitle>
          <CardDescription>
            Sign in to track your experience and level up!
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className={cn("flex items-center gap-4 p-3 bg-gray-50 rounded-lg", className)}>
        <div className="flex items-center gap-2">
          <div className="p-2 bg-blue-500 rounded-full">
            <Trophy className="h-4 w-4 text-white" />
          </div>
          <div>
            <div className="text-sm font-medium">Level {userExperience.level}</div>
            <div className="text-xs text-gray-500">
              {userExperience.experience_points.toLocaleString()} XP
            </div>
          </div>
        </div>
        
        {showProgress && (
          <div className="flex-1 max-w-32">
            <Progress value={userExperience.progress_percentage} className="h-2" />
            <div className="text-xs text-gray-500 mt-1">
              {Math.round(userExperience.progress_percentage)}% to next level
            </div>
          </div>
        )}
        
        {showDailyClaim && userExperience.can_claim_daily && (
          <Button 
            size="sm" 
            onClick={handleDailyClaim}
            disabled={claiming}
            className="bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600"
          >
            {claiming ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <>
                <Gift className="h-4 w-4 mr-1" />
                Claim
              </>
            )}
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-blue-500 rounded-full">
              <Trophy className="h-5 w-5 text-white" />
            </div>
            <div>
              <div className="text-lg font-bold">Level {userExperience.level}</div>
              <div className="text-sm text-gray-500">
                {userExperience.experience_points.toLocaleString()} XP
              </div>
            </div>
          </div>
          <Badge variant="outline" className="text-blue-600">
            {getTierName(userExperience.tier)}
          </Badge>
        </CardTitle>
        
        {showProgress && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress to Level {userExperience.level + 1}</span>
              <span>{Math.round(userExperience.progress_percentage)}%</span>
            </div>
            <Progress value={userExperience.progress_percentage} className="h-2" />
            <div className="text-xs text-gray-500">
              {userExperience.next_level_exp - userExperience.experience_points} XP needed
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {showDailyClaim && (
          <div className="space-y-3">
            <Separator />
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-orange-500" />
                <span className="font-medium">Daily Claim</span>
              </div>
              <Badge variant="secondary" className="flex items-center gap-1">
                <Star className="h-3 w-3" />
                {userExperience.daily_claim_streak} days
              </Badge>
            </div>
            
            {userExperience.can_claim_daily ? (
              <Button 
                onClick={handleDailyClaim}
                disabled={claiming}
                className="w-full bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white"
              >
                {claiming ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <>
                    <Gift className="h-4 w-4 mr-2" />
                    Claim Daily Reward
                  </>
                )}
              </Button>
            ) : (
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Clock className="h-6 w-6 text-gray-400 mx-auto mb-2" />
                <div className="text-sm text-gray-600 mb-3">
                  {userExperience.time_until_next_claim?.includes('Upgrade') ? (
                    <>
                      <div className="font-medium text-blue-600 mb-2">
                        Daily Experience Claims
                      </div>
                      <div className="text-xs text-gray-500 mb-3">
                        Available for Tier 1+ subscribers
                      </div>
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => window.location.href = '/ctn/subscription/plans'}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <Crown className="h-3 w-3 mr-1" />
                        Upgrade Account
                      </Button>
                    </>
                  ) : (
                    <>
                      {userExperience.time_until_next_claim ? 
                        `Next claim in ${formatTimeRemaining(userExperience.time_until_next_claim)}` :
                        'Already claimed today'
                      }
                    </>
                  )}
                </div>
              </div>
            )}
            
            {userExperience.consecutive_claim_boost > 0 && (
              <div className="flex items-center gap-2 text-sm text-green-600">
                <TrendingUp className="h-4 w-4" />
                <span>
                  {Math.round(userExperience.consecutive_claim_boost * 100)}% streak bonus active
                </span>
              </div>
            )}
          </div>
        )}

        {showStats && (
          <div className="space-y-3">
            <Separator />
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <Award className="h-5 w-5 text-blue-500 mx-auto mb-1" />
                <div className="text-sm font-medium">Reputation</div>
                <div className="text-lg font-bold text-blue-600">
                  {userExperience.reputation_score}
                </div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <Timer className="h-5 w-5 text-green-500 mx-auto mb-1" />
                <div className="text-sm font-medium">XP Active</div>
                <div className="text-lg font-bold text-green-600">
                  {userExperience.experience_active ? 'Yes' : 'No'}
                </div>
              </div>
            </div>
            
            {userExperience.experience_active && (
              <div className="text-center text-sm text-green-600">
                <Zap className="h-4 w-4 inline mr-1" />
                Experience gain active until{' '}
                {new Date(userExperience.experience_active_until).toLocaleDateString()}
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Minimal experience badge for headers/navigation
export function ExperienceBadge({ className }: { className?: string }) {
  const { userExperience } = useTierAccess();
  
  if (!userExperience) return null;
  
  return (
    <div className={cn("flex items-center gap-2 px-2 py-1 bg-blue-50 rounded-full", className)}>
      <Trophy className="h-4 w-4 text-blue-500" />
      <span className="text-sm font-medium text-blue-700">
        Level {userExperience.level}
      </span>
    </div>
  );
}
