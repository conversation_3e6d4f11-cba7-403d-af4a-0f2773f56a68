"use client";

import React, { useState } from 'react';
import { ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { useTierAccess } from '@/hooks/use-tier-access';
import { useSidebar } from '@/components/ui/sidebar';
import {
  Crown,
  Target,
  Shield,
  Compass
} from 'lucide-react';

interface ToolItem {
  id: string;
  title: string;
  url: string;
  icon: React.ComponentType<any>;
  description: string;
  tierRequired?: string;
}

interface ToolCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  items: ToolItem[];
}

const TOOLS_CATEGORIES: ToolCategory[] = [
  {
    id: 'premium-tools',
    title: 'Premium Tools',
    icon: Crown,
    description: 'Advanced trading and analysis tools',
    items: [
      {
        id: 'Strategies & Indicators',
        title: 'Trading Bots',
        url: '/ctn/tools/indicators',
        icon: Target,
        description: 'Automated trading strategies and indicators',
        tierRequired: 'tier2'
      },
      {
        id: 'market-master',
        title: 'Market Master',
        url: '/ctn/market-master',
        icon: Crown,
        description: 'Advanced market analysis and predictions',
        tierRequired: 'tier2'
      }
    ]
  },
  {
    id: 'admin-tools',
    title: 'Admin Tools',
    icon: Shield,
    description: 'Administrative and governance tools',
    items: [
      {
        id: 'staff-access',
        title: 'Staff Access',
        url: '/ctn/staff-access',
        icon: Shield,
        description: 'Administrative dashboard and controls',
        tierRequired: 'tier3'
      },
      {
        id: 'governance',
        title: 'Governance',
        url: '/ctn/governance',
        icon: Crown,
        description: 'Platform governance and voting',
        tierRequired: 'tier3'
      },
      {
        id: 'moderation',
        title: 'Moderation',
        url: '/ctn/moderation',
        icon: Shield,
        description: 'Content moderation tools',
        tierRequired: 'tier3'
      }
    ]
  }
];

interface NavToolsAccordionProps {
  onItemClick?: () => void;
}

export function NavToolsAccordion({ onItemClick }: NavToolsAccordionProps) {
  const { userExperience, getTierLevel } = useTierAccess();
  const { state } = useSidebar();
  const [openCategories, setOpenCategories] = useState<string[]>(['premium-tools']);

  // Check if user has access to a tier-protected item
  const hasAccess = (tierRequired?: string) => {
    if (!tierRequired) return true;
    if (!userExperience) return false;
    return getTierLevel(userExperience.tier) >= getTierLevel(tierRequired);
  };

  const toggleCategory = (categoryId: string) => {
    setOpenCategories(prev =>
      prev.includes(categoryId)
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const getTierBadgeColor = (tier?: string) => {
    switch (tier) {
      case 'tier1': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'tier2': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'tier3': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    }
  };

  const getTierLabel = (tier?: string) => {
    switch (tier) {
      case 'tier1': return 'T1+';
      case 'tier2': return 'T2+';
      case 'tier3': return 'T3+';
      default: return 'Free';
    }
  };

  return (
    <SidebarGroup>
      {state === "expanded" && (
        <div className="px-2 py-2">
          <div className="flex items-center gap-2 text-xs font-medium text-muted-foreground uppercase tracking-wider">
            <Compass className="h-4 w-4" />
            <span>Tools</span>
          </div>
        </div>
      )}

      <SidebarMenu>
        {TOOLS_CATEGORIES.map((category) => {
          const isOpen = openCategories.includes(category.id);
          const accessibleItems = category.items.filter(item => hasAccess(item.tierRequired));

          if (accessibleItems.length === 0) {
            return null;
          }

          return (
            <Collapsible
              key={category.id}
              asChild
              open={isOpen}
              onOpenChange={() => toggleCategory(category.id)}
            >
              <SidebarMenuItem>
                <SidebarMenuButton asChild tooltip={category.description}>
                  <CollapsibleTrigger className="group/collapsible">
                    <category.icon className="h-4 w-4" />
                    <span>{category.title}</span>
                    <ChevronRight className="ml-auto h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </CollapsibleTrigger>
                </SidebarMenuButton>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {accessibleItems.map((item) => (
                      <SidebarMenuSubItem key={item.id}>
                        <SidebarMenuSubButton asChild>
                          <Link
                            href={item.url}
                            onClick={onItemClick}
                            className="group flex items-center gap-2 rounded-md px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground"
                          >
                            <item.icon className="h-4 w-4 text-muted-foreground group-hover:text-foreground" />
                            <span className="flex-1">{item.title}</span>
                            {item.tierRequired && (
                              <Badge
                                variant="outline"
                                className={`text-xs px-1.5 py-0.5 ${getTierBadgeColor(item.tierRequired)}`}
                              >
                                {getTierLabel(item.tierRequired)}
                              </Badge>
                            )}
                          </Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}