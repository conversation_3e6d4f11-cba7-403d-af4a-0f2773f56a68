'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Crown, Lock, ArrowRight } from 'lucide-react';
import { useTierAccess } from '@/hooks/use-tier-access';

interface TierProtectedMenuItemProps {
  item: {
    id: string;
    title: string;
    url: string;
    icon: React.ComponentType<any>;
    description: string;
    tierRequired?: string;
  };
  onItemClick?: () => void;
  className?: string;
  children?: React.ReactNode;
}

export function TierProtectedMenuItem({ 
  item, 
  onItemClick, 
  className = "",
  children 
}: TierProtectedMenuItemProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [hasAccess, setHasAccess] = useState(true);
  const [userTier, setUserTier] = useState<string>('tier0');
  const { checkTierAccess } = useTierAccess();

  // Check access when component mounts or tier requirement changes
  useEffect(() => {
    const checkAccess = async () => {
      if (!item.tierRequired) {
        setHasAccess(true);
        return;
      }

      try {
        const accessInfo = await checkTierAccess(item.url, 'view');
        setHasAccess(accessInfo.hasTierAccess);
        setUserTier(accessInfo.userTier);
      } catch (error) {
        console.error('Error checking tier access:', error);
        setHasAccess(false);
        setUserTier('tier0');
      }
    };

    checkAccess();
  }, [item.tierRequired, item.url, checkTierAccess]);

  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case 'tier1': return 'text-blue-600 border-blue-200 bg-blue-50 dark:bg-blue-950/20';
      case 'tier2': return 'text-purple-600 border-purple-200 bg-purple-50 dark:bg-purple-950/20';
      case 'tier3': return 'text-amber-600 border-amber-200 bg-amber-50 dark:bg-amber-950/20';
      default: return 'text-gray-600 border-gray-200 bg-gray-50 dark:bg-gray-950/20';
    }
  };

  const getTierLabel = (tier: string) => {
    switch (tier) {
      case 'tier1': return 'T1+';
      case 'tier2': return 'T2+';
      case 'tier3': return 'T3+';
      default: return 'Free';
    }
  };

  const handleClick = (e: React.MouseEvent) => {
    if (!hasAccess && item.tierRequired) {
      e.preventDefault();
      // Could trigger upgrade modal here
      console.log(`Access denied: ${item.title} requires ${getTierLabel(item.tierRequired)}`);
      return;
    }
    
    if (onItemClick) {
      onItemClick();
    }
  };

  // If no tier requirement, render normally
  if (!item.tierRequired) {
    return (
      <Link
        href={item.url}
        className={`flex items-center justify-between w-full hover:bg-accent/50 transition-colors ${className}`}
        onClick={onItemClick}
      >
        {children || (
          <>
            <div className="flex items-center gap-2">
              <item.icon className="h-4 w-4" />
              <span className="text-xs">{item.title}</span>
            </div>
          </>
        )}
      </Link>
    );
  }

  return (
    <div 
      className="relative"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Main menu item */}
      <Link
        href={hasAccess ? item.url : '#'}
        className={`flex items-center justify-between w-full hover:bg-accent/50 transition-colors ${
          !hasAccess ? 'cursor-not-allowed' : ''
        } ${className}`}
        onClick={handleClick}
      >
        {children || (
          <>
            <div className="flex items-center gap-2">
              <item.icon className={`h-4 w-4 ${!hasAccess ? 'text-muted-foreground' : ''}`} />
              <span className={`text-xs ${!hasAccess ? 'text-muted-foreground' : ''}`}>
                {item.title}
              </span>
            </div>
            <div className="flex items-center gap-1">
              {item.tierRequired && (
                <Badge
                  variant="outline"
                  className={`text-xs ${getTierBadgeColor(item.tierRequired)}`}
                >
                  {getTierLabel(item.tierRequired)}
                </Badge>
              )}
            </div>
          </>
        )}
      </Link>

      {/* Hover overlay for insufficient access */}
      {isHovered && !hasAccess && item.tierRequired && (
        <div className="absolute inset-0 bg-background/95 backdrop-blur-sm border border-amber-200 rounded-md flex items-center justify-between px-3 py-2 z-10 shadow-lg">
          <div className="flex items-center gap-2">
            <Lock className="h-4 w-4 text-amber-600" />
            <div className="flex flex-col">
              <span className="text-xs font-medium text-amber-700">
                {getTierLabel(item.tierRequired)} Required
              </span>
              <span className="text-xs text-muted-foreground">
                You have {getTierLabel(userTier)}
              </span>
            </div>
          </div>
          <Button
            size="sm"
            variant="outline"
            className="h-6 px-2 text-xs border-amber-200 text-amber-700 hover:bg-amber-50"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              // Navigate to upgrade page
              window.location.href = '/ctn/account/billing';
            }}
          >
            <Crown className="h-3 w-3 mr-1" />
            Upgrade
            <ArrowRight className="h-3 w-3 ml-1" />
          </Button>
        </div>
      )}
    </div>
  );
}

// Helper component for custom children content
export function TierProtectedMenuItemContent({
  item,
  hasAccess = true,
  children
}: {
  item: { icon: React.ComponentType<any>; title: string; tierRequired?: string };
  hasAccess?: boolean;
  children?: React.ReactNode;
}) {
  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case 'tier1': return 'text-blue-600 border-blue-200 bg-blue-50 dark:bg-blue-950/20';
      case 'tier2': return 'text-purple-600 border-purple-200 bg-purple-50 dark:bg-purple-950/20';
      case 'tier3': return 'text-amber-600 border-amber-200 bg-amber-50 dark:bg-amber-950/20';
      default: return 'text-gray-600 border-gray-200 bg-gray-50 dark:bg-gray-950/20';
    }
  };

  const getTierLabel = (tier: string) => {
    switch (tier) {
      case 'tier1': return 'T1+';
      case 'tier2': return 'T2+';
      case 'tier3': return 'T3+';
      default: return 'Free';
    }
  };

  return (
    <>
      <div className="flex items-center gap-2">
        <item.icon className={`h-4 w-4 ${!hasAccess ? 'text-muted-foreground' : ''}`} />
        <span className={`text-xs ${!hasAccess ? 'text-muted-foreground' : ''}`}>
          {item.title}
        </span>
      </div>
      <div className="flex items-center gap-1">
        {children}
        {item.tierRequired && (
          <Badge
            variant="outline"
            className={`text-xs ${getTierBadgeColor(item.tierRequired)}`}
          >
            {getTierLabel(item.tierRequired)}
          </Badge>
        )}
      </div>
    </>
  );
}
