"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ArticleCard } from "./ArticleCard";
import { LoadingSkeleton } from "./LoadingSkeleton";
import { Hash, TrendingUp, Calendar, Users, Eye } from "lucide-react";
import { Article, UserProfile } from "@/types";
import Link from "next/link";
import { rpcHelper } from "@/utils/supabase-rpc-helper";

interface HashtagExplorerProps {
  userProfile: UserProfile;
  initialHashtag?: string;
}

interface HashtagInfo {
  name: string;
  usage_count: number;
  recent_usage_count: number;
  description?: string;
  created_at: string;
}

interface HashtagArticle extends Article {
  relevance_score: number;
}

export default function HashtagExplorer({ userProfile, initialHashtag }: HashtagExplorerProps) {
  const [currentHashtag, setCurrentHashtag] = useState(initialHashtag || "");
  const [hashtagInfo, setHashtagInfo] = useState<HashtagInfo | null>(null);
  const [articles, setArticles] = useState<HashtagArticle[]>([]);
  const [relatedHashtags, setRelatedHashtags] = useState<HashtagInfo[]>([]);
  const [trendingHashtags, setTrendingHashtags] = useState<HashtagInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isFollowingHashtag, setIsFollowingHashtag] = useState(false);

  const supabase = createClient();

  const loadHashtagData = useCallback(async (hashtag: string) => {
    if (!hashtag.trim()) return;

    setIsLoading(true);
    try {
      // Get hashtag info by joining articles with article_hashtags and counting
      const { data: hashtagCountData, error: hashtagError } = await supabase
        .from('articles')
        .select(`
          id, 
          created_at,
          article_hashtags!inner(article_tags)
        `)
        .contains('article_hashtags.article_tags', [hashtag.toLowerCase()]);

      if (hashtagError) {
        console.warn("Error counting hashtag usage:", hashtagError);
        setHashtagInfo({
          name: hashtag,
          usage_count: 0,
          recent_usage_count: 0,
          created_at: new Date().toISOString()
        });
      } else {
        const totalCount = hashtagCountData?.length || 0;
        const recentCount = hashtagCountData?.filter(article => {
          const articleDate = new Date(article.created_at);
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          return articleDate > thirtyDaysAgo;
        }).length || 0;

        const firstUsage = hashtagCountData?.reduce((earliest, article) => {
          const articleDate = new Date(article.created_at);
          return !earliest || articleDate < earliest ? articleDate : earliest;
        }, null as Date | null);

        setHashtagInfo({
          name: hashtag,
          usage_count: totalCount,
          recent_usage_count: recentCount,
          created_at: firstUsage?.toISOString() || new Date().toISOString()
        });
      }

      // Get articles with this hashtag using the new relationship
      const { data: articlesData, error: articlesError } = await supabase
        .from('articles')
        .select(`
          *,
          article_hashtags!inner(article_tags)
        `)
        .contains('article_hashtags.article_tags', [hashtag.toLowerCase()])
        .order('created_at', { ascending: false })
        .limit(20);

      if (articlesError) {
        console.warn("Error loading hashtag articles:", articlesError);
        setArticles([]);
      } else {
        // Transform articles data and add relevance score
        const transformedArticles = articlesData?.map((article, index) => ({
          ...article,
          relevance_score: 100 - (index * 2) // Simple relevance scoring
        })) || [];
        setArticles(transformedArticles);
      }

      // Get related hashtags from article_hashtags table
      const { data: relatedData, error: relatedError } = await supabase
        .from('article_hashtags')
        .select('article_tags')
        .not('article_tags', 'cs', `{${hashtag.toLowerCase()}}`) // Exclude current hashtag
        .limit(50); // Get more to analyze

      if (relatedError) {
        console.warn("Error loading related hashtags:", relatedError);
        setRelatedHashtags([]);
      } else {
        // Process related hashtags by counting occurrences
        const hashtagCounts = new Map<string, number>();
        
        relatedData?.forEach(row => {
          row.article_tags?.forEach((tag: string) => {
            if (tag.toLowerCase() !== hashtag.toLowerCase()) {
              const count = hashtagCounts.get(tag) || 0;
              hashtagCounts.set(tag, count + 1);
            }
          });
        });

        // Convert to sorted array
        const sortedRelated = Array.from(hashtagCounts.entries())
          .sort(([,a], [,b]) => b - a)
          .slice(0, 10)
          .map(([name, usage_count]) => ({
            name,
            usage_count,
            recent_usage_count: Math.floor(usage_count * 0.7), // Estimate recent usage
            created_at: new Date().toISOString()
          }));

        setRelatedHashtags(sortedRelated);
      }

      // Check if user is following this hashtag (if hashtag_follows table exists)
      try {
        const { data: followData, error: followError } = await supabase
          .from("hashtag_follows")
          .select("id")
          .eq("user_id", userProfile.user_id)
          .eq("hashtag_name", hashtag.toLowerCase())
          .maybeSingle();

        if (!followError) {
          setIsFollowingHashtag(!!followData);
        } else {
          setIsFollowingHashtag(false);
        }
      } catch (error) {
        // Hashtag follows table might not exist
        setIsFollowingHashtag(false);
      }
    } catch (error) {
      console.error("Error loading hashtag data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [userProfile.user_id, supabase]);

  const loadTrendingHashtags = useCallback(async () => {
    try {
      const { data } = await rpcHelper.getTrendingHashtags(15);
      setTrendingHashtags(data || []);
    } catch (error) {
      console.error("Error loading trending hashtags:", error);
      setTrendingHashtags([]);
    }
  }, []);

  useEffect(() => {
    loadTrendingHashtags();
  }, [loadTrendingHashtags]);

  useEffect(() => {
    if (currentHashtag) {
      loadHashtagData(currentHashtag);
    }
  }, [currentHashtag, loadHashtagData]);

  const toggleFollowHashtag = async () => {
    if (!currentHashtag) return;

    try {
      if (isFollowingHashtag) {
        const { error } = await supabase
          .from("hashtag_follows")
          .delete()
          .eq("user_id", userProfile.user_id)
          .eq("hashtag_name", currentHashtag.toLowerCase());

        if (error) throw error;
        setIsFollowingHashtag(false);
      } else {
        const { error } = await supabase
          .from("hashtag_follows")
          .insert({
            user_id: userProfile.user_id,
            hashtag_name: currentHashtag.toLowerCase(),
          });

        if (error) throw error;
        setIsFollowingHashtag(true);
      }
    } catch (error) {
      console.error("Error toggling hashtag follow:", error);
      // If table doesn't exist, just ignore the error
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      const cleanQuery = searchQuery.replace(/^#/, "");
      setCurrentHashtag(cleanQuery);
      setSearchQuery("");
    }
  };

  const formatCount = (count: number) => {
    if (count >= 1000000) return `${(count / 1000000).toFixed(1)}M`;
    if (count >= 1000) return `${(count / 1000).toFixed(1)}K`;
    return count.toString();
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold flex items-center justify-center">
          <Hash className="h-8 w-8 mr-2" />
          Hashtag Explorer
        </h1>
        <p className="text-muted-foreground">
          Discover trending topics and explore content by hashtags
        </p>

        {/* Search */}
        <form onSubmit={handleSearch} className="max-w-md mx-auto">
          <div className="flex gap-2">
            <Input
              type="text"
              placeholder="Search hashtags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button type="submit">Search</Button>
          </div>
        </form>
      </div>

      {/* Current Hashtag Info */}
      {currentHashtag && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Hash className="h-5 w-5 mr-2" />
                #{currentHashtag}
              </CardTitle>
              <Button
                onClick={toggleFollowHashtag}
                variant={isFollowingHashtag ? "default" : "outline"}
                size="sm"
              >
                {isFollowingHashtag ? "Following" : "Follow"}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {hashtagInfo ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <Eye className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {formatCount(hashtagInfo.usage_count)} total uses
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    {formatCount(hashtagInfo.recent_usage_count)} recent uses
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Since {new Date(hashtagInfo.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">Loading hashtag information...</p>
            )}
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {currentHashtag ? (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">
                Articles tagged #{currentHashtag}
              </h2>
              
              {isLoading ? (
                <div className="space-y-4">
                  <LoadingSkeleton />
                  <LoadingSkeleton />
                  <LoadingSkeleton />
                </div>
              ) : articles.length > 0 ? (
                <div className="space-y-4">
                  {articles.map((article) => (
                    <ArticleCard 
                      key={article.id} 
                      article={article} 
                      userProfile={userProfile}
                    />
                  ))}
                </div>
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <Hash className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">No articles found</h3>
                    <p className="text-muted-foreground">
                      No articles have been tagged with #{currentHashtag} yet.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Hash className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-xl font-semibold mb-2">Explore Hashtags</h3>
                <p className="text-muted-foreground mb-4">
                  Search for a hashtag above or click on trending tags to get started.
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Trending Hashtags */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Trending Hashtags
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {trendingHashtags.map((hashtag) => (
                  <div
                    key={hashtag.name}
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-muted cursor-pointer transition-colors"
                    onClick={() => setCurrentHashtag(hashtag.name)}
                  >
                    <div className="flex items-center space-x-2">
                      <Hash className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">#{hashtag.name}</span>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {formatCount(hashtag.recent_usage_count)}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Related Hashtags */}
          {currentHashtag && relatedHashtags.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Hash className="h-5 w-5 mr-2" />
                  Related Hashtags
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {relatedHashtags.map((hashtag) => (
                    <Badge
                      key={hashtag.name}
                      variant="outline"
                      className="cursor-pointer hover:bg-primary hover:text-primary-foreground"
                      onClick={() => setCurrentHashtag(hashtag.name)}
                    >
                      #{hashtag.name}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
