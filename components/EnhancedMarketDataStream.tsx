import React, { useEffect, useRef, useCallback, useState } from 'react';

export interface MarketDataPoint {
  symbol: string;
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export interface StreamConfig {
  symbol: string;
  interval: string;
  onUpdate: (data: MarketDataPoint) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
}

export interface StreamConnectionStatus {
  status: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';
  attempts: number;
  lastError?: string;
}

export class EnhancedMarketDataStream {
  private wsRef: WebSocket | null = null;
  private config: StreamConfig;
  private reconnectTimeoutRef: NodeJS.Timeout | null = null;
  private connectionStatus: StreamConnectionStatus;
  private onStatusChange?: (status: StreamConnectionStatus) => void;

  constructor(config: StreamConfig, onStatusChange?: (status: StreamConnectionStatus) => void) {
    this.config = {
      reconnectDelay: 3000, // Faster reconnect attempts
      maxReconnectAttempts: 3, // Fewer attempts since WebSocket often fails in dev
      ...config,
    };
    this.connectionStatus = {
      status: 'disconnected',
      attempts: 0,
    };
    this.onStatusChange = onStatusChange;
  }

  private updateStatus(update: Partial<StreamConnectionStatus>) {
    this.connectionStatus = { ...this.connectionStatus, ...update };
    this.onStatusChange?.(this.connectionStatus);
  }

  connect() {
    if (this.wsRef?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return;
    }

    this.cleanup();
    this.updateStatus({ status: 'connecting' });

    // Multiple WebSocket endpoints to try
    const endpoints = [
      `wss://stream.binance.com:9443/ws/${this.config.symbol.toLowerCase()}@kline_${this.config.interval}`,
      `wss://stream.binance.com/ws/${this.config.symbol.toLowerCase()}@kline_${this.config.interval}`,
      `wss://data-stream.binance.vision/ws/${this.config.symbol.toLowerCase()}@kline_${this.config.interval}`,
    ];

    const tryConnect = (endpointIndex: number = 0) => {
      if (endpointIndex >= endpoints.length) {
        console.warn(`❌ All WebSocket endpoints failed for ${this.config.symbol}. WebSocket streaming not available - this is normal in development due to CORS restrictions.`);
        
        // Don't treat this as a critical error - just inform that WebSocket isn't available
        this.updateStatus({ 
          status: 'error', 
          lastError: 'WebSocket unavailable (CORS/Network restrictions)' 
        });
        
        // Call error callback to trigger fallback
        this.config.onError?.('WebSocket streaming unavailable - using simulation fallback');
        return;
      }

      const wsUrl = endpoints[endpointIndex];
      console.log(`Attempting to connect to: ${wsUrl}`);

      try {
        const ws = new WebSocket(wsUrl);
        this.wsRef = ws;

        // Connection timeout - shorter for faster fallback
        const connectTimeout = setTimeout(() => {
          if (ws.readyState === WebSocket.CONNECTING) {
            console.warn(`Connection timeout for ${wsUrl}, trying next endpoint`);
            ws.close();
            tryConnect(endpointIndex + 1);
          }
        }, 5000); // Reduced from 10s to 5s

        ws.onopen = () => {
          clearTimeout(connectTimeout);
          console.log(`✅ Connected to ${this.config.symbol} at ${wsUrl}`);
          this.updateStatus({ 
            status: 'connected', 
            attempts: 0,
            lastError: undefined 
          });
          this.config.onConnect?.();
        };

        ws.onmessage = (event) => {
          try {
            const msg = JSON.parse(event.data);
            if (msg.k && msg.k.x) { // Only process closed/completed candles
              const kline = msg.k;
              const dataPoint: MarketDataPoint = {
                symbol: this.config.symbol,
                timestamp: parseInt(kline.t),
                open: parseFloat(kline.o),
                high: parseFloat(kline.h),
                low: parseFloat(kline.l),
                close: parseFloat(kline.c),
                volume: parseFloat(kline.v),
              };
              
              this.config.onUpdate(dataPoint);
            }
          } catch (error) {
            console.warn('Error parsing WebSocket message:', error);
          }
        };

        ws.onerror = (error) => {
          clearTimeout(connectTimeout);
          console.warn(`⚠️ WebSocket connection failed for ${this.config.symbol} at ${wsUrl}:`, error);
          
          // Don't immediately set error status - let it try other endpoints first
          console.log(`Trying next endpoint (${endpointIndex + 1}/${endpoints.length})...`);
          
          // Try next endpoint
          tryConnect(endpointIndex + 1);
        };

        ws.onclose = (event) => {
          clearTimeout(connectTimeout);
          console.log(`🔌 WebSocket closed for ${this.config.symbol}:`, {
            code: event.code,
            reason: event.reason,
            wasClean: event.wasClean,
          });

          if (event.code !== 1000 && this.connectionStatus.status !== 'error') {
            this.scheduleReconnect();
          } else {
            this.updateStatus({ status: 'disconnected' });
            this.config.onDisconnect?.();
          }
        };

      } catch (error) {
        console.error('Failed to create WebSocket:', error);
        tryConnect(endpointIndex + 1);
      }
    };

    tryConnect();
  }

  private scheduleReconnect() {
    if (this.connectionStatus.attempts >= (this.config.maxReconnectAttempts || 10)) {
      console.log(`Max reconnection attempts (${this.config.maxReconnectAttempts}) reached for ${this.config.symbol}. Switching to simulation mode.`);
      this.updateStatus({ 
        status: 'error', 
        lastError: 'Max reconnection attempts reached - using simulation' 
      });
      
      // Trigger fallback to simulation
      this.config.onError?.('Max reconnection attempts reached - using simulation fallback');
      return;
    }

    this.updateStatus({ 
      status: 'reconnecting', 
      attempts: this.connectionStatus.attempts + 1 
    });

    console.log(`⏰ Scheduling reconnect attempt ${this.connectionStatus.attempts + 1}/${this.config.maxReconnectAttempts} in ${this.config.reconnectDelay}ms`);
    
    this.reconnectTimeoutRef = setTimeout(() => {
      this.reconnectTimeoutRef = null;
      this.connect();
    }, this.config.reconnectDelay);
  }

  disconnect() {
    this.cleanup();
    this.updateStatus({ status: 'disconnected', attempts: 0 });
    this.config.onDisconnect?.();
  }

  private cleanup() {
    if (this.reconnectTimeoutRef) {
      clearTimeout(this.reconnectTimeoutRef);
      this.reconnectTimeoutRef = null;
    }

    if (this.wsRef) {
      if (this.wsRef.readyState === WebSocket.OPEN || this.wsRef.readyState === WebSocket.CONNECTING) {
        this.wsRef.close(1000, 'Manual disconnect');
      }
      this.wsRef = null;
    }
  }

  updateSymbol(symbol: string, interval?: string) {
    this.config.symbol = symbol;
    if (interval) {
      this.config.interval = interval;
    }
    
    if (this.connectionStatus.status === 'connected') {
      this.disconnect();
      setTimeout(() => this.connect(), 100);
    }
  }

  getStatus(): StreamConnectionStatus {
    return { ...this.connectionStatus };
  }

  destroy() {
    this.cleanup();
    this.onStatusChange = undefined;
  }
}

// React Hook for using the enhanced market data stream
export function useMarketDataStream(config: StreamConfig) {
  const streamRef = useRef<EnhancedMarketDataStream | null>(null);
  const [status, setStatus] = useState<StreamConnectionStatus>({
    status: 'disconnected',
    attempts: 0,
  });

  const connect = useCallback(() => {
    if (!streamRef.current) {
      streamRef.current = new EnhancedMarketDataStream(config, setStatus);
    }
    streamRef.current.connect();
  }, [config]);

  const disconnect = useCallback(() => {
    streamRef.current?.disconnect();
  }, []);

  const updateSymbol = useCallback((symbol: string, interval?: string) => {
    streamRef.current?.updateSymbol(symbol, interval);
  }, []);

  useEffect(() => {
    return () => {
      streamRef.current?.destroy();
    };
  }, []);

  return {
    connect,
    disconnect,
    updateSymbol,
    status,
    isConnected: status.status === 'connected',
    isConnecting: status.status === 'connecting' || status.status === 'reconnecting',
  };
}

// React Component for market data streaming
export const MarketDataStreamProvider: React.FC<{
  symbol: string;
  interval: string;
  onUpdate: (data: MarketDataPoint) => void;
  autoConnect?: boolean;
  children?: React.ReactNode;
}> = ({ symbol, interval, onUpdate, autoConnect = true, children }) => {
  const stream = useMarketDataStream({
    symbol,
    interval,
    onUpdate,
  });

  useEffect(() => {
    if (autoConnect) {
      stream.connect();
    }
    return () => {
      stream.disconnect();
    };
  }, [stream, autoConnect]);

  useEffect(() => {
    if (stream.isConnected) {
      stream.updateSymbol(symbol, interval);
    }
  }, [symbol, interval, stream]);

  return <>{children}</>;
};