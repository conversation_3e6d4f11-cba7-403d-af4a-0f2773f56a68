"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calculator, 
  Download, 
  Plus, 
  Trash2, 
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus,
  FileSpreadsheet,
  Crown
} from 'lucide-react';

interface ChainData {
  id: string;
  name: string;
  symbol: string;
  dailyActiveAddresses: number;
  dailyTransactions: number;
  tvl: number;
  marketCap: number;
  fees: number;
  validators: number;
  activeDevelopers: number;
  realTimeTps: number;
  avgFee: number;
  finalityTime: number;
  energyPerTx: number;
}

interface ManualComparisonToolProps {
  isActive?: boolean;
  onRefresh?: () => void;
}

// Derived metrics calculations based on tools.md
const calculateDerivedMetrics = (chain: ChainData) => {
  const metrics: Record<string, number> = {};
  
  // All the formulas from tools.md
  if (chain.marketCap && chain.dailyActiveAddresses) {
    metrics.fdmc_daa = chain.marketCap / chain.dailyActiveAddresses;
  }
  
  if (chain.marketCap && chain.dailyTransactions) {
    metrics.fdmc_transactions = chain.marketCap / chain.dailyTransactions;
  }
  
  if (chain.marketCap && chain.tvl) {
    metrics.mc_tvl = chain.marketCap / Math.max(chain.tvl, 1);
  }
  
  if (chain.fees && chain.dailyTransactions) {
    metrics.fees_transactions = chain.fees / chain.dailyTransactions;
  }
  
  if (chain.marketCap && chain.activeDevelopers) {
    metrics.mc_developers = chain.marketCap / chain.activeDevelopers;
  }
  
  if (chain.dailyTransactions && chain.dailyActiveAddresses) {
    metrics.tx_daa = chain.dailyTransactions / chain.dailyActiveAddresses;
  }
  
  if (chain.tvl && chain.dailyActiveAddresses) {
    metrics.tvl_daa = Math.max(chain.tvl, 1) / chain.dailyActiveAddresses;
  }
  
  if (chain.fees && chain.dailyActiveAddresses) {
    metrics.fees_daa = chain.fees / chain.dailyActiveAddresses;
  }
  
  if (chain.avgFee && chain.realTimeTps) {
    metrics.fee_tps = chain.avgFee / chain.realTimeTps;
  }
  
  if (chain.energyPerTx && chain.realTimeTps) {
    metrics.energy_tps = chain.energyPerTx / chain.realTimeTps;
  }
  
  if (chain.validators) {
    if (chain.dailyActiveAddresses) {
      metrics.daa_validators = chain.dailyActiveAddresses / chain.validators;
    }
    if (chain.marketCap) {
      metrics.mc_validators = chain.marketCap / chain.validators;
    }
    if (chain.dailyTransactions) {
      metrics.tx_validators = chain.dailyTransactions / chain.validators;
    }
    if (chain.fees) {
      metrics.fees_validators = chain.fees / chain.validators;
    }
  }
  
  return metrics;
};

// Export to CSV functionality
const exportToCSV = (chains: ChainData[]) => {
  const headers = [
    'Chain Name',
    'Symbol', 
    'Daily Active Addresses',
    'Daily Transactions',
    'TVL',
    'Market Cap',
    'Fees',
    'Validators',
    'Active Developers',
    'Real-time TPS',
    'Avg Fee',
    'Finality Time',
    'Energy per Tx',
    'FDMC/DAA',
    'FDMC/Transactions',
    'MC/TVL',
    'Fees/Transactions',
    'MC/Developers',
    'TX/DAA',
    'TVL/DAA',
    'Fees/DAA',
    'Fee/TPS',
    'Energy/TPS',
    'DAA/Validators',
    'MC/Validators',
    'TX/Validators',
    'Fees/Validators'
  ];
  
  const rows = chains.map(chain => {
    const derived = calculateDerivedMetrics(chain);
    return [
      chain.name,
      chain.symbol,
      chain.dailyActiveAddresses,
      chain.dailyTransactions,
      chain.tvl,
      chain.marketCap,
      chain.fees,
      chain.validators,
      chain.activeDevelopers,
      chain.realTimeTps,
      chain.avgFee,
      chain.finalityTime,
      chain.energyPerTx,
      derived.fdmc_daa?.toFixed(2) || 'N/A',
      derived.fdmc_transactions?.toFixed(2) || 'N/A',
      derived.mc_tvl?.toFixed(2) || 'N/A',
      derived.fees_transactions?.toFixed(6) || 'N/A',
      derived.mc_developers?.toFixed(0) || 'N/A',
      derived.tx_daa?.toFixed(2) || 'N/A',
      derived.tvl_daa?.toFixed(2) || 'N/A',
      derived.fees_daa?.toFixed(6) || 'N/A',
      derived.fee_tps?.toFixed(6) || 'N/A',
      derived.energy_tps?.toFixed(0) || 'N/A',
      derived.daa_validators?.toFixed(0) || 'N/A',
      derived.mc_validators?.toFixed(0) || 'N/A',
      derived.tx_validators?.toFixed(0) || 'N/A',
      derived.fees_validators?.toFixed(2) || 'N/A'
    ];
  });
  
  const csvContent = [headers, ...rows]
    .map(row => row.map(cell => `"${cell}"`).join(','))
    .join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `blockchain_comparison_${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export function ManualComparisonTool({ onRefresh }: ManualComparisonToolProps) {
  const [chains, setChains] = useState<ChainData[]>([
    {
      id: '1',
      name: '',
      symbol: '',
      dailyActiveAddresses: 0,
      dailyTransactions: 0,
      tvl: 0,
      marketCap: 0,
      fees: 0,
      validators: 0,
      activeDevelopers: 0,
      realTimeTps: 0,
      avgFee: 0,
      finalityTime: 0,
      energyPerTx: 0
    }
  ]);

  const addChain = () => {
    const newChain: ChainData = {
      id: Date.now().toString(),
      name: '',
      symbol: '',
      dailyActiveAddresses: 0,
      dailyTransactions: 0,
      tvl: 0,
      marketCap: 0,
      fees: 0,
      validators: 0,
      activeDevelopers: 0,
      realTimeTps: 0,
      avgFee: 0,
      finalityTime: 0,
      energyPerTx: 0
    };
    setChains([...chains, newChain]);
  };

  const removeChain = (id: string) => {
    setChains(chains.filter(chain => chain.id !== id));
  };

  const updateChain = (id: string, field: keyof ChainData, value: string | number) => {
    setChains(chains.map(chain => 
      chain.id === id ? { ...chain, [field]: value } : chain
    ));
  };

  const calculateScore = (chain: ChainData) => {
    const derived = calculateDerivedMetrics(chain);
    let score = 0;
    let factors = 0;

    // Simple scoring algorithm
    if (chain.realTimeTps > 0) {
      score += Math.min(chain.realTimeTps / 1000 * 20, 20);
      factors++;
    }
    
    if (chain.avgFee >= 0) {
      score += Math.max(20 - chain.avgFee * 10, 0);
      factors++;
    }
    
    if (chain.validators > 0) {
      score += Math.min(Math.log10(chain.validators) * 5, 15);
      factors++;
    }
    
    if (chain.activeDevelopers > 0) {
      score += Math.min(chain.activeDevelopers / 100 * 15, 15);
      factors++;
    }
    
    if (chain.finalityTime > 0) {
      score += Math.max(15 - Math.log10(chain.finalityTime) * 3, 0);
      factors++;
    }

    return factors > 0 ? Math.round(score / factors * 5) : 0; // Scale to 100
  };

  const formatNumber = (num: number, decimals: number = 2) => {
    if (num === 0) return '0';
    if (num >= 1e9) return `${(num / 1e9).toFixed(decimals)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(decimals)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(decimals)}K`;
    return num.toFixed(decimals);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold flex items-center gap-2">
            <Calculator className="h-5 w-5 text-blue-500" />
            Manual Comparison Tool
            <Badge variant="outline" className="text-xs text-blue-600">Tier 1+</Badge>
          </h3>
          <p className="text-sm text-muted-foreground">
            Input custom metrics to compare blockchain networks manually
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={addChain}>
            <Plus className="h-4 w-4 mr-2" />
            Add Chain
          </Button>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => exportToCSV(chains)}
            disabled={chains.length === 0 || chains.every(c => !c.name)}
          >
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <Tabs defaultValue="input" className="space-y-6">
        <TabsList>
          <TabsTrigger value="input">Data Input</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="metrics">Derived Metrics</TabsTrigger>
        </TabsList>

        <TabsContent value="input" className="space-y-4">
          {chains.map((chain, index) => (
            <Card key={chain.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">
                    Chain {index + 1}: {chain.name || 'Unnamed'}
                  </CardTitle>
                  {chains.length > 1 && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => removeChain(chain.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor={`name-${chain.id}`}>Chain Name</Label>
                    <Input
                      id={`name-${chain.id}`}
                      value={chain.name}
                      onChange={(e) => updateChain(chain.id, 'name', e.target.value)}
                      placeholder="e.g., Bitcoin"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`symbol-${chain.id}`}>Symbol</Label>
                    <Input
                      id={`symbol-${chain.id}`}
                      value={chain.symbol}
                      onChange={(e) => updateChain(chain.id, 'symbol', e.target.value)}
                      placeholder="e.g., BTC"
                    />
                  </div>
                  <div>
                    <Label htmlFor={`daa-${chain.id}`}>Daily Active Addresses</Label>
                    <Input
                      id={`daa-${chain.id}`}
                      type="number"
                      value={chain.dailyActiveAddresses}
                      onChange={(e) => updateChain(chain.id, 'dailyActiveAddresses', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`tx-${chain.id}`}>Daily Transactions</Label>
                    <Input
                      id={`tx-${chain.id}`}
                      type="number"
                      value={chain.dailyTransactions}
                      onChange={(e) => updateChain(chain.id, 'dailyTransactions', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`tvl-${chain.id}`}>TVL (USD)</Label>
                    <Input
                      id={`tvl-${chain.id}`}
                      type="number"
                      value={chain.tvl}
                      onChange={(e) => updateChain(chain.id, 'tvl', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`mc-${chain.id}`}>Market Cap (USD)</Label>
                    <Input
                      id={`mc-${chain.id}`}
                      type="number"
                      value={chain.marketCap}
                      onChange={(e) => updateChain(chain.id, 'marketCap', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`fees-${chain.id}`}>Daily Fees (USD)</Label>
                    <Input
                      id={`fees-${chain.id}`}
                      type="number"
                      value={chain.fees}
                      onChange={(e) => updateChain(chain.id, 'fees', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`validators-${chain.id}`}>Validators</Label>
                    <Input
                      id={`validators-${chain.id}`}
                      type="number"
                      value={chain.validators}
                      onChange={(e) => updateChain(chain.id, 'validators', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`devs-${chain.id}`}>Active Developers</Label>
                    <Input
                      id={`devs-${chain.id}`}
                      type="number"
                      value={chain.activeDevelopers}
                      onChange={(e) => updateChain(chain.id, 'activeDevelopers', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`tps-${chain.id}`}>Real-time TPS</Label>
                    <Input
                      id={`tps-${chain.id}`}
                      type="number"
                      step="0.1"
                      value={chain.realTimeTps}
                      onChange={(e) => updateChain(chain.id, 'realTimeTps', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`fee-${chain.id}`}>Avg Fee (USD)</Label>
                    <Input
                      id={`fee-${chain.id}`}
                      type="number"
                      step="0.001"
                      value={chain.avgFee}
                      onChange={(e) => updateChain(chain.id, 'avgFee', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor={`finality-${chain.id}`}>Finality Time (seconds)</Label>
                    <Input
                      id={`finality-${chain.id}`}
                      type="number"
                      step="0.1"
                      value={chain.finalityTime}
                      onChange={(e) => updateChain(chain.id, 'finalityTime', parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5 text-yellow-500" />
                Comparison Results
              </CardTitle>
              <CardDescription>
                Ranked comparison of all chains based on input metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {chains
                  .filter(chain => chain.name)
                  .sort((a, b) => calculateScore(b) - calculateScore(a))
                  .map((chain, index) => {
                    const score = calculateScore(chain);
                    return (
                      <div key={chain.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div className="text-2xl font-bold text-muted-foreground">
                            #{index + 1}
                          </div>
                          <div>
                            <div className="font-semibold">{chain.name}</div>
                            <div className="text-sm text-muted-foreground">{chain.symbol}</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold">{score}/100</div>
                          <div className="text-sm text-muted-foreground">Overall Score</div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Derived Metrics</CardTitle>
              <CardDescription>
                Calculated metrics based on the formulas from tools.md specification
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Chain</th>
                      <th className="text-right p-2">FDMC/DAA</th>
                      <th className="text-right p-2">MC/TVL</th>
                      <th className="text-right p-2">TX/DAA</th>
                      <th className="text-right p-2">Fees/TX</th>
                      <th className="text-right p-2">MC/Devs</th>
                      <th className="text-right p-2">DAA/Validators</th>
                    </tr>
                  </thead>
                  <tbody>
                    {chains.filter(chain => chain.name).map(chain => {
                      const derived = calculateDerivedMetrics(chain);
                      return (
                        <tr key={chain.id} className="border-b">
                          <td className="p-2 font-medium">{chain.name}</td>
                          <td className="text-right p-2">{formatNumber(derived.fdmc_daa || 0)}</td>
                          <td className="text-right p-2">{formatNumber(derived.mc_tvl || 0)}</td>
                          <td className="text-right p-2">{formatNumber(derived.tx_daa || 0)}</td>
                          <td className="text-right p-2">${formatNumber(derived.fees_transactions || 0, 6)}</td>
                          <td className="text-right p-2">{formatNumber(derived.mc_developers || 0, 0)}</td>
                          <td className="text-right p-2">{formatNumber(derived.daa_validators || 0, 0)}</td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
