"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Shield, 
  UserX, 
  Volume, 
  VolumeX, 
  Eye, 
  EyeOff, 
  Lock,
  AlertTriangle,
  Ban,
  CheckCircle
} from "lucide-react";
import { UserProfile } from "@/types";
import { toast } from "@/hooks/use-toast";

interface UserSafetyProps {
  userProfile: UserProfile;
}

interface BlockedUser {
  id: string;
  username: string;
  avatar_url: string | null;
  blocked_at: string;
  reason?: string;
}

interface MutedUser {
  id: string;
  username: string;
  avatar_url: string | null;
  muted_at: string;
  duration?: string;
}

interface PrivacySettings {
  profile_visibility: "public" | "followers" | "private";
  show_followers: boolean;
  show_following: boolean;
  show_activity: boolean;
  allow_mentions: "everyone" | "followers" | "none";
  allow_direct_messages: "everyone" | "followers" | "none";
  show_online_status: boolean;
  content_warnings: boolean;
}

export default function UserSafety({ userProfile }: UserSafetyProps) {
  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([]);
  const [mutedUsers, setMutedUsers] = useState<MutedUser[]>([]);
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    profile_visibility: "public",
    show_followers: true,
    show_following: true,
    show_activity: true,
    allow_mentions: "everyone",
    allow_direct_messages: "followers",
    show_online_status: true,
    content_warnings: false,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"privacy" | "blocked" | "muted">("privacy");
  const [reportText, setReportText] = useState("");
  const [reportReason, setReportReason] = useState("");

  const supabase = createClient();

  const loadUserSafetyData = useCallback(async () => {
    try {
      setIsLoading(true);

      // Load blocked users
      const { data: blockedData, error: blockedError } = await (supabase as any)
        .from("blocked_users")
        .select(`
          blocked_user_id,
          reason,
          created_at,
          users!blocked_users_blocked_user_id_fkey(username, avatar_url)
        `)
        .eq("user_id", userProfile.user_id);

      if (blockedError) {
        console.error("Error loading blocked users:", blockedError);
      } else {
        setBlockedUsers(
          (blockedData || []).map((item: any) => ({
            id: item.blocked_user_id,
            username: item.users.username,
            avatar_url: item.users.avatar_url,
            blocked_at: item.created_at,
            reason: item.reason,
          }))
        );
      }

      // Load muted users
      const { data: mutedData, error: mutedError } = await (supabase as any)
        .from("muted_users")
        .select(`
          muted_user_id,
          duration,
          created_at,
          users!muted_users_muted_user_id_fkey(username, avatar_url)
        `)
        .eq("user_id", userProfile.user_id);

      if (mutedError) {
        console.error("Error loading muted users:", mutedError);
      } else {
        setMutedUsers(
          (mutedData || []).map((item: any) => ({
            id: item.muted_user_id,
            username: item.users.username,
            avatar_url: item.users.avatar_url,
            muted_at: item.created_at,
            duration: item.duration,
          }))
        );
      }

      // Load privacy settings
      const { data: privacyData, error: privacyError } = await (supabase as any)
        .from("privacy_settings")
        .select("*")
        .eq("user_id", userProfile.user_id)
        .single();

      if (privacyError && privacyError.code !== "PGRST116") {
        console.error("Error loading privacy settings:", privacyError);
      } else if (privacyData) {
        setPrivacySettings(privacyData);
      }
    } catch (error) {
      console.error("Error loading user safety data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [userProfile.user_id, supabase]);

  useEffect(() => {
    loadUserSafetyData();
  }, [loadUserSafetyData]);

  const updatePrivacySettings = async (newSettings: Partial<PrivacySettings>) => {
    try {
      const updatedSettings = { ...privacySettings, ...newSettings };

      const { error } = await (supabase as any)
        .from("privacy_settings")
        .upsert({
          user_id: userProfile.user_id,
          ...updatedSettings,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      setPrivacySettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "Privacy settings updated",
      });
    } catch (error) {
      console.error("Error updating privacy settings:", error);
      toast({
        title: "Error",
        description: "Failed to update privacy settings",
        variant: "destructive",
      });
    }
  };

  const unblockUser = async (userId: string) => {
    try {
      const { error } = await (supabase as any)
        .from("blocked_users")
        .delete()
        .eq("user_id", userProfile.user_id)
        .eq("blocked_user_id", userId);

      if (error) throw error;

      setBlockedUsers(prev => prev.filter(user => user.id !== userId));
      
      toast({
        title: "Success",
        description: "User unblocked",
      });
    } catch (error) {
      console.error("Error unblocking user:", error);
      toast({
        title: "Error",
        description: "Failed to unblock user",
        variant: "destructive",
      });
    }
  };

  const unmuteUser = async (userId: string) => {
    try {
      const { error } = await (supabase as any)
        .from("muted_users")
        .delete()
        .eq("user_id", userProfile.user_id)
        .eq("muted_user_id", userId);

      if (error) throw error;

      setMutedUsers(prev => prev.filter(user => user.id !== userId));
      
      toast({
        title: "Success",
        description: "User unmuted",
      });
    } catch (error) {
      console.error("Error unmuting user:", error);
      toast({
        title: "Error",
        description: "Failed to unmute user",
        variant: "destructive",
      });
    }
  };

  const submitReport = async () => {
    if (!reportText.trim() || !reportReason) {
      toast({
        title: "Error",
        description: "Please fill in all fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await (supabase as any).rpc("create_safety_report", {
        p_reporter_id: userProfile.user_id,
        p_reason: reportReason,
        p_description: reportText,
        p_report_type: "safety_concern",
      });

      if (error) throw error;

      setReportText("");
      setReportReason("");
      
      toast({
        title: "Success",
        description: "Report submitted successfully",
      });
    } catch (error) {
      console.error("Error submitting report:", error);
      toast({
        title: "Error",
        description: "Failed to submit report",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold flex items-center">
          <Shield className="h-8 w-8 mr-2" />
          Safety & Privacy
        </h1>
        <p className="text-muted-foreground">
          Manage your privacy settings and safety preferences
        </p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg">
        <Button
          variant={activeTab === "privacy" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("privacy")}
          className="flex-1"
        >
          <Lock className="h-4 w-4 mr-2" />
          Privacy
        </Button>
        <Button
          variant={activeTab === "blocked" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("blocked")}
          className="flex-1"
        >
          <UserX className="h-4 w-4 mr-2" />
          Blocked ({blockedUsers.length})
        </Button>
        <Button
          variant={activeTab === "muted" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("muted")}
          className="flex-1"
        >
          <VolumeX className="h-4 w-4 mr-2" />
          Muted ({mutedUsers.length})
        </Button>
      </div>

      {/* Privacy Settings */}
      {activeTab === "privacy" && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profile Visibility</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Who can see your profile
                </label>
                <Select
                  value={privacySettings.profile_visibility}
                  onValueChange={(value: any) =>
                    updatePrivacySettings({ profile_visibility: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Everyone</SelectItem>
                    <SelectItem value="followers">Followers only</SelectItem>
                    <SelectItem value="private">Private</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Show Followers List</h4>
                    <p className="text-sm text-muted-foreground">
                      Allow others to see who follows you
                    </p>
                  </div>
                  <Switch
                    checked={privacySettings.show_followers}
                    onCheckedChange={(checked) =>
                      updatePrivacySettings({ show_followers: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Show Following List</h4>
                    <p className="text-sm text-muted-foreground">
                      Allow others to see who you follow
                    </p>
                  </div>
                  <Switch
                    checked={privacySettings.show_following}
                    onCheckedChange={(checked) =>
                      updatePrivacySettings({ show_following: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">Show Activity</h4>
                    <p className="text-sm text-muted-foreground">
                      Show your recent likes and comments
                    </p>
                  </div>
                  <Switch
                    checked={privacySettings.show_activity}
                    onCheckedChange={(checked) =>
                      updatePrivacySettings({ show_activity: checked })
                    }
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Communication Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Who can mention you
                </label>
                <Select
                  value={privacySettings.allow_mentions}
                  onValueChange={(value: any) =>
                    updatePrivacySettings({ allow_mentions: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="everyone">Everyone</SelectItem>
                    <SelectItem value="followers">Followers only</SelectItem>
                    <SelectItem value="none">No one</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Who can send you direct messages
                </label>
                <Select
                  value={privacySettings.allow_direct_messages}
                  onValueChange={(value: any) =>
                    updatePrivacySettings({ allow_direct_messages: value })
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="everyone">Everyone</SelectItem>
                    <SelectItem value="followers">Followers only</SelectItem>
                    <SelectItem value="none">No one</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Show Online Status</h4>
                  <p className="text-sm text-muted-foreground">
                    Let others see when you're online
                  </p>
                </div>
                <Switch
                  checked={privacySettings.show_online_status}
                  onCheckedChange={(checked) =>
                    updatePrivacySettings({ show_online_status: checked })
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Content Warnings</h4>
                  <p className="text-sm text-muted-foreground">
                    Show warnings for sensitive content
                  </p>
                </div>
                <Switch
                  checked={privacySettings.content_warnings}
                  onCheckedChange={(checked) =>
                    updatePrivacySettings({ content_warnings: checked })
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Report Safety Concern */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Report Safety Concern
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Use this form to report safety concerns, harassment, or platform abuse.
                </AlertDescription>
              </Alert>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Reason for report
                </label>
                <Select value={reportReason} onValueChange={setReportReason}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a reason" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="harassment">Harassment</SelectItem>
                    <SelectItem value="spam">Spam</SelectItem>
                    <SelectItem value="inappropriate_content">Inappropriate Content</SelectItem>
                    <SelectItem value="impersonation">Impersonation</SelectItem>
                    <SelectItem value="safety_concern">Safety Concern</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Description
                </label>
                <Textarea
                  value={reportText}
                  onChange={(e) => setReportText(e.target.value)}
                  placeholder="Please describe the issue in detail..."
                  className="min-h-[100px]"
                />
              </div>

              <Button onClick={submitReport} className="w-full">
                Submit Report
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Blocked Users */}
      {activeTab === "blocked" && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <UserX className="h-5 w-5 mr-2" />
              Blocked Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            {blockedUsers.length === 0 ? (
              <div className="text-center py-8">
                <UserX className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">No blocked users</h3>
                <p className="text-muted-foreground">
                  Users you block will appear here
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {blockedUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                        {user.avatar_url ? (
                          <img
                            src={user.avatar_url}
                            alt={user.username}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-sm font-medium">
                            {user.username.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">@{user.username}</p>
                        {user.reason && (
                          <p className="text-sm text-muted-foreground">{user.reason}</p>
                        )}
                        <p className="text-xs text-muted-foreground">
                          Blocked {new Date(user.blocked_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Button
                      onClick={() => unblockUser(user.id)}
                      variant="outline"
                      size="sm"
                    >
                      Unblock
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Muted Users */}
      {activeTab === "muted" && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <VolumeX className="h-5 w-5 mr-2" />
              Muted Users
            </CardTitle>
          </CardHeader>
          <CardContent>
            {mutedUsers.length === 0 ? (
              <div className="text-center py-8">
                <VolumeX className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">No muted users</h3>
                <p className="text-muted-foreground">
                  Users you mute will appear here
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {mutedUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-full bg-muted flex items-center justify-center">
                        {user.avatar_url ? (
                          <img
                            src={user.avatar_url}
                            alt={user.username}
                            className="w-10 h-10 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-sm font-medium">
                            {user.username.charAt(0).toUpperCase()}
                          </span>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">@{user.username}</p>
                        {user.duration && (
                          <Badge variant="secondary" className="text-xs">
                            {user.duration}
                          </Badge>
                        )}
                        <p className="text-xs text-muted-foreground">
                          Muted {new Date(user.muted_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Button
                      onClick={() => unmuteUser(user.id)}
                      variant="outline"
                      size="sm"
                    >
                      Unmute
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
