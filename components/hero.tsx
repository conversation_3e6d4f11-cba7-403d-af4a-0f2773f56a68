"use client";

import React, { Suspense } from "react";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  CardHeader,
  Card<PERSON><PERSON>le,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import NewsSection from "@/components/News/NewsSection";
import { motion } from "framer-motion";
import { Tables } from "@/types";

type Article = Tables<'articles'>;

type HeroProps = {
  cryptoNews: any[];
  articles: Article[];
};

// Animation variants
const sectionVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0 },
};

const cardVariants = {
  hidden: { opacity: 0, y: 20 }, // Start off-screen
  visible: { opacity: 1, y: 0 }, // Slide into place
};

const buttonVariants = {
  hover: { scale: 1.05 },
};

export default function Hero({ cryptoNews, articles }: HeroProps) {
  return (
    <div className="flex flex-col min-h-screen transition-colors duration-200">
      <main className="grow">
        {/* Welcome Section with Overlay */}
        <div className="relative w-full h-full bg-[url('/hero.png')] bg-cover bg-center bg-no-repeat rounded-lg z-0">
          {/* Blue Overlay */}
          <div className="absolute inset-0 z-10 opacity-90"></div>

          <motion.section
            className="container relative z-30 px-4 py-24 mx-auto md:py-32"
            initial="hidden"
            animate="visible"
            variants={sectionVariants}
            transition={{ duration: 0.5 }}
          >
            <div className="max-w-3xl mx-auto space-y-8 text-center">
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
                Welcome to Crypto Talks Network
              </h1>
              <p className="text-xl text-center">
                Our Swedish crypto market mastery course is now live. SPECIAL
                price for a limited time, just click the button below.
              </p>
              <motion.div whileHover="hover" variants={buttonVariants}>
                <Link href="https://marketmastery.se/limited-offer">
                  <Button className="px-8 py-3 text-lg font-semibold rounded-lg">
                    LIMITED OFFER
                  </Button>
                </Link>
              </motion.div>
            </div>
          </motion.section>
        </div>

        <Suspense fallback={<div>Loading...</div>}>
          <NewsSection cryptoNews={cryptoNews} />
        </Suspense>

        {/* Community Articles Section */}
        <motion.section
          id="articles"
          className="py-16"
          initial="hidden"
          animate="visible"
          variants={sectionVariants}
          transition={{ duration: 0.5 }}
        >
          <div className="container px-4 mx-auto">
            <h2 className="mb-8 text-3xl font-bold text-center">
              Community Articles
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {articles.length > 0 ? (
                articles.map((article) => (
                  <motion.div
                    key={article.id}
                    whileInView="visible"
                    initial="hidden"
                    variants={cardVariants}
                    transition={{ duration: 0.5 }}
                    viewport={{ once: false }}
                  >
                    <Card className="drop-shadow-xl">
                      <CardHeader>
                        <CardTitle>
                          <div className="text-ellipsis whitespace-nowrap overflow-hidden">
                            {article.title}
                          </div>
                        </CardTitle>
                        <CardDescription>By {article.author || 'Unknown'}</CardDescription>
                      </CardHeader>
                      <CardContent className="grow">
                        <p className="overflow-hidden text-ellipsis whitespace-nowrap min-h-[4.5em]">
                          {article.content || 'No content available'}
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))
              ) : (
                <p className="text-center">No articles available.</p>
              )}
            </div>
          </div>
        </motion.section>

        {/* Technical Analysis Section */}
        <motion.section
          id="analysis"
          className="py-16"
          initial="hidden"
          animate="visible"
          variants={sectionVariants}
          transition={{ duration: 0.5 }}
        >
          <div className="container px-4 mx-auto">
            <h2 className="mb-8 text-3xl font-bold text-center">
              Technical Analysis
            </h2>
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
              {[1, 2].map((index) => (
                <motion.div
                  key={index}
                  whileInView="visible"
                  initial="hidden"
                  variants={cardVariants}
                  transition={{ duration: 0.5 }} // Animation duration
                  viewport={{ once: false }} // Allows repeated animations
                >
                  <Card className="drop-shadow-xl">
                    <CardHeader>
                      <CardTitle>Chart Analysis {index}</CardTitle>
                      <CardDescription>By Crypto Analyst</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p>
                        Expert technical analysis and trading view chart
                        interpretations.
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.section>
      </main>
    </div>
  );
}
