// components/ArticleTimeline.tsx
"use client";

import {
  useState,
  useRef,
  useCallback,
  useMemo,
  useEffect,
  startTransition,
} from "react";
import { createClient } from "@/utils/supabase/client";
import { toast } from "@/hooks/use-toast";
import { ArticleCard } from "../ArticleCard";
import { LoadingSkeleton } from "../LoadingSkeleton";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Filter,
  SortAsc,
  SortDesc,
  Clock,
  Flame,
  MessageSquare,
} from "lucide-react";
import {
  ArticleTimelineProps,
  ArticleTimelineState,
  ArticleSort,
  ArticleFilter,
  EnhancedArticle,
  TimeRange,
  TIME_RANGES,
} from "@/types/articleTypes";


const ITEMS_PER_PAGE = 10;

export default function ArticleTimeline({
  articles,
  userProfile,
  errorMessage,
  rpcFunction = "get_articles_with_interactions",
  title = "Latest Articles",
}: ArticleTimelineProps) {
  const [articleList, setArticleList] = useState<EnhancedArticle[]>(
    articles.map((article) => ({
      ...article,
      is_liked: false,
      is_bookmarked: false,
      interaction_count: 0,
      last_interaction: null,
    }))
  );

  const [state, setState] = useState<ArticleTimelineState>({
    sort: { field: "timestamp", direction: "desc" },
    filter: { timeRange: "all" },
    page: 1,
    hasMore: true,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const timelineRef = useRef<HTMLDivElement>(null);
  const supabase = useMemo(() => createClient(), []);
  const interactionsLoaded = useRef(false);
  const userId = useMemo(() => userProfile.user_id, [userProfile.user_id]);

  // Load initial interaction states
  useEffect(() => {
    const loadInteractions = async () => {
      // Skip if already loaded or for guest users
      if (interactionsLoaded.current || !userId || userId === '00000000-0000-0000-0000-000000000000') {
        setIsInitialLoad(false);
        return;
      }

      interactionsLoaded.current = true;

      try {
        const { data: interactions } = await (supabase as any)
          .from("user_article_interactions")
          .select("article_id, is_liked, is_bookmarked")
          .eq("user_id", userId);

        if (interactions) {
          const interactionMap = new Map(
            interactions.map((i: any) => [i.article_id, i])
          );

          setArticleList((prev) =>
            prev.map((article) => {
              const interaction = interactionMap.get(article.id) as { is_liked?: boolean; is_bookmarked?: boolean } | undefined;
              return {
                ...article,
                is_liked: interaction?.is_liked || false,
                is_bookmarked: interaction?.is_bookmarked || false,
              };
            })
          );
        }
      } catch (error) {
        console.error("Error loading interactions:", error);
        // Reset the flag so it can be retried if needed
        interactionsLoaded.current = false;
      } finally {
        setIsInitialLoad(false);
      }
    };

    loadInteractions();
  }, [userId, supabase]);

  // Sort and filter articles
  const sortedAndFilteredArticles = useMemo(() => {
    let filtered = [...articleList];

    // Apply filters
    if (state.filter.timeRange && state.filter.timeRange !== "all") {
      const now = new Date();
      const cutoff = new Date();
      switch (state.filter.timeRange) {
        case "today":
          cutoff.setDate(now.getDate() - 1);
          break;
        case "week":
          cutoff.setDate(now.getDate() - 7);
          break;
        case "month":
          cutoff.setMonth(now.getMonth() - 1);
          break;
        case "year":
          cutoff.setFullYear(now.getFullYear() - 1);
          break;
      }
      filtered = filtered.filter(
        (article) =>
          new Date(article.timestamp || article.created_at || 0) >= cutoff
      );
    }

    if (state.filter.tags?.length) {
      filtered = filtered.filter((article) =>
        article.tags?.some((tag) => state.filter.tags?.includes(tag))
      );
    }

    if (state.filter.author) {
      filtered = filtered.filter(
        (article) => article.author === state.filter.author
      );
    }

    // Apply sort
    return filtered.sort((a, b) => {
      const aValue = a[state.sort.field];
      const bValue = b[state.sort.field];
      const modifier = state.sort.direction === "asc" ? 1 : -1;

      if (!aValue && !bValue) return 0;
      if (!aValue) return 1;
      if (!bValue) return -1;

      return (
        ((typeof aValue === "string"
          ? new Date(aValue).getTime()
          : Number(aValue)) -
          (typeof bValue === "string"
            ? new Date(bValue).getTime()
            : Number(bValue))) *
        modifier
      );
    });
  }, [articleList, state.sort, state.filter]);

  const handleSort = (field: ArticleSort["field"]) => {
    setState((prev) => ({
      ...prev,
      sort: {
        field,
        direction:
          prev.sort.field === field && prev.sort.direction === "desc"
            ? "asc"
            : "desc",
      },
    }));
  };

  const handleFilter = (filter: Partial<ArticleFilter>) => {
    setState((prev) => ({
      ...prev,
      filter: { ...prev.filter, ...filter },
      page: 1,
    }));
  };

  const handleTimeRangeChange = (value: string) => {
    handleFilter({ timeRange: value as TimeRange });
  };

  const handleLike = useCallback(
    async (articleId: number, isLiked: boolean, userId: string) => {
      // Optimistic update
      startTransition(() => {
        setArticleList((prevArticles) =>
          prevArticles.map((article) =>
            article.id === articleId
              ? {
                  ...article,
                  likes: isLiked
                    ? (article.likes ?? 0) - 1
                    : (article.likes ?? 0) + 1,
                  is_liked: !isLiked,
                }
              : article
          )
        );
      });

      try {
        const { data, error } = await (supabase as any).rpc(
          "toggle_article_interaction",
          {
            p_user_id: userId,
            p_article_id: articleId,
            p_interaction_type: "like",
          }
        );

        if (error) throw error;

        // Update article with returned data
        setArticleList((prevArticles) =>
          prevArticles.map((article) =>
            article.id === articleId
              ? {
                  ...article,
                  is_liked: data.is_liked,
                  likes: data.likes_count,
                  interaction_count: data.interaction_count,
                }
              : article
          )
        );
      } catch (error) {
        // Revert optimistic update
        setArticleList((prevArticles) =>
          prevArticles.map((article) =>
            article.id === articleId
              ? {
                  ...article,
                  likes: isLiked
                    ? (article.likes ?? 0) + 1
                    : (article.likes ?? 0) - 1,
                  is_liked: isLiked,
                }
              : article
          )
        );
        console.error("Error handling like:", error);
        toast({
          title: "Error",
          description: "Failed to update like status",
          variant: "destructive",
        });
      }
    },
    [supabase]
  );

  const handleBookmark = useCallback(
    async (articleId: number) => {
      const article = articleList.find((a) => a.id === articleId);
      if (!article) return;

      const newIsBookmarked = !article.is_bookmarked;

      // Optimistic update
      setArticleList((prev) =>
        prev.map((a) =>
          a.id === articleId ? { ...a, is_bookmarked: newIsBookmarked } : a
        )
      );

      try {
        const { data, error } = await (supabase as any).rpc(
          "toggle_article_interaction",
          {
            p_user_id: userId,
            p_article_id: articleId,
            p_interaction_type: "bookmark",
          }
        );

        if (error) throw error;

        // Update article with returned data
        setArticleList((prev) =>
          prev.map((a) =>
            a.id === articleId
              ? {
                  ...a,
                  is_bookmarked: data.is_bookmarked,
                  interaction_count: data.interaction_count,
                }
              : a
          )
        );

        toast({
          title: newIsBookmarked ? "Bookmarked" : "Removed from bookmarks",
          description: newIsBookmarked
            ? "Article added to your bookmarks"
            : "Article removed from your bookmarks",
        });
      } catch (error) {
        // Revert optimistic update
        setArticleList((prev) =>
          prev.map((a) =>
            a.id === articleId ? { ...a, is_bookmarked: !newIsBookmarked } : a
          )
        );
        console.error("Error bookmarking article:", error);
        toast({
          title: "Error",
          description: "Failed to update bookmark",
          variant: "destructive",
        });
      }
    },
    [articleList, supabase, userId]
  );

  const handleShare = useCallback(async (article: EnhancedArticle) => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: article.title || "Shared Article",
          text: article.content || "Check out this article",
          url: `${window.location.origin}/ctn/articles/${article.id}`,
        });
      } else {
        await navigator.clipboard.writeText(
          `${window.location.origin}/ctn/articles/${article.id}`
        );
        toast({
          title: "Success",
          description: "Link copied to clipboard",
        });
      }
    } catch (error) {
      console.error("Error sharing:", error);
      toast({
        title: "Error",
        description: "Failed to share article",
        variant: "destructive",
      });
    }
  }, []);

  const loadedArticleIds = useRef(
    new Set(articles.map((article) => article.id))
  );

  const loadMoreArticles = useCallback(async () => {
    if (isLoading || !state.hasMore) return;
    
    // Skip loading more articles for guest users or use a different approach
    if (userId === '00000000-0000-0000-0000-000000000000') {
      setState((prev) => ({ ...prev, hasMore: false }));
      return;
    }

    setIsLoading(true);
    try {
      const lastArticle = articleList[articleList.length - 1];
      const lastTimestamp = lastArticle?.created_at || lastArticle?.timestamp;

      const { data, error } = await supabase.rpc(
        rpcFunction,
        {
          p_user_id: userId,
          p_last_timestamp: lastTimestamp,
          p_limit: ITEMS_PER_PAGE,
          p_time_range: state.filter.timeRange,
          p_sort_field: state.sort.field,
          p_sort_direction: state.sort.direction,
          // Remove p_tags as it's not supported by the current RPC function
        }
      );

      if (error) {
        console.error("RPC function error details:", error);
        throw error;
      }

      // Handle different response formats based on the function type
      let articles: EnhancedArticle[];
      let has_more = false;

      if (rpcFunction === "get_followed_articles") {
        // For get_followed_articles, data is directly the array of articles
        articles = data || [];
        has_more = articles.length === ITEMS_PER_PAGE; // Simple check
      } else if (Array.isArray(data)) {
        // For get_articles_with_interactions, based on types it returns an array
        articles = data || [];
        has_more = articles.length === ITEMS_PER_PAGE; // Check if we got a full page
      } else if (data && typeof data === 'object') {
        // If it's an object with articles property
        articles = data.articles || [];
        has_more = data.has_more !== undefined ? data.has_more : (articles.length === ITEMS_PER_PAGE);
      } else {
        // Fallback
        articles = [];
        has_more = false;
      }

      // Apply client-side tag filtering if tags are specified
      if (state.filter.tags && state.filter.tags.length > 0) {
        articles = articles.filter((article: EnhancedArticle) => {
          // Check if any of the article's tags match the filter tags
          return state.filter.tags!.some(filterTag => 
            article.tags && article.tags.includes(filterTag)
          );
        });
      }

      // Filter out duplicates
      const newUniqueArticles = articles.filter((article: EnhancedArticle) => {
        if (loadedArticleIds.current.has(article.id)) {
          return false;
        }
        loadedArticleIds.current.add(article.id);
        return true;
      });

      if (newUniqueArticles.length > 0) {
        setArticleList((prev) => [...prev, ...newUniqueArticles]);
      }

      // If we got fewer articles than requested, there are no more
      // For tag filtering, we need to be more conservative about hasMore
      const actualHasMore = has_more && articles.length === ITEMS_PER_PAGE;
      
      // If we're filtering by tags and got no results, but there might be more data,
      // we should still allow trying to load more
      const shouldAllowLoadMore = state.filter.tags && state.filter.tags.length > 0 
        ? has_more && newUniqueArticles.length === 0 && articles.length > 0
        : actualHasMore && newUniqueArticles.length > 0;

      setState((prev) => ({
        ...prev,
        hasMore: shouldAllowLoadMore || actualHasMore,
        page: prev.page + 1,
      }));

      // Show user-friendly message when no more articles are available
      const isFilteredSearch = state.filter.tags && state.filter.tags.length > 0;
      const shouldShowNoMoreMessage = !shouldAllowLoadMore && !actualHasMore && (
        (isFilteredSearch && newUniqueArticles.length === 0) ||
        (!isFilteredSearch && newUniqueArticles.length === 0)
      );

      if (shouldShowNoMoreMessage) {
        toast({
          title: "No more articles",
          description: isFilteredSearch
            ? "No more articles found with the selected tags." 
            : "You've reached the end of the articles.",
          variant: "default",
        });
      }

    } catch (error: any) {
      console.error("Error loading more articles:", {
        error,
        errorMessage: error?.message || 'Unknown error',
        errorDetails: error?.details || 'No details available',
        errorHint: error?.hint || 'No hint available',
        errorCode: error?.code || 'No code available'
      });
      
      // More specific error handling
      let errorMessage = "Failed to load more articles";
      if (error?.message) {
        errorMessage = error.message;
      } else if (error?.details) {
        errorMessage = error.details;
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });

      // If there's an error, assume no more articles are available
      setState((prev) => ({ ...prev, hasMore: false }));
    } finally {
      setIsLoading(false);
    }
  }, [
    isLoading,
    state.hasMore,
    state.page,
    state.filter.timeRange,
    state.filter.tags,
    state.sort.field,
    state.sort.direction,
    userId,
    rpcFunction,
  ]);

  // Debounce the scroll handler to prevent too many calls
  const handleScroll = useCallback(
    debounce(() => {
      if (timelineRef.current) {
        const { scrollTop, scrollHeight, clientHeight } = timelineRef.current;
        if (
          scrollTop + clientHeight >= scrollHeight - 100 &&
          !isLoading &&
          state.hasMore
        ) {
          loadMoreArticles();
        }
      }
    }, 200),
    [loadMoreArticles, isLoading, state.hasMore]
  );

  // Cleanup function for the debounced scroll handler
  useEffect(() => {
    return () => {
      handleScroll.cancel();
    };
  }, [handleScroll]);

  // Add a utility function for debouncing
  function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): T & { cancel: () => void } {
    let timeout: NodeJS.Timeout | null = null;

    const debounced = (...args: Parameters<T>) => {
      if (timeout) {
        clearTimeout(timeout);
      }
      timeout = setTimeout(() => {
        func(...args);
      }, wait);
    };

    debounced.cancel = () => {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
    };

    return debounced as T & { cancel: () => void };
  }

  const handleTagClick = (tag: string) => {
    handleFilter({ tags: [tag] }); // Update filter to use the clicked tag
    // Optionally scroll to top
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <div className="flex flex-col mx-auto" ref={timelineRef} onScroll={handleScroll}>
      {errorMessage && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
          {errorMessage}
        </div>
      )}

      <div className="sticky top-0 z-10 p-4 space-y-4 rounded-xl">
        <h1 className="text-4xl font-bold text-center relative z-10">{title}</h1>

        {/* Controls */}
        <div className="space-y-4 relative z-10">
          <div className="flex flex-wrap gap-2 justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSort("timestamp")}
              className="gap-2"
            >
              <Clock className="w-4 h-4" />
              Date
              {state.sort.field === "timestamp" &&
                {
                  asc: <SortAsc className="w-4 h-4" />,
                  desc: <SortDesc className="w-4 h-4" />,
                }[state.sort.direction]}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSort("likes")}
              className="gap-2"
            >
              <Flame className="w-4 h-4" />
              Popularity
              {state.sort.field === "likes" &&
                {
                  asc: <SortAsc className="w-4 h-4" />,
                  desc: <SortDesc className="w-4 h-4" />,
                }[state.sort.direction]}
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handleSort("comments_count")}
              className="gap-2"
            >
              <MessageSquare className="w-4 h-4" />
              Comments
              {state.sort.field === "comments_count" &&
                {
                  asc: <SortAsc className="w-4 h-4" />,
                  desc: <SortDesc className="w-4 h-4" />,
                }[state.sort.direction]}
            </Button>

            <Select
              value={state.filter.timeRange}
              onValueChange={handleTimeRangeChange}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Time Range" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(TIME_RANGES).map(([value, label]) => (
                  <SelectItem key={value} value={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="p-4 m-4 mt-8">
        {isInitialLoad ? (
          <LoadingSkeleton />
        ) : sortedAndFilteredArticles.length > 0 ? (
          sortedAndFilteredArticles.map((article) => (
            <ArticleCard
              key={article.id}
              article={article}
              userProfile={userProfile}
              onLike={handleLike}
              onBookmark={handleBookmark}
              onShare={handleShare}
              onTagClick={handleTagClick}
              onError={(error) => {
                console.error("Article interaction error:", error);
                toast({
                  title: "Error",
                  description: "An error occurred",
                  variant: "destructive",
                });
              }}
            />
          ))
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No articles found
          </div>
        )}
      </div>

      {isLoading && <LoadingSkeleton />}

      {!isLoading && state.hasMore && (
        <div className="flex justify-center py-4">
          <Button onClick={loadMoreArticles} variant="outline">
            Load More
          </Button>
        </div>
      )}

      {!isLoading && !state.hasMore && articleList.length > 0 && (
        <div className="flex justify-center py-8">
          <div className="text-center text-muted-foreground">
            <p className="text-sm">
              {state.filter.tags && state.filter.tags.length > 0 
                ? "No more articles found with the selected tags." 
                : "You've reached the end of the articles."}
            </p>
            {state.filter.tags && state.filter.tags.length > 0 && (
              <Button 
                variant="ghost" 
                size="sm" 
                className="mt-2"
                onClick={() => handleFilter({ tags: [] })}
              >
                Clear tag filter to see more articles
              </Button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
