// components/Articles/CommentSection.tsx
"use client";

import { useState, useCallback, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { createClient } from "@/utils/supabase/client";
import { toast } from "@/hooks/use-toast";
import { UserProfile } from "@/types";
import { MentionTextarea, parseMentions, renderTextWithMentions } from "@/components/MentionSystem";
import { TypingIndicator, useTypingIndicator } from "@/components/TypingIndicator";
import { Heart, MessageSquare, Trash2, Flag } from "lucide-react";
import { useTierAccess } from "@/hooks/use-tier-access";

interface Comment {
  id: number;
  content: string;
  user_id: string;
  created_at: string;
  author: string;
  author_avatar?: string;
  parent_id?: number;
  likes_count?: number;
  is_liked?: boolean;
}

// Updated to match exact Supabase response structure
interface SupabaseComment {
  id: number;
  content: string;
  user_id: string;
  created_at: string;
  parent_id: number | null;
  users: {
    username: string;
    avatar_url: string | null;
  };
}

interface CommentSectionProps {
  articleId: number;
  currentUser: UserProfile | null;
}

// Simple time formatting utility
const formatTimeAgo = (dateString: string): string => {
  const now = new Date();
  const past = new Date(dateString);
  const diffMs = now.getTime() - past.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  if (diffMins < 1) return "just now";
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  return past.toLocaleDateString();
};

export function CommentSection({
  articleId,
  currentUser,
}: CommentSectionProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState("");
  const [replyTo, setReplyTo] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [commentLikes, setCommentLikes] = useState<Record<number, { count: number; isLiked: boolean }>>({});
  const supabase = createClient();
  const { userExperience, refreshUserExperience } = useTierAccess();

  // Typing indicator for the current user
  const typingIndicator = useTypingIndicator({
    articleId,
    userId: currentUser?.user_id || "",
    username: currentUser?.username || "",
    avatarUrl: currentUser?.avatar_url || null,
  });

  const loadComments = useCallback(async () => {
    try {
      console.log("Fetching comments for article:", articleId);

      // First, get the comments
      const { data: commentsData, error: commentsError } = await (supabase as any)
        .from("comments")
        .select("*")
        .eq("article_id", articleId)
        .order("created_at", { ascending: false });

      if (commentsError) throw commentsError;

      if (!commentsData) {
        setComments([]);
        return;
      }

      // Get unique user IDs using Array.from instead of spread operator
      interface CommentData {
        id: number;
        content: string;
        user_id: string;
        created_at: string;
        article_id: number;
        parent_id: number | null;
      }

            const userIds: string[] = Array.from(
              new Set(commentsData.map((comment: CommentData) => comment.user_id))
            );

      const { data: usersData, error: usersError } = await (supabase as any)
        .from("users")
        .select("user_id, username, avatar_url")
        .in("user_id", userIds);

      if (usersError) throw usersError;

      // Create a map of user data
      interface UserData {
        user_id: string;
        username: string;
        avatar_url: string | null;
      }

      const userMap = new Map<string, UserData>(
        usersData?.map((user: UserData) => [user.user_id, user]) || []
      );

      // Combine comment and user data
      interface UserData {
        user_id: string;
        username: string;
        avatar_url: string | null;
      }

      interface CommentData {
        id: number;
        content: string;
        user_id: string;
        created_at: string;
        article_id: number;
        parent_id: number | null;
      }

      const formattedComments: Comment[] = commentsData.map((comment: CommentData) => {
        const user: UserData | undefined = userMap.get(comment.user_id);
        return {
          id: comment.id,
          content: comment.content,
          user_id: comment.user_id,
          created_at: comment.created_at,
          author: user?.username || "Unknown User",
          author_avatar: user?.avatar_url || undefined,
          parent_id: comment.parent_id || undefined,
        };
      });

      setComments(formattedComments);
    } catch (error) {
      console.error("Error loading comments:", {
        error,
        message: error instanceof Error ? error.message : "Unknown error",
        articleId,
      });
      toast({
        title: "Error",
        description: "Failed to load comments",
        variant: "destructive",
      });
    }
  }, [articleId, supabase]);

  useEffect(() => {
    loadComments();
  }, [loadComments]);

  const handleSubmitComment = async () => {
    if (!currentUser) {
      toast({
        title: "Authentication required",
        description: "Please sign in to comment",
        variant: "destructive",
      });
      return;
    }

    if (!newComment.trim()) return;

    setIsLoading(true);
    try {
      const { error: insertError } = await (supabase as any).from("comments").insert({
        article_id: articleId,
        user_id: currentUser.user_id,
        content: newComment.trim(),
        parent_id: replyTo,
        comment_timestamp: new Date().toISOString(),
      });

      if (insertError) throw insertError;

      setNewComment("");
      setReplyTo(null);
      await loadComments(); // Reload comments to show the new one

      toast({
        title: "Success",
        description: "Comment posted successfully",
      });
    } catch (error) {
      console.error("Error posting comment:", error);
      toast({
        title: "Error",
        description: "Failed to post comment",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCommentLike = async (commentId: number, commentAuthorId: string) => {
    if (!currentUser) {
      toast({
        title: "Authentication required",
        description: "Please sign in to like comments",
        variant: "destructive",
      });
      return;
    }

    try {
      const currentState = commentLikes[commentId] || { count: 0, isLiked: false };
      const newIsLiked = !currentState.isLiked;
      
      // Optimistically update UI
      setCommentLikes(prev => ({
        ...prev,
        [commentId]: {
          count: currentState.count + (newIsLiked ? 1 : -1),
          isLiked: newIsLiked
        }
      }));

      if (newIsLiked) {
        // Add like
        const { error: likeError } = await (supabase as any)
          .from('comment_likes')
          .insert({
            comment_id: commentId,
            user_id: currentUser.user_id
          });

        if (likeError) throw likeError;

        // Award XP for liking a comment (if user has tier 1+)
        if (userExperience && userExperience.tier !== 'tier0' && userExperience.experience_active) {
          try {
            await (supabase as any).rpc('award_experience_for_comment_interaction', {
              p_user_id: currentUser.user_id,
              p_comment_id: commentId,
              p_comment_author_id: commentAuthorId,
              p_interaction_type: 'like'
            });
            
            // Refresh user experience
            await refreshUserExperience();
            
            toast({
              title: "XP Earned!",
              description: "You earned experience points for engaging with content",
            });
          } catch (xpError) {
            console.error('Error awarding XP:', xpError);
          }
        }
      } else {
        // Remove like
        const { error: unlikeError } = await (supabase as any)
          .from('comment_likes')
          .delete()
          .eq('comment_id', commentId)
          .eq('user_id', currentUser.user_id);

        if (unlikeError) throw unlikeError;
      }

    } catch (error) {
      console.error('Error handling comment like:', error);
      
      // Revert optimistic update
      setCommentLikes(prev => {
        const currentState = prev[commentId] || { count: 0, isLiked: false };
        return {
          ...prev,
          [commentId]: {
            count: currentState.count + (currentState.isLiked ? 1 : -1),
            isLiked: !currentState.isLiked
          }
        };
      });
      
      toast({
        title: "Error",
        description: "Failed to update like status",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    const diffInMinutes = Math.floor(diffInSeconds / 60);
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInSeconds < 60) {
      return "just now";
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? "" : "s"} ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours === 1 ? "" : "s"} ago`;
    } else if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays === 1 ? "" : "s"} ago`;
    } else {
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }
  };

  const CommentItem = ({ comment }: { comment: Comment }) => (
    <div className="flex gap-4 p-4">
      <Avatar className="h-10 w-10">
        <AvatarImage src={comment.author_avatar || "/default-avatar.jpg"} />
        <AvatarFallback>{comment.author?.[0]}</AvatarFallback>
      </Avatar>
      <div className="flex-1">
        <div className="flex items-center gap-2">
          <span className="font-semibold">{comment.author}</span>
          <span className="text-sm text-muted-foreground">
            {formatDate(comment.created_at)}
          </span>
        </div>
        <div className="mt-1">
          {renderTextWithMentions(comment.content)}
        </div>
        <div className="flex items-center gap-4 mt-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleCommentLike(comment.id, comment.user_id)}
          >
            <Heart className={`h-4 w-4 ${commentLikes[comment.id]?.isLiked ? 'text-red-500' : 'text-muted-foreground'}`} />
            <span className="ml-1 text-sm">
              {commentLikes[comment.id]?.count || 0}
            </span>
          </Button>
          {currentUser && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setReplyTo(comment.id)}
            >
              Reply
            </Button>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="mt-8 space-y-4">
      <h2 className="text-xl font-semibold">Comments</h2>

      {/* Comment Input */}
      {currentUser && (
        <div className="space-y-2">
          {replyTo && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>Replying to comment</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setReplyTo(null)}
              >
                Cancel
              </Button>
            </div>
          )}
          <MentionTextarea
            value={newComment}
            onChange={(value) => {
              setNewComment(value);
              if (value.trim()) {
                typingIndicator.startTyping();
              } else {
                typingIndicator.stopTyping();
              }
            }}
            placeholder={replyTo ? "Write a reply..." : "Write a comment..."}
            className="min-h-[80px]"
          />
          <Button
            onClick={handleSubmitComment}
            disabled={isLoading || !newComment.trim()}
          >
            {isLoading ? "Posting..." : "Post Comment"}
          </Button>
        </div>
      )}

      {/* Typing Indicators */}
      <TypingIndicator 
        articleId={articleId} 
        currentUserId={currentUser?.user_id || ""} 
        className="px-4 py-2"
      />

      {/* Comments List */}
      {comments.length > 0 ? (
        comments.map((comment) => (
          <CommentItem key={comment.id} comment={comment} />
        ))
      ) : (
        <p className="text-center text-muted-foreground py-4">
          No comments yet. Be the first to comment!
        </p>
      )}
    </div>
  );
}
