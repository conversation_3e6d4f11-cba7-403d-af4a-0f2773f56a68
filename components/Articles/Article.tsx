// components/Articles/Article.tsx
"use client";

import { useState, useCallback, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Heart, MessageSquare, Share2, Bookmark, Flag } from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { toast } from "@/hooks/use-toast";
import Link from "next/link";
import { Article as ArticleType, Profile } from "@/types";
import { CommentSection } from "./CommentSection";

type ArticleProps = {
  article: ArticleType;
  currentUser: Profile | null;
};

type InteractionResponse = {
  is_liked: boolean;
  is_bookmarked: boolean;
  interaction_count: number;
};

export default function Article({ article, currentUser }: ArticleProps) {
  const [isLiked, setIsLiked] = useState(article.is_liked || false);
  const [likeCount, setLikeCount] = useState(article.likes || 0);
  const [isBookmarked, setIsBookmarked] = useState(
    article.is_bookmarked || false
  );
  const supabase = createClient();

  const fetchInteractionState = useCallback(async () => {
    if (!currentUser) return;

    try {
      // First try to get existing interaction
      const { data: interactionData, error: interactionError } = await (supabase as any)
        .from("user_article_interactions")
        .select("is_liked, is_bookmarked")
        .eq("article_id", article.id)
        .eq("user_id", currentUser.user_id)
        .maybeSingle(); // Use maybeSingle() instead of single()

      // If there's a real error (not just no rows)
      if (interactionError && interactionError.code !== "PGRST116") {
        throw interactionError;
      }

      // If we have interaction data, use it
      if (interactionData) {
        setIsLiked(interactionData.is_liked);
        setIsBookmarked(interactionData.is_bookmarked);
      } else {
        // No interaction exists yet, set defaults
        setIsLiked(false);
        setIsBookmarked(false);
      }
    } catch (error) {
      console.error("Error fetching interaction state:", error);
      // Set defaults on error
      setIsLiked(false);
      setIsBookmarked(false);
    }
  }, [article.id, currentUser, supabase]);

  useEffect(() => {
    fetchInteractionState();
  }, [fetchInteractionState]);

  const handleLike = useCallback(async () => {
    if (!currentUser) {
      toast({
        title: "Authentication required",
        description: "Please sign in to like articles",
        variant: "destructive",
      });
      return;
    }

    try {
      // Optimistic update
      setIsLiked(!isLiked);
      setLikeCount((prev) => (isLiked ? prev - 1 : prev + 1));

      const { data, error } = await (supabase as any).rpc("toggle_article_interaction", {
        p_user_id: currentUser.user_id,
        p_article_id: article.id,
        p_interaction_type: "like",
      });

      if (error) throw error;

      // Update states based on response
      const response = data as InteractionResponse;
      setIsLiked(response.is_liked);

      // Fetch updated like count
      const { data: articleData, error: articleError } = await (supabase as any)
        .from("articles")
        .select("likes")
        .eq("id", article.id)
        .single();

      if (articleError) throw articleError;
      setLikeCount(articleData.likes);
    } catch (error) {
      // Revert optimistic update
      setIsLiked(isLiked);
      setLikeCount((prev) => (isLiked ? prev + 1 : prev - 1));
      console.error("Error handling like:", error);
      toast({
        title: "Error",
        description: "Failed to update like status",
        variant: "destructive",
      });
    }
  }, [isLiked, article.id, currentUser, supabase]);

  const handleBookmark = useCallback(async () => {
    if (!currentUser) {
      toast({
        title: "Authentication required",
        description: "Please sign in to bookmark articles",
        variant: "destructive",
      });
      return;
    }

    try {
      // Optimistic update
      setIsBookmarked(!isBookmarked);

      const { data, error } = await (supabase as any).rpc("toggle_article_interaction", {
        p_user_id: currentUser.user_id,
        p_article_id: article.id,
        p_interaction_type: "bookmark",
      });

      if (error) throw error;

      // Update state based on response
      const response = data as InteractionResponse;
      setIsBookmarked(response.is_bookmarked);
    } catch (error) {
      // Revert optimistic update
      setIsBookmarked(isBookmarked);
      console.error("Error handling bookmark:", error);
      toast({
        title: "Error",
        description: "Failed to update bookmark status",
        variant: "destructive",
      });
    }
  }, [isBookmarked, article.id, currentUser, supabase]);

  const handleShare = async () => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: article.title || "",
          text: article.content?.substring(0, 100) + "...",
          url: window.location.href,
        });
      } else {
        await navigator.clipboard.writeText(window.location.href);
        toast({
          title: "Success",
          description: "Link copied to clipboard",
        });
      }
    } catch (error) {
      console.error("Error sharing:", error);
      toast({
        title: "Error",
        description: "Failed to share article",
        variant: "destructive",
      });
    }
  };

  const handleReport = () => {
    toast({
      title: "Report submitted",
      description: "Thank you for your report. We will review it shortly.",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <Card className="max-w-5xl mx-auto p-6 my-2">
      <div className="flex items-center mb-6">
        <Avatar className="h-12 w-12">
          <AvatarImage src={article.author_avatar || "/default-avatar.jpg"} />
          <AvatarFallback>{article.author?.[0]}</AvatarFallback>
        </Avatar>
        <div className="ml-4">
          <Link
            href={`/ctn/profile/${article?.author_id}`}
            className="font-semibold hover:underline"
          >
            {article?.author}
          </Link>
          <p className="text-sm text-gray-500">
            {formatDate(article.created_at!)}
          </p>
        </div>
      </div>

      <h1 className="text-3xl font-bold mb-4">{article.title}</h1>

      <div className="md:flex md:gap-6 mb-6">
        <div
          className={`prose max-w-none ${article.image_url ? "md:w-1/2" : "w-full"}`}
        >
          <div className="space-y-4">
            {article.content
              ?.split("\n")
              .map((paragraph, index) => <p key={index}>{paragraph}</p>)}
          </div>
        </div>
        {article.image_url && (
          <div className="md:w-1/2 mb-6 md:mb-0">
            <img
              src={article.image_url}
              alt={article.title!}
              className="w-full h-[400px] rounded-lg object-cover"
            />
          </div>
        )}
      </div>

      <div className="flex items-center space-x-4 border-t pt-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLike}
          className="group"
        >
          <Heart
            className={`w-4 h-4 mr-2 ${
              isLiked ? "fill-red-500 text-red-500" : "group-hover:text-red-500"
            }`}
          />
          <span>{likeCount}</span>
        </Button>

        <Button variant="ghost" size="sm" onClick={handleBookmark}>
          <Bookmark
            className={`w-4 h-4 mr-2 ${isBookmarked ? "fill-current" : ""}`}
          />
          <span>Save</span>
        </Button>

        <Button variant="ghost" size="sm" onClick={handleShare}>
          <Share2 className="w-4 h-4 mr-2" />
          <span>Share</span>
        </Button>

        <Button variant="ghost" size="sm" onClick={handleReport}>
          <Flag className="w-4 h-4 mr-2" />
          <span>Report</span>
        </Button>
      </div>
      <CommentSection articleId={article.id} currentUser={currentUser} />
    </Card>
  );
}