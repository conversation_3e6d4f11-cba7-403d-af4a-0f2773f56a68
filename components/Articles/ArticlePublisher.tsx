"use client";

import { useState, useEffect, useCallback } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { Badge } from "../ui/badge";
import { cn } from "@/lib/utils";
import { MediaUpload } from "../MediaUpload";

interface ArticleData {
  author_id: string;
  title: string;
  content: string;
  tags: string[];
  image_url?: string | null;
}

interface CreateArticleResponse {
  id: number;
  title: string;
  content: string;
  author_id: string;
  image_url: string | null;
  created_at: string;
  tags: string[];
  views: number;
}

interface ArticlePublisherProps {
  userId: string;
  userPoints: number;
}

export default function ArticlePublisher({
  userId,
  userPoints,
}: ArticlePublisherProps) {
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [tags, setTags] = useState<string[]>([]);
  const [tagInput, setTagInput] = useState("");
  const [mediaUrls, setMediaUrls] = useState<Array<{url: string, type: 'image' | 'video'}>>([]);
  const [uploading, setUploading] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [points, setPoints] = useState(userPoints);
  const [badge, setBadge] = useState<string | null>(null);

  const router = useRouter();
  const supabase = createClient();

  useEffect(() => {
    const loadDraft = async () => {
      try {
        const { data: draft, error } = await (supabase as any)
          .from("article_drafts")
          .select("*")
          .eq("author_id", userId)
          .maybeSingle();

        if (error && error.code !== "PGRST116") {
          console.error("Error loading draft:", error);
          return;
        }

        if (draft) {
          setTitle(draft.title || "");
          setContent(draft.content || "");
          setTags(draft.tags || []);
          setWordCount(
            (draft.content || "").trim().split(/\s+/).filter(Boolean).length
          );
        }
      } catch (error) {
        console.error("Error loading draft:", error);
      }
    };

    loadDraft();
  }, [userId, supabase]);

  useEffect(() => {
    // Cleanup function for when component unmounts
    return () => {
      mediaUrls.forEach(media => {
        if (media.url.startsWith('blob:')) {
          URL.revokeObjectURL(media.url);
        }
      });
    };
  }, [mediaUrls]);

  const isSubmitDisabled =
    !title || !content || wordCount < 200 || tags.length !== 3 || uploading;

  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const contentValue = e.target.value;
    setContent(contentValue);
    setWordCount(contentValue.trim().split(/\s+/).filter(Boolean).length);
  };

  const handleAddTag = (e?: React.KeyboardEvent) => {
    if (e?.key === "Enter") {
      e.preventDefault();
    }

    if (tags.length >= 3) {
      toast({
        title: "Tag limit reached",
        description: "Maximum of 3 tags allowed",
        variant: "destructive",
      });
      return;
    }

    const normalizedTag = tagInput.trim().toLowerCase();
    if (normalizedTag && !tags.includes(normalizedTag)) {
      setTags([...tags, normalizedTag]);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleMediaUpload = (mediaUrl: string, mediaType: 'image' | 'video') => {
    setMediaUrls(prev => [...prev, { url: mediaUrl, type: mediaType }]);
  };

  const saveDraft = async () => {
    if (!title && !content && tags.length === 0) {
      toast({
        title: "Nothing to save",
        description: "Please add some content before saving a draft",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await (supabase as any).rpc("save_article_draft", {
        p_author_id: userId,
        p_title: title,
        p_content: content,
        p_tags: tags,
        p_image_url: null,
      });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Draft saved successfully",
      });
    } catch (error: any) {
      console.error("Error saving draft:", error);
      toast({
        title: "Error",
        description: "Failed to save draft",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const {
      data: { user },
      error: authError,
    } = await (supabase as any).auth.getUser();

    if (authError || !user) {
      toast({
        title: "Authentication Error",
        description: "You must be logged in to publish articles",
        variant: "destructive",
      });
      return;
    }

    if (isSubmitDisabled) {
      toast({
        title: "Validation Error",
        description:
          tags.length !== 3
            ? "Please add exactly 3 tags to your article"
            : "Title, content, and at least 200 words are required.",
        variant: "destructive",
      });
      return;
    }

    setUploading(true);

    try {
      // Create article first
      const { data: newArticle, error: createError } = await (supabase as any).rpc(
        "create_article_with_tags",
        {
          p_author_id: userId,
          p_title: title,
          p_content: content,
          p_tags: tags,
          p_image_url: mediaUrls.find(m => m.type === 'image')?.url || null,
        }
      );

      if (createError) throw createError;
      if (!newArticle?.[0]?.id)
        throw new Error("Failed to create article: No ID returned");

      // If we have media URLs that are already uploaded, we're done
      // The media URLs were already uploaded via the MediaUpload component

      // Delete draft after successful publication
      await (supabase as any).from("article_drafts").delete().eq("author_id", userId);

      setPoints(points + 500);
      toast({
        title: "Success",
        description: "Article published successfully!",
      });

      router.push(`/ctn/articles/${newArticle[0].id}`);
    } catch (error: any) {
      console.error("Error publishing article:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to publish article",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="container max-w-6xl py-2 space-y-8">
      {/* Header Section */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight">
          Create New Article
        </h1>
        <p className="text-muted-foreground">
          Share your knowledge with the community
        </p>
      </div>

      {/* Main Content */}
      <div className="space-y-6">
        {/* Title Section */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Title
              </label>
              <Input
                placeholder="Write an engaging title..."
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="text-lg font-medium"
              />
            </div>
          </CardContent>
        </Card>

        {/* Content Editor */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <label className="text-sm font-medium leading-none">
                Content
              </label>
              <Textarea
                placeholder="Write your story here (minimum 200 words)..."
                value={content}
                onChange={handleContentChange}
                className="min-h-[400px] resize-y"
              />
              <div className="flex justify-between text-sm text-muted-foreground">
                <span>Minimum 200 words required</span>
                <span
                  className={cn(
                    "font-medium",
                    wordCount < 200 ? "text-destructive" : "text-primary"
                  )}
                >
                  {wordCount} words
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tags Section */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <label className="text-sm font-medium leading-none">
                  Required Tags (3)
                </label>
                <span
                  className={cn(
                    "text-xs",
                    tags.length === 3
                      ? "text-primary font-medium"
                      : "text-muted-foreground"
                  )}
                >
                  {tags.length}/3 tags selected
                </span>
              </div>

              <div className="flex flex-wrap gap-2">
                {tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="flex items-center gap-1"
                  >
                    #{tag}
                    <button
                      onClick={() => removeTag(tag)}
                      className="ml-1 hover:text-destructive"
                      aria-label={`Remove ${tag} tag`}
                    >
                      ×
                    </button>
                  </Badge>
                ))}
              </div>

              <div className="flex gap-2">
                <Input
                  placeholder={
                    tags.length >= 3
                      ? "Maximum tags reached"
                      : "Add a tag and press Enter..."
                  }
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleAddTag(e)}
                  maxLength={20}
                  disabled={tags.length >= 3}
                />
                <Button
                  variant="secondary"
                  onClick={() => handleAddTag()}
                  disabled={tags.length >= 3 || !tagInput.trim()}
                >
                  Add Tag
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Media Upload Section */}
        <MediaUpload
          userId={userId}
          onMediaUpload={handleMediaUpload}
          className="bg-muted/30"
        />

        {/* Submission Requirements */}
        <Card className="bg-muted/50">
          <CardContent className="pt-6">
            <div className="space-y-2">
              <h3 className="font-medium">Before publishing, please ensure:</h3>
              <ul className="space-y-1 text-sm text-muted-foreground">
                <li
                  className={cn(
                    "flex items-center gap-2",
                    title ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  <span className="h-1.5 w-1.5 rounded-full bg-current" />
                  Your article has a title
                </li>
                <li
                  className={cn(
                    "flex items-center gap-2",
                    wordCount >= 200 ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  <span className="h-1.5 w-1.5 rounded-full bg-current" />
                  Content is at least 200 words
                </li>
                <li
                  className={cn(
                    "flex items-center gap-2",
                    tags.length === 3 ? "text-primary" : "text-muted-foreground"
                  )}
                >
                  <span className="h-1.5 w-1.5 rounded-full bg-current" />
                  Exactly 3 tags are added
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4">
          <Button
            variant="outline"
            className="w-32"
            onClick={saveDraft}
            disabled={uploading || (!title && !content && tags.length === 0)}
          >
            Save Draft
          </Button>
          <Button
            disabled={isSubmitDisabled}
            className="w-32"
            onClick={handleSubmit}
          >
            {uploading ? (
              <div className="flex items-center gap-2">
                <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                    fill="none"
                  />
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  />
                </svg>
                Publishing
              </div>
            ) : (
              "Publish"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
