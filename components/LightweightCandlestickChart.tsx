import React, {
  useRef,
  useEffect,
  useContext,
  useImperative<PERSON>andle,
  useState,
  createContext,
  forwardRef,
  useCallback,
} from "react";
import { SimpleMarkersPrimitive } from "./SimpleMarkersPrimitive";

export type CandlestickData = {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
};

export type TimeSeriesData = {
  time: number;
  value: number;
};

export type ChartOptions = Record<string, any>;

export type Indicator = {
  id: string;
  apply: (ctx: {
    chart: any;
    mainSeries: any;
    addLineSeries: (opts: any) => any;
  }) => void;
};

export interface LightweightCandlestickChartProps {
  data: CandlestickData[];
  options?: Record<string, any>;
  chartOptions?: ChartOptions;
  indicators?: Indicator[];
  width?: number | string;
  height?: number;
  enableStreaming?: boolean;
  onStreamingUpdate?: (data: CandlestickData) => void;
  panePrimitives?: any[]; // Array of pane primitives to attach (deprecated)
  seriesMarkers?: any[]; // Array of series markers to display
  onVisibleLogicalRangeChange?: (logicalRange: any) => void;
}

export interface ChartRef {
  updateData: (bar: CandlestickData) => void;
  setData: (data: CandlestickData[]) => void;
  chart: any;
  mainSeries: any;
}

// Chart context type
interface ChartContextType {
  chart: any;
  mainSeries: any;
  addLineSeries: (opts: any) => any;
  addAreaSeries: (opts: any) => any;
  addHistogramSeries: (opts: any) => any;
}

// Chart context for overlays/indicators
export const ChartContext = createContext<ChartContextType | null>(null);
// Enhanced streaming component with better error handling and reconnection
export const ChartCandleStream: React.FC<{ 
  symbol: string; 
  interval?: string;
  onUpdate?: (data: CandlestickData) => void;
}> = ({ symbol, interval = '1m', onUpdate }) => {
  const ctx = useContext(ChartContext);
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');

  const connect = useCallback(() => {
    if (!ctx || !ctx.mainSeries || !symbol) {
      console.log('Cannot connect: missing context, series, or symbol');
      return;
    }

    // Close existing connection if any
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.close(1000, 'Reconnecting');
    }

    setConnectionStatus('connecting');
    
    // Try different Binance WebSocket endpoints
    const endpoints = [
      `wss://stream.binance.com:9443/ws/${symbol.toLowerCase()}@kline_${interval}`,
      `wss://stream.binance.com/ws/${symbol.toLowerCase()}@kline_${interval}`,
      `wss://data-stream.binance.vision/ws/${symbol.toLowerCase()}@kline_${interval}`
    ];
    
    const wsUrl = endpoints[0]; // Start with the first endpoint
    console.log('Connecting to:', wsUrl);
    
    // Check if WebSocket is supported
    if (typeof WebSocket === 'undefined') {
      console.error('WebSocket is not supported in this environment');
      setConnectionStatus('disconnected');
      return;
    }
    
    try {
      const ws = new WebSocket(wsUrl);
      wsRef.current = ws;

      ws.onopen = (event) => {
        console.log(`✅ Connected to ${symbol} stream at ${wsUrl}`);
        console.log('Connection event:', event);
        setConnectionStatus('connected');
      };

      ws.onmessage = (event) => {
        try {
          console.log('Received message:', event.data.substring(0, 200) + '...');
          const msg = JSON.parse(event.data);
          if (msg.k) {
            const k = msg.k;
            console.log('Kline data:', k);
            // Convert Binance kline to lightweight-charts format
            const bar: CandlestickData = {
              time: Math.floor(k.t / 1000),
              open: parseFloat(k.o),
              high: parseFloat(k.h),
              low: parseFloat(k.l),
              close: parseFloat(k.c),
            };
            
            console.log('Updating chart with bar:', bar);
            
            // Use the efficient update method for real-time data
            try {
              if (ctx.mainSeries && typeof ctx.mainSeries.update === 'function') {
                ctx.mainSeries.update(bar);
              }
              
              // Optional callback for external handling
              if (onUpdate) {
                onUpdate(bar);
              }
            } catch (error) {
              console.warn('Error updating chart from WebSocket:', error);
              // Series might be disposed, stop trying to update
              if (wsRef.current) {
                wsRef.current.close(1000, 'Chart disposed');
              }
            }
          } else {
            console.log('Non-kline message:', msg);
          }
        } catch (error) {
          console.warn('Error parsing WebSocket message:', error);
        }
      };

      ws.onerror = (error) => {
        console.error('❌ WebSocket error for', symbol, 'at', wsUrl);
        console.error('Error details:', error);
        console.error('WebSocket state:', ws.readyState);
        setConnectionStatus('disconnected');
      };

      ws.onclose = (event) => {
        console.log(`🔌 WebSocket closed for ${symbol}:`, {
          code: event.code,
          reason: event.reason,
          wasClean: event.wasClean,
          url: wsUrl
        });
        setConnectionStatus('disconnected');
        
        // Auto-reconnect after 5 seconds unless manually closed
        if (event.code !== 1000 && reconnectTimeoutRef.current === null) {
          console.log(`⏰ Scheduling reconnect for ${symbol} in 5 seconds...`);
          reconnectTimeoutRef.current = setTimeout(() => {
            reconnectTimeoutRef.current = null;
            console.log(`🔄 Attempting to reconnect ${symbol}...`);
            connect();
          }, 5000);
        }
      };
      
      // Set a timeout to detect connection issues
      setTimeout(() => {
        if (ws.readyState === WebSocket.CONNECTING) {
          console.warn('⚠️ WebSocket still connecting after 10 seconds, may have issues');
        }
      }, 10000);
      
    } catch (error) {
      console.error('❌ Failed to create WebSocket:', error);
      setConnectionStatus('disconnected');
    }
  }, [ctx, symbol, interval, onUpdate]);

  useEffect(() => {
    connect();
    
    return () => {
      // Clean up timeout
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
      
      // Clean up WebSocket
      if (wsRef.current) {
        if (wsRef.current.readyState === WebSocket.OPEN || wsRef.current.readyState === WebSocket.CONNECTING) {
          wsRef.current.close(1000, 'Component unmounting');
        }
        wsRef.current = null;
      }
    };
  }, [connect]);

  // Expose connection status if needed
  return null;
};

// Main chart container, SSR-safe, always renders a candlestick chart
const LightweightCandlestickChart = forwardRef<
  ChartRef,
  LightweightCandlestickChartProps & { children?: React.ReactNode }
>(({
  data,
  options = {},
  chartOptions = {},
  indicators = [],
  panePrimitives = [],
  seriesMarkers = [],
  width = '100%',
  height = 520,
  enableStreaming = false,
  onStreamingUpdate,
  onVisibleLogicalRangeChange,
  children,
}, ref) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<any>(null);
  const mainSeriesRef = useRef<any>(null);
  const [ready, setReady] = useState(enableStreaming ? false : true);
  const [contextValue, setContextValue] = useState<any>(null);

  // Expose chart methods via ref
  useImperativeHandle(ref, () => ({
    updateData: (bar: CandlestickData) => {
      try {
        if (ready && mainSeriesRef.current && typeof mainSeriesRef.current.update === 'function') {
          mainSeriesRef.current.update(bar);
          if (onStreamingUpdate) {
            onStreamingUpdate(bar);
          }
        } else {
          console.warn('Chart not ready or series disposed, skipping update');
        }
      } catch (error) {
        console.warn('Error updating chart data:', error);
      }
    },
    setData: (newData: CandlestickData[]) => {
      try {
        if (ready && mainSeriesRef.current && typeof mainSeriesRef.current.setData === 'function') {
          const validData = newData
            .filter(d => d && typeof d.time === 'number' && !isNaN(d.time))
            .sort((a, b) => a.time - b.time);
          if (validData.length > 0) {
            mainSeriesRef.current.setData(validData);
          }
        } else {
          console.warn('Chart not ready or series disposed, skipping setData');
        }
      } catch (error) {
        console.warn('Error setting chart data:', error);
      }
    },
    chart: chartRef.current,
    mainSeries: mainSeriesRef.current,
  }), [ready, onStreamingUpdate]);

  useEffect(() => {
    let chart: any;
    let mainSeries: any;
    let cleanup = () => {};
    let isMounted = true;
    let appliedIndicators: any[] = [];
    let appliedPrimitives: any[] = [];
    let visibleLogicalRangeSubscription: any = null;

    (async () => {
      try {
        // Prevent duplicate creation and ensure proper cleanup
        if (chartRef.current) {
          console.log('Chart already exists, cleaning up first');
          try {
            if (mainSeriesRef.current && chartRef.current) {
              chartRef.current.removeSeries(mainSeriesRef.current);
            }
            chartRef.current.remove();
          } catch (err) {
            console.warn('Error during pre-cleanup:', err);
          }
          chartRef.current = null;
          mainSeriesRef.current = null;
          setReady(false);
          setContextValue(null);
        }

        const mod = await import("lightweight-charts");
        console.log('Lightweight charts module:', mod);
        console.log('Available exports:', Object.keys(mod));
        
        // Handle different import structures  
        const createChart = mod.createChart;
        if (!createChart) {
          console.error('Could not find createChart in module:', Object.keys(mod));
          return;
        }
        
        if (!chartContainerRef.current || !isMounted) return;

        // Clear any existing chart content
        chartContainerRef.current.innerHTML = '';

        // Get container dimensions with better fallbacks
        const containerRect = chartContainerRef.current.getBoundingClientRect();
        const containerWidth = containerRect.width || chartContainerRef.current.offsetWidth || 800;
        const containerHeight = height || 520;

        console.log(`📏 Creating chart with initial size: ${containerWidth}x${containerHeight}`);

        chart = createChart(chartContainerRef.current, {
          width: Math.floor(containerWidth),
          height: Math.floor(containerHeight),
          layout: {
            background: { type: 'solid', color: 'transparent' },
            ...chartOptions?.layout,
          },
          ...chartOptions,
        });
        chartRef.current = chart;
        
        console.log('Chart created:', chart);
        console.log('Chart methods:', Object.getOwnPropertyNames(chart));
        console.log('Chart prototype methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(chart)));

        // Use the v5 API - chart.addSeries with series constructor
        try {
          const { CandlestickSeries } = mod;
          console.log('CandlestickSeries:', CandlestickSeries);
          
          if (typeof chart.addSeries === 'function' && CandlestickSeries) {
            console.log('Using chart.addSeries with CandlestickSeries');
            mainSeries = chart.addSeries(CandlestickSeries, {
              upColor: '#26a69a',
              downColor: '#ef5350',
              borderVisible: false,
              wickUpColor: '#26a69a',
              wickDownColor: '#ef5350',
              ...options,
            });
          } else if (chart.addCandlestickSeries) {
            console.log('Using addCandlestickSeries method');
            mainSeries = chart.addCandlestickSeries({
              upColor: '#26a69a',
              downColor: '#ef5350',
              borderVisible: false,
              wickUpColor: '#26a69a',
              wickDownColor: '#ef5350',
              ...options,
            });
          } else {
            console.error('No candlestick method available');
            console.error('chart.addSeries exists:', typeof chart.addSeries);
            console.error('CandlestickSeries exists:', !!CandlestickSeries);
            return;
          }
        } catch (seriesError) {
          console.error('Error creating candlestick series:', seriesError);
          return;
        }
        mainSeriesRef.current = mainSeries;

        // Subscribe to visible logical range changes
        try {
          if (chart.timeScale && typeof chart.timeScale().subscribeVisibleLogicalRangeChange === 'function') {
            visibleLogicalRangeSubscription = chart.timeScale().subscribeVisibleLogicalRangeChange((logicalRange: any) => {
              console.log('Visible logical range changed:', logicalRange);
              // Trigger any range-dependent updates here
              // This can be used for dynamic data loading, indicators recalculation, etc.
              if (logicalRange && typeof logicalRange.from === 'number' && typeof logicalRange.to === 'number') {
                // Call the prop callback if provided
                if (onVisibleLogicalRangeChange) {
                  onVisibleLogicalRangeChange(logicalRange);
                }
                
                // Emit custom event for external listeners
                const event = new CustomEvent('chartVisibleRangeChange', { 
                  detail: { logicalRange, chart, mainSeries }
                });
                window.dispatchEvent(event);
              }
            });
            console.log('Subscribed to visible logical range changes');
          }
        } catch (rangeError) {
          console.warn('Failed to subscribe to visible logical range:', rangeError);
        }
        
        // Set data with proper validation
        if (Array.isArray(data) && data.length > 0) {
          console.log('🔍 LightweightChart received', data.length, 'candles');
          console.log('Sample received data:', data.slice(0, 3));
          
          const validData = data
            .filter(d => {
              const isValid = d && 
                typeof d.time === 'number' && 
                !isNaN(d.time) && 
                d.time > 0 &&
                typeof d.open === 'number' && 
                typeof d.high === 'number' && 
                typeof d.low === 'number' && 
                typeof d.close === 'number' &&
                !isNaN(d.open) && !isNaN(d.high) && !isNaN(d.low) && !isNaN(d.close) &&
                d.open > 0 && d.high > 0 && d.low > 0 && d.close > 0 &&
                d.high >= d.low && // High must be >= Low
                d.high >= Math.max(d.open, d.close) && // High must be >= Open and Close
                d.low <= Math.min(d.open, d.close); // Low must be <= Open and Close
              
              if (!isValid) {
                console.warn('❌ Chart component filtered out invalid candle:', d);
              }
              return isValid;
            })
            .sort((a, b) => a.time - b.time)
            // Remove duplicates and ensure strictly ascending timestamps
            .reduce((acc: any[], current) => {
              if (acc.length === 0 || current.time > acc[acc.length - 1].time) {
                acc.push(current);
              } else if (current.time === acc[acc.length - 1].time) {
                // If same timestamp, merge the data (use latest close, update high/low)
                console.log('⚠️ Chart: Merging duplicate timestamp:', current.time);
                const lastCandle = acc[acc.length - 1];
                acc[acc.length - 1] = {
                  ...lastCandle,
                  high: Math.max(lastCandle.high, current.high),
                  low: Math.min(lastCandle.low, current.low),
                  close: current.close, // Use the latest close price
                };
              } else {
                console.warn('⚠️ Chart: Skipping out-of-order timestamp:', current.time, 'vs', acc[acc.length - 1].time);
              }
              // Skip if timestamp is earlier than the last one (should not happen after sort)
              return acc;
            }, []);
          
          console.log(`🎯 Chart: Setting ${validData.length} valid candles out of ${data.length} received`);
          console.log('Final chart data sample:', validData.slice(0, 3));
          
          if (validData.length > 0) {
            mainSeries.setData(validData);
          } else {
            console.error('❌ No valid data to set on chart!');
          }
        }


        // Apply indicators if any
        if (Array.isArray(indicators) && indicators.length > 0) {
          indicators.forEach((indicator, index) => {
            if (indicator && typeof indicator.apply === 'function') {
              try {
                const indicatorSeries = indicator.apply({
                  chart,
                  mainSeries,
                  addLineSeries: (opts: any) => {
                    const { LineSeries } = mod;
                    if (chart.addLineSeries) {
                      return chart.addLineSeries(opts);
                    } else if (chart.addSeries && LineSeries) {
                      return chart.addSeries(LineSeries, opts);
                    }
                    throw new Error('addLineSeries method not available');
                  },
                });
                appliedIndicators.push({ id: indicator.id, series: indicatorSeries });
                console.log(`Applied indicator: ${indicator.id}`);
              } catch (err) {
                console.warn('Failed to apply indicator:', indicator.id, err);
              }
            }
          });
        }

        // Apply series markers if any (preferred method for trading signals)
        if (Array.isArray(seriesMarkers) && seriesMarkers.length > 0 && mainSeries) {
          try {
            console.log('Applying series markers:', seriesMarkers.length, 'markers');
            console.log('Markers data:', seriesMarkers);
            console.log('Available mainSeries methods:', Object.getOwnPropertyNames(mainSeries));
            
            // Try different methods to apply markers
            let markersApplied = false;
            
            // Method 1: Try setMarkers (if available)
            if (typeof mainSeries.setMarkers === 'function') {
              mainSeries.setMarkers(seriesMarkers);
              markersApplied = true;
              console.log('✅ Successfully applied markers using setMarkers()');
            }
            // Method 2: Try createSeriesMarkers from the module
            else if (mod.createSeriesMarkers && typeof mod.createSeriesMarkers === 'function') {
              mod.createSeriesMarkers(mainSeries, seriesMarkers);
              markersApplied = true;
              console.log('✅ Successfully applied markers using createSeriesMarkers()');
            }
            // Method 3: Try direct marker application via chart
            else if (chart.createSeriesMarkers && typeof chart.createSeriesMarkers === 'function') {
              chart.createSeriesMarkers(mainSeries, seriesMarkers);
              markersApplied = true;
              console.log('✅ Successfully applied markers using chart.createSeriesMarkers()');
            }
            // Method 4: Try addMarkers if available
            else if (typeof mainSeries.addMarkers === 'function') {
              mainSeries.addMarkers(seriesMarkers);
              markersApplied = true;
              console.log('✅ Successfully applied markers using addMarkers()');
            }
            
            if (!markersApplied) {
              console.warn('❌ No marker method available. Available methods:');
              console.log('Lightweight Charts module methods:', Object.getOwnPropertyNames(mod));
              console.log('Main series methods:', Object.getOwnPropertyNames(mainSeries));
              console.log('Chart methods:', Object.getOwnPropertyNames(chart));
              
              // Fallback: Create a simple pane primitive for markers
              console.log('📍 Falling back to custom marker primitive');
              try {
                const markerPrimitive = new SimpleMarkersPrimitive(seriesMarkers);
                
                if (chart.panes && typeof chart.panes === 'function') {
                  const panes = chart.panes();
                  if (panes.length > 0 && panes[0].attachPrimitive) {
                    panes[0].attachPrimitive(markerPrimitive);
                    console.log('✅ Applied markers using fallback pane primitive');
                    markersApplied = true;
                  }
                }
              } catch (primitiveError) {
                console.error('❌ Fallback primitive also failed:', primitiveError);
              }
            }
          } catch (err) {
            console.error('❌ Failed to apply series markers:', err);
          }
        } else if (seriesMarkers.length === 0) {
          console.log('No series markers to apply');
        }

        // Attach pane primitives using the correct v5.0 API (deprecated)
        if (Array.isArray(panePrimitives) && panePrimitives.length > 0) {
          panePrimitives.forEach((primitive, primitiveIndex) => {
            if (primitive) {
              try {
                console.log(`Attempting to attach pane primitive ${primitiveIndex}:`, {
                  primitiveType: primitive.constructor?.name || 'Unknown',
                  hasPaneViews: typeof primitive.paneViews === 'function',
                  hasAttached: typeof primitive.attached === 'function',
                  hasDetached: typeof primitive.detached === 'function'
                });

                let attached = false;
                
                // Method 1: Use chart.panes()[0].attachPrimitive() - the documented way
                if (chart.panes && typeof chart.panes === 'function') {
                  const panes = chart.panes();
                  console.log('Available panes:', panes.length);
                  
                  if (panes.length > 0) {
                    const mainPane = panes[0]; // Get the main pane
                    console.log('Main pane methods:', Object.getOwnPropertyNames(mainPane));
                    
                    if (mainPane && typeof mainPane.attachPrimitive === 'function') {
                      mainPane.attachPrimitive(primitive);
                      attached = true;
                      appliedPrimitives.push({ index: primitiveIndex, primitive, attachedToChart: true });
                      console.log(`✅ Attached pane primitive ${primitiveIndex} to main pane via mainPane.attachPrimitive()`);
                    }
                  }
                }
                
                // Method 2: Try chart-level attachment if available
                if (!attached && typeof chart.attachPrimitive === 'function') {
                  chart.attachPrimitive(primitive);
                  attached = true;
                  appliedPrimitives.push({ index: primitiveIndex, primitive, attachedToChart: true });
                  console.log(`✅ Attached pane primitive ${primitiveIndex} to chart via chart.attachPrimitive()`);
                }
                
                // Method 3: Manual attachment with proper IPanePrimitive interface
                if (!attached && typeof primitive.attached === 'function') {
                  primitive.attached({
                    pane: chart.panes ? chart.panes()[0] : null,
                    requestUpdate: () => {
                      console.log('Primitive requested update');
                      if (chart && typeof chart.timeScale === 'function') {
                        chart.timeScale().fitContent();
                      }
                    }
                  });
                  attached = true;
                  appliedPrimitives.push({ index: primitiveIndex, primitive, attachedToChart: false });
                  console.log(`✅ Manually attached pane primitive ${primitiveIndex} with IPanePrimitive interface`);
                }
                
                if (!attached) {
                  console.error(`❌ Could not attach primitive ${primitiveIndex} - no compatible attachment method found`);
                  console.error('Chart methods:', Object.getOwnPropertyNames(chart));
                  console.error('Primitive methods:', Object.getOwnPropertyNames(primitive));
                }
              } catch (err) {
                console.error(`❌ Failed to attach primitive ${primitiveIndex}:`, err);
              }
            }
          });
        }

        // Provide context for overlays/indicators
        const { LineSeries, AreaSeries, HistogramSeries } = mod;
        setContextValue({
          chart,
          mainSeries,
          addLineSeries: (opts: any) => {
            if (chart.addLineSeries) {
              return chart.addLineSeries(opts);
            } else if (chart.addSeries && LineSeries) {
              return chart.addSeries(LineSeries, opts);
            }
            throw new Error('addLineSeries method not available');
          },
          addAreaSeries: (opts: any) => {
            if (chart.addAreaSeries) {
              return chart.addAreaSeries(opts);
            } else if (chart.addSeries && AreaSeries) {
              return chart.addSeries(AreaSeries, opts);
            }
            throw new Error('addAreaSeries method not available');
          },
          addHistogramSeries: (opts: any) => {
            if (chart.addHistogramSeries) {
              return chart.addHistogramSeries(opts);
            } else if (chart.addSeries && HistogramSeries) {
              return chart.addSeries(HistogramSeries, opts);
            }
            throw new Error('addHistogramSeries method not available');
          },
        });
        setReady(true);

        // Enhanced responsive resize handling
        let resizeObserver: ResizeObserver | null = null;
        let lastWidth = 0;
        let lastHeight = 0;

        const handleResize = (source = 'manual') => {
          if (chart && chartContainerRef.current) {
            const newWidth = chartContainerRef.current.offsetWidth;
            const newHeight = chartContainerRef.current.offsetHeight;

            // Only resize if dimensions actually changed
            if (newWidth > 0 && newHeight > 0 && (newWidth !== lastWidth || newHeight !== lastHeight)) {
              console.log(`📏 Chart resizing (${source}): ${lastWidth}x${lastHeight} → ${newWidth}x${newHeight}`);

              chart.applyOptions({
                width: Math.floor(newWidth),
                height: Math.floor(newHeight),
              });

              lastWidth = newWidth;
              lastHeight = newHeight;
            }
          }
        };

        // Window resize handler (for event listener)
        const handleWindowResize = () => handleResize('window');

        // Set up ResizeObserver to detect container size changes (sidebar collapse/expand)
        if (isMounted && chartContainerRef.current && typeof ResizeObserver !== 'undefined') {
          resizeObserver = new ResizeObserver((entries) => {
            for (const entry of entries) {
              const { width, height } = entry.contentRect;
              if (width > 0 && height > 0 && chart) {
                // Use requestAnimationFrame to ensure smooth resizing
                requestAnimationFrame(() => {
                  if (chart && isMounted) {
                    handleResize('ResizeObserver');
                  }
                });
              }
            }
          });

          resizeObserver.observe(chartContainerRef.current);
          console.log('📏 ResizeObserver attached to chart container');
        } else if (typeof ResizeObserver === 'undefined') {
          console.warn('📏 ResizeObserver not supported, using fallback polling for resize detection');

          // Fallback: Poll for size changes every 500ms
          const pollForResize = () => {
            if (!isMounted || !chartContainerRef.current || !chart) return;

            const currentWidth = chartContainerRef.current.offsetWidth;
            const currentHeight = chartContainerRef.current.offsetHeight;

            // Store last known dimensions
            if (!chart._lastKnownWidth || !chart._lastKnownHeight) {
              chart._lastKnownWidth = currentWidth;
              chart._lastKnownHeight = currentHeight;
              return;
            }

            if (currentWidth !== chart._lastKnownWidth || currentHeight !== chart._lastKnownHeight) {
              handleResize('polling');
              chart._lastKnownWidth = currentWidth;
              chart._lastKnownHeight = currentHeight;
            }
          };

          const pollInterval = setInterval(pollForResize, 500);

          // Store interval for cleanup
          chart._pollInterval = pollInterval;
        }

        // Fallback: Window resize listener (for older browsers or edge cases)
        window.addEventListener("resize", handleWindowResize);

        // Initial resize to fit container - multiple attempts for reliability
        if (isMounted) {
          // Immediate resize
          handleResize();

          // Delayed resize to catch any layout changes
          setTimeout(handleResize, 50);
          setTimeout(handleResize, 200);

          // Final resize after animations might complete
          setTimeout(handleResize, 500);
        }

        cleanup = () => {
          isMounted = false;

          // Clean up ResizeObserver
          if (resizeObserver) {
            resizeObserver.disconnect();
            resizeObserver = null;
            console.log('📏 ResizeObserver disconnected');
          }

          // Clean up polling fallback
          if (chart && chart._pollInterval) {
            clearInterval(chart._pollInterval);
            chart._pollInterval = null;
            console.log('📏 Resize polling stopped');
          }

          // Clean up window resize listener
          window.removeEventListener("resize", handleWindowResize);

          // Unsubscribe from visible logical range changes
          if (visibleLogicalRangeSubscription) {
            try {
              visibleLogicalRangeSubscription();
              console.log('Unsubscribed from visible logical range changes');
            } catch (err) {
              console.warn('Error unsubscribing from visible logical range:', err);
            }
            visibleLogicalRangeSubscription = null;
          }
          
          // Clean up applied indicators
          appliedIndicators.forEach(({ id, series }) => {
            try {
              if (series && chart) {
                chart.removeSeries(series);
                console.log(`Removed indicator series: ${id}`);
              }
            } catch (err) {
              console.warn(`Error removing indicator series ${id}:`, err);
            }
          });
          appliedIndicators = [];
          
          // Clean up applied primitives
          appliedPrimitives.forEach(({ index, primitive }) => {
            try {
              if (primitive && typeof primitive.detached === 'function') {
                primitive.detached();
                console.log(`Detached primitive ${index}`);
              }
            } catch (err) {
              console.warn(`Error detaching primitive ${index}:`, err);
            }
          });
          appliedPrimitives = [];
          
          // Clean up chart and series
          try {
            if (mainSeries && chart) {
              chart.removeSeries(mainSeries);
            }
          } catch (err) {
            console.warn('Error removing main series:', err);
          }
          
          try {
            if (chart) {
              chart.remove();
            }
          } catch (err) {
            console.warn('Error removing chart:', err);
          }
          
          // Clear references
          chartRef.current = null;
          mainSeriesRef.current = null;
          setReady(false);
          setContextValue(null);
        };
      } catch (error) {
        console.error('Error initializing chart:', error);
        if (isMounted) {
          setReady(false);
          setContextValue(null);
        }
      }
    })();

    return () => {
      isMounted = false;
      cleanup();
      setReady(false);
      setContextValue(null);
      chartRef.current = null;
      mainSeriesRef.current = null;
    };
    // Only re-run if data, options, chartOptions change
    // Create stable keys for indicators and primitives to trigger proper re-renders
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    JSON.stringify(data),
    JSON.stringify(options),
    JSON.stringify(chartOptions),
    // Create a stable key for indicators based on their IDs and enabled state
    indicators?.map(i => `${i.id}`).join(',') || '',
    // Create a stable key for primitives 
    panePrimitives?.length || 0,
    // Create a stable key for series markers
    seriesMarkers?.length || 0,
    width,
    height,
  ]);

  return (
    <div
      ref={chartContainerRef}
      style={{
        width: typeof width === 'string' ? width : `${width}px`,
        height: `${height}px`,
        minWidth: 320,
        maxWidth: "100%",
        margin: "0 auto",
        position: "relative",
        zIndex: 1,
      }}
    >
      {ready && contextValue && (
        <ChartContext.Provider value={contextValue}>
          {children}
        </ChartContext.Provider>
      )}
    </div>
  );
});

LightweightCandlestickChart.displayName = 'LightweightCandlestickChart';

// Overlay/indicator series component (example: line)
export const ChartLineOverlay: React.FC<{ options?: any; data?: any[] }> = ({
  options = {},
  data = [],
}) => {
  const ctx = useContext(ChartContext);
  const seriesRef = useRef<any>(null);

  useEffect(() => {
    if (!ctx || !ctx.chart) return;
    
    let line: any;
    try {
      line = ctx.addLineSeries(options);
      seriesRef.current = line;
      if (Array.isArray(data) && data.length > 0) {
        line.setData(data);
      }
    } catch (error) {
      console.warn('Error creating line overlay:', error);
      return;
    }
    
    return () => {
      try {
        if (ctx.chart && line) {
          ctx.chart.removeSeries(line);
        }
      } catch (error) {
        console.warn('Error removing line overlay:', error);
      }
      seriesRef.current = null;
    };
    // Only re-run if options/data change
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(options), JSON.stringify(data)]);
  return null;
};

export default LightweightCandlestickChart;
