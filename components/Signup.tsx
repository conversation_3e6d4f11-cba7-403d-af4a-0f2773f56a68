"use client";

import { signUpAction } from "@/app/actions"; // Adjust the import path as necessary
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import Link from "next/link";
import { useState } from "react";
import { FormMessage, Message } from "./form-message";
import { useToast } from "@/hooks/use-toast"; // Adjust the import path as necessary
import Logo from "./icons/Logo";
import { Card, CardContent } from "./ui/card";

interface SignupProps {
  searchParams: Message;
}

export default function Signup({ searchParams }: SignupProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast(); // Use the custom toast hook

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true); // Set submitting state to true

    const formData = new FormData(e.currentTarget as HTMLFormElement); // Use e.currentTarget for better type safety
    try {
      await signUpAction(formData);
      toast({
        title: "Success!",
        description: "Sign up successful! Please verify your email.",
      }); // Show success toast with verification message
    } catch (error) {
      toast({
        title: "Error!",
        description: "Sign up failed. Please try again.",
      }); // Show error toast
    } finally {
      setIsSubmitting(false); // Reset submitting state after action is complete
    }
  };

  return (
    <>
      <Card className="liquid-glass glass-shadow glass-shadow-hover">
        <CardContent className="p-8">
          <Link href="/">
            <Logo className="h-auto w-1/2 lg:flex lg:flex-row m-auto" />
          </Link>
          {/* Show FormMessage only when searchParams contain a valid message */}
          <div className="w-full flex justify-center mb-4">
            <FormMessage message={searchParams} />
          </div>

          <form
            onSubmit={handleSubmit}
            className="flex flex-col w-full max-w-sm mx-auto space-y-4"
            autoComplete="off"
          >
            <h1 className="text-2xl font-bold text-center mb-2">Create Account</h1>
            <p className="text-sm text-center text-muted-foreground mb-6">
              Already have an account?{" "}
              <Link
                className="text-primary font-medium hover:text-primary/80 underline transition-colors"
                href="/signin"
              >
                Sign in
              </Link>
            </p>
            <div className="flex flex-col gap-4 mt-6">
              <Label htmlFor="username">Username</Label>
              <Input
                name="username"
                id="username"
                placeholder="cooluser123"
                required
                autoComplete="username" // Google autofill warning for usernames
              />

              <Label htmlFor="email">Email</Label>
              <Input
                name="email"
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
                autoComplete="email" // Ensures email is auto-filled
              />

              <Label htmlFor="password">Password</Label>
              <Input
                type="password"
                name="password"
                id="password"
                placeholder="Your password"
                minLength={8}
                required
                autoComplete="new-password" // Specific for password fields during signup
              />

              <Button 
                type="submit" 
                disabled={isSubmitting} 
                variant="liquid-glass"
                className="w-full bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 border-primary/30 hover:border-primary/50 text-foreground font-medium"
              >
                {isSubmitting ? "Signing up..." : "Sign up"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </>
  );
}
