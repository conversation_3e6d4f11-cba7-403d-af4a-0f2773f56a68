"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface ModernLayoutProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'glass' | 'gradient' | 'crypto';
  animation?: 'fade' | 'slide' | 'scale' | 'none';
}

export function ModernContainer({ 
  children, 
  className,
  variant = 'default',
  animation = 'fade'
}: ModernLayoutProps) {
  const baseClasses = "container-modern";
  
  const variantClasses = {
    default: "",
    glass: "glass-card",
    gradient: "bg-gradient-to-br from-orange-100 to-blue-100 dark:from-orange-900/20 dark:to-blue-900/20",
    crypto: "bg-gradient-to-br from-background via-background/95 to-background/90"
  };
  
  const animationClasses = {
    fade: "animate-fade-in",
    slide: "animate-slide-up", 
    scale: "animate-scale-in",
    none: ""
  };

  return (
    <div className={cn(
      baseClasses,
      variantClasses[variant],
      animationClasses[animation],
      className
    )}>
      {children}
    </div>
  );
}

export function ModernCard({ 
  children, 
  className,
  variant = 'default',
  animation = 'fade'
}: ModernLayoutProps) {
  const baseClasses = "card-modern p-6";
  
  const variantClasses = {
    default: "",
    glass: "glass-card",
    gradient: "bg-gradient-to-br from-primary/5 to-secondary/5 border-primary/20",
    crypto: "bg-gradient-to-br from-card/80 to-card/60 border-primary/20"
  };
  
  const animationClasses = {
    fade: "animate-fade-in",
    slide: "animate-slide-up",
    scale: "animate-scale-in", 
    none: ""
  };

  return (
    <div className={cn(
      baseClasses,
      variantClasses[variant],
      animationClasses[animation],
      className
    )}>
      {children}
    </div>
  );
}

export function ModernSection({ 
  children, 
  className,
  variant = 'default',
  animation = 'fade'
}: ModernLayoutProps) {
  const baseClasses = "section-modern";
  
  const variantClasses = {
    default: "",
    glass: "glass-card",
    gradient: "bg-gradient-to-br from-background to-muted/20",
    crypto: "relative overflow-hidden"
  };
  
  const animationClasses = {
    fade: "animate-fade-in",
    slide: "animate-slide-up",
    scale: "animate-scale-in",
    none: ""
  };

  return (
    <section className={cn(
      baseClasses,
      variantClasses[variant],
      animationClasses[animation],
      className
    )}>
      {variant === 'crypto' && (
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 pointer-events-none" />
      )}
      <div className="relative z-10">
        {children}
      </div>
    </section>
  );
}

interface ModernGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: 1 | 2 | 3 | 4 | 6 | 12;
  gap?: 'sm' | 'md' | 'lg' | 'xl';
  responsive?: boolean;
}

export function ModernGrid({ 
  children, 
  className,
  cols = 3,
  gap = 'md',
  responsive = true
}: ModernGridProps) {
  const gapClasses = {
    sm: "gap-4",
    md: "gap-6", 
    lg: "gap-8",
    xl: "gap-12"
  };
  
  const colClasses = responsive ? {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4",
    6: "grid-cols-2 md:grid-cols-3 lg:grid-cols-6",
    12: "grid-cols-4 md:grid-cols-6 lg:grid-cols-12"
  } : {
    1: "grid-cols-1",
    2: "grid-cols-2",
    3: "grid-cols-3", 
    4: "grid-cols-4",
    6: "grid-cols-6",
    12: "grid-cols-12"
  };

  return (
    <div className={cn(
      "grid",
      colClasses[cols],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  );
}

interface ModernButtonProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'gradient' | 'glass' | 'crypto';
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export function ModernButton({ 
  children, 
  className,
  variant = 'default',
  size = 'md',
  onClick,
  disabled = false,
  type = 'button'
}: ModernButtonProps) {
  const baseClasses = "relative overflow-hidden rounded-lg px-6 py-3 font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg active:scale-95 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary";
  
  const variantClasses = {
    default: "bg-primary text-primary-foreground hover:bg-primary/90",
    gradient: "bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700",
    glass: "glass-card text-foreground hover:bg-card/80",
    crypto: "bg-gradient-to-r from-orange-500 to-blue-500 text-white hover:from-orange-600 hover:to-blue-600"
  };
  
  const sizeClasses = {
    sm: "px-4 py-2 text-sm",
    md: "px-6 py-3 text-base",
    lg: "px-8 py-4 text-lg"
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={cn(
        baseClasses,
        variantClasses[variant],
        sizeClasses[size],
        disabled && "opacity-50 cursor-not-allowed hover:scale-100",
        className
      )}
    >
      {children}
    </button>
  );
}

interface ModernBadgeProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'crypto' | 'success' | 'warning' | 'error';
  size?: 'sm' | 'md' | 'lg';
}

export function ModernBadge({ 
  children, 
  className,
  variant = 'default',
  size = 'md'
}: ModernBadgeProps) {
  const baseClasses = "inline-flex items-center rounded-full font-medium";
  
  const variantClasses = {
    default: "bg-muted text-muted-foreground",
    crypto: "bg-gradient-to-r from-bitcoin-orange/20 to-ethereum-blue/20 text-primary border border-primary/20",
    success: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
    warning: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200", 
    error: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
  };
  
  const sizeClasses = {
    sm: "px-2 py-1 text-xs",
    md: "px-3 py-1 text-sm",
    lg: "px-4 py-2 text-base"
  };

  return (
    <span className={cn(
      baseClasses,
      variantClasses[variant],
      sizeClasses[size],
      className
    )}>
      {children}
    </span>
  );
}

interface ModernMetricProps {
  label: string;
  value: string | number;
  change?: number | 'up' | 'down' | 'neutral';
  icon?: React.ReactNode;
  className?: string;
  variant?: 'default' | 'crypto' | 'glass';
}

export function ModernMetric({ 
  label, 
  value, 
  change,
  icon,
  className,
  variant = 'default'
}: ModernMetricProps) {
  const variantClasses = {
    default: "crypto-metric",
    crypto: "crypto-metric bg-gradient-to-r from-bitcoin-orange/10 to-ethereum-blue/10 border-primary/20",
    glass: "crypto-metric glass"
  };

  const changeColor =
    typeof change === 'number'
      ? change > 0
        ? "status-positive"
        : change < 0
        ? "status-negative"
        : "status-neutral"
      : "status-neutral";

  return (
    <div className={cn(variantClasses[variant], className)}>
      <div className="flex items-center gap-3">
        {icon && <div className="text-primary">{icon}</div>}
        <div>
          <p className="text-sm text-muted-foreground">{label}</p>
          <p className="text-2xl font-bold">{value}</p>
        </div>
      </div>
      {change !== undefined && (
        <div className={cn("text-sm font-medium", changeColor)}>
          {typeof change === 'number' && change > 0 ? '+' : ''}{change}
        </div>
      )}
    </div>
  );
}
