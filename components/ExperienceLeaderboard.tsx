'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Trophy, 
  Star, 
  Crown, 
  Award, 
  TrendingUp,
  Medal,
  Zap,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { createClient } from '@/utils/supabase/client';
import { useTierAccess } from '@/hooks/use-tier-access';
import { rpcHelper } from '@/utils/supabase-rpc-helper';

interface LeaderboardUser {
  user_id: string;
  username: string;
  display_name: string;
  avatar_url: string;
  tier: string;
  experience_points: number;
  level: number;
  reputation_score: number;
  rank: number;
}

interface ExperienceLeaderboardProps {
  limit?: number;
  showCurrentUser?: boolean;
  compact?: boolean;
  className?: string;
}

export function ExperienceLeaderboard({
  limit = 50,
  showCurrentUser = true,
  compact = false,
  className
}: ExperienceLeaderboardProps) {
  const [leaderboard, setLeaderboard] = useState<LeaderboardUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [currentUserRank, setCurrentUserRank] = useState<LeaderboardUser | null>(null);
  
  const { userExperience, getTierName } = useTierAccess();
  const supabase = createClient();

  const fetchLeaderboard = async () => {
    try {
      setLoading(true);
      
      // Use the RPC helper with fallback
      const { data } = await rpcHelper.getExperienceLeaderboard(limit);
      
      setLeaderboard(data || []);

      // Find current user's rank if user is available
      if (showCurrentUser && userExperience && data) {
        const userRank = data.find((user: LeaderboardUser) => 
          user.user_id === userExperience.user_id
        );
        setCurrentUserRank(userRank || null);
      }
      
    } catch (error) {
      console.error('Error in fetchLeaderboard:', error);
      // This should not happen with the helper, but just in case
      setLeaderboard([]);
      setCurrentUserRank(null);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchLeaderboard();
  };

  useEffect(() => {
    fetchLeaderboard();
  }, [limit, userExperience]);

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Crown className="h-5 w-5 text-yellow-500" />;
      case 2: return <Medal className="h-5 w-5 text-gray-400" />;
      case 3: return <Award className="h-5 w-5 text-amber-600" />;
      default: return <span className="text-sm font-bold text-gray-500">#{rank}</span>;
    }
  };

  const getTierBadgeColor = (tier: string) => {
    switch (tier) {
      case 'tier1': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'tier2': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'tier3': return 'bg-green-100 text-green-800 border-green-200';
      case 'tier4': return 'bg-gold-100 text-gold-800 border-gold-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-gold-500" />
            Experience Leaderboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Trophy className="h-4 w-4 text-gold-500" />
              Top Players
            </CardTitle>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={handleRefresh}
              disabled={refreshing}
            >
              <RefreshCw className={cn("h-4 w-4", refreshing && "animate-spin")} />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {leaderboard.slice(0, 5).map((user) => (
              <div key={user.user_id} className="flex items-center gap-3 p-2 rounded-lg hover:text-shadow-gray-900 hover:bg-gray-50">
                <div className="flex items-center justify-center w-6">
                  {getRankIcon(user.rank)}
                </div>
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user.avatar_url} alt={user.display_name} />
                  <AvatarFallback>{user.display_name?.[0] || user.username[0]}</AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {user.display_name || user.username}
                  </p>
                  <p className="text-xs text-gray-500">
                    Level {user.level} • {user.experience_points.toLocaleString()} XP
                  </p>
                </div>
                <Badge 
                  variant="outline" 
                  className={cn("text-xs", getTierBadgeColor(user.tier))}
                >
                  {getTierName(user.tier)}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="h-5 w-5 text-gold-500" />
              Experience Leaderboard
            </CardTitle>
            <CardDescription>
              Top players ranked by experience points and level
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="experience" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="experience">Experience</TabsTrigger>
            <TabsTrigger value="reputation">Reputation</TabsTrigger>
          </TabsList>

          <TabsContent value="experience" className="space-y-4">
            {/* Current User Rank */}
            {showCurrentUser && currentUserRank && (
              <div className="p-4 bg-blue-50 rounded-lg border text-shadow-gray-900 border-blue-200">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8">
                    {getRankIcon(currentUserRank.rank)}
                  </div>
                  <Avatar>
                    <AvatarImage src={currentUserRank.avatar_url} alt={currentUserRank.display_name} />
                    <AvatarFallback>{currentUserRank.display_name?.[0] || currentUserRank.username[0]}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="font-medium">
                      {currentUserRank.display_name || currentUserRank.username} (You)
                    </p>
                    <p className="text-sm text-gray-600">
                      Level {currentUserRank.level} • {currentUserRank.experience_points.toLocaleString()} XP
                    </p>
                  </div>
                  <Badge 
                    variant="outline"
                    className={getTierBadgeColor(currentUserRank.tier)}
                  >
                    {getTierName(currentUserRank.tier)}
                  </Badge>
                </div>
              </div>
            )}

            {/* Top Players */}
            <div className="space-y-2">
              {leaderboard.map((user) => (
                <div 
                  key={user.user_id} 
                  className={cn(
                    "flex items-center gap-4 p-4 rounded-lg border transition-colors",
                    user.rank <= 3 ? "bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200" : "hover:bg-gray-50",
                    user.user_id === userExperience?.user_id && "ring-2 ring-blue-500 bg-blue-50"
                  )}
                >
                  <div className="flex items-center justify-center w-10">
                    {getRankIcon(user.rank)}
                  </div>
                  
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={user.avatar_url} alt={user.display_name} />
                    <AvatarFallback>{user.display_name?.[0] || user.username[0]}</AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <p className="font-medium">
                        {user.display_name || user.username}
                      </p>
                      {user.user_id === userExperience?.user_id && (
                        <Badge variant="secondary" className="text-xs">You</Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <span className="flex items-center gap-1">
                        <Zap className="h-3 w-3" />
                        Level {user.level}
                      </span>
                      <span>{user.experience_points.toLocaleString()} XP</span>
                      <span className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        {user.reputation_score} reputation
                      </span>
                    </div>
                  </div>
                  
                  <Badge 
                    variant="outline"
                    className={getTierBadgeColor(user.tier)}
                  >
                    {getTierName(user.tier)}
                  </Badge>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="reputation" className="space-y-4">
            <div className="space-y-2">
              {[...leaderboard]
                .sort((a, b) => b.reputation_score - a.reputation_score)
                .map((user, index) => (
                  <div 
                    key={user.user_id} 
                    className="flex items-center gap-4 p-4 rounded-lg border hover:bg-gray-50"
                  >
                    <div className="flex items-center justify-center w-10">
                      <span className="text-sm font-bold text-gray-500">#{index + 1}</span>
                    </div>
                    
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={user.avatar_url} alt={user.display_name} />
                      <AvatarFallback>{user.display_name?.[0] || user.username[0]}</AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1">
                      <p className="font-medium">
                        {user.display_name || user.username}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {user.reputation_score} reputation
                        </span>
                        <span>Level {user.level}</span>
                      </div>
                    </div>
                    
                    <Badge 
                      variant="outline"
                      className={getTierBadgeColor(user.tier)}
                    >
                      {getTierName(user.tier)}
                    </Badge>
                  </div>
                ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
