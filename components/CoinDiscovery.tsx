"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { exportCoinDiscoveryData } from '@/utils/export-utils';
import { 
  Search, 
  TrendingUp, 
  Star, 
  Filter,
  Clock,
  DollarSign,
  Users,
  Zap,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  Heart,
  ExternalLink,
  RefreshCw,
  Download
} from 'lucide-react';

interface Coin {
  id: string;
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  change7d: number;
  marketCap: number;
  volume24h: number;
  rank: number;
  logo?: string;
  category: string;
  launchDate: string;
  isNew: boolean;
  isTrending: boolean;
  isWatchlisted: boolean;
  tags: string[];
  description: string;
  socialScore: number;
  technicalScore: number;
  fundamentalScore: number;
}

interface CoinDiscoveryProps {
  onCoinSelect?: (coin: Coin) => void;
  onWatchlistToggle?: (coinId: string) => void;
  loading?: boolean;
}

// Mock data generator
const generateMockCoins = (): Coin[] => {
  const symbols = [
    'BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'AVAX', 'MATIC', 'UNI', 'ATOM',
    'ALGO', 'VET', 'FTM', 'HBAR', 'ICP', 'THETA', 'XTZ', 'EOS', 'AAVE', 'MKR',
    'COMP', 'YFI', 'SNX', 'CRV', 'BAL', 'REN', 'ZRX', 'KNC', '1INCH', 'SUSHI',
    'LRC', 'ENJ', 'MANA', 'SAND', 'AXS', 'FLOW', 'CHZ', 'HOT', 'WIN', 'BTT'
  ];

  const names = [
    'Bitcoin', 'Ethereum', 'Solana', 'Cardano', 'Polkadot', 'Chainlink', 'Avalanche', 'Polygon', 'Uniswap', 'Cosmos',
    'Algorand', 'VeChain', 'Fantom', 'Hedera', 'Internet Computer', 'Theta', 'Tezos', 'EOS', 'Aave', 'Maker',
    'Compound', 'yearn.finance', 'Synthetix', 'Curve', 'Balancer', 'Ren', '0x', 'Kyber Network', '1inch', 'SushiSwap',
    'Loopring', 'Enjin', 'Decentraland', 'The Sandbox', 'Axie Infinity', 'Flow', 'Chiliz', 'Holo', 'WINkLink', 'BitTorrent'
  ];

  const categories = ['DeFi', 'Layer 1', 'Layer 2', 'NFT', 'Gaming', 'Metaverse', 'Exchange', 'Privacy', 'Oracle', 'Storage'];
  const tags = ['Hot', 'New', 'DeFi', 'Gaming', 'NFT', 'AI', 'Web3', 'Metaverse', 'Layer2', 'Staking'];

  return symbols.map((symbol, index) => ({
    id: symbol.toLowerCase(),
    symbol: symbol.toUpperCase(),
    name: names[index] || symbol,
    price: Math.random() * 1000 + 0.01,
    change24h: (Math.random() - 0.5) * 20,
    change7d: (Math.random() - 0.5) * 40,
    marketCap: Math.random() * 100000000000 + 1000000,
    volume24h: Math.random() * 10000000000 + 100000,
    rank: index + 1,
    category: categories[Math.floor(Math.random() * categories.length)],
    launchDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    isNew: Math.random() > 0.85, // 15% chance of being new
    isTrending: Math.random() > 0.8, // 20% chance of trending
    isWatchlisted: Math.random() > 0.9, // 10% chance of being watchlisted
    tags: Array.from({ length: Math.floor(Math.random() * 3) + 1 }, () => 
      tags[Math.floor(Math.random() * tags.length)]
    ).filter((tag, i, arr) => arr.indexOf(tag) === i),
    description: `${names[index] || symbol} is a revolutionary blockchain platform focusing on ${categories[Math.floor(Math.random() * categories.length)].toLowerCase()} solutions.`,
    socialScore: Math.floor(Math.random() * 100),
    technicalScore: Math.floor(Math.random() * 100),
    fundamentalScore: Math.floor(Math.random() * 100),
  }));
};

export function CoinDiscovery({ 
  onCoinSelect, 
  onWatchlistToggle,
  loading = false 
}: CoinDiscoveryProps) {
  const [coins] = useState<Coin[]>(generateMockCoins());
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'rank' | 'change24h' | 'volume24h' | 'marketCap'>('rank');
  const [priceRange, setPriceRange] = useState<'all' | 'under1' | '1to10' | '10to100' | 'over100'>('all');
  const [showOnlyNew, setShowOnlyNew] = useState(false);
  const [showOnlyTrending, setShowOnlyTrending] = useState(false);

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(coins.map(coin => coin.category)))];

  // Filter and sort coins
  const filteredCoins = useMemo(() => {
    let filtered = coins.filter(coin => {
      // Search filter
      if (searchQuery && !coin.name.toLowerCase().includes(searchQuery.toLowerCase()) && 
          !coin.symbol.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Category filter
      if (selectedCategory !== 'all' && coin.category !== selectedCategory) {
        return false;
      }

      // Price range filter
      if (priceRange === 'under1' && coin.price >= 1) return false;
      if (priceRange === '1to10' && (coin.price < 1 || coin.price >= 10)) return false;
      if (priceRange === '10to100' && (coin.price < 10 || coin.price >= 100)) return false;
      if (priceRange === 'over100' && coin.price < 100) return false;

      // Special filters
      if (showOnlyNew && !coin.isNew) return false;
      if (showOnlyTrending && !coin.isTrending) return false;

      return true;
    });

    // Sort
    filtered.sort((a, b) => {
      if (sortBy === 'rank') return a.rank - b.rank;
      if (sortBy === 'change24h') return b.change24h - a.change24h;
      if (sortBy === 'volume24h') return b.volume24h - a.volume24h;
      if (sortBy === 'marketCap') return b.marketCap - a.marketCap;
      return 0;
    });

    return filtered;
  }, [coins, searchQuery, selectedCategory, sortBy, priceRange, showOnlyNew, showOnlyTrending]);

  const formatNumber = (num: number, type: 'currency' | 'percentage' | 'compact' = 'compact') => {
    if (type === 'currency') {
      return new Intl.NumberFormat('en-US', { 
        style: 'currency', 
        currency: 'USD',
        notation: num > 1000000 ? 'compact' : 'standard',
        maximumFractionDigits: num < 1 ? 4 : 2
      }).format(num);
    }
    if (type === 'percentage') {
      return `${num > 0 ? '+' : ''}${num.toFixed(2)}%`;
    }
    return new Intl.NumberFormat('en-US', { 
      notation: 'compact',
      maximumFractionDigits: 2
    }).format(num);
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <ArrowUpRight className="h-3 w-3" />;
    if (change < 0) return <ArrowDownRight className="h-3 w-3" />;
    return null;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'bg-green-500';
    if (score >= 60) return 'bg-yellow-500';
    if (score >= 40) return 'bg-orange-500';
    return 'bg-red-500';
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5 text-orange-500" />
              Coin Discovery
            </CardTitle>
            <CardDescription>
              Discover new cryptocurrencies and trending tokens
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" disabled={loading}>
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => exportCoinDiscoveryData(filteredCoins)}
              disabled={!filteredCoins.length}
            >
              <Download className="h-4 w-4" />
            </Button>
            <Badge variant="outline">
              {filteredCoins.length} coins
            </Badge>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap items-center gap-4 pt-4">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search coins..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-48"
            />
          </div>

          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={sortBy} onValueChange={(value: typeof sortBy) => setSortBy(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="rank">Rank</SelectItem>
              <SelectItem value="change24h">24h Change</SelectItem>
              <SelectItem value="volume24h">Volume</SelectItem>
              <SelectItem value="marketCap">Market Cap</SelectItem>
            </SelectContent>
          </Select>

          <Select value={priceRange} onValueChange={(value: typeof priceRange) => setPriceRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Prices</SelectItem>
              <SelectItem value="under1">Under $1</SelectItem>
              <SelectItem value="1to10">$1 - $10</SelectItem>
              <SelectItem value="10to100">$10 - $100</SelectItem>
              <SelectItem value="over100">Over $100</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center gap-2">
            <Button
              variant={showOnlyNew ? "default" : "outline"}
              size="sm"
              onClick={() => setShowOnlyNew(!showOnlyNew)}
            >
              New
            </Button>
            <Button
              variant={showOnlyTrending ? "default" : "outline"}
              size="sm"
              onClick={() => setShowOnlyTrending(!showOnlyTrending)}
            >
              🔥 Trending
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="grid" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="grid">Grid View</TabsTrigger>
            <TabsTrigger value="list">List View</TabsTrigger>
            <TabsTrigger value="watchlist">Watchlist</TabsTrigger>
          </TabsList>

          {/* Grid View */}
          <TabsContent value="grid">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredCoins.slice(0, 12).map((coin) => (
                <Card key={coin.id} className="cursor-pointer hover:shadow-md transition-all">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                          {coin.symbol.slice(0, 2)}
                        </div>
                        <div>
                          <h3 className="font-semibold">{coin.symbol}</h3>
                          <p className="text-sm text-muted-foreground">{coin.name}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        {coin.isNew && <Badge variant="secondary" className="text-xs">NEW</Badge>}
                        {coin.isTrending && <Badge variant="secondary" className="text-xs">🔥</Badge>}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            onWatchlistToggle?.(coin.id);
                          }}
                        >
                          <Heart className={`h-4 w-4 ${coin.isWatchlisted ? 'text-red-500 fill-current' : ''}`} />
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-lg font-bold">{formatNumber(coin.price, 'currency')}</span>
                        <div className={`flex items-center gap-1 ${getChangeColor(coin.change24h)}`}>
                          {getChangeIcon(coin.change24h)}
                          <span className="text-sm font-medium">
                            {formatNumber(coin.change24h, 'percentage')}
                          </span>
                        </div>
                      </div>

                      <div className="text-xs text-muted-foreground space-y-1">
                        <div className="flex justify-between">
                          <span>Rank:</span>
                          <span>#{coin.rank}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Market Cap:</span>
                          <span>{formatNumber(coin.marketCap, 'currency')}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Volume 24h:</span>
                          <span>{formatNumber(coin.volume24h, 'currency')}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1 mt-2">
                        {coin.tags.slice(0, 2).map(tag => (
                          <Badge key={tag} variant="outline" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>

                      {/* Score indicators */}
                      <div className="flex items-center gap-2 mt-2">
                        <div className="flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${getScoreColor(coin.socialScore)}`}></div>
                          <span className="text-xs">Social: {coin.socialScore}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <div className={`w-2 h-2 rounded-full ${getScoreColor(coin.technicalScore)}`}></div>
                          <span className="text-xs">Tech: {coin.technicalScore}</span>
                        </div>
                      </div>

                      <Button
                        className="w-full mt-3"
                        size="sm"
                        onClick={() => onCoinSelect?.(coin)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* List View */}
          <TabsContent value="list">
            <div className="space-y-2">
              {filteredCoins.slice(0, 20).map((coin) => (
                <Card key={coin.id} className="cursor-pointer hover:bg-accent/50 transition-all">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <span className="text-sm text-muted-foreground w-8">#{coin.rank}</span>
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                          {coin.symbol.slice(0, 2)}
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">{coin.symbol}</span>
                            <span className="text-sm text-muted-foreground">{coin.name}</span>
                            {coin.isNew && <Badge variant="secondary" className="text-xs">NEW</Badge>}
                            {coin.isTrending && <Badge variant="secondary" className="text-xs">🔥</Badge>}
                          </div>
                          <div className="flex gap-1 mt-1">
                            {coin.tags.slice(0, 3).map(tag => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-6 text-right">
                        <div>
                          <div className="font-semibold">{formatNumber(coin.price, 'currency')}</div>
                          <div className={`text-sm ${getChangeColor(coin.change24h)}`}>
                            {formatNumber(coin.change24h, 'percentage')}
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <div>MC: {formatNumber(coin.marketCap, 'currency')}</div>
                          <div>Vol: {formatNumber(coin.volume24h, 'currency')}</div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              onWatchlistToggle?.(coin.id);
                            }}
                          >
                            <Heart className={`h-4 w-4 ${coin.isWatchlisted ? 'text-red-500 fill-current' : ''}`} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onCoinSelect?.(coin)}
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Watchlist */}
          <TabsContent value="watchlist">
            <div className="space-y-4">
              {filteredCoins.filter(coin => coin.isWatchlisted).length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Heart className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No coins in your watchlist yet.</p>
                  <p className="text-sm">Click the heart icon on any coin to add it to your watchlist.</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {filteredCoins.filter(coin => coin.isWatchlisted).map((coin) => (
                    <Card key={coin.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                            {coin.symbol.slice(0, 2)}
                          </div>
                          <div>
                            <h3 className="font-semibold">{coin.symbol}</h3>
                            <p className="text-sm text-muted-foreground">{coin.name}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">{formatNumber(coin.price, 'currency')}</div>
                          <div className={`text-sm ${getChangeColor(coin.change24h)}`}>
                            {formatNumber(coin.change24h, 'percentage')}
                          </div>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}