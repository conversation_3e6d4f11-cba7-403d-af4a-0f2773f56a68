"use client";

import React, { useState } from 'react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from '@/components/ui/sidebar';
import { useTierAccess } from '@/hooks/use-tier-access';
import { NotificationBadge } from './NotificationBadge';
import { ExperienceBadge } from './ExperienceSystem';
import { useSidebar } from '@/components/ui/sidebar';
import {
  Home,
  Search,
  FileText,
  Gift,
  Crown,
  Bell,
  PenSquare,
  <PERSON><PERSON>,
  Settings2,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Compass,
  TrendingUp,
  Shield
} from 'lucide-react';
import { TierProtectedMenuItem } from '@/components/TierProtectedMenuItem';

interface NavCategory {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
  items: NavItem[];
}

interface NavItem {
  id: string;
  title: string;
  url: string;
  icon: React.ComponentType<any>;
  description: string;
  tierRequired?: string;
  isNew?: boolean;
  isPopular?: boolean;
  showNotificationBadge?: boolean;
  subItems?: NavSubItem[];
}

interface NavSubItem {
  title: string;
  url: string;
  description: string;
}

interface NavMainAccordionProps {
  onItemClick?: () => void;
  userId: string;
}

// Individual menu items (not in dropdown)
const getMainMenuItems = (userId: string): NavItem[] => [
  {
    id: 'home',
    title: 'Home',
    url: '/ctn',
    icon: Home,
    description: 'Your personalized crypto news feed'
  },
  {
    id: 'publish',
    title: 'Publish Article',
    url: '/ctn/articles/publish',
    icon: PenSquare,
    description: 'Create and publish crypto content'
  },
  {
    id: 'research',
    title: 'Research Papers',
    url: '/ctn/research',
    icon: FileText,
    description: 'Access premium research and analysis',
    tierRequired: 'tier1'
  },
  {
    id: 'search-hashtags',
    title: 'Search & Hashtags',
    url: '/ctn/search',
    icon: Search,
    description: 'Find articles, users, and trending topics',
    subItems: [
      { title: 'Search', url: '/ctn/search', description: 'Find articles, users, and topics' },
      { title: 'Hashtags', url: '/ctn/hashtag', description: 'Browse trending crypto topics' }
    ]
  },
  {
    id: 'leaderboards',
    title: 'Leaderboards',
    url: '/ctn/leaderboards',
    icon: Crown,
    description: 'See top contributors and rankings'
  }
];

const getNavCategories = (userId: string): NavCategory[] => [
  {
    id: 'tools',
    title: 'Tools & Analytics',
    icon: BarChart3,
    description: 'Trading tools and market analysis',
    items: [
      {
        id: 'tools-dashboard',
        title: 'Tools Dashboard',
        url: '/ctn/tools',
        icon: BarChart3,
        description: 'Unified crypto tools dashboard with portfolio, charts, heatmap, and more'
      },
      {
        id: 'indicators',
        title: 'Trading Indicators',
        url: '/ctn/tools/indicators',
        icon: TrendingUp,
        description: 'Advanced trading strategies and technical indicators',
        tierRequired: 'tier1'
      }
    ]
  },
  {
    id: 'account',
    title: 'Account Management',
    icon: Shield,
    description: 'Profile, settings, subscription, and account controls',
    items: [
      {
        id: 'profile',
        title: 'My Profile',
        url: `/ctn/profile/${userId}`,
        icon: Bot,
        description: 'Manage your profile and view your content'
      },
      {
        id: 'daily-claim',
        title: 'Daily Claim',
        url: '/ctn/daily-claim',
        icon: Gift,
        description: 'Claim your daily rewards and bonuses',
        tierRequired: 'tier1'
      },
      {
        id: 'notifications',
        title: 'Notifications',
        url: '/ctn/notifications',
        icon: Bell,
        description: 'Stay updated with latest activities',
        showNotificationBadge: true
      },
      {
        id: 'analytics-insights',
        title: 'Analytics & Insights',
        url: '/ctn/analytics',
        icon: BarChart3,
        description: 'Comprehensive market analytics and insights',
        subItems: [
          { title: 'Analytics Dashboard', url: '/ctn/analytics', description: 'Market analytics and insights' },
          { title: 'Platform Analytics', url: '/ctn/analytics/platform', description: 'Platform usage metrics' }
        ]
      },
      {
        id: 'upgrade',
        title: 'Upgrade Plan',
        url: '/ctn/subscription/plans',
        icon: CreditCard,
        description: 'Unlock premium features and tools'
      },
      {
        id: 'settings',
        title: 'Settings',
        url: '/ctn/settings',
        icon: Settings2,
        description: 'Account settings and preferences',
        subItems: [
          { title: 'Account Settings', url: '/ctn/settings/account', description: 'Personal information and security' },
          { title: 'Notifications', url: '/ctn/settings/notifications', description: 'Notification preferences' },
          { title: 'Privacy', url: '/ctn/settings/privacy', description: 'Privacy and data settings' }
        ]
      }
    ]
  }
];

export function NavMainAccordion({ onItemClick, userId }: NavMainAccordionProps) {
  const { userExperience, getTierLevel } = useTierAccess();
  const { state } = useSidebar();
  const [openCategories, setOpenCategories] = useState<string[]>(['tools']);

  // Check if user has access to a tier-protected item
  const hasAccess = (tierRequired?: string) => {
    if (!tierRequired) return true;
    if (!userExperience) return false;
    return getTierLevel(userExperience.tier) >= getTierLevel(tierRequired);
  };

  const mainMenuItems = getMainMenuItems(userId);
  const navCategories = getNavCategories(userId);

  const toggleCategory = (categoryId: string) => {
    setOpenCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const getTierBadgeColor = (tier?: string) => {
    switch (tier) {
      case 'tier1': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'tier2': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      case 'tier3': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default: return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    }
  };

  const getTierLabel = (tier?: string) => {
    switch (tier) {
      case 'tier1': return 'T1+';
      case 'tier2': return 'T2+';
      case 'tier3': return 'T3+';
      default: return 'Free';
    }
  };

  return (
    <TooltipProvider>
      <SidebarGroup>
      {/* Experience Badge - hidden when collapsed */}
      {userExperience && state === "expanded" && (
        <div className="px-2 mb-4">
          <ExperienceBadge />
        </div>
      )}

      {state === "expanded" && (
        <div className="px-2 py-2">
          <div className="flex items-center gap-2 text-xs font-medium text-muted-foreground uppercase tracking-wider">
            <Compass className="h-4 w-4" />
            <span>Navigation</span>
          </div>
        </div>
      )}
      <SidebarMenu>
        {/* Individual Menu Items */}
        {mainMenuItems.filter(item => hasAccess(item.tierRequired)).map((item) => (
          <SidebarMenuItem key={item.id}>
            {item.subItems ? (
              <Collapsible>
                <SidebarMenuButton asChild tooltip={item.description}>
                  <CollapsibleTrigger className="group/collapsible">
                    <item.icon className="h-4 w-4" />
                    <span>{item.title}</span>
                    <ChevronRight className="ml-auto h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                  </CollapsibleTrigger>
                </SidebarMenuButton>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.subItems.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.title}>
                        <SidebarMenuSubButton asChild>
                          <Link href={subItem.url} onClick={onItemClick}>
                            <span>{subItem.title}</span>
                          </Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </Collapsible>
            ) : (
              <SidebarMenuButton asChild tooltip={item.description}>
                <Link href={item.url} onClick={onItemClick}>
                  <item.icon className="h-4 w-4" />
                  <span>{item.title}</span>
                  {item.tierRequired && (
                    <Badge variant="outline" className="ml-auto text-xs bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200">
                      {getTierLabel(item.tierRequired)}
                    </Badge>
                  )}
                </Link>
              </SidebarMenuButton>
            )}
          </SidebarMenuItem>
        ))}

        {/* Dropdown Categories */}
        {navCategories.map((category) => {
          const isOpen = openCategories.includes(category.id);
          
          // Filter items based on access
          const accessibleItems = category.items.filter(item => hasAccess(item.tierRequired));
          
          // Show category icons as direct links when collapsed
          if (state === "collapsed") {
            // Determine the best route for each category
            const getBestRouteForCategory = (categoryId: string) => {
              switch (categoryId) {
                case 'discovery':
                  return '/ctn'; // Home is most popular
                case 'analytics':  
                  return '/ctn/analytics'; // Main analytics dashboard
                case 'activities':
                  return '/ctn/leaderboards'; // Most accessible activity
                case 'account':
                  return `/ctn/profile/${userId}`; // User's profile
                default:
                  return accessibleItems[0]?.url || '/ctn';
              }
            };

            const bestRoute = getBestRouteForCategory(category.id);

            return (
              <SidebarMenuItem key={category.id}>
                <Tooltip>
                    <TooltipTrigger asChild>
                      <SidebarMenuButton asChild>
                        <Link
                          href={bestRoute}
                          className="w-full justify-center hover:bg-accent/50 transition-colors p-2"
                          onClick={onItemClick}
                        >
                          <category.icon className="h-5 w-5" />
                        </Link>
                      </SidebarMenuButton>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="max-w-xs">
                      <div className="space-y-2">
                        <p className="font-medium">{category.title}</p>
                        <p className="text-xs text-muted-foreground">{category.description}</p>
                        <p className="text-xs text-blue-600">Click to go to {
                          category.id === 'discovery' ? 'Home' :
                          category.id === 'analytics' ? 'Analytics Dashboard' :
                          category.id === 'activities' ? 'Leaderboards' :
                          category.id === 'account' ? 'My Profile' : 'main page'
                        }</p>
                        <div className="pt-2 border-t">
                          <p className="text-xs font-medium mb-1">Other options ({accessibleItems.length}):</p>
                          {accessibleItems.slice(0, 3).map((item) => (
                            <div key={item.id} className="flex items-center gap-2 text-xs text-muted-foreground">
                              <item.icon className="h-3 w-3" />
                              <span>{item.title}</span>
                            </div>
                          ))}
                          {accessibleItems.length > 3 && (
                            <div className="text-xs text-muted-foreground">...and {accessibleItems.length - 3} more</div>
                          )}
                        </div>
                      </div>
                    </TooltipContent>
                  </Tooltip>
              </SidebarMenuItem>
            );
          }

          // Expanded state - full accordion
          return (
            <Collapsible
              key={category.id}
              open={isOpen}
              onOpenChange={() => toggleCategory(category.id)}
            >
              <SidebarMenuItem>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton 
                          className="w-full justify-between hover:bg-accent/50 transition-colors"
                        >
                          <div className="flex items-center gap-2">
                            <category.icon className="h-4 w-4" />
                            <span>{category.title}</span>
                          </div>
                          {isOpen ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                    </TooltipTrigger>
                    <TooltipContent side="right" className="max-w-xs">
                      <div className="space-y-1">
                        <p className="font-medium">{category.title}</p>
                        <p className="text-xs text-muted-foreground">{category.description}</p>
                        <p className="text-xs text-blue-600">{accessibleItems.length} items available</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {category.items.map((item) => {
                      const itemHasAccess = hasAccess(item.tierRequired);
                      
                      // Show tier-protected items but make them non-clickable if no access
                      if (item.tierRequired && !itemHasAccess) {
                        return (
                          <SidebarMenuSubItem key={item.id}>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <SidebarMenuSubButton 
                                    asChild
                                    className="opacity-50 cursor-not-allowed"
                                  >
                                    <div className="flex items-center justify-between w-full">
                                      <div className="flex items-center gap-2">
                                        <item.icon className="h-4 w-4" />
                                        <span className="text-xs">{item.title}</span>
                                      </div>
                                      <Badge 
                                        variant="outline" 
                                        className={`text-xs ${getTierBadgeColor(item.tierRequired)}`}
                                      >
                                        {getTierLabel(item.tierRequired)}
                                      </Badge>
                                    </div>
                                  </SidebarMenuSubButton>
                                </TooltipTrigger>
                                <TooltipContent side="right" className="max-w-xs">
                                  <div className="space-y-1">
                                    <p className="font-medium">{item.title}</p>
                                    <p className="text-xs text-muted-foreground">{item.description}</p>
                                    <p className="text-xs text-orange-600">Requires {getTierLabel(item.tierRequired)} subscription</p>
                                  </div>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </SidebarMenuSubItem>
                        );
                      }
                      
                      // Handle items with sub-items (like Settings)
                      if (item.subItems && item.subItems.length > 0) {
                        return (
                          <SidebarMenuSubItem key={item.id}>
                            <div className="space-y-1">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <SidebarMenuSubButton asChild>
                                      <Link
                                        href={item.url}
                                        className="flex items-center justify-between w-full hover:bg-accent/50 transition-colors"
                                        onClick={onItemClick}
                                      >
                                        <div className="flex items-center gap-2">
                                          <item.icon className="h-4 w-4" />
                                          <span className="text-xs font-medium">{item.title}</span>
                                        </div>

                                      </Link>
                                    </SidebarMenuSubButton>
                                  </TooltipTrigger>
                                  <TooltipContent side="right" className="max-w-xs">
                                    <div className="space-y-1">
                                      <p className="font-medium">{item.title}</p>
                                      <p className="text-xs text-muted-foreground">{item.description}</p>
                                      <div className="mt-2 pt-2 border-t">
                                        <p className="text-xs font-medium">Quick access:</p>
                                        {item.subItems.map(subItem => (
                                          <p key={subItem.title} className="text-xs text-muted-foreground">• {subItem.title}</p>
                                        ))}
                                      </div>
                                    </div>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>

                              {/* Sub-items */}
                              <div className="ml-4 space-y-1">
                                {item.subItems.map(subItem => (
                                  <TooltipProvider key={subItem.title}>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Link
                                          href={subItem.url}
                                          className="flex items-center gap-2 px-2 py-1 text-xs text-muted-foreground hover:text-foreground hover:bg-accent/30 rounded transition-colors"
                                          onClick={onItemClick}
                                        >
                                          <span>•</span>
                                          <span>{subItem.title}</span>
                                        </Link>
                                      </TooltipTrigger>
                                      <TooltipContent side="right">
                                        <p className="text-xs">{subItem.description}</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                ))}
                              </div>
                            </div>
                          </SidebarMenuSubItem>
                        );
                      }

                      return (
                        <SidebarMenuSubItem key={item.id}>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <SidebarMenuSubButton asChild>
                                  <TierProtectedMenuItem
                                    item={item}
                                    onItemClick={onItemClick}
                                    className="flex items-center justify-between w-full hover:bg-accent/50 transition-colors"
                                  >
                                    <div className="flex items-center gap-2">
                                      <item.icon className="h-4 w-4" />
                                      <span className="text-xs">{item.title}</span>
                                    </div>
                                    <div className="flex items-center gap-1">
                                      {item.showNotificationBadge && (
                                        <NotificationBadge userId={userId} />
                                      )}
                                      {item.tierRequired && (
                                        <Badge
                                          variant="outline"
                                          className={`text-xs ${getTierBadgeColor(item.tierRequired)}`}
                                        >
                                          {getTierLabel(item.tierRequired)}
                                        </Badge>
                                      )}
                                    </div>
                                  </TierProtectedMenuItem>
                                </SidebarMenuSubButton>
                              </TooltipTrigger>
                              <TooltipContent side="right" className="max-w-xs">
                                <div className="space-y-1">
                                  <p className="font-medium">{item.title}</p>
                                  <p className="text-xs text-muted-foreground">{item.description}</p>
                                  {item.tierRequired && (
                                    <p className="text-xs text-blue-600">Available with {getTierLabel(item.tierRequired)}</p>
                                  )}
                                  {item.subItems && (
                                    <div className="mt-2 pt-2 border-t">
                                      <p className="text-xs font-medium">Sub-items:</p>
                                      {item.subItems.map(subItem => (
                                        <p key={subItem.title} className="text-xs text-muted-foreground">• {subItem.title}</p>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </SidebarMenuSubItem>
                      );
                    })}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
    </TooltipProvider>
  );
}
