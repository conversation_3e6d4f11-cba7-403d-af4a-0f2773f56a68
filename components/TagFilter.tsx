// components/TagFilter.tsx
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useEffect, useState } from "react";
import { createClient } from "@/utils/supabase/client";

interface TagFilterProps {
  selectedTags: string[];
  onTagSelect: (tags: string[]) => void;
}

export function TagFilter({ selectedTags, onTagSelect }: TagFilterProps) {
  const [availableTags, setAvailableTags] = useState<{ name: string; count: number }[]>([]);
  const supabase = createClient();

  useEffect(() => {
    const fetchTags = async () => {
      const { data, error } = await (supabase as any)
        .from('tags')
        .select(`
          name,
          article_tags!inner (
            article_id
          )
        `)
        .order('name');

      if (error) {
        console.error('Error fetching tags:', error);
        return;
      }

      interface ArticleTag {
        article_id: string;
      }

      interface TagWithArticleTags {
        name: string;
        article_tags: ArticleTag[];
      }

      const formattedTags = data.map((tag: TagWithArticleTags) => ({
        name: tag.name,
        count: tag.article_tags.length
      }));

      setAvailableTags(formattedTags);
    };

    fetchTags();
  }, [supabase]);

  const toggleTag = (tagName: string) => {
    const newSelectedTags = selectedTags.includes(tagName)
      ? selectedTags.filter(t => t !== tagName)
      : [...selectedTags, tagName];
    onTagSelect(newSelectedTags);
  };

  return (
    <ScrollArea className="w-full whitespace-nowrap">
      <div className="flex gap-2 p-1">
        {availableTags.map((tag) => (
          <Badge
            key={tag.name}
            variant={selectedTags.includes(tag.name) ? "default" : "outline"}
            className="cursor-pointer hover:bg-primary/20"
            onClick={() => toggleTag(tag.name)}
          >
            {tag.name} ({tag.count})
          </Badge>
        ))}
      </div>
    </ScrollArea>
  );
}