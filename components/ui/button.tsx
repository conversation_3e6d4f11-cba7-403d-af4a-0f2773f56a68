import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-300 focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 backdrop-blur-xl backdrop-saturate-150 relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/20 before:to-transparent before:opacity-50 before:pointer-events-none",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-to-br from-blue-500/70 via-blue-600/50 to-purple-600/70 text-white border border-white/30 shadow-2xl shadow-blue-500/20 hover:shadow-blue-500/30 hover:from-blue-600/80 hover:to-purple-700/80 hover:scale-[1.02] active:scale-[0.98]",
        destructive:
          "bg-gradient-to-br from-red-500/70 via-red-600/50 to-red-700/70 text-white border border-white/30 shadow-2xl shadow-red-500/20 hover:shadow-red-500/30 hover:from-red-600/80 hover:to-red-700/80 hover:scale-[1.02] active:scale-[0.98]",
        outline:
          "border border-white/40 dark:border-white/30 bg-gradient-to-br from-white/30 via-white/20 to-white/10 dark:from-white/10 dark:via-white/5 dark:to-transparent shadow-2xl hover:from-white/40 dark:hover:from-white/15 hover:shadow-xl hover:border-white/50 dark:hover:border-white/40 hover:scale-[1.02] active:scale-[0.98]",
        secondary:
          "bg-gradient-to-br from-white/50 via-white/30 to-white/20 dark:from-white/15 dark:via-white/10 dark:to-white/5 text-secondary-foreground border border-white/40 dark:border-white/20 shadow-2xl hover:from-white/60 dark:hover:from-white/20 hover:shadow-xl hover:scale-[1.02] active:scale-[0.98]",
        ghost: "hover:bg-gradient-to-br hover:from-white/25 hover:to-white/10 dark:hover:from-white/10 dark:hover:to-white/5 backdrop-blur-md hover:shadow-lg transition-all duration-300 hover:scale-[1.02] active:scale-[0.98]",
        link: "text-primary underline-offset-4 hover:underline backdrop-blur-sm",
        "liquid-glass": "liquid-glass glass-shadow glass-shadow-hover",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-lg px-3 text-xs",
        lg: "h-10 rounded-lg px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
