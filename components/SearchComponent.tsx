"use client";

import { useState, useCallback, useEffect } from "react";
import { createClient } from "@/utils/supabase/client";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArticleCard } from "./ArticleCard";
import { LoadingSkeleton } from "./LoadingSkeleton";
import { Search, Filter, Calendar as CalendarIcon, X } from "lucide-react";
import { Article, UserProfile } from "@/types";
import { toast } from "@/hooks/use-toast";

interface SearchResult extends Article {
  relevance_score: number;
}

interface SearchSuggestion {
  suggestion: string;
  type: string;
  count: number;
}

interface SearchComponentProps {
  userProfile: UserProfile;
}

export default function SearchComponent({ userProfile }: SearchComponentProps) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [author, setAuthor] = useState("");
  const [sortBy, setSortBy] = useState("relevance");
  const [dateFrom, setDateFrom] = useState<Date | undefined>();
  const [dateTo, setDateTo] = useState<Date | undefined>();
  const [showFilters, setShowFilters] = useState(false);
  const [offset, setOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const supabase = createClient();

  // Get search suggestions using direct queries to avoid RPC issues
  const getSuggestions = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim() || searchQuery.length < 2) {
      setSuggestions([]);
      return;
    }

    try {
      const allSuggestions: SearchSuggestion[] = [];

      // Get hashtag suggestions with error handling
      try {
        const { data: hashtagData, error: hashtagError } = await supabase
          .rpc('search_hashtags', {
            p_query: searchQuery,
            p_limit: 3
          });

        if (hashtagError) {
          console.warn("Hashtag search RPC failed:", hashtagError);
        } else if (hashtagData) {
          hashtagData.forEach((tag: any) => {
            allSuggestions.push({
              suggestion: `#${tag.name}`,
              type: 'hashtag',
              count: tag.usage_count || 0
            });
          });
        }
      } catch (hashtagRpcError) {
        console.warn("Hashtag RPC function not available or failed:", hashtagRpcError);
        
        // Fallback: direct hashtag table query
        try {
          const { data: fallbackHashtags } = await supabase
            .from('hashtags')
            .select('name, usage_count')
            .ilike('name', `%${searchQuery}%`)
            .order('usage_count', { ascending: false })
            .limit(3);

          if (fallbackHashtags) {
            fallbackHashtags.forEach((tag: any) => {
              allSuggestions.push({
                suggestion: `#${tag.name}`,
                type: 'hashtag',
                count: tag.usage_count || 0
              });
            });
          }
        } catch (fallbackError) {
          console.warn("Hashtag fallback query failed:", fallbackError);
        }
      }

      // Get user suggestions using direct table query
      try {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('user_id, username, avatar_url, followers_count')
          .ilike('username', `%${searchQuery}%`)
          .not('username', 'is', null)
          .order('followers_count', { ascending: false, nullsFirst: false })
          .limit(2);

        if (userError) {
          console.warn("User search failed:", userError);
        } else if (userData) {
          userData.forEach((user: any) => {
            allSuggestions.push({
              suggestion: `@${user.username}`,
              type: 'user',
              count: user.followers_count || 0
            });
          });
        }
      } catch (userQueryError) {
        console.warn("User search query failed:", userQueryError);
      }

      setSuggestions(allSuggestions);
    } catch (error) {
      console.error("Error getting suggestions:", error);
      setSuggestions([]);
    }
  }, [supabase]);

  // Debounce suggestions
  useEffect(() => {
    const timer = setTimeout(() => {
      getSuggestions(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query, getSuggestions]);

  const performSearch = useCallback(async (resetResults = true) => {
    if (!query.trim()) {
      setResults([]);
      return;
    }

    setIsSearching(true);
    try {
      // First try the simple search_articles RPC function
      let searchResults: any[] = [];
      
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('search_articles', {
            search_query: query
          });

        if (rpcError) {
          console.warn("RPC search failed, falling back to direct query:", rpcError);
          throw rpcError;
        }

        // The RPC returns minimal data, so we need to get full article details
        if (rpcData && rpcData.length > 0) {
          const articleIds = rpcData.map((item: any) => item.id);
          
          const { data: fullArticles, error: articlesError } = await supabase
            .from('articles')
            .select(`
              *,
              profiles:user_id (
                username,
                avatar_url,
                user_id
              ),
              article_hashtags (
                hashtags (
                  name
                )
              )
            `)
            .in('id', articleIds)
            .order('created_at', { ascending: false });

          if (articlesError) throw articlesError;
          searchResults = fullArticles || [];
        }
      } catch (rpcError) {
        // Fallback to direct table search if RPC fails
        console.log("Using fallback search method");
        
        let query_builder = supabase
          .from('articles')
          .select(`
            *,
            profiles:user_id (
              username,
              avatar_url,
              user_id
            ),
            article_hashtags (
              hashtags (
                name
              )
            )
          `)
          .or(`title.ilike.%${query}%,content.ilike.%${query}%,description.ilike.%${query}%`)
          .eq('status', 'published')
          .order('created_at', { ascending: false });

        // Apply filters if provided
        if (selectedTags.length > 0) {
          // This is a simplified filter - in a real app you'd need a more complex join
          query_builder = query_builder.containedBy('tags', selectedTags);
        }

        if (author) {
          query_builder = query_builder.ilike('profiles.username', `%${author}%`);
        }

        if (dateFrom) {
          query_builder = query_builder.gte('created_at', dateFrom.toISOString());
        }

        if (dateTo) {
          query_builder = query_builder.lte('created_at', dateTo.toISOString());
        }

        const { data: fallbackData, error: fallbackError } = await query_builder
          .range(resetResults ? 0 : offset, (resetResults ? 0 : offset) + 19);

        if (fallbackError) throw fallbackError;
        searchResults = fallbackData || [];
      }

      // Add relevance score for compatibility
      const resultsWithScore = searchResults.map((article: any) => ({
        ...article,
        relevance_score: 1.0 // Default relevance score
      }));
      
      if (resetResults) {
        setResults(resultsWithScore);
        setOffset(20);
      } else {
        setResults(prev => [...prev, ...resultsWithScore]);
        setOffset(prev => prev + 20);
      }

      setHasMore(searchResults.length === 20);
    } catch (error) {
      console.error("Error searching articles:", error);
      toast({
        title: "Search Error",
        description: "Unable to search articles. Please try again.",
        variant: "destructive"
      });
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [query, userProfile.user_id, selectedTags, author, dateFrom, dateTo, sortBy, offset, supabase]);

  const handleSearch = () => {
    setOffset(0);
    performSearch(true);
    setSuggestions([]);
  };

  const handleLoadMore = () => {
    performSearch(false);
  };

  const addTag = (tag: string) => {
    if (!selectedTags.includes(tag)) {
      setSelectedTags([...selectedTags, tag]);
    }
  };

  const removeTag = (tag: string) => {
    setSelectedTags(selectedTags.filter(t => t !== tag));
  };

  const clearFilters = () => {
    setSelectedTags([]);
    setAuthor("");
    setDateFrom(undefined);
    setDateTo(undefined);
    setSortBy("relevance");
  };

  const applySuggestion = (suggestion: SearchSuggestion) => {
    if (suggestion.type === "tag") {
      addTag(suggestion.suggestion);
    } else if (suggestion.type === "author") {
      setAuthor(suggestion.suggestion);
    }
    setSuggestions([]);
  };

  const handleLike = useCallback(
    async (articleId: number, isLiked: boolean, userId: string) => {
      try {
        const { data, error } = await (supabase as any).rpc("toggle_article_interaction", {
          p_user_id: userId,
          p_article_id: articleId,
          p_interaction_type: "like",
        });

        if (error) throw error;

        setResults(prevResults =>
          prevResults.map(article =>
            article.id === articleId
              ? {
                  ...article,
                  is_liked: data.is_liked,
                  likes: data.likes_count,
                }
              : article
          )
        );
      } catch (error) {
        console.error("Error handling like:", error);
        toast({
          title: "Error",
          description: "Failed to update like status",
          variant: "destructive",
        });
      }
    },
    [supabase]
  );

  const handleBookmark = useCallback(
    async (articleId: number) => {
      try {
        const { data, error } = await (supabase as any).rpc("toggle_article_interaction", {
          p_user_id: userProfile.user_id,
          p_article_id: articleId,
          p_interaction_type: "bookmark",
        });

        if (error) throw error;

        setResults(prevResults =>
          prevResults.map(article =>
            article.id === articleId
              ? {
                  ...article,
                  is_bookmarked: data.is_bookmarked,
                }
              : article
          )
        );

        toast({
          title: data.is_bookmarked ? "Bookmarked" : "Removed from bookmarks",
          description: data.is_bookmarked
            ? "Article added to your bookmarks"
            : "Article removed from your bookmarks",
        });
      } catch (error) {
        console.error("Error bookmarking article:", error);
        toast({
          title: "Error",
          description: "Failed to update bookmark",
          variant: "destructive",
        });
      }
    },
    [supabase, userProfile.user_id]
  );

  const handleShare = useCallback(async (article: Article) => {
    try {
      if (navigator.share) {
        await navigator.share({
          title: article.title || "Shared Article",
          text: article.content || "Check out this article",
          url: `${window.location.origin}/ctn/articles/${article.id}`,
        });
      } else {
        await navigator.clipboard.writeText(
          `${window.location.origin}/ctn/articles/${article.id}`
        );
        toast({
          title: "Success",
          description: "Link copied to clipboard",
        });
      }
    } catch (error) {
      console.error("Error sharing:", error);
      toast({
        title: "Error",
        description: "Failed to share article",
        variant: "destructive",
      });
    }
  }, []);

  return (
    <div className="container max-w-6xl mx-auto py-8 space-y-6">
      {/* Search Header */}
      <div className="text-center space-y-2">
        <h1 className="text-4xl font-bold tracking-tight">🔍 Search Articles</h1>
        <p className="text-muted-foreground">
          Find articles, authors, and topics
        </p>
      </div>

      {/* Search Input */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search for articles, topics, or authors..."
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  onKeyDown={(e) => e.key === "Enter" && handleSearch()}
                  className="pl-10"
                />
                
                {/* Search Suggestions */}
                {suggestions.length > 0 && (
                  <Card className="absolute top-full left-0 right-0 z-10 mt-1">
                    <CardContent className="p-2">
                      {suggestions.map((suggestion, index) => (
                        <div
                          key={index}
                          className="flex items-center justify-between p-2 hover:bg-muted rounded cursor-pointer"
                          onClick={() => applySuggestion(suggestion)}
                        >
                          <span className="flex items-center gap-2">
                            <Badge variant={suggestion.type === "tag" ? "secondary" : "outline"}>
                              {suggestion.type}
                            </Badge>
                            {suggestion.suggestion}
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {suggestion.count}
                          </span>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                )}
              </div>
              
              <Button onClick={handleSearch} disabled={isSearching}>
                {isSearching ? "Searching..." : "Search"}
              </Button>
              
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Advanced Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Advanced Filters
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Clear All
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Tags */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Tags</label>
              <div className="flex flex-wrap gap-2">
                {selectedTags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    #{tag}
                    <button onClick={() => removeTag(tag)} className="hover:text-destructive">
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>

            {/* Author */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Author</label>
              <Input
                placeholder="Filter by author..."
                value={author}
                onChange={(e) => setAuthor(e.target.value)}
              />
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">From Date</label>
                <Input
                  type="date"
                  value={dateFrom ? dateFrom.toISOString().split('T')[0] : ''}
                  onChange={(e) => setDateFrom(e.target.value ? new Date(e.target.value) : undefined)}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">To Date</label>
                <Input
                  type="date"
                  value={dateTo ? dateTo.toISOString().split('T')[0] : ''}
                  onChange={(e) => setDateTo(e.target.value ? new Date(e.target.value) : undefined)}
                />
              </div>
            </div>

            {/* Sort By */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Sort By</label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="relevance">Relevance</SelectItem>
                  <SelectItem value="likes">Most Liked</SelectItem>
                  <SelectItem value="views">Most Viewed</SelectItem>
                  <SelectItem value="date">Newest First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results */}
      <div className="space-y-4">
        {isSearching && offset === 0 ? (
          <LoadingSkeleton />
        ) : results.length > 0 ? (
          <>
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold">
                Search Results ({results.length})
              </h2>
            </div>
            
            {results.map((article) => (
              <ArticleCard
                key={article.id}
                article={article}
                userProfile={userProfile}
                onLike={handleLike}
                onBookmark={handleBookmark}
                onShare={handleShare}
                onTagClick={addTag}
              />
            ))}

            {hasMore && (
              <div className="flex justify-center py-4">
                <Button 
                  onClick={handleLoadMore} 
                  variant="outline"
                  disabled={isSearching}
                >
                  {isSearching ? "Loading..." : "Load More"}
                </Button>
              </div>
            )}
          </>
        ) : query && !isSearching ? (
          <div className="text-center py-12">
            <h3 className="text-lg font-semibold mb-2">No results found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search terms or filters.
            </p>
          </div>
        ) : null}
      </div>
    </div>
  );
}
