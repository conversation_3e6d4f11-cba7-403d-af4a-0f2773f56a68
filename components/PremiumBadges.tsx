'use client';

import { Badge } from '@/components/ui/badge';
import { Crown, Star, Sparkles, Shield, Zap, Award } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface PremiumBadgeProps {
  type: 'premium' | 'pro' | 'enterprise' | 'verified' | 'early_access' | 'creator';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

const badgeConfig = {
  premium: {
    icon: Crown,
    label: 'Premium',
    color: 'bg-gradient-to-r from-yellow-400 to-orange-500',
    textColor: 'text-white',
    description: 'Premium member'
  },
  pro: {
    icon: Star,
    label: 'Pro',
    color: 'bg-gradient-to-r from-purple-500 to-pink-500',
    textColor: 'text-white',
    description: 'Pro member'
  },
  enterprise: {
    icon: Shield,
    label: 'Enterprise',
    color: 'bg-gradient-to-r from-gray-700 to-gray-900',
    textColor: 'text-white',
    description: 'Enterprise member'
  },
  verified: {
    icon: Award,
    label: 'Verified',
    color: 'bg-gradient-to-r from-blue-500 to-cyan-500',
    textColor: 'text-white',
    description: 'Verified creator'
  },
  early_access: {
    icon: Zap,
    label: 'Early Access',
    color: 'bg-gradient-to-r from-green-500 to-emerald-500',
    textColor: 'text-white',
    description: 'Early access member'
  },
  creator: {
    icon: Sparkles,
    label: 'Creator',
    color: 'bg-gradient-to-r from-pink-500 to-rose-500',
    textColor: 'text-white',
    description: 'Content creator'
  }
};

const sizeConfig = {
  sm: {
    iconSize: 12,
    badgeClass: 'h-5 px-2 text-xs',
    textClass: 'text-xs'
  },
  md: {
    iconSize: 14,
    badgeClass: 'h-6 px-3 text-sm',
    textClass: 'text-sm'
  },
  lg: {
    iconSize: 16,
    badgeClass: 'h-7 px-4 text-base',
    textClass: 'text-base'
  }
};

export function PremiumBadge({ 
  type, 
  size = 'md', 
  showLabel = true, 
  className 
}: PremiumBadgeProps) {
  const config = badgeConfig[type];
  const sizeConf = sizeConfig[size];
  const Icon = config.icon;

  return (
    <Badge
      className={cn(
        config.color,
        config.textColor,
        sizeConf.badgeClass,
        'flex items-center gap-1.5 font-medium border-0 shadow-sm',
        className
      )}
      title={config.description}
    >
      <Icon size={sizeConf.iconSize} />
      {showLabel && (
        <span className={sizeConf.textClass}>{config.label}</span>
      )}
    </Badge>
  );
}

// Component for displaying multiple badges
export interface PremiumBadgeGroupProps {
  badges: PremiumBadgeProps['type'][];
  size?: PremiumBadgeProps['size'];
  showLabels?: boolean;
  className?: string;
}

export function PremiumBadgeGroup({ 
  badges, 
  size = 'sm', 
  showLabels = false, 
  className 
}: PremiumBadgeGroupProps) {
  if (!badges.length) return null;

  return (
    <div className={cn('flex items-center gap-1', className)}>
      {badges.map((badge, index) => (
        <PremiumBadge
          key={`${badge}-${index}`}
          type={badge}
          size={size}
          showLabel={showLabels}
        />
      ))}
    </div>
  );
}

// Profile premium indicator
export interface ProfilePremiumIndicatorProps {
  subscription?: {
    plan_name: string;
    is_premium: boolean;
    status: string;
  };
  features?: string[];
  className?: string;
}

export function ProfilePremiumIndicator({ 
  subscription, 
  features = [], 
  className 
}: ProfilePremiumIndicatorProps) {
  if (!subscription?.is_premium) return null;

  const getBadgeType = (planName: string): PremiumBadgeProps['type'] => {
    switch (planName) {
      case 'tier2': return 'premium';
      case 'tier3': return 'pro';
      case 'tier4': return 'enterprise';
      default: return 'premium';
    }
  };

  const badges: PremiumBadgeProps['type'][] = [];
  
  // Add subscription badge
  badges.push(getBadgeType(subscription.plan_name));
  
  // Add feature-specific badges
  if (features.includes('verified_checkmark')) {
    badges.push('verified');
  }
  if (features.includes('early_features')) {
    badges.push('early_access');
  }

  return (
    <PremiumBadgeGroup 
      badges={badges}
      size="sm"
      showLabels={false}
      className={className}
    />
  );
}

// Article premium content indicator
export interface ArticlePremiumIndicatorProps {
  isPremiumContent?: boolean;
  authorSubscription?: {
    plan_name: string;
    is_premium: boolean;
  };
  className?: string;
}

export function ArticlePremiumIndicator({ 
  isPremiumContent, 
  authorSubscription, 
  className 
}: ArticlePremiumIndicatorProps) {
  if (!isPremiumContent && !authorSubscription?.is_premium) return null;

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {isPremiumContent && (
        <Badge 
          variant="secondary" 
          className="bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-800 border-orange-200"
        >
          <Crown size={12} className="mr-1" />
          Premium Content
        </Badge>
      )}
      {authorSubscription?.is_premium && (
        <PremiumBadge 
          type={authorSubscription.plan_name as PremiumBadgeProps['type']} 
          size="sm" 
          showLabel={false}
        />
      )}
    </div>
  );
}

// Subscription status indicator
export interface SubscriptionStatusIndicatorProps {
  status: string;
  trialEndDate?: string;
  currentPeriodEnd?: string;
  cancelAtPeriodEnd?: boolean;
  className?: string;
}

export function SubscriptionStatusIndicator({ 
  status, 
  trialEndDate, 
  currentPeriodEnd, 
  cancelAtPeriodEnd,
  className 
}: SubscriptionStatusIndicatorProps) {
  const getStatusConfig = () => {
    switch (status) {
      case 'trial':
        return {
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          label: 'Trial',
          icon: Zap,
          description: trialEndDate ? `Trial ends ${new Date(trialEndDate).toLocaleDateString()}` : 'Trial active'
        };
      case 'active':
        return {
          color: cancelAtPeriodEnd 
            ? 'bg-orange-100 text-orange-800 border-orange-200'
            : 'bg-green-100 text-green-800 border-green-200',
          label: cancelAtPeriodEnd ? 'Ending Soon' : 'Active',
          icon: cancelAtPeriodEnd ? Shield : Star,
          description: currentPeriodEnd 
            ? `${cancelAtPeriodEnd ? 'Ends' : 'Renews'} ${new Date(currentPeriodEnd).toLocaleDateString()}`
            : 'Active subscription'
        };
      case 'cancelled':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          label: 'Cancelled',
          icon: Shield,
          description: 'Subscription cancelled'
        };
      case 'expired':
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          label: 'Expired',
          icon: Shield,
          description: 'Subscription expired'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          label: 'Tier 1',
          icon: Shield,
          description: 'Tier 1 account'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  return (
    <Badge 
      className={cn(config.color, 'flex items-center gap-1', className)}
      title={config.description}
    >
      <Icon size={12} />
      {config.label}
    </Badge>
  );
}

// Premium feature locked indicator
export interface FeatureLockedIndicatorProps {
  featureName: string;
  requiredPlan?: string;
  children: React.ReactNode;
  onUpgradeClick?: () => void;
  className?: string;
}

export function FeatureLockedIndicator({ 
  featureName, 
  requiredPlan = 'Premium',
  children,
  onUpgradeClick,
  className 
}: FeatureLockedIndicatorProps) {
  return (
    <div className={cn('relative', className)}>
      <div className="relative opacity-50 pointer-events-none">
        {children}
      </div>
      <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
        <div className="text-center p-4">
          <Crown className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
          <p className="text-sm font-medium text-gray-900 mb-1">
            {featureName} Locked
          </p>
          <p className="text-xs text-gray-600 mb-3">
            Requires {requiredPlan} subscription
          </p>
          {onUpgradeClick && (
            <button
              onClick={onUpgradeClick}
              className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs px-3 py-1.5 rounded-full font-medium hover:from-yellow-500 hover:to-orange-600 transition-all"
            >
              Upgrade Now
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
