'use client';

import { useChainMetrics, use<PERSON>hainComparison, useDexScreenerData, useTabLoadingState } from '@/hooks/useOnChainData';
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, Activity, BarChart3, TrendingUp, Users, Zap, DollarSign, Database, Clock, Shield, Code } from 'lucide-react';
import { MetricChartsGrid } from '@/components/InteractiveMetricChart';
import { OnChainModels } from '@/components/OnChainModels';
import { InflationCharts } from '@/components/InflationCharts';
import { WalletBalanceAnalysis } from '@/components/WalletBalanceAnalysis';
import { ManualComparisonTool } from '@/components/ManualComparisonTool';
import { AutomatedComparisonTool } from '@/components/AutomatedComparisonTool';
import { ModernContainer, ModernSection, ModernGrid, ModernButton, ModernBadge, ModernMetric } from '@/components/modern-layout';
import { CachedTierProtection } from '@/components/CachedTierProtection';

// Loading skeleton component
function MetricsSkeleton() {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-full"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

// Chain selector component
function ChainSelector({ selectedChain, onChainChange }: { 
  selectedChain: string; 
  onChainChange: (chain: string) => void; 
}) {
  const chains = [
    { id: 'bitcoin', name: 'Bitcoin', symbol: 'BTC', logo: '₿' },
    { id: 'ethereum', name: 'Ethereum', symbol: 'ETH', logo: 'Ξ' },
    { id: 'solana', name: 'Solana', symbol: 'SOL', logo: '◎' },
    { id: 'cardano', name: 'Cardano', symbol: 'ADA', logo: '₳' },
    { id: 'polkadot', name: 'Polkadot', symbol: 'DOT', logo: '●' },
  ];

  return (
    <div className="flex flex-wrap gap-2 mb-6">
      {chains.map((chain) => (
        <ModernButton
          key={chain.id}
          variant={selectedChain === chain.id ? 'default' : 'glass'}
          size="sm"
          onClick={() => onChainChange(chain.id)}
          className="flex items-center gap-2"
        >
          <span className="text-lg">{chain.logo}</span>
          {chain.name}
        </ModernButton>
      ))}
    </div>
  );
}

// Enhanced metrics display
function EnhancedMetricsDisplay({ chainId }: { chainId: string }) {
  const { metrics, derivedMetrics, marketData, isLoading, refresh } = useChainMetrics(chainId);
  const { isTabLoading, showSkeletons, showStaleIndicator } = useTabLoadingState();

  if (showSkeletons) {
    return <MetricsSkeleton />;
  }

  if (!metrics) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            No data available for {chainId}. <button onClick={refresh} className="text-primary underline">Try refreshing</button>
          </p>
        </CardContent>
      </Card>
    );
  }

  const formatNumber = (num: number) => {
    if (num >= 1e9) return `${(num / 1e9).toFixed(2)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(2)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(2)}K`;
    return num.toFixed(2);
  };

  const formatCurrency = (num: number) => {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD', 
      notation: 'compact' 
    }).format(num);
  };

  return (
    <div className="space-y-6">
      {/* Refresh indicator */}
      {showStaleIndicator && (
        <div className="flex items-center gap-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <RefreshCw className="h-4 w-4 text-yellow-600" />
          <span className="text-sm text-yellow-800">Data is being refreshed in background...</span>
        </div>
      )}

      {/* Key Metrics Grid */}
      <ModernGrid grid-cols={3}>
        <ModernMetric
          label="Daily Active Addresses"
          value={formatNumber(metrics.dailyActiveAddresses || 0)}
          icon={<Users className="h-4 w-4" />}
          change={metrics.dailyActiveAddresses > 100000 ? 'up' : 'down'}
        />
        <ModernMetric
          label="Daily Transactions"
          value={formatNumber(metrics.dailyTransactions || 0)}
          icon={<Activity className="h-4 w-4" />}
          change={metrics.dailyTransactions > 500000 ? 'up' : 'down'}
        />
        <ModernMetric
          label="Market Cap"
          value={formatCurrency(metrics.marketCap || marketData.marketCap || 0)}
          icon={<DollarSign className="h-4 w-4" />}
          change="up"
        />
        <ModernMetric
          label="Price"
          value={formatCurrency(metrics.price || marketData.price || 0)}
          icon={<TrendingUp className="h-4 w-4" />}
          change="up"
        />
        <ModernMetric
          label="Total Value Locked"
          value={formatCurrency(metrics.tvl || 0)}
          icon={<Database className="h-4 w-4" />}
          change={metrics.tvl > 1000000000 ? 'up' : 'neutral'}
        />
        <ModernMetric
          label="Validators"
          value={formatNumber(metrics.validators || 0)}
          icon={<Shield className="h-4 w-4" />}
          change="up"
        />
      </ModernGrid>

      {/* Technical Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Technical Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <div className="text-sm text-muted-foreground">Max TPS</div>
              <div className="text-2xl font-bold">{formatNumber(metrics.maxTps || 0)}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Real-time TPS</div>
              <div className="text-2xl font-bold">{formatNumber(metrics.realTimeTps || 0)}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Avg Fee</div>
              <div className="text-2xl font-bold">${(metrics.avgFee || 0).toFixed(4)}</div>
            </div>
            <div>
              <div className="text-sm text-muted-foreground">Finality Time</div>
              <div className="text-2xl font-bold">{(metrics.finalityTime || 0).toFixed(1)}s</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Developer Activity */}
      {metrics.activeDevelopers && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5" />
              Developer Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Active Developers (3 months)</div>
                <div className="text-3xl font-bold">{formatNumber(metrics.activeDevelopers)}</div>
              </div>
              <ModernBadge variant={metrics.activeDevelopers > 500 ? 'success' : 'default'}>
                {metrics.activeDevelopers > 1000 ? 'Very Active' : 
                 metrics.activeDevelopers > 500 ? 'Active' : 'Moderate'}
              </ModernBadge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Main cached content component
export function CachedOnChainContent() {
  const [selectedChain, setSelectedChain] = useState('bitcoin');
  const [activeTab, setActiveTab] = useState('charts');
  
  // Use cached data for comparison and DexScreener
  const { results: comparisonResults, insights } = useChainComparison();
  const { tokenProfiles, trendingPairs } = useDexScreenerData();
  // Get data for the selected chain
  const { metrics, isLoading, refresh } = useChainMetrics(selectedChain);

  return (
    <ModernContainer>
      <ModernSection>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">On-Chain Analytics</h1>
            <p className="text-muted-foreground mt-2">
              Real-time blockchain metrics and analysis with intelligent caching
            </p>
          </div>
          <ModernButton
            variant="glass"
            size="sm"
            onClick={refresh}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh Data
          </ModernButton>
        </div>

        <ChainSelector selectedChain={selectedChain} onChainChange={setSelectedChain} />

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="charts">Charts</TabsTrigger>
            <TabsTrigger value="models">Models</TabsTrigger>
            <TabsTrigger value="inflation">Inflation</TabsTrigger>
            <TabsTrigger value="wallets">Wallets</TabsTrigger>
            <TabsTrigger value="compare">Compare</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
            <TabsTrigger value="auto">Auto</TabsTrigger>
          </TabsList>

          <TabsContent value="charts" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain?tab=charts" requiredTier="tier1">
              <EnhancedMetricsDisplay chainId={selectedChain} />
              {metrics && (
                <MetricChartsGrid 
                  metrics={[{
                    id: 'chain-metrics',
                    title: `${selectedChain} Metrics`,
                    description: 'Real-time blockchain metrics',
                    data: [],
                    color: '#3b82f6',
                    icon: <></>,
                  }]}
                />
              )}
            </CachedTierProtection>
          </TabsContent>

          <TabsContent value="models" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain?tab=models" requiredTier="tier1">
              {metrics && (
                <OnChainModels 
                  selectedChain={{
                    name: selectedChain,
                    symbol: selectedChain.toUpperCase(),
                    price: metrics.price || 0,
                    marketCap: metrics.marketCap || 0,
                    circulatingSupply: metrics.circulatingSupply || 0,
                    timestamp: Date.now()
                  }}
                  onRefresh={refresh}
                />
              )}
            </CachedTierProtection>
          </TabsContent>

          <TabsContent value="inflation" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain?tab=inflation" requiredTier="tier1">
              {metrics && (
                <InflationCharts 
                  selectedChain={{
                    name: selectedChain,
                    symbol: selectedChain.toUpperCase(),
                    inflation: { mom: 0, yoy: 0 },
                    circulatingSupply: metrics.circulatingSupply || 0,
                    price: metrics.price || 0
                  }}
                  onRefresh={refresh}
                />
              )}
            </CachedTierProtection>
          </TabsContent>

          <TabsContent value="wallets" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain?tab=wallets" requiredTier="tier1">
              {metrics && (
                <WalletBalanceAnalysis 
                  selectedChain={{
                    name: selectedChain,
                    symbol: selectedChain.toUpperCase(),
                    price: metrics.price || 0,
                    circulatingSupply: metrics.circulatingSupply || 0
                  }}
                  onRefresh={refresh}
                />
              )}
            </CachedTierProtection>
          </TabsContent>

          <TabsContent value="compare" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain?tab=compare" requiredTier="tier2">
              <ManualComparisonTool 
                onRefresh={refresh}
              />
            </CachedTierProtection>
          </TabsContent>

          <TabsContent value="metrics" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain?tab=metrics" requiredTier="tier1">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>DexScreener Data</CardTitle>
                    <CardDescription>
                      Latest DEX data from cached results ({tokenProfiles.length} tokens)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {tokenProfiles.length > 0 ? (
                      <div className="space-y-2">
                        {tokenProfiles.slice(0, 5).map((token: any, idx: number) => (
                          <div key={idx} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                            <span className="font-medium">{token.name || `Token ${idx + 1}`}</span>
                            <ModernBadge variant="default">Cached</ModernBadge>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No DexScreener data available</p>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Comparison Insights</CardTitle>
                    <CardDescription>
                      Cached analysis results ({insights.length} insights)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {insights.length > 0 ? (
                      <div className="space-y-2">
                        {insights.slice(0, 3).map((insight: string, idx: number) => (
                          <div key={idx} className="p-2 bg-blue-50 rounded text-sm">
                            {insight}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-muted-foreground">No insights available</p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </CachedTierProtection>
          </TabsContent>

          <TabsContent value="auto" className="space-y-6">
            <CachedTierProtection pagePath="/ctn/tools/onchain?tab=auto" requiredTier="tier3">
              <AutomatedComparisonTool 
                onRefresh={refresh}
              />
            </CachedTierProtection>
          </TabsContent>
        </Tabs>
      </ModernSection>
    </ModernContainer>
  );
}