"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { 
  Bell, 
  Heart, 
  MessageSquare, 
  UserPlus, 
  FileText, 
  Settings,
  Check,
  CheckCheck,
  Trash2
} from "lucide-react";
import { UserProfile } from "@/types";
import { toast } from "@/hooks/use-toast";
import Link from "next/link";

// Simple date formatting utility
const formatTimeAgo = (date: string) => {
  const now = new Date();
  const past = new Date(date);
  const diffMs = now.getTime() - past.getTime();
  const diffMins = Math.floor(diffMs / 60000);
  const diffHours = Math.floor(diffMs / 3600000);
  const diffDays = Math.floor(diffMs / 86400000);

  if (diffMins < 1) return "just now";
  if (diffMins < 60) return `${diffMins}m ago`;
  if (diffHours < 24) return `${diffHours}h ago`;
  if (diffDays < 7) return `${diffDays}d ago`;
  return past.toLocaleDateString();
};

interface Notification {
  id: string;
  type: "follow" | "like" | "comment" | "mention" | "article_published";
  title: string;
  message: string;
  data: any;
  read: boolean;
  created_at: string;
  priority?: string;
}

interface NotificationSettings {
  email_notifications: boolean;
  push_notifications: boolean;
  follow_notifications: boolean;
  like_notifications: boolean;
  comment_notifications: boolean;
  mention_notifications: boolean;
}

interface NotificationsComponentProps {
  userProfile: UserProfile;
}

const notificationIcons = {
  follow: UserPlus,
  like: Heart,
  comment: MessageSquare,
  mention: MessageSquare,
  article_published: FileText,
};

const notificationColors = {
  follow: "text-blue-500",
  like: "text-red-500",
  comment: "text-green-500",
  mention: "text-yellow-500",
  article_published: "text-purple-500",
};

export default function NotificationsComponent({ userProfile }: NotificationsComponentProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings>({
    email_notifications: true,
    push_notifications: true,
    follow_notifications: true,
    like_notifications: true,
    comment_notifications: true,
    mention_notifications: true,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"all" | "unread" | "settings">("all");
  const [isUpdatingSettings, setIsUpdatingSettings] = useState(false);

  const supabase = createClient();

  const loadNotifications = useCallback(async () => {
    try {
      // Use direct table query with the correct structure
      let query = supabase
        .from("notifications")
        .select(`
          id,
          type,
          title,
          message,
          data,
          read_at,
          created_at,
          priority
        `)
        .eq("user_id", userProfile.user_id)
        .order("created_at", { ascending: false })
        .limit(50);

      // Filter for unread only if that tab is active
      if (activeTab === "unread") {
        query = query.is("read_at", null);
      }

      const { data, error } = await query;

      if (error) throw error;
      
      // Transform the data to match the expected format
      const transformedData = (data || []).map((notification: any) => ({
        id: notification.id,
        type: notification.type,
        title: notification.title,
        message: notification.message,
        data: notification.data,
        read: !!notification.read_at,
        created_at: notification.created_at,
        priority: notification.priority
      }));
      
      setNotifications(transformedData);
    } catch (error) {
      console.debug("Error loading notifications:", error);
      // Set empty notifications instead of showing error
      setNotifications([]);
    } finally {
      setIsLoading(false);
    }
  }, [supabase, userProfile.user_id, activeTab]);

  const loadSettings = useCallback(async () => {
    try {
      // Since notification_settings table doesn't exist, use default settings
      const defaultSettings = {
        email_notifications: true,
        push_notifications: true,
        follow_notifications: true,
        like_notifications: true,
        comment_notifications: true,
        mention_notifications: true
      };
      
      setSettings(defaultSettings);
    } catch (error) {
      console.debug("Error loading notification settings:", error);
      // Set default settings on error
      setSettings({
        email_notifications: true,
        push_notifications: true,
        follow_notifications: true,
        like_notifications: true,
        comment_notifications: true,
        mention_notifications: true
      });
    }
  }, []);

  useEffect(() => {
    loadNotifications();
    loadSettings();
  }, [loadNotifications, loadSettings]);

  const markAsRead = async (notificationIds: string[]) => {
    try {
      // Use direct table update instead of missing RPC function
      const { error } = await supabase
        .from("notifications")
        .update({ read_at: new Date().toISOString() })
        .eq("user_id", userProfile.user_id)
        .in("id", notificationIds);

      if (error) throw error;

      setNotifications(prev =>
        prev.map(notification =>
          notificationIds.includes(notification.id)
            ? { ...notification, read: true }
            : notification
        )
      );

      toast({
        title: "Success",
        description: `Marked ${notificationIds.length} notification(s) as read`,
      });
    } catch (error) {
      console.error("Error marking notifications as read:", error);
      toast({
        title: "Error",
        description: "Failed to mark notifications as read",
        variant: "destructive",
      });
    }
  };

  const markAllAsRead = async () => {
    const unreadIds = notifications.filter(n => !n.read).map(n => n.id);
    if (unreadIds.length > 0) {
      await markAsRead(unreadIds);
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      const { error } = await (supabase as any)
        .from("notifications")
        .delete()
        .eq("id", notificationId)
        .eq("user_id", userProfile.user_id);

      if (error) throw error;

      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      
      toast({
        title: "Success",
        description: "Notification deleted",
      });
    } catch (error) {
      console.error("Error deleting notification:", error);
      toast({
        title: "Error",
        description: "Failed to delete notification",
        variant: "destructive",
      });
    }
  };

  const updateSettings = async (newSettings: Partial<NotificationSettings>) => {
    setIsUpdatingSettings(true);
    try {
      const updatedSettings = { ...settings, ...newSettings };
      
      const { error } = await (supabase as any)
        .from("notification_settings")
        .upsert({
          user_id: userProfile.user_id,
          ...updatedSettings,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;

      setSettings(updatedSettings);
      
      toast({
        title: "Success",
        description: "Notification settings updated",
      });
    } catch (error) {
      console.error("Error updating settings:", error);
      toast({
        title: "Error",
        description: "Failed to update settings",
        variant: "destructive",
      });
    } finally {
      setIsUpdatingSettings(false);
    }
  };

  const getNotificationContent = (notification: Notification) => {
    const IconComponent = notificationIcons[notification.type];
    const iconColor = notificationColors[notification.type];

    let actionElement = null;
    
    if (notification.data) {
      switch (notification.type) {
        case "like":
        case "comment":
          if (notification.data.article_id) {
            actionElement = (
              <Link 
                href={`/ctn/articles/${notification.data.article_id}`}
                className="text-primary hover:underline text-sm"
              >
                View Article
              </Link>
            );
          }
          break;
        case "follow":
          if (notification.data.follower_id) {
            actionElement = (
              <Link 
                href={`/ctn/profile/${notification.data.follower_id}`}
                className="text-primary hover:underline text-sm"
              >
                View Profile
              </Link>
            );
          }
          break;
      }
    }

    return (
      <div className="flex items-start space-x-3">
        <div className={`p-2 rounded-full bg-muted ${iconColor}`}>
          <IconComponent className="w-4 h-4" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">{notification.title}</h4>
            <div className="flex items-center space-x-2">
              {!notification.read && (
                <div className="w-2 h-2 bg-blue-500 rounded-full" />
              )}
              <span className="text-xs text-muted-foreground">
                {formatTimeAgo(notification.created_at)}
              </span>
            </div>
          </div>
          <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
          {actionElement && (
            <div className="mt-2">
              {actionElement}
            </div>
          )}
        </div>
        <div className="flex items-center space-x-1">
          {!notification.read && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => markAsRead([notification.id])}
              className="h-8 w-8 p-0"
            >
              <Check className="w-4 h-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => deleteNotification(notification.id)}
            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
      </div>
    );
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="container max-w-4xl mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            🔔 Notifications
          </h1>
          {unreadCount > 0 && (
            <p className="text-muted-foreground">
              You have {unreadCount} unread notification{unreadCount !== 1 ? "s" : ""}
            </p>
          )}
        </div>
        
        {unreadCount > 0 && (
          <Button onClick={markAllAsRead} variant="outline" size="sm">
            <CheckCheck className="w-4 h-4 mr-2" />
            Mark All Read
          </Button>
        )}
      </div>

      {/* Tabs */}
      <div className="flex space-x-4 border-b">
        {[
          { key: "all", label: "All", count: notifications.length },
          { key: "unread", label: "Unread", count: unreadCount },
          { key: "settings", label: "Settings", icon: Settings },
        ].map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveTab(tab.key as any)}
            className={`flex items-center space-x-2 px-4 py-2 border-b-2 transition-colors ${
              activeTab === tab.key
                ? "border-primary text-primary"
                : "border-transparent text-muted-foreground hover:text-foreground"
            }`}
          >
            {tab.icon && <tab.icon className="w-4 h-4" />}
            <span>{tab.label}</span>
            {tab.count !== undefined && tab.count > 0 && (
              <Badge variant="secondary" className="text-xs">
                {tab.count}
              </Badge>
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      {activeTab === "settings" ? (
        <Card>
          <CardHeader>
            <CardTitle>Notification Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Email Notifications</h4>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications via email
                  </p>
                </div>
                <Switch
                  checked={settings.email_notifications}
                  onCheckedChange={(checked) =>
                    updateSettings({ email_notifications: checked })
                  }
                  disabled={isUpdatingSettings}
                />
              </div>

              <Separator />

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Push Notifications</h4>
                  <p className="text-sm text-muted-foreground">
                    Receive browser push notifications
                  </p>
                </div>
                <Switch
                  checked={settings.push_notifications}
                  onCheckedChange={(checked) =>
                    updateSettings({ push_notifications: checked })
                  }
                  disabled={isUpdatingSettings}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Notification Types</h4>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <UserPlus className="w-4 h-4 text-blue-500" />
                      <div>
                        <p className="font-medium">New Followers</p>
                        <p className="text-sm text-muted-foreground">
                          When someone follows you
                        </p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.follow_notifications}
                      onCheckedChange={(checked) =>
                        updateSettings({ follow_notifications: checked })
                      }
                      disabled={isUpdatingSettings}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Heart className="w-4 h-4 text-red-500" />
                      <div>
                        <p className="font-medium">Likes</p>
                        <p className="text-sm text-muted-foreground">
                          When someone likes your articles
                        </p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.like_notifications}
                      onCheckedChange={(checked) =>
                        updateSettings({ like_notifications: checked })
                      }
                      disabled={isUpdatingSettings}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <MessageSquare className="w-4 h-4 text-green-500" />
                      <div>
                        <p className="font-medium">Comments</p>
                        <p className="text-sm text-muted-foreground">
                          When someone comments on your articles
                        </p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.comment_notifications}
                      onCheckedChange={(checked) =>
                        updateSettings({ comment_notifications: checked })
                      }
                      disabled={isUpdatingSettings}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <MessageSquare className="w-4 h-4 text-yellow-500" />
                      <div>
                        <p className="font-medium">Mentions</p>
                        <p className="text-sm text-muted-foreground">
                          When someone mentions you
                        </p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.mention_notifications}
                      onCheckedChange={(checked) =>
                        updateSettings({ mention_notifications: checked })
                      }
                      disabled={isUpdatingSettings}
                    />
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : notifications.length > 0 ? (
            notifications.map((notification) => (
              <Card
                key={notification.id}
                className={`${!notification.read ? "bg-muted/30" : ""}`}
              >
                <CardContent className="p-4">
                  {getNotificationContent(notification)}
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <Bell className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  {activeTab === "unread" ? "No unread notifications" : "No notifications"}
                </h3>
                <p className="text-muted-foreground">
                  {activeTab === "unread"
                    ? "You're all caught up!"
                    : "Notifications will appear here when you receive them."}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
