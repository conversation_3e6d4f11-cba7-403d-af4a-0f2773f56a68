"use client";

import { useState, useCallback } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Hash, TrendingUp } from "lucide-react";
import Link from "next/link";

interface HashtagProps {
  tag: string;
  variant?: "default" | "secondary" | "outline";
  size?: "sm" | "default" | "lg";
  interactive?: boolean;
  showIcon?: boolean;
  usageCount?: number;
  trending?: boolean;
  className?: string;
  onClick?: (tag: string) => void;
}

interface HashtagInputProps {
  value: string[];
  onChange: (tags: string[]) => void;
  maxTags?: number;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

// Individual hashtag component
export function Hashtag({
  tag,
  variant = "default",
  size = "default",
  interactive = true,
  showIcon = false,
  usageCount,
  trending = false,
  className,
  onClick,
}: HashtagProps) {
  const cleanTag = tag.startsWith('#') ? tag.slice(1) : tag;
  const displayTag = showIcon ? `#${cleanTag}` : cleanTag;

  const handleClick = useCallback(() => {
    if (onClick) {
      onClick(cleanTag);
    }
  }, [onClick, cleanTag]);

  const badgeContent = (
    <div className="flex items-center gap-1">
      {showIcon && <Hash className="w-3 h-3" />}
      <span>{displayTag}</span>
      {usageCount && (
        <span className="text-xs opacity-70">
          {usageCount > 1000 ? `${Math.floor(usageCount / 1000)}k` : usageCount}
        </span>
      )}
      {trending && <TrendingUp className="w-3 h-3" />}
    </div>
  );

  if (interactive) {
    return (
      <Link href={`/ctn/hashtag?tag=${encodeURIComponent(cleanTag)}`} passHref>
        <Badge
          variant={variant}
          className={cn(
            "cursor-pointer hover:opacity-80 transition-opacity",
            size === "sm" && "text-xs px-2 py-0.5",
            size === "lg" && "text-base px-3 py-1",
            className
          )}
          onClick={handleClick}
        >
          {badgeContent}
        </Badge>
      </Link>
    );
  }

  return (
    <Badge
      variant={variant}
      className={cn(
        size === "sm" && "text-xs px-2 py-0.5",
        size === "lg" && "text-base px-3 py-1",
        className
      )}
    >
      {badgeContent}
    </Badge>
  );
}

// Hashtag input component for creating articles
export function HashtagInput({
  value,
  onChange,
  maxTags = 3,
  placeholder = "Add tags (press Enter)",
  disabled = false,
  className,
}: HashtagInputProps) {
  const [inputValue, setInputValue] = useState("");

  const addTag = useCallback((tagInput: string) => {
    if (!tagInput.trim() || value.length >= maxTags) return;

    const cleanTag = tagInput.trim().toLowerCase().replace(/^#+/, '');
    if (cleanTag && !value.includes(cleanTag)) {
      onChange([...value, cleanTag]);
    }
    setInputValue("");
  }, [value, onChange, maxTags]);

  const removeTag = useCallback((tagToRemove: string) => {
    onChange(value.filter(tag => tag !== tagToRemove));
  }, [value, onChange]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      removeTag(value[value.length - 1]);
    }
  }, [inputValue, addTag, removeTag, value]);

  return (
    <div className={cn("space-y-3", className)}>
      <div className="flex flex-wrap gap-2">
        {value.map((tag) => (
          <div key={tag} className="relative group">
            <Hashtag
              tag={tag}
              variant="secondary"
              interactive={false}
              showIcon
            />
            {!disabled && (
              <button
                onClick={() => removeTag(tag)}
                className="absolute -top-1 -right-1 w-4 h-4 bg-destructive text-destructive-foreground rounded-full text-xs opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center"
                aria-label={`Remove ${tag} tag`}
              >
                ×
              </button>
            )}
          </div>
        ))}
      </div>

      {value.length < maxTags && !disabled && (
        <div className="flex gap-2">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="flex-1 px-3 py-2 text-sm bg-background border border-input rounded-md placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            disabled={disabled}
          />
          <Button
            type="button"
            onClick={() => addTag(inputValue)}
            disabled={!inputValue.trim() || value.length >= maxTags}
            variant="outline"
            size="sm"
          >
            Add
          </Button>
        </div>
      )}

      <div className="text-xs text-muted-foreground">
        {value.length}/{maxTags} tags used
        {value.length >= maxTags && " (maximum reached)"}
      </div>
    </div>
  );
}

// Utility functions for hashtag processing
export function parseHashtags(text: string): string[] {
  const hashtagRegex = /#(\w+)/g;
  const hashtags: string[] = [];
  let match;

  while ((match = hashtagRegex.exec(text)) !== null) {
    hashtags.push(match[1].toLowerCase());
  }

  return [...new Set(hashtags)]; // Remove duplicates
}

export function renderTextWithHashtags(text: string): React.ReactNode[] {
  const hashtagRegex = /#(\w+)/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match: RegExpExecArray | null;

  while ((match = hashtagRegex.exec(text)) !== null) {
    // Add text before hashtag
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }
    
    // Add hashtag as clickable component
    const hashtag = match[1];
    parts.push(
      <Hashtag
        key={`hashtag-${match.index}`}
        tag={hashtag}
        variant="outline"
        size="sm"
        showIcon
        className="mx-1"
      />
    );
    
    lastIndex = match.index + match[0].length;
  }
  
  // Add remaining text
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  
  return parts;
}

// Hashtag suggestions component
interface HashtagSuggestionsProps {
  query: string;
  onSelect: (hashtag: string) => void;
  className?: string;
}

export function HashtagSuggestions({ query, onSelect, className }: HashtagSuggestionsProps) {
  const [suggestions, setSuggestions] = useState<Array<{name: string, count: number}>>([]);
  const [isLoading, setIsLoading] = useState(false);

  // This would typically fetch from your hashtag suggestions API
  // For now, showing some common crypto-related hashtags
  const commonSuggestions = [
    { name: "bitcoin", count: 1250 },
    { name: "ethereum", count: 980 },
    { name: "defi", count: 750 },
    { name: "nft", count: 640 },
    { name: "blockchain", count: 1100 },
    { name: "crypto", count: 2000 },
    { name: "trading", count: 850 },
    { name: "altcoin", count: 420 },
  ];

  const filteredSuggestions = query 
    ? commonSuggestions.filter(s => s.name.toLowerCase().includes(query.toLowerCase()))
    : commonSuggestions.slice(0, 5);

  if (!query || filteredSuggestions.length === 0) {
    return null;
  }

  return (
    <div className={cn("bg-popover border rounded-md shadow-md p-2 space-y-1", className)}>
      <div className="text-xs font-medium text-muted-foreground px-2 py-1">
        Suggested hashtags
      </div>
      {filteredSuggestions.map((suggestion) => (
        <button
          key={suggestion.name}
          onClick={() => onSelect(suggestion.name)}
          className="w-full flex items-center justify-between px-2 py-1.5 text-sm hover:bg-accent rounded-sm transition-colors"
        >
          <div className="flex items-center gap-2">
            <Hash className="w-3 h-3" />
            <span>{suggestion.name}</span>
          </div>
          <span className="text-xs text-muted-foreground">
            {suggestion.count}
          </span>
        </button>
      ))}
    </div>
  );
}
