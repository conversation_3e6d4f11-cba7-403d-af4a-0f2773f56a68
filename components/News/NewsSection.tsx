// components/NewsSection.tsx
import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import Link from "next/link";
import { Button } from "@/components/ui/button";

interface NewsArticle {
  source: {
    id: string | null;
    name: string;
  };
  author: string | null;
  title: string;
  description: string;
  url: string;
  urlToImage?: string; // Optional
  publishedAt: string; // ISO date string
}

interface NewsSectionProps {
  cryptoNews: NewsArticle[]; // Adjusted type according to your actual news data structure
}

export default function NewsSection({ cryptoNews }: NewsSectionProps) {
  // Get the  newest articles
  const latestArticles = cryptoNews.slice(0, 2);

  return (
    <section className="py-16">
      <div className="container px-4 mx-auto">
        <h2 className="mb-8 text-3xl font-bold text-center">
          Latest Crypto News
        </h2>
        {latestArticles.length > 0 ? (
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-2">
            {latestArticles.map((article, index) => (
              <Card key={index} className="drop-shadow-xl">
                {article.urlToImage && (
                  <img
                    src={article.urlToImage}
                    alt={article.title}
                    className="object-cover w-full h-48 rounded-t-lg"
                  />
                )}
                <CardHeader>
                  <CardTitle>{article.title}</CardTitle>
                  <CardDescription>
                    By {article.author || "Unknown"} |{" "}
                    <span>
                      {new Date(article.publishedAt).toLocaleDateString()}
                    </span>
                  </CardDescription>
                </CardHeader>
                <CardContent className="grow flex flex-col justify-between">
                  <p className="line-clamp-3">{article.description}</p>
                  <Link href={article.url} className="mt-4">
                    <Button className="w-1/4 right-0">Read More</Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div>No news available at the moment.</div>
        )}
      </div>
    </section>
  );
}
