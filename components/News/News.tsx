import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link"; // Import next/link for client-side navigation
import { NewsArticle } from "@/types";

interface NewsProps {
  newsArticles: NewsArticle[];
}

export default function NewsPage({ newsArticles }: NewsProps) {
  return (
    <div className="container mx-auto">
      <h1 className="mb-8 text-4xl font-bold text-center">
        Latest Cryptocurrency News
      </h1>

      {newsArticles.length > 0 ? (
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {newsArticles.map((article, index) => (
            <Card key={index} className="flex flex-col h-full">
              {/* Card background colors */}
              {article.urlToImage && (
                <img
                  src={article.urlToImage}
                  alt={article.title}
                  className="object-cover w-full h-48 rounded-t-lg"
                />
              )}
              <CardHeader className="flex-1">
                <CardTitle className="text-ellipsis overflow-hidden">
                  {article.title}
                </CardTitle>
                <CardDescription className="text-ellipsis overflow-hidden h-16">
                  {article.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="mt-auto">
                <Link href={article.url} passHref>
                  <Button className="mt-4 w-full">Read More</Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <p className="text-center text-gray-600 dark:text-gray-300">
          No news available at the moment.
        </p>
      )}
    </div>
  );
}
