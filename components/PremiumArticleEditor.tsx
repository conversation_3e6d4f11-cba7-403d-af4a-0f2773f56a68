'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Crown, 
  Sparkles, 
  Palette, 
  Eye, 
  Users, 
  Save, 
  Wand2,
  Type,
  Image,
  Video,
  FileText,
  Settings,
  Lock,
  Globe,
  X
} from 'lucide-react';
import { PremiumBadge, FeatureLockedIndicator } from './PremiumBadges';
import { premiumManager, premiumChecker } from '@/utils/premiumManager';

interface PremiumEditorProps {
  userId: string;
  initialContent?: string;
  initialTitle?: string;
  onSave?: (content: any) => void;
  onPublish?: (content: any) => void;
}

interface EditorFeatures {
  advanced_editor: boolean;
  ai_writing_assistant: boolean;
  custom_themes: boolean;
  collaboration_tools: boolean;
  premium_content_tools: boolean;
}

export function PremiumArticleEditor({ 
  userId, 
  initialContent = '', 
  initialTitle = '',
  onSave,
  onPublish 
}: PremiumEditorProps) {
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [features, setFeatures] = useState<EditorFeatures>({
    advanced_editor: false,
    ai_writing_assistant: false,
    custom_themes: false,
    collaboration_tools: false,
    premium_content_tools: false
  });
  const [currentTheme, setCurrentTheme] = useState('default');
  const [isPremiumContent, setIsPremiumContent] = useState(false);
  const [collaborators, setCollaborators] = useState<string[]>([]);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [aiLoading, setAiLoading] = useState(false);
  const editorRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    loadEditorFeatures();
  }, [userId]);

  const loadEditorFeatures = async () => {
    try {
      setLoading(true);
      const featureChecks = await premiumManager.checkMultiplePremiumFeatures(userId, [
        'advanced_editor',
        'ai_writing_assistant',
        'custom_themes',
        'collaboration_tools',
        'premium_content_tools'
      ]);

      setFeatures({
        advanced_editor: featureChecks.advanced_editor?.hasFeature || false,
        ai_writing_assistant: featureChecks.ai_writing_assistant?.hasFeature || false,
        custom_themes: featureChecks.custom_themes?.hasFeature || false,
        collaboration_tools: featureChecks.collaboration_tools?.hasFeature || false,
        premium_content_tools: featureChecks.premium_content_tools?.hasFeature || false
      });
    } catch (error) {
      console.error('Error loading editor features:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAiAssist = async () => {
    if (!features.ai_writing_assistant) return;
    
    setAiLoading(true);
    try {
      // Simulate AI writing assistance
      const suggestions = [
        'Consider adding more concrete examples to support your points.',
        'This paragraph could benefit from a stronger transition sentence.',
        'Try breaking this long sentence into two for better readability.',
        'Consider adding statistics or data to strengthen this argument.'
      ];
      setAiSuggestions(suggestions);
    } catch (error) {
      console.error('AI assistance error:', error);
    } finally {
      setAiLoading(false);
    }
  };

  const handleFormatText = (format: string) => {
    if (!features.advanced_editor) return;
    
    const textarea = editorRef.current;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = content.substring(start, end);
    
    let formattedText = selectedText;
    switch (format) {
      case 'bold':
        formattedText = `**${selectedText}**`;
        break;
      case 'italic':
        formattedText = `*${selectedText}*`;
        break;
      case 'heading':
        formattedText = `## ${selectedText}`;
        break;
      case 'quote':
        formattedText = `> ${selectedText}`;
        break;
    }

    const newContent = content.substring(0, start) + formattedText + content.substring(end);
    setContent(newContent);
  };

  const themes = [
    { id: 'default', name: 'Default', preview: 'bg-white border-gray-200' },
    { id: 'dark', name: 'Dark Mode', preview: 'bg-gray-900 border-gray-700' },
    { id: 'minimal', name: 'Minimal', preview: 'bg-gray-50 border-gray-100' },
    { id: 'colorful', name: 'Colorful', preview: 'bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200' },
    { id: 'professional', name: 'Professional', preview: 'bg-blue-50 border-blue-200' }
  ];

  const handleSave = () => {
    const articleData = {
      title,
      content,
      theme: currentTheme,
      is_premium_content: isPremiumContent,
      collaborators: collaborators.length > 0 ? collaborators : undefined
    };
    onSave?.(articleData);
  };

  const handlePublish = () => {
    const articleData = {
      title,
      content,
      theme: currentTheme,
      is_premium_content: isPremiumContent,
      collaborators: collaborators.length > 0 ? collaborators : undefined,
      published: true
    };
    onPublish?.(articleData);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-12 bg-gray-100 rounded animate-pulse" />
        <div className="h-96 bg-gray-100 rounded animate-pulse" />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Editor Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <FileText className="w-5 h-5" />
              Article Editor
              {Object.values(features).some(f => f) && (
                <PremiumBadge type="premium" size="sm" />
              )}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Save Draft
              </Button>
              <Button onClick={handlePublish}>
                <Globe className="w-4 h-4 mr-2" />
                Publish
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Editor */}
        <div className="lg:col-span-3 space-y-4">
          {/* Title Input */}
          <Card>
            <CardContent className="pt-6">
              <Input
                placeholder="Article title..."
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="text-2xl font-bold border-none p-0 h-auto focus-visible:ring-0"
              />
            </CardContent>
          </Card>

          {/* Advanced Formatting Toolbar */}
          {features.advanced_editor ? (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2 flex-wrap">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFormatText('bold')}
                  >
                    <Type className="w-4 h-4 mr-1" />
                    Bold
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFormatText('italic')}
                  >
                    <Type className="w-4 h-4 mr-1" />
                    Italic
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFormatText('heading')}
                  >
                    <Type className="w-4 h-4 mr-1" />
                    Heading
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleFormatText('quote')}
                  >
                    <Type className="w-4 h-4 mr-1" />
                    Quote
                  </Button>
                  <Separator orientation="vertical" className="h-6" />
                  <Button variant="outline" size="sm">
                    <Image className="w-4 h-4 mr-1" />
                    Image
                  </Button>
                  <Button variant="outline" size="sm">
                    <Video className="w-4 h-4 mr-1" />
                    Video
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <FeatureLockedIndicator 
              featureName="Advanced Formatting" 
              requiredPlan="Premium"
            >
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-2 flex-wrap">
                    <Button variant="outline" size="sm" disabled>
                      <Type className="w-4 h-4 mr-1" />
                      Bold
                    </Button>
                    <Button variant="outline" size="sm" disabled>
                      <Type className="w-4 h-4 mr-1" />
                      Italic
                    </Button>
                    <Button variant="outline" size="sm" disabled>
                      <Type className="w-4 h-4 mr-1" />
                      Heading
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </FeatureLockedIndicator>
          )}

          {/* Content Editor */}
          <Card className={currentTheme !== 'default' ? themes.find(t => t.id === currentTheme)?.preview : ''}>
            <CardContent className="pt-6">
              <Textarea
                ref={editorRef}
                placeholder="Start writing your article..."
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="min-h-[400px] border-none resize-none focus-visible:ring-0 text-lg leading-relaxed"
              />
            </CardContent>
          </Card>

          {/* AI Writing Assistant */}
          {features.ai_writing_assistant && aiSuggestions.length > 0 && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Wand2 className="w-4 h-4" />
                  AI Writing Suggestions
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {aiSuggestions.map((suggestion, index) => (
                  <div key={index} className="p-3 bg-white rounded border text-sm">
                    {suggestion}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          {/* Premium Content Settings */}
          {features.premium_content_tools ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Crown className="w-4 h-4" />
                  Premium Content
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="premium-content" className="text-sm">
                    Premium Only
                  </Label>
                  <Switch
                    id="premium-content"
                    checked={isPremiumContent}
                    onCheckedChange={setIsPremiumContent}
                  />
                </div>
                {isPremiumContent && (
                  <div className="text-xs text-gray-600 p-2 bg-yellow-50 rounded border border-yellow-200">
                    This article will only be visible to premium subscribers
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <FeatureLockedIndicator featureName="Premium Content Tools">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Crown className="w-4 h-4" />
                    Premium Content
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <Label className="text-sm">Premium Only</Label>
                    <Switch disabled />
                  </div>
                </CardContent>
              </Card>
            </FeatureLockedIndicator>
          )}

          {/* Custom Themes */}
          {features.custom_themes ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Palette className="w-4 h-4" />
                  Article Theme
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {themes.map((theme) => (
                  <button
                    key={theme.id}
                    onClick={() => setCurrentTheme(theme.id)}
                    className={`w-full p-3 rounded border text-left transition-all ${
                      currentTheme === theme.id 
                        ? 'ring-2 ring-blue-500 border-blue-500' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className={`h-6 w-full rounded mb-2 ${theme.preview}`} />
                    <div className="text-sm font-medium">{theme.name}</div>
                  </button>
                ))}
              </CardContent>
            </Card>
          ) : (
            <FeatureLockedIndicator featureName="Custom Themes">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Palette className="w-4 h-4" />
                    Article Theme
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {themes.slice(0, 2).map((theme) => (
                      <button
                        key={theme.id}
                        disabled
                        className="w-full p-3 rounded border text-left opacity-50"
                      >
                        <div className={`h-6 w-full rounded mb-2 ${theme.preview}`} />
                        <div className="text-sm font-medium">{theme.name}</div>
                      </button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </FeatureLockedIndicator>
          )}

          {/* AI Writing Assistant */}
          {features.ai_writing_assistant ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Sparkles className="w-4 h-4" />
                  AI Assistant
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Button
                  onClick={handleAiAssist}
                  disabled={aiLoading}
                  className="w-full"
                  variant="outline"
                >
                  {aiLoading ? (
                    <>
                      <Wand2 className="w-4 h-4 mr-2 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-4 h-4 mr-2" />
                      Get Writing Tips
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ) : (
            <FeatureLockedIndicator featureName="AI Writing Assistant">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Sparkles className="w-4 h-4" />
                    AI Assistant
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Button disabled className="w-full" variant="outline">
                    <Wand2 className="w-4 h-4 mr-2" />
                    Get Writing Tips
                  </Button>
                </CardContent>
              </Card>
            </FeatureLockedIndicator>
          )}

          {/* Collaboration Tools */}
          {features.collaboration_tools ? (
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  Collaborators
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Input
                  placeholder="Add collaborator email..."
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      const email = e.currentTarget.value;
                      if (email && !collaborators.includes(email)) {
                        setCollaborators([...collaborators, email]);
                        e.currentTarget.value = '';
                      }
                    }
                  }}
                />
                {collaborators.length > 0 && (
                  <div className="space-y-2">
                    {collaborators.map((email, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">{email}</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setCollaborators(collaborators.filter((_, i) => i !== index))}
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ) : (
            <FeatureLockedIndicator featureName="Collaboration Tools">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    Collaborators
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Input disabled placeholder="Add collaborator email..." />
                </CardContent>
              </Card>
            </FeatureLockedIndicator>
          )}
        </div>
      </div>
    </div>
  );
}
