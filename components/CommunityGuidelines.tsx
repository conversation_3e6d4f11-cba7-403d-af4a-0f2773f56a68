"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  X, 
  Plus,
  Edit3,
  Trash2,
  Eye,
  EyeOff,
  Save,
  BookOpen
} from "lucide-react";
import { UserProfile } from "@/types";
import { toast } from "@/hooks/use-toast";

interface CommunityGuidelinesProps {
  userProfile: UserProfile;
}

interface Guideline {
  id: number;
  title: string;
  description: string;
  category: string;
  severity: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface GuidelineCategory {
  category: string;
  count: number;
}

interface AutoFlag {
  id: number;
  name: string;
  keywords: string[];
  severity: number;
  action: string;
  is_active: boolean;
}

export default function CommunityGuidelines({ userProfile }: CommunityGuidelinesProps) {
  const [guidelines, setGuidelines] = useState<Guideline[]>([]);
  const [categories, setCategories] = useState<GuidelineCategory[]>([]);
  const [autoFlags, setAutoFlags] = useState<AutoFlag[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingGuideline, setEditingGuideline] = useState<Guideline | null>(null);
  const [newGuideline, setNewGuideline] = useState({
    title: "",
    description: "",
    category: "",
    severity: 1
  });
  const [newAutoFlag, setNewAutoFlag] = useState({
    name: "",
    keywords: "",
    severity: 1,
    action: "flag"
  });
  const [activeTab, setActiveTab] = useState("guidelines");
  
  const supabase = createClient();

  const loadGuidelines = useCallback(async () => {
    try {
      const { data, error } = await (supabase as any)
        .from("community_guidelines")
        .select("*")
        .order("category", { ascending: true })
        .order("severity", { ascending: false });

      if (error) throw error;
      setGuidelines(data || []);

      // Get categories with counts
      const categoryMap = new Map();
      data?.forEach((guideline: Guideline) => {
        const count: number = categoryMap.get(guideline.category) || 0;
        categoryMap.set(guideline.category, count + 1);
      });

      const categoryList = Array.from(categoryMap.entries()).map(([category, count]) => ({
        category,
        count
      }));
      setCategories(categoryList);

    } catch (error) {
      console.error("Error loading guidelines:", error);
      toast({
        title: "Error",
        description: "Failed to load community guidelines",
        variant: "destructive",
      });
    }
  }, [supabase]);

  const loadAutoFlags = useCallback(async () => {
    try {
      const { data, error } = await (supabase as any)
        .from("auto_flag_rules")
        .select("*")
        .order("severity", { ascending: false });

      if (error) throw error;
      setAutoFlags(data || []);

    } catch (error) {
      console.error("Error loading auto flags:", error);
      toast({
        title: "Error",
        description: "Failed to load auto-flagging rules",
        variant: "destructive",
      });
    }
  }, [supabase]);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      await Promise.all([loadGuidelines(), loadAutoFlags()]);
      setIsLoading(false);
    };
    loadData();
  }, [loadGuidelines, loadAutoFlags]);

  const handleCreateGuideline = async () => {
    if (!newGuideline.title || !newGuideline.description || !newGuideline.category) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await (supabase as any)
        .from("community_guidelines")
        .insert([{
          ...newGuideline,
          is_active: true
        }]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Guideline created successfully",
      });

      setNewGuideline({ title: "", description: "", category: "", severity: 1 });
      loadGuidelines();
    } catch (error) {
      console.error("Error creating guideline:", error);
      toast({
        title: "Error",
        description: "Failed to create guideline",
        variant: "destructive",
      });
    }
  };

  const handleUpdateGuideline = async (guideline: Guideline) => {
    try {
      const { error } = await (supabase as any)
        .from("community_guidelines")
        .update({
          title: guideline.title,
          description: guideline.description,
          category: guideline.category,
          severity: guideline.severity,
          is_active: guideline.is_active,
          updated_at: new Date().toISOString()
        })
        .eq("id", guideline.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Guideline updated successfully",
      });

      setEditingGuideline(null);
      loadGuidelines();
    } catch (error) {
      console.error("Error updating guideline:", error);
      toast({
        title: "Error",
        description: "Failed to update guideline",
        variant: "destructive",
      });
    }
  };

  const handleDeleteGuideline = async (id: number) => {
    if (!confirm("Are you sure you want to delete this guideline?")) return;

    try {
      const { error } = await (supabase as any)
        .from("community_guidelines")
        .delete()
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Guideline deleted successfully",
      });

      loadGuidelines();
    } catch (error) {
      console.error("Error deleting guideline:", error);
      toast({
        title: "Error",
        description: "Failed to delete guideline",
        variant: "destructive",
      });
    }
  };

  const handleCreateAutoFlag = async () => {
    if (!newAutoFlag.name || !newAutoFlag.keywords) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      const { error } = await (supabase as any)
        .from("auto_flag_rules")
        .insert([{
          ...newAutoFlag,
          keywords: newAutoFlag.keywords.split(",").map(k => k.trim()),
          is_active: true
        }]);

      if (error) throw error;

      toast({
        title: "Success",
        description: "Auto-flagging rule created successfully",
      });

      setNewAutoFlag({ name: "", keywords: "", severity: 1, action: "flag" });
      loadAutoFlags();
    } catch (error) {
      console.error("Error creating auto flag:", error);
      toast({
        title: "Error",
        description: "Failed to create auto-flagging rule",
        variant: "destructive",
      });
    }
  };

  const handleToggleAutoFlag = async (id: number, isActive: boolean) => {
    try {
      const { error } = await (supabase as any)
        .from("auto_flag_rules")
        .update({ is_active: isActive })
        .eq("id", id);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Auto-flagging rule ${isActive ? "enabled" : "disabled"}`,
      });

      loadAutoFlags();
    } catch (error) {
      console.error("Error toggling auto flag:", error);
      toast({
        title: "Error",
        description: "Failed to update auto-flagging rule",
        variant: "destructive",
      });
    }
  };

  const getSeverityColor = (severity: number) => {
    switch (severity) {
      case 1: return "bg-green-100 text-green-800";
      case 2: return "bg-yellow-100 text-yellow-800";
      case 3: return "bg-orange-100 text-orange-800";
      case 4: return "bg-red-100 text-red-800";
      case 5: return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold">Community Guidelines</h1>
        </div>
        <div className="animate-pulse space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Community Guidelines</h1>
        <div className="flex gap-2">
          <Button
            variant={activeTab === "guidelines" ? "default" : "outline"}
            onClick={() => setActiveTab("guidelines")}
          >
            <BookOpen className="h-4 w-4 mr-2" />
            Guidelines
          </Button>
          <Button
            variant={activeTab === "autoflags" ? "default" : "outline"}
            onClick={() => setActiveTab("autoflags")}
          >
            <Shield className="h-4 w-4 mr-2" />
            Auto-Flagging
          </Button>
        </div>
      </div>

      {activeTab === "guidelines" && (
        <div className="space-y-6">
          {/* Categories Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {categories.map((category) => (
              <Card key={category.category}>
                <CardContent className="p-4">
                  <div className="text-sm font-medium">{category.category}</div>
                  <div className="text-2xl font-bold">{category.count}</div>
                  <div className="text-xs text-muted-foreground">guidelines</div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Create New Guideline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Plus className="h-5 w-5" />
                Create New Guideline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={newGuideline.title}
                    onChange={(e) => setNewGuideline({ ...newGuideline, title: e.target.value })}
                    placeholder="Guideline title"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={newGuideline.category}
                    onChange={(e) => setNewGuideline({ ...newGuideline, category: e.target.value })}
                    placeholder="e.g., Content, Behavior, Spam"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={newGuideline.description}
                  onChange={(e) => setNewGuideline({ ...newGuideline, description: e.target.value })}
                  placeholder="Detailed description of the guideline"
                  className="min-h-20"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="severity">Severity Level</Label>
                <Select value={newGuideline.severity.toString()} onValueChange={(value) => setNewGuideline({ ...newGuideline, severity: parseInt(value) })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 - Minor</SelectItem>
                    <SelectItem value="2">2 - Low</SelectItem>
                    <SelectItem value="3">3 - Medium</SelectItem>
                    <SelectItem value="4">4 - High</SelectItem>
                    <SelectItem value="5">5 - Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={handleCreateGuideline}>
                Create Guideline
              </Button>
            </CardContent>
          </Card>

          {/* Guidelines List */}
          <div className="space-y-4">
            {guidelines.map((guideline) => (
              <Card key={guideline.id}>
                <CardContent className="p-6">
                  {editingGuideline?.id === guideline.id ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Input
                          value={editingGuideline.title}
                          onChange={(e) => setEditingGuideline({ ...editingGuideline, title: e.target.value })}
                        />
                        <Input
                          value={editingGuideline.category}
                          onChange={(e) => setEditingGuideline({ ...editingGuideline, category: e.target.value })}
                        />
                      </div>
                      <Textarea
                        value={editingGuideline.description}
                        onChange={(e) => setEditingGuideline({ ...editingGuideline, description: e.target.value })}
                        className="min-h-20"
                      />
                      <div className="flex gap-2">
                        <Button onClick={() => handleUpdateGuideline(editingGuideline)}>
                          <Save className="h-4 w-4 mr-2" />
                          Save
                        </Button>
                        <Button variant="outline" onClick={() => setEditingGuideline(null)}>
                          Cancel
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-semibold text-lg">{guideline.title}</h3>
                          <div className="flex gap-2 mt-1">
                            <Badge variant="outline">{guideline.category}</Badge>
                            <Badge className={getSeverityColor(guideline.severity)}>
                              Severity {guideline.severity}
                            </Badge>
                            <Badge variant={guideline.is_active ? "default" : "secondary"}>
                              {guideline.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingGuideline(guideline)}
                          >
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteGuideline(guideline.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-muted-foreground">{guideline.description}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {activeTab === "autoflags" && (
        <div className="space-y-6">
          {/* Create Auto-Flag Rule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Create Auto-Flagging Rule
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Rule Name</Label>
                  <Input
                    id="name"
                    value={newAutoFlag.name}
                    onChange={(e) => setNewAutoFlag({ ...newAutoFlag, name: e.target.value })}
                    placeholder="Rule name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="action">Action</Label>
                  <Select value={newAutoFlag.action} onValueChange={(value) => setNewAutoFlag({ ...newAutoFlag, action: value })}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="flag">Flag for Review</SelectItem>
                      <SelectItem value="auto_remove">Auto Remove</SelectItem>
                      <SelectItem value="shadowban">Shadowban</SelectItem>
                      <SelectItem value="require_review">Require Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="keywords">Keywords (comma-separated)</Label>
                <Textarea
                  id="keywords"
                  value={newAutoFlag.keywords}
                  onChange={(e) => setNewAutoFlag({ ...newAutoFlag, keywords: e.target.value })}
                  placeholder="spam, scam, offensive word, etc."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="severity">Severity Level</Label>
                <Select value={newAutoFlag.severity.toString()} onValueChange={(value) => setNewAutoFlag({ ...newAutoFlag, severity: parseInt(value) })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 - Minor</SelectItem>
                    <SelectItem value="2">2 - Low</SelectItem>
                    <SelectItem value="3">3 - Medium</SelectItem>
                    <SelectItem value="4">4 - High</SelectItem>
                    <SelectItem value="5">5 - Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button onClick={handleCreateAutoFlag}>
                Create Rule
              </Button>
            </CardContent>
          </Card>

          {/* Auto-Flag Rules List */}
          <div className="space-y-4">
            {autoFlags.map((flag) => (
              <Card key={flag.id}>
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{flag.name}</h3>
                        <Switch
                          checked={flag.is_active}
                          onCheckedChange={(checked) => handleToggleAutoFlag(flag.id, checked)}
                        />
                      </div>
                      <div className="flex gap-2 mb-3">
                        <Badge className={getSeverityColor(flag.severity)}>
                          Severity {flag.severity}
                        </Badge>
                        <Badge variant="outline">{flag.action}</Badge>
                        <Badge variant={flag.is_active ? "default" : "secondary"}>
                          {flag.is_active ? "Active" : "Inactive"}
                        </Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        <strong>Keywords:</strong> {flag.keywords.join(", ")}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
