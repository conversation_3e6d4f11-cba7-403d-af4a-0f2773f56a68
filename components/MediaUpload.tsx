"use client";

import { useState, useRef, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/hooks/use-toast";
import { 
  Upload, 
  Video, 
  Image as ImageIcon, 
  X, 
  Play, 
  Pause,
  Volume2,
  VolumeX
} from "lucide-react";
import { cn } from "@/lib/utils";
import { createClient } from "@/utils/supabase/client";

interface MediaUploadProps {
  userId: string;
  onMediaUpload: (mediaUrl: string, mediaType: 'image' | 'video') => void;
  acceptedTypes?: string[];
  maxFileSize?: number;
  className?: string;
}

interface MediaFile {
  file: File;
  type: 'image' | 'video';
  preview: string;
}

const DEFAULT_ACCEPTED_TYPES = [
  'image/jpeg',
  'image/png', 
  'image/webp',
  'image/gif',
  'video/mp4',
  'video/webm',
  'video/ogg'
];

const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB
const MAX_VIDEO_SIZE = 50 * 1024 * 1024; // 50MB

export function MediaUpload({
  userId,
  onMediaUpload,
  acceptedTypes = DEFAULT_ACCEPTED_TYPES,
  maxFileSize = MAX_FILE_SIZE,
  className
}: MediaUploadProps) {
  const [uploadedMedia, setUploadedMedia] = useState<MediaFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isPlaying, setIsPlaying] = useState<{ [key: string]: boolean }>({});
  const [isMuted, setIsMuted] = useState<{ [key: string]: boolean }>({});
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRefs = useRef<{ [key: string]: HTMLVideoElement }>({});
  const supabase = createClient();

  const validateFile = useCallback((file: File): boolean => {
    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');
    
    if (!acceptedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: `Please upload a valid ${isImage ? 'image' : 'video'} file`,
        variant: "destructive",
      });
      return false;
    }

    const maxSize = isImage ? MAX_IMAGE_SIZE : MAX_VIDEO_SIZE;
    if (file.size > maxSize) {
      toast({
        title: "File too large",
        description: `${isImage ? 'Image' : 'Video'} must be less than ${maxSize / (1024 * 1024)}MB`,
        variant: "destructive",
      });
      return false;
    }

    return true;
  }, [acceptedTypes]);

  const createFilePreview = useCallback((file: File): string => {
    return URL.createObjectURL(file);
  }, []);

  const handleFileSelect = useCallback((files: FileList) => {
    const newMediaFiles: MediaFile[] = [];
    
    Array.from(files).forEach(file => {
      if (validateFile(file)) {
        const mediaType = file.type.startsWith('image/') ? 'image' : 'video';
        const preview = createFilePreview(file);
        
        newMediaFiles.push({
          file,
          type: mediaType,
          preview
        });
      }
    });

    if (newMediaFiles.length > 0) {
      setUploadedMedia(prev => [...prev, ...newMediaFiles]);
    }
  }, [validateFile, createFilePreview]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    const files = e.dataTransfer.files;
    handleFileSelect(files);
  }, [handleFileSelect]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFileSelect(e.target.files);
    }
  }, [handleFileSelect]);

  const removeMedia = useCallback((index: number) => {
    setUploadedMedia(prev => {
      const updated = [...prev];
      URL.revokeObjectURL(updated[index].preview);
      updated.splice(index, 1);
      return updated;
    });
  }, []);

  const uploadToSupabase = useCallback(async (mediaFile: MediaFile): Promise<string> => {
    const { file, type } = mediaFile;
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.${fileExt}`;
    const filePath = `${userId}/media/${type}s/${fileName}`;

    const { error: uploadError } = await (supabase as any).storage
      .from('ctn')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      });

    if (uploadError) {
      throw new Error(`Failed to upload ${type}: ${uploadError.message}`);
    }

    const { data: publicUrlData } = (supabase as any).storage
      .from('ctn')
      .getPublicUrl(filePath);

    return publicUrlData.publicUrl;
  }, [userId, supabase]);

  const handleUploadAll = useCallback(async () => {
    if (uploadedMedia.length === 0) return;
    
    setUploading(true);
    setUploadProgress(0);

    try {
      for (let i = 0; i < uploadedMedia.length; i++) {
        const mediaFile = uploadedMedia[i];
        const mediaUrl = await uploadToSupabase(mediaFile);
        onMediaUpload(mediaUrl, mediaFile.type);
        
        // Clean up preview URL
        URL.revokeObjectURL(mediaFile.preview);
      }

      setUploadedMedia([]);
      toast({
        title: "Success",
        description: `${uploadedMedia.length} file(s) uploaded successfully`,
      });
    } catch (error: any) {
      console.error('Error uploading media:', error);
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload media files",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [uploadedMedia, uploadToSupabase, onMediaUpload]);

  const toggleVideoPlay = useCallback((index: number) => {
    const video = videoRefs.current[index];
    if (video) {
      if (isPlaying[index]) {
        video.pause();
      } else {
        video.play();
      }
      setIsPlaying(prev => ({ ...prev, [index]: !prev[index] }));
    }
  }, [isPlaying]);

  const toggleVideoMute = useCallback((index: number) => {
    const video = videoRefs.current[index];
    if (video) {
      video.muted = !video.muted;
      setIsMuted(prev => ({ ...prev, [index]: !prev[index] }));
    }
  }, []);

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Upload Area */}
          <div
            className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center transition-colors hover:border-muted-foreground/50 cursor-pointer"
            onDrop={handleDrop}
            onDragOver={(e) => e.preventDefault()}
            onDragEnter={(e) => e.preventDefault()}
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="w-8 h-8 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">
              Drop files here or click to upload
            </p>
            <p className="text-sm text-muted-foreground">
              Support for images (JPEG, PNG, WebP, GIF) and videos (MP4, WebM, OGG)
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Max size: 5MB for images, 50MB for videos
            </p>
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept={acceptedTypes.join(',')}
            multiple
            onChange={handleFileInputChange}
            className="hidden"
          />

          {/* Media Preview */}
          {uploadedMedia.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium">
                  Selected Media ({uploadedMedia.length})
                </h3>
                <Button
                  onClick={handleUploadAll}
                  disabled={uploading}
                  className="flex items-center gap-2"
                >
                  <Upload className="w-4 h-4" />
                  {uploading ? 'Uploading...' : 'Upload All'}
                </Button>
              </div>

              {uploading && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Uploading...</span>
                    <span>{Math.round(uploadProgress)}%</span>
                  </div>
                  <Progress value={uploadProgress} className="w-full" />
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {uploadedMedia.map((media, index) => (
                  <div key={index} className="relative group">
                    <div className="relative rounded-lg overflow-hidden bg-muted aspect-square">
                      {media.type === 'image' ? (
                        <img
                          src={media.preview}
                          alt={`Preview ${index}`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="relative w-full h-full">
                          <video
                            ref={(el) => {
                              if (el) videoRefs.current[index] = el;
                            }}
                            src={media.preview}
                            className="w-full h-full object-cover"
                            onPlay={() => setIsPlaying(prev => ({ ...prev, [index]: true }))}
                            onPause={() => setIsPlaying(prev => ({ ...prev, [index]: false }))}
                            onEnded={() => setIsPlaying(prev => ({ ...prev, [index]: false }))}
                            muted
                          />
                          
                          {/* Video Controls Overlay */}
                          <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleVideoPlay(index);
                                }}
                                className="bg-black/50 hover:bg-black/70 text-white border-none"
                              >
                                {isPlaying[index] ? (
                                  <Pause className="w-4 h-4" />
                                ) : (
                                  <Play className="w-4 h-4" />
                                )}
                              </Button>
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleVideoMute(index);
                                }}
                                className="bg-black/50 hover:bg-black/70 text-white border-none"
                              >
                                {isMuted[index] ? (
                                  <VolumeX className="w-4 h-4" />
                                ) : (
                                  <Volume2 className="w-4 h-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {/* Media Type Badge */}
                      <div className="absolute top-2 left-2">
                        <div className="bg-black/70 text-white px-2 py-1 rounded-md text-xs flex items-center gap-1">
                          {media.type === 'image' ? (
                            <ImageIcon className="w-3 h-3" />
                          ) : (
                            <Video className="w-3 h-3" />
                          )}
                          {media.type}
                        </div>
                      </div>

                      {/* Remove Button */}
                      <Button
                        size="sm"
                        variant="destructive"
                        className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeMedia(index);
                        }}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                    
                    {/* File Info */}
                    <div className="mt-2 text-sm">
                      <p className="font-medium truncate">{media.file.name}</p>
                      <p className="text-muted-foreground">
                        {(media.file.size / (1024 * 1024)).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
