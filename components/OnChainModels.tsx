"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { StaticChart, PercentageChart } from '@/components/StaticChart';
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  DollarSign, 
  Wallet, 
  Clock,
  Info,
  Download,
  RefreshCw,
  Eye,
  BarChart3
} from 'lucide-react';

interface ChainMetrics {
  name: string;
  symbol: string;
  price: number;
  marketCap: number;
  circulatingSupply: number;
  timestamp: number;
}

interface OnChainModelsProps {
  selectedChain: ChainMetrics;
  isActive?: boolean;
  onRefresh?: () => void;
}

// Bitcoin Rainbow Chart - Only for BTC
function BitcoinRainbowChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  if (selectedChain.symbol !== 'BTC') {
    return (
      <Card className="opacity-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-orange-500" />
            Bitcoin Rainbow Chart
            <Badge variant="outline" className="text-xs">BTC Only</Badge>
          </CardTitle>
          <CardDescription>Long-term price prediction model based on logarithmic regression</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <p>This model is only available for Bitcoin</p>
            <p className="text-xs mt-2">Select Bitcoin from the dropdown to view the Rainbow Chart</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Generate rainbow chart data for Bitcoin using static price as baseline
  const generateRainbowData = () => {
    const data = [];
    const baselinePrice = selectedChain.price; // Use actual BTC price from static data
    const daysToShow = 365;
    
    for (let i = daysToShow; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      
      // Create realistic price movement around the baseline
      const dayProgress = i / daysToShow;
      const trendFactor = 0.7 + dayProgress * 0.6; // Price generally trends up over time
      const volatility = (Math.random() - 0.5) * 0.15; // ±7.5% daily volatility
      const seasonalFactor = Math.sin(dayProgress * Math.PI * 4) * 0.05; // Seasonal cycles
      
      const priceVariation = trendFactor + volatility + seasonalFactor;
      const historicalPrice = Math.max(1000, baselinePrice * priceVariation);
      
      // Calculate rainbow band based on price level relative to regression model
      // Bitcoin rainbow bands are based on logarithmic regression
      const daysSinceGenesis = 5000 + i; // Approximate days since Bitcoin genesis
      const regressionPrice = Math.exp(5.84 * Math.log(daysSinceGenesis) - 17.01);
      const priceRatio = historicalPrice / Math.max(regressionPrice, 1);
      
      // Map price ratio to rainbow bands (0-8)
      let band = 4; // Default to middle band
      if (priceRatio > 10) band = 0; // Maximum bubble
      else if (priceRatio > 5) band = 1; // Sell seriously
      else if (priceRatio > 3) band = 2; // FOMO intensifies
      else if (priceRatio > 2) band = 3; // Is this a bubble?
      else if (priceRatio > 1.5) band = 4; // HODL!
      else if (priceRatio > 1) band = 5; // Still cheap
      else if (priceRatio > 0.5) band = 6; // Accumulate
      else if (priceRatio > 0.3) band = 7; // Buy
      else band = 8; // Fire sale
      
      data.push({
        time: date.toISOString().split('T')[0],
        value: historicalPrice,
        band: band
      });
    }
    
    return data;
  };

  const rainbowData = generateRainbowData();
  const currentBand = rainbowData[rainbowData.length - 1]?.band || 4;
  const bandColors = [
    '#8B0000', '#FF0000', '#FF4500', '#FFA500', '#FFFF00', 
    '#ADFF2F', '#00FF00', '#00FFFF', '#0000FF'
  ];
  const bandNames = [
    'Maximum Bubble', 'Sell Seriously', 'FOMO Intensifies', 'Is This A Bubble?',
    'HODL!', 'Still Cheap', 'Accumulate', 'Buy', 'Fire Sale'
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5 text-orange-500" />
          Bitcoin Rainbow Chart
          <Badge variant="outline" className="text-xs">BTC Only</Badge>
        </CardTitle>
        <CardDescription>Long-term price prediction model based on logarithmic regression</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Current Band:</span>
            <Badge 
              style={{ backgroundColor: bandColors[currentBand] }}
              className="text-white"
            >
              {bandNames[currentBand]}
            </Badge>
          </div>
          <div className="text-xs text-muted-foreground">
            Price: ${selectedChain.price.toLocaleString()}
          </div>
        </div>
        
        <StaticChart
          data={rainbowData}
          type="line"
          color={bandColors[currentBand]}
          height={300}
          formatValue={(value) => `$${value.toLocaleString()}`}
          formatDate={(date) => new Date(date).toLocaleDateString()}
          showGrid={true}
          showTooltip={true}
        />
        
        <div className="mt-4 grid grid-cols-3 gap-2">
          {bandNames.slice(0, 9).map((name, index) => (
            <div key={index} className="flex items-center gap-2 text-xs">
              <div 
                className="w-3 h-3 rounded"
                style={{ backgroundColor: bandColors[index] }}
              />
              <span className={index === currentBand ? 'font-semibold' : ''}>{name}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

// MVRV (Market Value to Realized Value) Chart
function MVRVChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const [showZScore, setShowZScore] = useState(false);
  
  const generateMVRVData = () => {
    const data = [];
    for (let i = 0; i < 365; i++) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const mvrv = 0.8 + Math.random() * 2.4; // MVRV typically ranges 0.5-3.0
      const zScore = (mvrv - 1) / 0.5; // Normalized Z-score
      
      data.push({
        time: date.toISOString().split('T')[0],
        value: showZScore ? zScore : mvrv
      });
    }
    return data.reverse();
  };

  const mvrvData = generateMVRVData();
  const currentValue = mvrvData[mvrvData.length - 1]?.value || 1;
  
  const getSignal = (value: number) => {
    if (showZScore) {
      if (value > 2) return { text: 'Sell Signal', color: 'text-red-500' };
      if (value < -1) return { text: 'Buy Signal', color: 'text-green-500' };
      return { text: 'Hold', color: 'text-yellow-500' };
    } else {
      if (value > 2.5) return { text: 'Overvalued', color: 'text-red-500' };
      if (value < 1) return { text: 'Undervalued', color: 'text-green-500' };
      return { text: 'Fair Value', color: 'text-yellow-500' };
    }
  };

  const signal = getSignal(currentValue);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-blue-500" />
          MVRV {showZScore ? 'Z-Score' : 'Ratio'}
        </CardTitle>
        <CardDescription>
          Market Value to Realized Value ratio - measures if price is above/below "fair value"
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="z-score"
              checked={showZScore}
              onCheckedChange={setShowZScore}
            />
            <Label htmlFor="z-score" className="text-sm">Show Z-Score</Label>
          </div>
          <Badge className={signal.color}>
            {signal.text}
          </Badge>
        </div>
        
        <div className="mb-4">
          <div className="text-2xl font-bold">
            {currentValue.toFixed(2)}{showZScore ? 'σ' : ''}
          </div>
          <div className="text-xs text-muted-foreground">
            Current {showZScore ? 'Z-Score' : 'MVRV Ratio'}
          </div>
        </div>
        
        <StaticChart
          data={mvrvData}
          type="line"
          color="#3b82f6"
          height={300}
          formatValue={(value) => showZScore ? `${value.toFixed(2)}σ` : value.toFixed(2)}
          formatDate={(date) => new Date(date).toLocaleDateString()}
          showGrid={true}
          showTooltip={true}
          referenceLines={showZScore ? [
            { y: 2, stroke: '#ef4444', strokeDasharray: '5 5', label: 'Sell Zone' },
            { y: -1, stroke: '#10b981', strokeDasharray: '5 5', label: 'Buy Zone' }
          ] : [
            { y: 2.5, stroke: '#ef4444', strokeDasharray: '5 5', label: 'Overvalued' },
            { y: 1, stroke: '#10b981', strokeDasharray: '5 5', label: 'Undervalued' }
          ]}
        />
        
        <div className="mt-4 text-xs text-muted-foreground">
          <p>
            {showZScore 
              ? "Z-Score > 2: Sell zone | Z-Score < -1: Buy zone"
              : "MVRV > 2.5: Overvalued | MVRV < 1: Undervalued"
            }
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// NRPL (Net Realized Profit/Loss) Chart
function NRPLChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const generateNRPLData = () => {
    const data = [];
    for (let i = 0; i < 365; i++) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const nrpl = (Math.random() - 0.5) * 2000000000; // Random P/L in USD

      data.push({
        time: date.toISOString().split('T')[0],
        value: nrpl
      });
    }
    return data.reverse();
  };

  const nrplData = generateNRPLData();
  const currentNRPL = nrplData[nrplData.length - 1]?.value || 0;
  const isProfit = currentNRPL > 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5 text-green-500" />
          Net Realized Profit/Loss (NRPL)
        </CardTitle>
        <CardDescription>
          Daily net profit or loss realized by all network participants
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className={`text-2xl font-bold ${isProfit ? 'text-green-500' : 'text-red-500'}`}>
            {isProfit ? '+' : ''}${(currentNRPL / 1000000).toFixed(1)}M
          </div>
          <div className="text-xs text-muted-foreground">
            Daily Net {isProfit ? 'Profit' : 'Loss'}
          </div>
        </div>

        <StaticChart
          data={nrplData}
          type="area"
          color={isProfit ? "#10b981" : "#ef4444"}
          gradientColors={isProfit ? ["#10b981", "#10b98120"] : ["#ef4444", "#ef444420"]}
          height={300}
          formatValue={(value) => `$${(value / 1000000).toFixed(1)}M`}
          formatDate={(date) => new Date(date).toLocaleDateString()}
          showGrid={true}
          showTooltip={true}
          referenceLines={[
            { y: 0, stroke: '#666', strokeDasharray: '2 2', label: 'Break Even' }
          ]}
        />

        <div className="mt-4 text-xs text-muted-foreground">
          <p>
            High positive NRPL often coincides with local price tops.
            High negative NRPL often coincides with local price bottoms.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// NUPL (Net Unrealized Profit/Loss) Chart
function NUPLChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const generateNUPLData = () => {
    const data = [];
    for (let i = 0; i < 365; i++) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const nupl = Math.random() * 2 - 1; // NUPL ranges from -1 to 1

      data.push({
        time: date.toISOString().split('T')[0],
        value: nupl
      });
    }
    return data.reverse();
  };

  const nuplData = generateNUPLData();
  const currentNUPL = nuplData[nuplData.length - 1]?.value || 0;

  const getNUPLPhase = (value: number) => {
    if (value > 0.75) return { phase: 'Euphoria', color: 'text-red-500', bg: 'bg-red-50' };
    if (value > 0.5) return { phase: 'Greed', color: 'text-orange-500', bg: 'bg-orange-50' };
    if (value > 0.25) return { phase: 'Optimism', color: 'text-yellow-500', bg: 'bg-yellow-50' };
    if (value > 0) return { phase: 'Hope', color: 'text-green-500', bg: 'bg-green-50' };
    if (value > -0.25) return { phase: 'Fear', color: 'text-blue-500', bg: 'bg-blue-50' };
    if (value > -0.5) return { phase: 'Anxiety', color: 'text-purple-500', bg: 'bg-purple-50' };
    return { phase: 'Capitulation', color: 'text-red-700', bg: 'bg-red-100' };
  };

  const phase = getNUPLPhase(currentNUPL);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="h-5 w-5 text-purple-500" />
          Net Unrealized Profit/Loss (NUPL)
        </CardTitle>
        <CardDescription>
          Market sentiment indicator based on unrealized gains/losses
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <div className="text-2xl font-bold">
            {(currentNUPL * 100).toFixed(1)}%
          </div>
          <Badge className={`${phase.color} ${phase.bg}`}>
            {phase.phase}
          </Badge>
        </div>

        <PercentageChart
          data={nuplData.map(d => ({ ...d, value: d.value * 100 }))}
          type="area"
          color="#8b5cf6"
          gradientColors={["#8b5cf6", "#8b5cf620"]}
          height={300}
          showGrid={true}
          showTooltip={true}
          referenceLines={[
            { y: 75, stroke: '#ef4444', strokeDasharray: '3 3', label: 'Euphoria' },
            { y: 50, stroke: '#f97316', strokeDasharray: '3 3', label: 'Greed' },
            { y: 25, stroke: '#eab308', strokeDasharray: '3 3', label: 'Optimism' },
            { y: 0, stroke: '#666', strokeDasharray: '2 2', label: 'Neutral' },
            { y: -25, stroke: '#3b82f6', strokeDasharray: '3 3', label: 'Fear' },
            { y: -50, stroke: '#8b5cf6', strokeDasharray: '3 3', label: 'Anxiety' }
          ]}
        />

        <div className="mt-4 grid grid-cols-2 gap-2 text-xs">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              <span>Euphoria (75-100%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-orange-500 rounded"></div>
              <span>Greed (50-75%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-yellow-500 rounded"></div>
              <span>Optimism (25-50%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Hope (0-25%)</span>
            </div>
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded"></div>
              <span>Fear (0 to -25%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-purple-500 rounded"></div>
              <span>Anxiety (-25 to -50%)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-700 rounded"></div>
              <span>Capitulation (-50 to -100%)</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Realized Cap vs Market Cap Chart
function RealizedCapChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const generateCapData = () => {
    const data = [];
    for (let i = 0; i < 365; i++) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const marketCap = selectedChain.marketCap * (0.7 + Math.random() * 0.6);
      const realizedCap = marketCap * (0.6 + Math.random() * 0.3); // Realized cap typically lower

      data.push({
        time: date.toISOString().split('T')[0],
        marketCap: marketCap,
        realizedCap: realizedCap,
        ratio: marketCap / realizedCap
      });
    }
    return data.reverse();
  };

  const capData = generateCapData();
  const currentData = capData[capData.length - 1];
  const ratio = currentData?.ratio || 1;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-blue-500" />
          Realized Cap vs Market Cap
        </CardTitle>
        <CardDescription>
          Market cap vs the aggregate cost basis of all coins
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <div className="text-lg font-bold text-blue-500">
              ${(currentData?.marketCap / 1e9 || 0).toFixed(1)}B
            </div>
            <div className="text-xs text-muted-foreground">Market Cap</div>
          </div>
          <div>
            <div className="text-lg font-bold text-green-500">
              ${(currentData?.realizedCap / 1e9 || 0).toFixed(1)}B
            </div>
            <div className="text-xs text-muted-foreground">Realized Cap</div>
          </div>
        </div>

        <div className="mb-4">
          <div className="text-2xl font-bold">
            {ratio.toFixed(2)}x
          </div>
          <div className="text-xs text-muted-foreground">
            Market Cap / Realized Cap Ratio
          </div>
        </div>

        <StaticChart
          data={capData.map(d => ({ time: d.time, value: d.ratio }))}
          type="line"
          color="#3b82f6"
          height={300}
          formatValue={(value) => `${value.toFixed(2)}x`}
          formatDate={(date) => new Date(date).toLocaleDateString()}
          showGrid={true}
          showTooltip={true}
          referenceLines={[
            { y: 3, stroke: '#ef4444', strokeDasharray: '5 5', label: 'Potential Top' },
            { y: 1, stroke: '#10b981', strokeDasharray: '5 5', label: 'Potential Bottom' }
          ]}
          multipleLines={[
            { key: 'ratio', color: '#3b82f6', name: 'Market/Realized Cap Ratio' }
          ]}
        />

        <div className="mt-4 text-xs text-muted-foreground">
          <p>
            Ratio &gt; 3: Potential top | Ratio &lt; 1: Potential bottom
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

export function OnChainModels({ selectedChain, onRefresh }: OnChainModelsProps) {
  const [selectedModel, setSelectedModel] = useState('mvrv');

  const models = [
    { id: 'rainbow', name: 'Bitcoin Rainbow Chart', component: BitcoinRainbowChart, btcOnly: true },
    { id: 'realized_cap', name: 'Realized Cap vs Market Cap', component: RealizedCapChart },
    { id: 'nrpl', name: 'Net Realized Profit/Loss', component: NRPLChart },
    { id: 'nupl', name: 'Net Unrealized Profit/Loss', component: NUPLChart },
    { id: 'mvrv', name: 'MVRV & MVRV-Z Score', component: MVRVChart },
  ];

  const SelectedModelComponent = models.find(m => m.id === selectedModel)?.component || MVRVChart;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold">On-Chain Models</h3>
          <p className="text-sm text-muted-foreground">
            Advanced on-chain analysis models for {selectedChain.name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {models.map(model => (
                <SelectItem 
                  key={model.id} 
                  value={model.id}
                  disabled={model.btcOnly && selectedChain.symbol !== 'BTC'}
                >
                  {model.name}
                  {model.btcOnly && <Badge variant="outline" className="ml-2 text-xs">BTC Only</Badge>}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <SelectedModelComponent selectedChain={selectedChain} />
    </div>
  );
}
