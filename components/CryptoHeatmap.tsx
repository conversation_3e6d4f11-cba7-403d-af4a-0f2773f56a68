"use client";

import React, { useState, use<PERSON>emo, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useLazyHeatmapData } from "@/utils/lazy-real-time-data";
import { exportHeatmapData } from "@/utils/export-utils";
import { getHeatmapData } from "@/utils/mock-data-service";
import {
  Activity,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  RefreshCw,
  Maximize2,
  Download,
  Eye,
  EyeOff,
} from "lucide-react";

interface HeatmapCoin {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  change7d: number;
  marketCap: number;
  volume24h: number;
  logo?: string;
  rank: number;
}

interface CryptoHeatmapProps {
  data?: HeatmapCoin[];
  loading?: boolean;
  isActive?: boolean;
  onRefresh?: () => void;
  onCoinClick?: (coin: HeatmapCoin) => void;
}

// Optimized static data generator using centralized service
// This uses static data from mock-data-service.ts for non-real-time requirements
const generateMockHeatmapData = async (): Promise<HeatmapCoin[]> => {
  try {
    // Use centralized mock data service for static data
    console.log('📋 Fetching static heatmap data from centralized service');
    const centralizedData = await getHeatmapData();
    return centralizedData;
  } catch (error) {
    console.warn('⚠️ Centralized mock data service failed, using BASE coins fallback:', error);

    // Import BASE_COINS for emergency fallback - only use supported coins
    const { BASE_COINS } = await import('@/utils/mock-data-service');

    return BASE_COINS.map((coin, index) => ({
      symbol: coin.symbol,
      name: coin.name,
      price: Math.random() * 1000 + 0.01,
      change24h: (Math.random() - 0.5) * 20,
      change7d: (Math.random() - 0.5) * 40,
      marketCap: Math.random() * 100000000000 + 1000000,
      volume24h: Math.random() * 10000000000 + 100000,
      rank: index + 1,
    }));
  }
};

export function CryptoHeatmap({
  data,
  loading = false,
  isActive = false,
  onRefresh,
  onCoinClick,
}: CryptoHeatmapProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState<
    "change24h" | "change7d" | "marketCap" | "volume24h"
  >("change24h");
  const [displayMode, setDisplayMode] = useState<"change24h" | "change7d">(
    "change24h"
  );
  const [isExpanded, setIsExpanded] = useState(false);
  const [showLabels, setShowLabels] = useState(true);
  const [minMarketCap, setMinMarketCap] = useState<number>(0);

  // Lazy real-time data hooks - only fetch when active (lazy loading)
  const [realTimeHeatmapData, isRealTimeLoading, refreshRealTime, startRealTime, stopRealTime] =
    useLazyHeatmapData(isActive);

  // State for heatmap data
  const [heatmapData, setHeatmapData] = useState<HeatmapCoin[]>([]);
  const [isLoadingHeatmapData, setIsLoadingHeatmapData] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Load heatmap data only when component becomes active
  useEffect(() => {
    const loadHeatmapData = async () => {
      // Don't load if not active - lazy loading principle
      if (!isActive) {
        console.log('🛑 Heatmap not active, skipping data load');
        return;
      }

      setIsLoadingHeatmapData(true);
      try {
        if (realTimeHeatmapData && realTimeHeatmapData.length > 0) {
          // Convert real-time data to heatmap format
          console.log('📊 Using real-time heatmap data');
          const convertedData = realTimeHeatmapData.map((coin, index) => ({
            symbol: coin.symbol,
            name: coin.symbol, // In real implementation, this would be fetched
            price: coin.price,
            change24h: coin.change24h,
            change7d: coin.change24h * 1.5, // Mock 7d data
            marketCap: coin.marketCap,
            volume24h: coin.volume,
            rank: index + 1,
          }));
          setHeatmapData(convertedData);
        } else if (data && data.length > 0) {
          console.log('📈 Using provided heatmap data');
          setHeatmapData(data);
        } else {
          // Load from centralized mock data service (static data)
          console.log('📋 Loading static heatmap data from centralized service');
          const mockData = await generateMockHeatmapData();
          setHeatmapData(mockData);
        }
      } catch (error) {
        console.error('Failed to load heatmap data:', error);
        setHeatmapData([]);
      } finally {
        setIsLoadingHeatmapData(false);
      }
    };

    loadHeatmapData();
  }, [realTimeHeatmapData, data, isActive]); // Added isActive dependency

  // Filter and sort data
  const filteredData = useMemo(() => {
    let filtered = heatmapData.filter(
      (coin) =>
        coin.symbol.toLowerCase().includes(searchQuery.toLowerCase()) ||
        coin.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (minMarketCap > 0) {
      filtered = filtered.filter((coin) => coin.marketCap >= minMarketCap);
    }

    return filtered.sort((a, b) => Math.abs(b[sortBy]) - Math.abs(a[sortBy]));
  }, [heatmapData, searchQuery, sortBy, minMarketCap]);

  // Calculate grid dimensions
  const gridSize = isExpanded ? 8 : 6;
  const displayData = filteredData.slice(0, gridSize * gridSize);

  // Color calculation
  const getHeatmapColor = (change: number, intensity: number = 1) => {
    const absChange = Math.abs(change);
    const normalizedChange = Math.min(absChange / 10, 1) * intensity; // Normalize to 10% max change

    if (change > 0) {
      // Green for positive
      const greenIntensity = Math.floor(normalizedChange * 255);
      return {
        backgroundColor: `rgba(34, 197, 94, ${0.3 + normalizedChange * 0.7})`,
        color: normalizedChange > 0.5 ? "white" : "rgba(34, 197, 94, 1)",
      };
    } else {
      // Red for negative
      const redIntensity = Math.floor(normalizedChange * 255);
      return {
        backgroundColor: `rgba(239, 68, 68, ${0.3 + normalizedChange * 0.7})`,
        color: normalizedChange > 0.5 ? "white" : "rgba(239, 68, 68, 1)",
      };
    }
  };

  const formatNumber = (
    num: number,
    type: "currency" | "percentage" | "compact" = "compact"
  ) => {
    if (type === "currency") {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
        notation: num > 1000000 ? "compact" : "standard",
        maximumFractionDigits: num < 1 ? 4 : 2,
      }).format(num);
    }
    if (type === "percentage") {
      return `${num > 0 ? "+" : ""}${num.toFixed(2)}%`;
    }
    return new Intl.NumberFormat("en-US", {
      notation: "compact",
      maximumFractionDigits: 2,
    }).format(num);
  };

  return (
    <Card
      className={`${isExpanded ? "fixed inset-4 z-50 bg-background" : ""} transition-all duration-300`}
    >
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5 text-red-500" />
              Crypto Market Heatmap
            </CardTitle>
            <CardDescription>
              Real-time cryptocurrency performance visualization
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowLabels(!showLabels)}
            >
              {showLabels ? (
                <Eye className="h-4 w-4" />
              ) : (
                <EyeOff className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <Maximize2 className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                refreshRealTime();
                onRefresh?.();
              }}
              disabled={loading || isRealTimeLoading || isLoadingHeatmapData}
            >
              <RefreshCw
                className={`h-4 w-4 ${loading || isRealTimeLoading || isLoadingHeatmapData ? "animate-spin" : ""}`}
              />
            </Button>

            {/* Loading Status Indicator */}
            <div className="flex items-center gap-1">
              {isActive && !isRealTimeLoading && !isLoadingHeatmapData ? (
                <div
                  className="w-2 h-2 bg-green-500 rounded-full animate-pulse"
                  title="Active"
                />
              ) : isActive && (isRealTimeLoading || isLoadingHeatmapData) ? (
                <div
                  className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"
                  title="Loading"
                />
              ) : (
                <div
                  className="w-2 h-2 bg-gray-400 rounded-full"
                  title="Inactive"
                />
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => exportHeatmapData(filteredData)}
              disabled={!filteredData.length}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Controls */}
        <div className="flex flex-wrap items-center gap-4 mt-4">
          <div className="flex items-center gap-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search coins..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-48"
            />
          </div>

          <Select
            value={displayMode}
            onValueChange={(value: "change24h" | "change7d") =>
              setDisplayMode(value)
            }
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="change24h">24H Change</SelectItem>
              <SelectItem value="change7d">7D Change</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={sortBy}
            onValueChange={(value: typeof sortBy) => setSortBy(value)}
          >
            <SelectTrigger className="w-36">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="change24h">24H Change</SelectItem>
              <SelectItem value="change7d">7D Change</SelectItem>
              <SelectItem value="marketCap">Market Cap</SelectItem>
              <SelectItem value="volume24h">Volume</SelectItem>
            </SelectContent>
          </Select>

          <div className="flex items-center gap-2 text-sm">
            <span>Min Market Cap:</span>
            <Select
              value={minMarketCap.toString()}
              onValueChange={(value) => setMinMarketCap(Number(value))}
            >
              <SelectTrigger className="w-24">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="0">All</SelectItem>
                <SelectItem value="1000000">$1M</SelectItem>
                <SelectItem value="10000000">$10M</SelectItem>
                <SelectItem value="100000000">$100M</SelectItem>
                <SelectItem value="1000000000">$1B</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Legend */}
        <div className="flex items-center justify-between mt-4 text-sm">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-500 rounded-sm"></div>
              <span>Losers</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-300 rounded-sm"></div>
              <span>Neutral</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-500 rounded-sm"></div>
              <span>Gainers</span>
            </div>
          </div>
          <Badge variant="outline">
            Showing {displayData.length} of {filteredData.length} coins
          </Badge>
        </div>
      </CardHeader>

      <CardContent>
        {!isActive ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <Activity className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-muted-foreground">Heatmap will load when active</p>
              <p className="text-sm text-muted-foreground mt-1">
                Optimized for performance - data loads only when needed
              </p>
            </div>
          </div>
        ) : isLoadingHeatmapData ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2" />
              <p className="text-muted-foreground">Loading heatmap data...</p>
            </div>
          </div>
        ) : displayData.length === 0 ? (
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <Activity className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-muted-foreground">No data available</p>
              <p className="text-sm text-muted-foreground mt-1">
                Try adjusting your filters or refresh the data
              </p>
            </div>
          </div>
        ) : (
          <div
            className="grid gap-1"
            style={{
              gridTemplateColumns: `repeat(${gridSize}, 1fr)`,
              aspectRatio: "1/1",
              height: isExpanded ? "calc(100vh - 320px)" : "400px",
            }}
          >
            {displayData.map((coin) => {
            const change = coin[displayMode];
            const style = getHeatmapColor(change);

            return (
              <div
                key={coin.symbol}
                className="relative group cursor-pointer rounded-sm transition-all duration-200 hover:scale-105 hover:z-10 border border-white/20"
                style={style}
                onClick={() => onCoinClick?.(coin)}
              >
                {/* Content */}
                <div className="absolute inset-0 p-1 flex flex-col justify-between text-xs">
                  {showLabels && (
                    <>
                      <div className="font-medium truncate">{coin.symbol}</div>
                      <div className="text-xs opacity-80 truncate">
                        {coin.name}
                      </div>
                    </>
                  )}
                  <div className="font-semibold">
                    {formatNumber(change, "percentage")}
                  </div>
                </div>

                {/* Hover tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-black/90 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap">
                  <div className="font-medium">
                    {coin.name} ({coin.symbol})
                  </div>
                  <div>Price: {formatNumber(coin.price, "currency")}</div>
                  <div>24h: {formatNumber(coin.change24h, "percentage")}</div>
                  <div>7d: {formatNumber(coin.change7d, "percentage")}</div>
                  <div>
                    Market Cap: {formatNumber(coin.marketCap, "currency")}
                  </div>
                  <div>Volume: {formatNumber(coin.volume24h, "currency")}</div>
                  <div>Rank: #{coin.rank}</div>
                </div>
              </div>
            );
          })}
          </div>
        )}

        {/* Stats */}
        {!isLoadingHeatmapData && displayData.length > 0 && (
          <div className="flex justify-between items-center mt-4 text-xs text-muted-foreground">
          <div className="flex gap-4">
            <span>
              Gainers: {displayData.filter((c) => c[displayMode] > 0).length}
            </span>
            <span>
              Losers: {displayData.filter((c) => c[displayMode] < 0).length}
            </span>
            <span>
              Neutral: {displayData.filter((c) => c[displayMode] === 0).length}
            </span>
          </div>
          <span>Updated: {new Date().toLocaleTimeString()}</span>
          </div>
        )}
      </CardContent>

      {/* Close button for expanded mode */}
      {isExpanded && (
        <Button
          variant="outline"
          size="sm"
          className="absolute top-4 right-4"
          onClick={() => setIsExpanded(false)}
        >
          ×
        </Button>
      )}
    </Card>
  );
}
