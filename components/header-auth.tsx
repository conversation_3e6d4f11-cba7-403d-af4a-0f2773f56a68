"use client";

import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import { ThemeSwitcher } from "./theme-switcher";
import { Suspense } from "react";

export default function AuthButton() {
  return (
    <div className="flex gap-2">
      <Suspense fallback={<div>Loading...</div>}>
        <ThemeSwitcher />
        <Button asChild size="sm" variant={"outline"}>
          <Link href="/signin">Login or Sign Up</Link>
        </Button>
      </Suspense>
    </div>
  );
}
