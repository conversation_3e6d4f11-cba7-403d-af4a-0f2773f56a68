// Simple pane primitive to render markers when series markers aren't available
export class SimpleMarkersPrimitive {
  private markers: any[];
  private _requestUpdate?: () => void;
  private _pane: any = null;

  constructor(markers: any[] = []) {
    this.markers = markers || [];
    console.log('Created SimpleMarkersPrimitive with', markers.length, 'markers:', markers);
  }

  attached(param: { pane: any; requestUpdate: () => void }) {
    console.log('SimpleMarkersPrimitive attached:', param);
    this._requestUpdate = param.requestUpdate;
    this._pane = param.pane;
    
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }

  detached() {
    console.log('SimpleMarkersPrimitive detached');
    this._requestUpdate = undefined;
  }

  paneViews() {
    console.log('SimpleMarkersPrimitive paneViews called with', this.markers.length, 'markers');
    
    if (!this.markers || this.markers.length === 0) {
      return [];
    }
    
    return [
      {
        zOrder: () => 10, // High z-order to render over candlesticks
        renderer: (height: number, width: number) => {
          return {
            draw: (target: any) => {
              if (!target || typeof target.save !== 'function') {
                console.warn('Invalid context in SimpleMarkersPrimitive');
                return;
              }

              console.log('Drawing markers with canvas context:', { height, width, markerCount: this.markers.length });

              target.save();
              
              try {
                this.markers.forEach((marker, index) => {
                  this.drawMarker(target, marker, height, width, index);
                });
              } finally {
                target.restore();
              }
            }
          };
        }
      }
    ];
  }

  private drawMarker(ctx: any, marker: any, height: number, width: number, index: number) {
    // Try to get proper coordinate conversion from pane
    let x, y;
    
    if (this._pane && typeof this._pane.timeToCoordinate === 'function' && typeof this._pane.priceToCoordinate === 'function') {
      // Use proper coordinate conversion
      x = this._pane.timeToCoordinate(marker.time);
      
      // For markers, we need to estimate a price - use a relative position
      // Place markers further from candles for better visibility
      const estimatedPrice = marker.position === 'aboveBar' ? height * 0.1 : height * 0.9;
      y = estimatedPrice;
      
      console.log(`Marker ${index} - time: ${marker.time}, x: ${x}, y: ${y}`);
    } else {
      // Fallback to time-based coordinate mapping
      console.log('Using fallback coordinate mapping for marker', index);
      
      // Convert marker time to coordinate based on visible time range
      // This is a simplified approach - spread markers across the visible width
      x = ((marker.time - this.getMinTime()) / (this.getMaxTime() - this.getMinTime())) * width;
      y = marker.position === 'aboveBar' ? height * 0.1 : height * 0.9;
      
      console.log(`Marker ${index} fallback - time: ${marker.time}, x: ${x}, y: ${y}, timeRange: ${this.getMinTime()}-${this.getMaxTime()}`);
    }
    
    // Skip if coordinates are invalid
    if (x == null || y == null || x < 0 || x > width || y < 0 || y > height) {
      console.warn(`Invalid coordinates for marker ${index}:`, { x, y, width, height });
      return;
    }
    
    const size = 10; // Increased from 8 to 10 for better visibility
    
    // Add subtle shadow for better visibility
    ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
    ctx.shadowBlur = 3;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;
    
    ctx.fillStyle = marker.color || '#666';
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    
    // Draw marker shape
    ctx.beginPath();
    
    if (marker.shape === 'arrowUp') {
      // Triangle pointing up
      ctx.moveTo(x, y - size);
      ctx.lineTo(x - size, y + size);
      ctx.lineTo(x + size, y + size);
    } else if (marker.shape === 'arrowDown') {
      // Triangle pointing down
      ctx.moveTo(x, y + size);
      ctx.lineTo(x - size, y - size);
      ctx.lineTo(x + size, y - size);
    } else {
      // Circle for other shapes
      ctx.arc(x, y, size, 0, 2 * Math.PI);
    }
    
    ctx.closePath();
    ctx.fill();
    ctx.stroke();
    
    // Reset shadow for text
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    
    // Draw text if available - positioned further from marker
    if (marker.text) {
      ctx.fillStyle = marker.color || '#666';
      ctx.font = 'bold 11px Arial'; // Slightly larger and bold for better readability
      ctx.textAlign = 'center';
      
      // Position text further away from the marker
      const textOffset = marker.position === 'aboveBar' ? -25 : 35;
      ctx.fillText(marker.text, x, y + textOffset);
      
      // Add text background for better readability
      const textWidth = ctx.measureText(marker.text).width;
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'; // Semi-transparent white background
      ctx.fillRect(x - textWidth/2 - 3, y + textOffset - 12, textWidth + 6, 14);
      
      // Redraw text on top of background
      ctx.fillStyle = marker.color || '#666';
      ctx.fillText(marker.text, x, y + textOffset);
    }
  }

  private getMinTime(): number {
    if (this.markers.length === 0) return 0;
    return Math.min(...this.markers.map(m => m.time));
  }

  private getMaxTime(): number {
    if (this.markers.length === 0) return 0;
    return Math.max(...this.markers.map(m => m.time));
  }

  updateMarkers(newMarkers: any[]) {
    this.markers = newMarkers || [];
    if (this._requestUpdate) {
      this._requestUpdate();
    }
  }
}