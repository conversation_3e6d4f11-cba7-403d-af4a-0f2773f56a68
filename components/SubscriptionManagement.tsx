'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { CheckCircle, Crown, Star, Zap, Gift, ArrowRight } from 'lucide-react';
import { toast } from 'sonner';
import { createClient } from '@/utils/supabase/client';
import EnhancedSolanaPayment from '@/components/EnhancedSolanaPayment';

interface SubscriptionPlan {
  id: string;
  name: string;
  display_name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  sort_order: number;
  is_active: boolean;
}

interface UserSubscription {
  has_subscription: boolean;
  tier: string;
  plan_display_name?: string;
  status?: string;
  current_period_end?: string;
  cancel_at_period_end?: boolean;
  price_monthly?: number;
  price_yearly?: number;
}

const tierIcons = {
  tier0: Gift,
  tier1: Star,
  tier2: Zap,
  tier3: Crown,
  tier4: Crown,
};

const tierColors = {
  tier0: 'bg-gray-500',
  tier1: 'bg-blue-500',
  tier2: 'bg-purple-500',
  tier3: 'bg-yellow-500',
  tier4: 'bg-red-500',
};

// Expected tier display names for consistency
const tierDisplayNames = {
  tier0: 'Free',
  tier1: 'Member',
  tier2: 'Contributor', 
  tier3: 'Analyst',
  tier4: 'Baller',
};

export default function SubscriptionManagement({ isActive = true }: { isActive?: boolean }) {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [currentSubscription, setCurrentSubscription] = useState<UserSubscription | null>(null);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [loading, setLoading] = useState(true);
  const [processingPlan, setProcessingPlan] = useState<string | null>(null);
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null);
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);

  const supabase = createClient();

  useEffect(() => {
    if (isActive) {
      fetchPlans();
      fetchCurrentSubscription();
    }
  }, [isActive]);

  const fetchPlans = async () => {
    try {
      const { data, error } = await (supabase as any)
        .from('subscription_plans')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      setPlans(data || []);
    } catch (error) {
      console.error('Error fetching plans:', error);
      toast.error('Failed to load subscription plans');
    }
  };

  const fetchCurrentSubscription = async () => {
    try {
      const response = await fetch('/api/subscription');
      if (!response.ok) throw new Error('Failed to fetch subscription');
      
      const data = await response.json();
      setCurrentSubscription(data);
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (planName: string) => {
    if (planName === 'tier0') return; // Free plan
    
    // Find the selected plan
    const plan = plans.find(p => p.name === planName);
    if (!plan) return;
    
    setSelectedPlan(plan);
    setShowPaymentDialog(true);
  };

  const handleSolanaPaymentSuccess = (transactionId: string) => {
    toast.success('Subscription payment successful!');
    setShowPaymentDialog(false);
    fetchCurrentSubscription();
  };

  const handleCancelSubscription = async () => {
    try {
      const response = await fetch('/api/subscription', {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to cancel subscription');

      const result = await response.json();
      toast.success(result.message);
      fetchCurrentSubscription();
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error('Failed to cancel subscription');
    }
  };

  const calculateYearlySavings = (monthlyPrice: number, yearlyPrice: number) => {
    const monthlyCost = monthlyPrice * 12;
    const savings = monthlyCost - yearlyPrice;
    const percentage = Math.round((savings / monthlyCost) * 100);
    return { savings, percentage };
  };

  if (loading) {
    return (
      <div className="container mx-auto py-8 liquid-glass">
        <div className="grid gap-6 md:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="space-y-2">
                  {[...Array(3)].map((_, j) => (
                    <div key={j} className="h-4 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Choose Your Plan</h1>
        <p className="text-muted-foreground mb-6">
          Unlock powerful features with our subscription tiers
        </p>

        {/* Current Subscription Status */}
        {currentSubscription && currentSubscription.has_subscription && (
          <Card className="mb-6 border-primary liquid-glass">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center gap-4">
                <Badge variant="secondary" className="text-sm">
                  Current Plan: {tierDisplayNames[currentSubscription.tier as keyof typeof tierDisplayNames] || currentSubscription.plan_display_name}
                </Badge>
                {currentSubscription.cancel_at_period_end && (
                  <Badge variant="destructive">
                    Canceling at period end
                  </Badge>
                )}
              </div>
              {currentSubscription.current_period_end && (
                <p className="text-sm text-muted-foreground mt-2">
                  {currentSubscription.cancel_at_period_end ? 'Expires' : 'Renews'} on{' '}
                  {new Date(currentSubscription.current_period_end).toLocaleDateString()}
                </p>
              )}
              {currentSubscription.status === 'active' && !currentSubscription.cancel_at_period_end && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelSubscription}
                  className="mt-4"
                >
                  Cancel Subscription
                </Button>
              )}
            </CardContent>
          </Card>
        )}

        {/* Billing Cycle Toggle */}
        <Tabs value={billingCycle} onValueChange={(value) => setBillingCycle(value as 'monthly' | 'yearly')}>
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-2">
            <TabsTrigger value="monthly">Monthly</TabsTrigger>
            <TabsTrigger value="yearly">Yearly</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Subscription Plans */}
      <div className="grid gap-6 md:grid-cols-2">
        {plans.map((plan) => {
          const Icon = tierIcons[plan.name as keyof typeof tierIcons] || Star;
          const price = billingCycle === 'monthly' ? plan.price_monthly : plan.price_yearly;
          const isCurrentPlan = currentSubscription?.tier === plan.name;
          const yearlySavings = calculateYearlySavings(plan.price_monthly, plan.price_yearly);

          return (
            <Card
              key={plan.id}
              className={`relative transition-all duration-200 hover:shadow-lg ${
                isCurrentPlan ? 'border-primary ring-2 ring-primary/20' : ''
              } ${plan.name === 'tier3' || plan.name === 'tier4' ? 'border-yellow-500/50' : ''}`}
            >
              {plan.name === 'tier3' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-yellow-500 text-black">Most Popular</Badge>
                </div>
              )}

              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${tierColors[plan.name as keyof typeof tierColors]}`}>
                    <Icon className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">
                      {tierDisplayNames[plan.name as keyof typeof tierDisplayNames] || plan.display_name}
                    </CardTitle>
                    <CardDescription className="text-sm">{plan.description}</CardDescription>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <div className="mb-6">
                  {plan.name === 'tier0' ? (
                    <div className="text-3xl font-bold">Free</div>
                  ) : (
                    <div>
                      <div className="text-3xl font-bold">
                        ${price}
                        <span className="text-lg font-normal text-muted-foreground">
                          /{billingCycle === 'monthly' ? 'mo' : 'yr'}
                        </span>
                      </div>
                      {billingCycle === 'yearly' && yearlySavings.percentage > 0 && (
                        <div className="text-sm text-green-600 font-medium">
                          Save {yearlySavings.percentage}% (${yearlySavings.savings})
                        </div>
                      )}
                    </div>
                  )}
                </div>

                <div className="space-y-3 mb-6">
                  {plan.features.slice(0, 4).map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm">
                      <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                      <span>{feature.replace(/_/g, ' ').replace(/^\w/, (c) => c.toUpperCase())}</span>
                    </div>
                  ))}
                  {plan.features.length > 4 && (
                    <div className="text-sm text-muted-foreground">
                      +{plan.features.length - 4} more features
                    </div>
                  )}
                </div>

                {isCurrentPlan ? (
                  <Button className="w-full" disabled>
                    Current Plan
                  </Button>
                ) : plan.name === 'tier0' ? (
                  <Button variant="outline" className="w-full" disabled>
                    Free Forever
                  </Button>
                ) : (
                  <Button
                    className="w-full"
                    onClick={() => handleSubscribe(plan.name)}
                    disabled={processingPlan === plan.name}
                  >
                    {processingPlan === plan.name ? (
                      'Processing...'
                    ) : (
                      <>
                        Subscribe Now
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Feature Comparison */}
      <div className="mt-12">
        <h2 className="text-2xl font-bold text-center mb-8">Feature Comparison</h2>
        <Card className="liquid-glass">
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium">Features</th>
                    {plans.map((plan) => (
                      <th key={plan.id} className="text-center p-4 font-medium">
                        {tierDisplayNames[plan.name as keyof typeof tierDisplayNames] || plan.display_name}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-4">Experience System</td>
                    <td className="text-center p-4">❌</td>
                    <td className="text-center p-4">✅</td>
                    <td className="text-center p-4">✅</td>
                    <td className="text-center p-4">✅</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">OnChain Charts</td>
                    <td className="text-center p-4">❌</td>
                    <td className="text-center p-4">✅</td>
                    <td className="text-center p-4">✅</td>
                    <td className="text-center p-4">✅</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">Trading Bots</td>
                    <td className="text-center p-4">❌</td>
                    <td className="text-center p-4">❌</td>
                    <td className="text-center p-4">✅</td>
                    <td className="text-center p-4">✅</td>
                  </tr>
                  <tr className="border-b">
                    <td className="p-4">Staff Access</td>
                    <td className="text-center p-4">❌</td>
                    <td className="text-center p-4">❌</td>
                    <td className="text-center p-4">❌</td>
                    <td className="text-center p-4">✅</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden liquid-glass">
          <DialogHeader>
            <DialogTitle>
              Subscribe to {tierDisplayNames[selectedPlan?.name as keyof typeof tierDisplayNames] || selectedPlan?.display_name}
            </DialogTitle>
            <DialogDescription>
              Choose your payment method (Stripe or Solana Pay)
            </DialogDescription>
          </DialogHeader>
          
          {selectedPlan && (
            <EnhancedSolanaPayment
              recipientUserId="platform" // Platform receives subscription payments
              recipientUsername="CryptoTalks"
              paymentType="subscription"
              selectedTier={selectedPlan.name}
              billingCycle={billingCycle}
              onPaymentSuccess={handleSolanaPaymentSuccess}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
