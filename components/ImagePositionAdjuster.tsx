// components/ImagePositionAdjuster.tsx
import { useState, useRef, useEffect } from "react";
import { Button } from "./ui/button";
import { Slider } from "./ui/slider";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "./ui/dialog";
import { Label } from "./ui/label";
import { RotateCcw, Move, ZoomIn, ZoomOut } from "lucide-react";
import { cn } from "@/lib/utils";

interface ImagePositionAdjusterProps {
  imageUrl: string;
  aspectRatio: number; // width/height
  onSave: (position: { x: number; y: number; scale: number }) => void;
  onClose: () => void;
  open: boolean;
  type?: 'avatar' | 'banner';
}

export function ImagePositionAdjuster({
  imageUrl,
  aspectRatio,
  onSave,
  onClose,
  open,
  type = 'avatar',
}: ImagePositionAdjusterProps) {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [scale, setScale] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current || !imageRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const image = imageRef.current.getBoundingClientRect();

    const newX = e.clientX - dragStart.x;
    const newY = e.clientY - dragStart.y;

    // Calculate bounds
    const maxX = (image.width * scale - container.width) / 2;
    const maxY = (image.height * scale - container.height) / 2;

    // Constrain position
    const constrainedX = Math.max(-maxX, Math.min(maxX, newX));
    const constrainedY = Math.max(-maxY, Math.min(maxY, newY));

    setPosition({ x: constrainedX, y: constrainedY });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    setIsDragging(true);
    setDragStart({
      x: touch.clientX - position.x,
      y: touch.clientY - position.y,
    });
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!isDragging || !containerRef.current || !imageRef.current) return;

    const touch = e.touches[0];
    const container = containerRef.current.getBoundingClientRect();
    const image = imageRef.current.getBoundingClientRect();

    const newX = touch.clientX - dragStart.x;
    const newY = touch.clientY - dragStart.y;

    // Calculate bounds
    const maxX = (image.width * scale - container.width) / 2;
    const maxY = (image.height * scale - container.height) / 2;

    // Constrain position
    const constrainedX = Math.max(-maxX, Math.min(maxX, newX));
    const constrainedY = Math.max(-maxY, Math.min(maxY, newY));

    setPosition({ x: constrainedX, y: constrainedY });
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleMouseUp);
      window.addEventListener("touchmove", handleTouchMove);
      window.addEventListener("touchend", handleTouchEnd);
    }
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
      window.removeEventListener("touchmove", handleTouchMove);
      window.removeEventListener("touchend", handleTouchEnd);
    };
  }, [isDragging]);

  // Reset position and scale when image changes
  useEffect(() => {
    setPosition({ x: 0, y: 0 });
    setScale(1);
    setImageLoaded(false);
  }, [imageUrl]);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleReset = () => {
    setPosition({ x: 0, y: 0 });
    setScale(1);
  };

  const handleZoomIn = () => {
    setScale((prev) => Math.min(3, prev + 0.1));
  };

  const handleZoomOut = () => {
    setScale((prev) => Math.max(1, prev - 0.1));
  };

  const getImageTypeLabel = () => {
    return type === 'avatar' ? 'Profile Picture' : 'Banner Image';
  };

  const getRecommendations = () => {
    if (type === 'avatar') {
      return 'For best results, use a square image and center your face in the frame.';
    }
    return 'For best results, use a high-resolution image with a 3:1 aspect ratio.';
  };

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <DialogContent
        aria-describedby="image adjuster"
        className="sm:max-w-[600px]"
      >
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Move className="h-5 w-5" />
            Adjust {getImageTypeLabel()}
          </DialogTitle>
          <p className="text-sm text-muted-foreground mt-2">
            {getRecommendations()}
          </p>
        </DialogHeader>

        <div className="space-y-6">
          <div
            ref={containerRef}
            className={cn(
              "relative overflow-hidden bg-muted rounded-lg border-2 border-dashed border-border transition-colors",
              imageLoaded && "border-solid border-primary/20"
            )}
            style={{ aspectRatio, minHeight: "300px" }}
          >
            {!imageLoaded && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center space-y-2">
                  <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto" />
                  <p className="text-sm text-muted-foreground">Loading image...</p>
                </div>
              </div>
            )}
            <img
              ref={imageRef}
              src={imageUrl}
              alt="Adjust position"
              className={cn(
                "absolute transform-gpu cursor-move w-full h-full object-cover transition-opacity",
                isDragging && "select-none",
                imageLoaded ? "opacity-100" : "opacity-0"
              )}
              style={{
                transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
                transformOrigin: "center",
              }}
              onMouseDown={handleMouseDown}
              onTouchStart={handleTouchStart}
              onLoad={handleImageLoad}
              draggable={false}
            />
            
            {/* Crop overlay for avatar */}
            {type === 'avatar' && imageLoaded && (
              <div className="absolute inset-0 pointer-events-none">
                <div className="absolute inset-0 bg-black/40" />
                <div 
                  className="absolute bg-transparent border-2 border-white shadow-lg"
                  style={{
                    top: '50%',
                    left: '50%',
                    width: '200px',
                    height: '200px',
                    transform: 'translate(-50%, -50%)',
                    borderRadius: '50%',
                    boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.4)'
                  }}
                />
              </div>
            )}
            
            {/* Grid overlay for banner */}
            {type === 'banner' && imageLoaded && (
              <div className="absolute inset-0 pointer-events-none opacity-30">
                <div className="absolute inset-0 grid grid-cols-3 grid-rows-3">
                  {[...Array(9)].map((_, i) => (
                    <div key={i} className="border border-white/50" />
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div className="space-y-3">
              <Label className="text-sm font-medium flex items-center gap-2">
                <ZoomIn className="h-4 w-4" />
                Zoom Level
              </Label>
              <div className="space-y-2">
                <Slider
                  value={[scale]}
                  min={1}
                  max={3}
                  step={0.1}
                  onValueChange={([value]) => setScale(value)}
                  className="w-full"
                />
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>1x (Original)</span>
                  <span className="font-medium">{scale.toFixed(1)}x</span>
                  <span>3x (Maximum)</span>
                </div>
              </div>
            </div>
            
            <div className="flex items-center justify-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleZoomOut}
                disabled={scale <= 1}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleReset}
                className="gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Reset
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleZoomIn}
                disabled={scale >= 3}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="text-center">
              <p className="text-xs text-muted-foreground">
                💡 Drag the image to reposition • Use zoom controls to resize
              </p>
            </div>
          </div>

          <div className="flex justify-between items-center pt-4">
            <div className="text-xs text-muted-foreground">
              Position: X:{Math.round(position.x)}, Y:{Math.round(position.y)}
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                onClick={() => {
                  onSave({ x: position.x, y: position.y, scale });
                  onClose();
                }}
                disabled={!imageLoaded}
              >
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
