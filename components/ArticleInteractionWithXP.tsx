'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Heart, MessageCircle, Star, Zap, Gift, UserPlus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTierAccess } from '@/hooks/use-tier-access';
import { createClient } from '@/utils/supabase/client';
import { useToast } from '@/hooks/use-toast';
import EnhancedSolanaPayment from '@/components/EnhancedSolanaPayment';
import Link from 'next/link';

interface ArticleInteractionProps {
  articleId: string;
  authorId: string;
  initialLikes?: number;
  initialComments?: number;
  isLiked?: boolean;
  className?: string;
  isGuest?: boolean; // Add flag for guest users
}

export function ArticleInteraction({ 
  articleId, 
  authorId,
  initialLikes = 0,
  initialComments = 0,
  isLiked = false,
  className,
  isGuest = false
}: ArticleInteractionProps) {
  const [likes, setLikes] = useState(initialLikes);
  const [comments, setComments] = useState(initialComments);
  const [liked, setLiked] = useState(isLiked);
  const [loading, setLoading] = useState(false);
  const [experienceAwarded, setExperienceAwarded] = useState(false);
  const [showSignupModal, setShowSignupModal] = useState(false);
  
  const { userExperience, refreshUserExperience } = useTierAccess();
  const { toast } = useToast();
  const supabase = createClient();

  // Check if user has tier access for interactions
  const canInteract = isGuest ? false : (userExperience && userExperience.tier !== 'tier0');
  const hasActiveExperience = userExperience?.experience_active;

  const handleGuestAction = () => {
    setShowSignupModal(true);
  };

  const handleLike = async () => {
    if (isGuest) {
      handleGuestAction();
      return;
    }
    
    if (loading || !canInteract) return;
    
    setLoading(true);
    try {
      const { data: { user } } = await (supabase as any).auth.getUser();
      if (!user) return;

      // Prevent self-liking
      if (user.id === authorId) {
        toast({
          title: "Cannot like your own article",
          description: "You cannot give yourself experience points.",
          variant: "destructive",
        });
        return;
      }

      if (liked) {
        // Unlike
        const { error } = await (supabase as any)
          .from('likes')
          .delete()
          .eq('user_id', user.id)
          .eq('article_id', articleId);

        if (!error) {
          setLikes(prev => prev - 1);
          setLiked(false);
        }
      } else {
        // Like and potentially award experience
        const { error } = await (supabase as any)
          .from('likes')
          .insert({
            user_id: user.id,
            article_id: articleId
          });

        if (!error) {
          setLikes(prev => prev + 1);
          setLiked(true);
          
          // Check if experience was awarded (trigger handles this automatically)
          if (hasActiveExperience && !experienceAwarded) {
            setTimeout(async () => {
              await refreshUserExperience();
              
              // Show experience notification if author gains XP
              const { data: experienceCheck } = await (supabase as any)
                .from('user_experience_interactions')
                .select('experience_granted')
                .eq('giver_user_id', user.id)
                .eq('receiver_user_id', authorId)
                .eq('article_id', articleId)
                .eq('interaction_type', 'like')
                .single();

              if (experienceCheck?.experience_granted) {
                setExperienceAwarded(true);
                toast({
                  title: "Experience Awarded!",
                  description: "The author gained 5 XP from your like!",
                  duration: 3000,
                });
              }
            }, 1000);
          }
        }
      }
    } catch (error) {
      console.error('Error handling like:', error);
      toast({
        title: "Error",
        description: "Failed to update like. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleComment = () => {
    if (isGuest) {
      handleGuestAction();
      return;
    }
    
    // Navigate to article page with comment focus
    window.location.href = `/ctn/articles/${articleId}#comments`;
  };

  const handleTipSuccess = (transactionId: string) => {
    toast({
      title: "Tip sent successfully!",
      description: `Transaction: ${transactionId.slice(0, 8)}...`,
    });
  };

  return (
    <div className={cn("flex items-center gap-4", className)}>
      <div className="flex items-center gap-2">
        <Button
          variant={liked ? "default" : "outline"}
          size="sm"
          onClick={handleLike}
          disabled={loading || !canInteract}
          className={cn(
            "flex items-center gap-2",
            liked && "bg-red-500 hover:bg-red-600 text-white",
            !canInteract && "opacity-50 cursor-not-allowed"
          )}
        >
          <Heart 
            className={cn(
              "h-4 w-4",
              liked && "fill-current"
            )} 
          />
          {likes}
        </Button>
        
        {/* Experience indicator */}
        {canInteract && hasActiveExperience && !experienceAwarded && (
          <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
            <Zap className="h-3 w-3 mr-1" />
            +5 XP
          </Badge>
        )}
      </div>

      <Button
        variant="outline"
        size="sm"
        onClick={handleComment}
        disabled={!canInteract}
        className={cn(
          "flex items-center gap-2",
          !canInteract && "opacity-50 cursor-not-allowed"
        )}
      >
        <MessageCircle className="h-4 w-4" />
        {comments}
      </Button>

      {/* Tip Button */}
      <Dialog>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            disabled={!canInteract || authorId === userExperience?.user_id}
            className={cn(
              "flex items-center gap-2 text-orange-600 border-orange-200 hover:bg-orange-50",
              !canInteract && "opacity-50 cursor-not-allowed"
            )}
          >
            <Gift className="h-4 w-4" />
            Tip
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Send a Tip to Author</DialogTitle>
            <DialogDescription>
              Show your appreciation by sending a tip with cryptocurrency
            </DialogDescription>
          </DialogHeader>
          <EnhancedSolanaPayment
            recipientUserId={authorId}
            recipientUsername="Author"
            paymentType="donation"
            onPaymentSuccess={handleTipSuccess}
          />
        </DialogContent>
      </Dialog>

      {/* Tier requirement indicator */}
      {!canInteract && !isGuest && (
        <Badge variant="outline" className="text-xs text-orange-600 border-orange-200">
          <Star className="h-3 w-3 mr-1" />
          Tier 1+ Required
        </Badge>
      )}

      {/* Guest signup prompt */}
      {isGuest && (
        <Badge variant="outline" className="text-xs text-blue-600 border-blue-200">
          <UserPlus className="h-3 w-3 mr-1" />
          Sign up to interact
        </Badge>
      )}

      {/* Signup Modal for Guest Users */}
      <Dialog open={showSignupModal} onOpenChange={setShowSignupModal}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Join CryptoTalks</DialogTitle>
            <DialogDescription>
              Sign up to interact with articles, earn experience points, and join the crypto conversation!
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="text-center">
              <img
                src="/ct_logoclr.png"
                alt="CryptoTalks"
                className="w-16 h-16 mx-auto mb-4"
              />
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Heart className="h-4 w-4 text-red-500" />
                <span>Like and interact with articles</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Zap className="h-4 w-4 text-yellow-500" />
                <span>Earn experience points and level up</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Gift className="h-4 w-4 text-green-500" />
                <span>Send tips to authors with Solana Pay</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <MessageCircle className="h-4 w-4 text-blue-500" />
                <span>Join discussions and comment</span>
              </div>
            </div>

            <div className="flex gap-2">
              <Button asChild className="flex-1">
                <Link href="/signup">Sign Up</Link>
              </Button>
              <Button asChild variant="outline" className="flex-1">
                <Link href="/signin">Sign In</Link>
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Compact version for article cards
export function ArticleInteractionCompact({ 
  articleId, 
  authorId,
  initialLikes = 0,
  initialComments = 0,
  isLiked = false,
  className 
}: ArticleInteractionProps) {
  const [likes, setLikes] = useState(initialLikes);
  const [liked, setLiked] = useState(isLiked);
  const [loading, setLoading] = useState(false);
  
  const { userExperience } = useTierAccess();
  const { toast } = useToast();
  const supabase = createClient();

  const canInteract = userExperience && userExperience.tier !== 'tier0';

  const handleLike = async () => {
    if (loading || !canInteract) return;
    
    setLoading(true);
    try {
      const { data: { user } } = await (supabase as any).auth.getUser();
      if (!user) return;

      if (user.id === authorId) {
        toast({
          title: "Cannot like your own article",
          variant: "destructive",
        });
        return;
      }

      if (liked) {
        const { error } = await (supabase as any)
          .from('likes')
          .delete()
          .eq('user_id', user.id)
          .eq('article_id', articleId);

        if (!error) {
          setLikes(prev => prev - 1);
          setLiked(false);
        }
      } else {
        const { error } = await (supabase as any)
          .from('likes')
          .insert({
            user_id: user.id,
            article_id: articleId
          });

        if (!error) {
          setLikes(prev => prev + 1);
          setLiked(true);
        }
      }
    } catch (error) {
      console.error('Error handling like:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={cn("flex items-center gap-3 text-sm text-gray-600", className)}>
      <button
        onClick={handleLike}
        disabled={loading || !canInteract}
        className={cn(
          "flex items-center gap-1 hover:text-red-500 transition-colors",
          liked && "text-red-500",
          !canInteract && "opacity-50 cursor-not-allowed"
        )}
      >
        <Heart 
          className={cn(
            "h-4 w-4",
            liked && "fill-current"
          )} 
        />
        {likes}
      </button>
      
      <div className="flex items-center gap-1">
        <MessageCircle className="h-4 w-4" />
        {initialComments}
      </div>
      
      {!canInteract && (
        <Badge variant="outline" className="text-xs">
          Tier 1+
        </Badge>
      )}
    </div>
  );
}
