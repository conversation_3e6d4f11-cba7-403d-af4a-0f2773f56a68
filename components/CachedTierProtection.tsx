"use client";

import { useGlobalTier } from "@/components/GlobalTierProvider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Lock,
  Crown,
  Star,
  Shield,
  Zap,
  ArrowRight,
  LogIn,
} from "lucide-react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { useRouter } from "next/navigation";

interface CachedTierProtectionProps {
  children: React.ReactNode;
  pagePath: string;
  requiredTier?: "tier0" | "tier1" | "tier2" | "tier3" | "tier4";
  fallbackComponent?: React.ReactNode;
  className?: string;
  showBlur?: boolean;
}

const tierIcons = {
  tier1: Zap,
  tier2: Star,
  tier3: Shield,
  tier4: Crown,
};

const tierColors = {
  tier1: "bg-gradient-to-r from-yellow-400 to-orange-500",
  tier2: "bg-gradient-to-r from-purple-500 to-pink-500",
  tier3: "bg-gradient-to-r from-blue-500 to-cyan-500",
  tier4: "bg-gradient-to-r from-green-500 to-emerald-500",
};

const tierNames = {
  tier0: "Free",
  tier1: "Contributor",
  tier2: "Analyst",
  tier3: "Baller",
  tier4: "Elite",
};

export function CachedTierProtection({
  children,
  pagePath,
  requiredTier,
  fallbackComponent,
  className,
  showBlur = true,
}: CachedTierProtectionProps) {
  const { userTierData, loading, checkPageAccess } = useGlobalTier();
  const router = useRouter();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Check if user is logged in
  if (!userTierData?.isLoggedIn) {
  return (
    <div className={cn("relative h-full", className)}>
      {/* Blurred background content */}
      {showBlur && (
        <div className="filter blur-sm pointer-events-none opacity-50 h-full">
          {children}
        </div>
      )}

      {/* Login overlay */}
      <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-sm rounded-lg z-10">
        <Card className="max-w-sm w-full mx-4 shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-3">
              <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50">
                <LogIn className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <CardTitle className="flex items-center justify-center gap-2 text-lg">
              <Lock className="h-4 w-4" />
              Sign In Required
            </CardTitle>
            <CardDescription className="text-sm">
              Please sign in to access premium features and tools
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3 pt-0">
            <Button onClick={() => router.push("/signin")} className="w-full">
              <LogIn className="h-4 w-4 mr-2" />
              Sign In to Continue
            </Button>
            <div className="text-center">
              <Link
                href="/signup"
                className="text-xs text-muted-foreground hover:text-foreground transition-colors"
              >
                Don't have an account? Sign up
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
  }

  // Check tier access - use requiredTier prop if provided, otherwise fall back to page-based check
  const accessInfo = checkPageAccess(pagePath);
  const effectiveRequiredTier = requiredTier || accessInfo.requiredTier;
  
  // Convert tier numbers to check if user has access
  const tierLevels = { tier0: 0, tier1: 1, tier2: 2, tier3: 3, tier4: 4 };
  const userTierLevel = tierLevels[userTierData?.userTier as keyof typeof tierLevels] || 0;
  const requiredTierLevel = tierLevels[effectiveRequiredTier as keyof typeof tierLevels] || 0;
  
  const hasAccess = userTierLevel >= requiredTierLevel;

  // User has access - render content normally
  if (hasAccess) {
    return <>{children}</>;
  }

  // User doesn't have access - show upgrade modal
  if (fallbackComponent) {
    return <>{fallbackComponent}</>;
  }

  const RequiredIcon =
    tierIcons[effectiveRequiredTier as keyof typeof tierIcons] || Crown;
  const requiredTierColor =
    tierColors[effectiveRequiredTier as keyof typeof tierColors] ||
    "bg-gray-500";

  return (
    <div className={cn("relative h-full", className)}>
      {/* Blurred background content */}
      {showBlur && (
        <div className="filter blur-md pointer-events-none opacity-30 h-full">
          {children}
        </div>
      )}

      {/* Upgrade overlay */}
      <div className="absolute inset-0 flex items-center justify-center bg-black/30 backdrop-blur-sm rounded-lg z-10">
        <Card className="max-w-sm w-full mx-4 shadow-lg">
          <CardHeader className="text-center pb-4">
            <div className="flex justify-center mb-3">
              <div
                className={cn("p-3 rounded-full text-white shadow-lg", requiredTierColor)}
              >
                <RequiredIcon className="h-6 w-6" />
              </div>
            </div>
            <CardTitle className="flex items-center justify-center gap-2 text-lg">
              <Crown className="h-4 w-4" />
              Premium Feature
            </CardTitle>
            <CardDescription className="text-sm">
              This feature requires a{" "}
              {tierNames[effectiveRequiredTier as keyof typeof tierNames]}{" "}
              subscription or higher
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 pt-0">
            <div className="space-y-2 text-sm">
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">
                  Your current tier:
                </span>
                <Badge variant="outline" className="text-xs">
                  {tierNames[userTierData?.userTier as keyof typeof tierNames] || "Free"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-muted-foreground">
                  Required tier:
                </span>
                <Badge className={cn("text-white text-xs", requiredTierColor)}>
                  {tierNames[effectiveRequiredTier as keyof typeof tierNames]}
                </Badge>
              </div>
            </div>

            <div className="flex flex-col gap-2 pt-2">
              <Button asChild className="w-full h-9">
                <Link href="/ctn/subscription/plans">
                  <Crown className="h-4 w-4 mr-2" />
                  Upgrade to{" "}
                  {tierNames[accessInfo.requiredTier as keyof typeof tierNames]}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
              <Button variant="outline" asChild className="w-full h-8 text-xs">
                <Link href="/ctn">Back to Dashboard</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
