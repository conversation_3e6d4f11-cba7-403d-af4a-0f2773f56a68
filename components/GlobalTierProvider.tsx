'use client';

import { useEffect, useState, createContext, useContext, ReactNode } from 'react';
import { createClient } from '@/utils/supabase/client';
import { type TierAccessInfo } from '@/hooks/use-tier-access';

interface CachedUserData {
  isLoggedIn: boolean;
  userTier: string;
  userId: string | null;
  lastChecked: number;
  sessionId: string;
}

interface GlobalTierContextType {
  userTierData: CachedUserData | null;
  loading: boolean;
  refreshUserData: () => Promise<void>;
  checkPageAccess: (pagePath: string) => TierAccessInfo;
}

const GlobalTierContext = createContext<GlobalTierContextType | null>(null);

const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
const STORAGE_KEY = 'crypto_talks_user_tier_cache';

interface GlobalTierProviderProps {
  children: ReactNode;
}

// Generate session ID to invalidate cache on new sessions
const generateSessionId = () => Math.random().toString(36).substring(7);

export function GlobalTierProvider({ children }: GlobalTierProviderProps) {
  const [userTierData, setUserTierData] = useState<CachedUserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [currentSessionId] = useState(() => generateSessionId());
  const supabase = createClient();

  // Page access requirements mapping
  const pageRequirements: Record<string, string> = {
    // Main CTN routes (free access)
    '/ctn': 'tier0',
    '/ctn/search': 'tier0',
    '/ctn/hashtag': 'tier0',
    '/ctn/analytics': 'tier0',
    '/ctn/leaderboards': 'tier0',
    '/ctn/articles/publish': 'tier0',
    '/ctn/notifications': 'tier0',
    '/ctn/profile': 'tier0',
    '/ctn/settings': 'tier0',
    '/ctn/subscription': 'tier0',
    '/ctn/foryou': 'tier0',
    '/ctn/trending': 'tier0',
    '/ctn/news': 'tier0',

    // Tier 1 protected routes
    '/ctn/research': 'tier1',
    '/ctn/daily-claim': 'tier1',
    '/ctn/tools/calculator': 'tier1',
    '/ctn/tools/charts': 'tier1',
    '/ctn/tools/heatmap': 'tier1',
    '/ctn/tools/indicators': 'tier1',
    '/ctn/tools/onchain': 'tier1',
    '/ctn/tools/onchain/charts': 'tier1',
    '/ctn/tools/onchain/heatmap': 'tier1',
    '/ctn/tools/onchain-models': 'tier1',
    '/ctn/tools/comparison': 'tier1',
    '/ctn/tools/discovery': 'tier1',
    '/ctn/tools/inflation-analysis': 'tier1',
    '/ctn/charts': 'tier1',
    '/ctn/heatmap': 'tier1',

    // Tier 2 protected routes
    '/ctn/trading-bots': 'tier2',
    '/ctn/market-master': 'tier2',

    // Tier 3 protected routes
    '/ctn/tools/comparison?tab=automated': 'tier3',
    '/ctn/staff-access': 'tier3',
    '/ctn/governance': 'tier3',
    '/ctn/moderation': 'tier3',
  };

  const getTierLevel = (tier: string): number => {
    switch (tier) {
      case 'tier1': return 1;
      case 'tier2': return 2;
      case 'tier3': return 3;
      case 'tier4': return 4;
      default: return 0;
    }
  };

  // Load cached data from localStorage
  const loadCachedData = (): CachedUserData | null => {
    try {
      const cached = localStorage.getItem(STORAGE_KEY);
      if (!cached) return null;
      
      const data: CachedUserData = JSON.parse(cached);
      const now = Date.now();
      
      // Check if cache is expired or from different session
      if (
        now - data.lastChecked > CACHE_DURATION || 
        data.sessionId !== currentSessionId
      ) {
        localStorage.removeItem(STORAGE_KEY);
        return null;
      }
      
      return data;
    } catch (error) {
      console.error('Error loading cached tier data:', error);
      localStorage.removeItem(STORAGE_KEY);
      return null;
    }
  };

  // Save data to localStorage
  const saveCachedData = (data: CachedUserData) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving tier data to cache:', error);
    }
  };

  // Fetch fresh user data from database
  const fetchUserData = async (): Promise<CachedUserData> => {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) {
      return {
        isLoggedIn: false,
        userTier: 'tier0',
        userId: null,
        lastChecked: Date.now(),
        sessionId: currentSessionId
      };
    }

    // Get user's premium tier from users table
    const { data: userData } = await supabase
      .from('users')
      .select('premium_tier')
      .eq('user_id', user.id)
      .maybeSingle();

    // Get active subscription if no premium tier set
    const { data: subscription } = await supabase
      .from('user_subscriptions')
      .select('subscription_plan_id, status')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .maybeSingle();

    const userTier = userData?.premium_tier || subscription?.subscription_plan_id || 'tier0';

    const result: CachedUserData = {
      isLoggedIn: true,
      userTier,
      userId: user.id,
      lastChecked: Date.now(),
      sessionId: currentSessionId
    };

    return result;
  };

  // Initialize user data
  const initializeUserData = async () => {
    setLoading(true);
    
    // Try to load from cache first
    const cached = loadCachedData();
    if (cached) {
      console.log('✅ Using cached tier data:', cached);
      setUserTierData(cached);
      setLoading(false);
      return;
    }

    // Fetch fresh data if no valid cache
    try {
      console.log('🔄 Fetching fresh tier data...');
      const freshData = await fetchUserData();
      setUserTierData(freshData);
      saveCachedData(freshData);
      console.log('✅ Fresh tier data cached:', freshData);
    } catch (error) {
      console.error('Error fetching user tier data:', error);
      // Fallback to logged out state
      const fallbackData: CachedUserData = {
        isLoggedIn: false,
        userTier: 'tier0',
        userId: null,
        lastChecked: Date.now(),
        sessionId: currentSessionId
      };
      setUserTierData(fallbackData);
    } finally {
      setLoading(false);
    }
  };

  // Refresh user data (for manual refresh after upgrades)
  const refreshUserData = async () => {
    localStorage.removeItem(STORAGE_KEY);
    await initializeUserData();
  };

  // Check if user has access to a specific page
  const checkPageAccess = (pagePath: string): TierAccessInfo => {
    if (!userTierData) {
      return {
        hasTierAccess: false,
        userTier: 'tier0',
        requiredTier: 'tier1',
        tierLevel: 0,
        requiredLevel: 1,
        canUpgrade: true
      };
    }

    const requiredTier = pageRequirements[pagePath] || 'tier1';
    const userTierLevel = getTierLevel(userTierData.userTier);
    const requiredTierLevel = getTierLevel(requiredTier);
    const hasAccess = userTierLevel >= requiredTierLevel;

    return {
      hasTierAccess: Boolean(hasAccess),
      userTier: userTierData.userTier,
      requiredTier,
      tierLevel: userTierLevel,
      requiredLevel: requiredTierLevel,
      canUpgrade: userTierLevel < requiredTierLevel
    };
  };

  // Initialize on mount
  useEffect(() => {
    initializeUserData();
  }, []);

  // Listen for auth state changes to refresh cache
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event) => {
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT') {
        console.log('🔄 Auth state changed, refreshing tier data...');
        refreshUserData();
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  return (
    <GlobalTierContext.Provider value={{ userTierData, loading, refreshUserData, checkPageAccess }}>
      {children}
    </GlobalTierContext.Provider>
  );
}

export function useGlobalTier() {
  const context = useContext(GlobalTierContext);
  if (!context) {
    throw new Error('useGlobalTier must be used within a GlobalTierProvider');
  }
  return context;
}
