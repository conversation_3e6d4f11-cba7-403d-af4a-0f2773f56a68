"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface User {
  user_id: string;
  username: string;
  avatar_url: string | null;
  full_name: string | null;
}

interface MentionSuggestionsProps {
  isOpen: boolean;
  position: { top: number; left: number };
  query: string;
  onSelect: (user: User) => void;
  onClose: () => void;
}

function MentionSuggestions({ isOpen, position, query, onSelect, onClose }: MentionSuggestionsProps) {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const supabase = createClient();

  useEffect(() => {
    if (!isOpen || !query) {
      setUsers([]);
      return;
    }

    const searchUsers = async () => {
      setIsLoading(true);
      try {
        const { data, error } = await (supabase as any)
          .from('users')
          .select('user_id, username, avatar_url, full_name')
          .ilike('username', `%${query}%`)
          .limit(10);

        if (error) throw error;
        setUsers(data || []);
        setSelectedIndex(0);
      } catch (error) {
        console.error('Error searching users:', error);
        setUsers([]);
      } finally {
        setIsLoading(false);
      }
    };

    const timeoutId = setTimeout(searchUsers, 150);
    return () => clearTimeout(timeoutId);
  }, [query, isOpen, supabase]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => Math.min(prev + 1, users.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => Math.max(prev - 1, 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (users[selectedIndex]) {
            onSelect(users[selectedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, users, selectedIndex, onSelect, onClose]);

  if (!isOpen || users.length === 0) {
    return null;
  }

  return (
    <div
      className="absolute z-50 bg-background border border-border rounded-lg shadow-lg max-h-60 overflow-y-auto"
      style={{
        top: position.top,
        left: position.left,
        minWidth: '200px',
      }}
    >
      {isLoading ? (
        <div className="p-3 text-center text-muted-foreground">
          Loading...
        </div>
      ) : (
        <div className="py-1">
          {users.map((user, index) => (
            <button
              key={user.user_id}
              className={cn(
                "w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-accent",
                index === selectedIndex && "bg-accent"
              )}
              onClick={() => onSelect(user)}
            >
              <Avatar className="w-8 h-8">
                <AvatarImage src={user.avatar_url || ""} />
                <AvatarFallback>
                  {user.username[0]?.toUpperCase() || "U"}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="font-medium truncate">@{user.username}</p>
                {user.full_name && (
                  <p className="text-sm text-muted-foreground truncate">
                    {user.full_name}
                  </p>
                )}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

interface MentionTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  minRows?: number;
  maxRows?: number;
  disabled?: boolean;
}

export function MentionTextarea({
  value,
  onChange,
  placeholder = "Type @ to mention someone...",
  className = "",
  minRows = 3,
  maxRows = 10,
  disabled = false,
}: MentionTextareaProps) {
  const [mentionState, setMentionState] = useState({
    isOpen: false,
    query: "",
    startPosition: 0,
    position: { top: 0, left: 0 },
  });

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const calculateCaretPosition = useCallback(() => {
    if (!textareaRef.current) return { top: 0, left: 0 };

    const textarea = textareaRef.current;
    const { selectionStart } = textarea;
    
    // Create a temporary div to measure text dimensions
    const div = document.createElement('div');
    const style = getComputedStyle(textarea);
    
    // Copy textarea styles to div
    div.style.font = style.font;
    div.style.fontSize = style.fontSize;
    div.style.fontFamily = style.fontFamily;
    div.style.lineHeight = style.lineHeight;
    div.style.padding = style.padding;
    div.style.border = style.border;
    div.style.whiteSpace = 'pre-wrap';
    div.style.wordWrap = 'break-word';
    div.style.visibility = 'hidden';
    div.style.position = 'absolute';
    div.style.width = `${textarea.offsetWidth}px`;
    
    document.body.appendChild(div);
    
    // Get text up to cursor position
    const textBeforeCursor = value.substring(0, selectionStart);
    div.textContent = textBeforeCursor;
    
    // Add a span to measure cursor position
    const span = document.createElement('span');
    span.textContent = '|';
    div.appendChild(span);
    
    const rect = span.getBoundingClientRect();
    const textareaRect = textarea.getBoundingClientRect();
    
    document.body.removeChild(div);
    
    return {
      top: rect.top - textareaRect.top + textarea.scrollTop + 20,
      left: rect.left - textareaRect.left,
    };
  }, [value]);

  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    const cursorPosition = e.target.selectionStart;
    
    onChange(newValue);

    // Check for mention trigger
    const textBeforeCursor = newValue.substring(0, cursorPosition);
    const mentionMatch = textBeforeCursor.match(/@(\w*)$/);

    if (mentionMatch) {
      const [, query] = mentionMatch;
      const position = calculateCaretPosition();
      
      setMentionState({
        isOpen: true,
        query,
        startPosition: cursorPosition - mentionMatch[0].length,
        position,
      });
    } else {
      setMentionState(prev => ({ ...prev, isOpen: false }));
    }
  }, [onChange, calculateCaretPosition]);

  const handleMentionSelect = useCallback((user: User) => {
    if (!textareaRef.current) return;

    const beforeMention = value.substring(0, mentionState.startPosition);
    const afterMention = value.substring(textareaRef.current.selectionStart);
    const mentionText = `@${user.username}`;
    
    const newValue = beforeMention + mentionText + afterMention;
    onChange(newValue);

    setMentionState(prev => ({ ...prev, isOpen: false }));

    // Set cursor position after the mention
    setTimeout(() => {
      if (textareaRef.current) {
        const newPosition = mentionState.startPosition + mentionText.length;
        textareaRef.current.setSelectionRange(newPosition, newPosition);
        textareaRef.current.focus();
      }
    }, 0);
  }, [value, onChange, mentionState.startPosition]);

  const handleCloseMentions = useCallback(() => {
    setMentionState(prev => ({ ...prev, isOpen: false }));
  }, []);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      const scrollHeight = textareaRef.current.scrollHeight;
      const minHeight = minRows * 20; // Approximate line height
      const maxHeight = maxRows * 20;
      
      textareaRef.current.style.height = `${Math.min(Math.max(scrollHeight, minHeight), maxHeight)}px`;
    }
  }, [value, minRows, maxRows]);

  return (
    <div className="relative">
      <textarea
        ref={textareaRef}
        value={value}
        onChange={handleTextChange}
        placeholder={placeholder}
        disabled={disabled}
        className={cn(
          "w-full px-3 py-2 text-sm bg-background border border-input rounded-md",
          "placeholder:text-muted-foreground",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "resize-none overflow-y-auto",
          className
        )}
        style={{ minHeight: `${minRows * 20}px` }}
      />
      
      <MentionSuggestions
        isOpen={mentionState.isOpen}
        position={mentionState.position}
        query={mentionState.query}
        onSelect={handleMentionSelect}
        onClose={handleCloseMentions}
      />
    </div>
  );
}

// Utility function to parse mentions from text
export function parseMentions(text: string): string[] {
  const mentionRegex = /@(\w+)/g;
  const mentions: string[] = [];
  let match;
  
  while ((match = mentionRegex.exec(text)) !== null) {
    mentions.push(match[1]);
  }
  
  return [...new Set(mentions)]; // Remove duplicates
}

// Utility function to render text with mentions highlights
export function renderTextWithMentions(text: string): React.ReactNode[] {
  const mentionRegex = /@(\w+)/g;
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  let match: RegExpExecArray | null;

  while ((match = mentionRegex.exec(text)) !== null) {
    // Add text before mention
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }
    
    // Add mention as clickable link
    const username = match[1];
    parts.push(
      <span
        key={`mention-${match.index}`}
        className="text-primary font-medium hover:underline cursor-pointer"
        onClick={() => {
          // Navigate to user profile
          window.location.href = `/ctn/profile/${username}`;
        }}
      >
        @{username}
      </span>
    );
    
    lastIndex = match.index + match[0].length;
  }
  
  // Add remaining text
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  
  return parts;
}
