"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface TypingUser {
  user_id: string;
  username: string;
  avatar_url: string | null;
  timestamp: string;
}

interface TypingIndicatorProps {
  articleId: number;
  currentUserId: string;
  className?: string;
}

export function TypingIndicator({ articleId, currentUserId, className }: TypingIndicatorProps) {
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const supabase = createClient();

  useEffect(() => {
    // Subscribe to typing events for this article
    const channel = supabase
      .channel(`typing:article:${articleId}`)
      .on('broadcast', { event: 'typing' }, (payload) => {
        const { user_id, username, avatar_url, is_typing } = payload.payload;
        
        // Don't show our own typing indicator
        if (user_id === currentUserId) return;

        setTypingUsers(prev => {
          const filtered = prev.filter(u => u.user_id !== user_id);
          
          if (is_typing) {
            return [...filtered, {
              user_id,
              username,
              avatar_url,
              timestamp: new Date().toISOString()
            }];
          }
          
          return filtered;
        });
      })
      .subscribe();

    // Clean up old typing indicators every 5 seconds
    const interval = setInterval(() => {
      setTypingUsers(prev => {
        const fiveSecondsAgo = new Date(Date.now() - 5000).toISOString();
        return prev.filter(user => user.timestamp > fiveSecondsAgo);
      });
    }, 5000);

    return () => {
      supabase.removeChannel(channel);
      clearInterval(interval);
    };
  }, [articleId, currentUserId, supabase]);

  if (typingUsers.length === 0) {
    return null;
  }

  return (
    <div className={cn("flex items-center gap-2 text-sm text-muted-foreground", className)}>
      <div className="flex -space-x-1">
        {typingUsers.slice(0, 3).map((user) => (
          <Avatar key={user.user_id} className="w-6 h-6 border-2 border-background">
            <AvatarImage src={user.avatar_url || ""} />
            <AvatarFallback className="text-xs">
              {user.username[0]?.toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>
        ))}
      </div>
      
      <div className="flex items-center gap-1">
        <span>
          {typingUsers.length === 1
            ? `${typingUsers[0].username} is typing`
            : typingUsers.length === 2
            ? `${typingUsers[0].username} and ${typingUsers[1].username} are typing`
            : `${typingUsers[0].username} and ${typingUsers.length - 1} others are typing`}
        </span>
        
        <div className="flex gap-1">
          <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
          <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
          <div className="w-1 h-1 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
        </div>
      </div>
    </div>
  );
}

interface UseTypingIndicatorProps {
  articleId: number;
  userId: string;
  username: string;
  avatarUrl: string | null;
}

export function useTypingIndicator({ 
  articleId, 
  userId, 
  username, 
  avatarUrl 
}: UseTypingIndicatorProps) {
  const [isTyping, setIsTyping] = useState(false);
  const supabase = createClient();

  const sendTypingStatus = useCallback((typing: boolean) => {
    if (typing === isTyping) return;
    
    setIsTyping(typing);
    
    supabase
      .channel(`typing:article:${articleId}`)
      .send({
        type: 'broadcast',
        event: 'typing',
        payload: {
          user_id: userId,
          username,
          avatar_url: avatarUrl,
          is_typing: typing
        }
      });
  }, [articleId, userId, username, avatarUrl, isTyping, supabase]);

  const startTyping = useCallback(() => {
    sendTypingStatus(true);
  }, [sendTypingStatus]);

  const stopTyping = useCallback(() => {
    sendTypingStatus(false);
  }, [sendTypingStatus]);

  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (isTyping) {
      // Auto-stop typing after 3 seconds of inactivity
      timeout = setTimeout(() => {
        stopTyping();
      }, 3000);
    }

    return () => {
      clearTimeout(timeout);
    };
  }, [isTyping, stopTyping]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (isTyping) {
        sendTypingStatus(false);
      }
    };
  }, [isTyping, sendTypingStatus]);

  return {
    startTyping,
    stopTyping,
    isTyping
  };
}
