import { Sidebar } from "../ui/sidebar";
import { Skeleton } from "../ui/skeleton";

// components/skeletons/SidebarSkeleton.tsx
export function SidebarSkeleton() {
  return (
    <Sidebar
      side="right"
      className="bg-secondary text-foreground border-r border-zinc-800"
    >
      <div className="p-4 space-y-4">
        <Skeleton className="h-10 w-full" />
        <div className="space-y-2">
          <Skeleton className="h-6 w-3/4" />
          <div className="flex flex-wrap gap-2">
            {[1, 2, 3, 4, 5].map((i) => (
              <Skeleton key={i} className="h-6 w-16" />
            ))}
          </div>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-6 w-3/4" />
          {[1, 2, 3, 4, 5].map((i) => (
            <Skeleton key={i} className="h-4 w-full" />
          ))}
        </div>
      </div>
    </Sidebar>
  );
}
