"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ArticleCard } from "./ArticleCard";
import { LoadingSkeleton } from "./LoadingSkeleton";
import { Sparkles, TrendingUp, Users, Hash } from "lucide-react";
import { Article, UserProfile } from "@/types";

interface RecommendationEngineProps {
  userProfile: UserProfile;
  currentArticleId?: number;
}

interface RecommendedArticle extends Article {
  recommendation_score: number;
  recommendation_reason: string;
}

interface TrendingTopic {
  tag_name: string;
  usage_count: number;
  recent_usage_count: number;
}

export default function RecommendationEngine({ 
  userProfile, 
  currentArticleId 
}: RecommendationEngineProps) {
  const [recommendations, setRecommendations] = useState<RecommendedArticle[]>([]);
  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"foryou" | "trending" | "following">("foryou");

  const supabase = createClient();

  const loadRecommendations = useCallback(async () => {
    try {
      setIsLoading(true);

      // Get article recommendations
      const { data: recommendationsData, error: recError } = await (supabase as any).rpc(
        "get_article_recommendations",
        {
          p_user_id: userProfile.user_id,
          p_limit: 10,
          p_exclude_article_id: currentArticleId || null,
        }
      );

      if (recError) {
        console.error("Error loading recommendations:", recError);
      } else {
        setRecommendations(recommendationsData || []);
      }

      // Get trending topics
      const { data: trendingData, error: trendError } = await (supabase as any).rpc(
        "get_trending_tags",
        {
          p_limit: 8,
          p_days: 7,
        }
      );

      if (trendError) {
        console.error("Error loading trending topics:", trendError);
      } else {
        setTrendingTopics(trendingData || []);
      }
    } catch (error) {
      console.error("Error loading recommendations:", error);
    } finally {
      setIsLoading(false);
    }
  }, [userProfile.user_id, currentArticleId, supabase]);

  useEffect(() => {
    loadRecommendations();
  }, [loadRecommendations]);

  const getRecommendationReason = (score: number) => {
    if (score >= 10) return "From people you follow";
    if (score >= 5) return "Based on your interests";
    if (score >= 2) return "Trending in your topics";
    return "Discover something new";
  };

  const getRecommendationIcon = (reason: string) => {
    if (reason.includes("follow")) return <Users className="h-4 w-4" />;
    if (reason.includes("interests")) return <Sparkles className="h-4 w-4" />;
    if (reason.includes("Trending")) return <TrendingUp className="h-4 w-4" />;
    return <Hash className="h-4 w-4" />;
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <LoadingSkeleton />
        <LoadingSkeleton />
        <LoadingSkeleton />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Recommendation Tabs */}
      <div className="flex space-x-1 bg-muted p-1 rounded-lg">
        <Button
          variant={activeTab === "foryou" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("foryou")}
          className="flex-1"
        >
          <Sparkles className="h-4 w-4 mr-2" />
          For You
        </Button>
        <Button
          variant={activeTab === "trending" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("trending")}
          className="flex-1"
        >
          <TrendingUp className="h-4 w-4 mr-2" />
          Trending
        </Button>
        <Button
          variant={activeTab === "following" ? "default" : "ghost"}
          size="sm"
          onClick={() => setActiveTab("following")}
          className="flex-1"
        >
          <Users className="h-4 w-4 mr-2" />
          Following
        </Button>
      </div>

      {/* Trending Topics */}
      {trendingTopics.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-sm">
              <TrendingUp className="h-4 w-4 mr-2" />
              Trending Topics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {trendingTopics.map((topic) => (
                <Badge
                  key={topic.tag_name}
                  variant="secondary"
                  className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                >
                  #{topic.tag_name}
                  <span className="ml-1 text-xs opacity-70">
                    {topic.recent_usage_count}
                  </span>
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {activeTab === "foryou" && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center">
            <Sparkles className="h-5 w-5 mr-2" />
            Recommended for You
          </h3>
          
          {recommendations.length === 0 ? (
            <Card>
              <CardContent className="py-8 text-center">
                <Sparkles className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">
                  No recommendations yet. Start following people and engaging with articles to get personalized suggestions.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {recommendations.map((article) => (
                <div key={article.id} className="space-y-2">
                  <div className="flex items-center text-sm text-muted-foreground">
                    {getRecommendationIcon(getRecommendationReason(article.recommendation_score))}
                    <span className="ml-2">
                      {getRecommendationReason(article.recommendation_score)}
                    </span>
                    <Badge variant="outline" className="ml-auto">
                      {Math.round(article.recommendation_score)} pts
                    </Badge>
                  </div>
                  <ArticleCard article={article} userProfile={userProfile} />
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Trending Articles */}
      {activeTab === "trending" && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Trending Articles
          </h3>
          
          <TrendingArticles userProfile={userProfile} />
        </div>
      )}

      {/* Following Articles */}
      {activeTab === "following" && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center">
            <Users className="h-5 w-5 mr-2" />
            From People You Follow
          </h3>
          
          <FollowingArticles userProfile={userProfile} />
        </div>
      )}
    </div>
  );
}

// Component for trending articles
function TrendingArticles({ userProfile }: { userProfile: UserProfile }) {
  const [articles, setArticles] = useState<Article[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const loadTrendingArticles = async () => {
      try {
        const { data, error } = await (supabase as any).rpc("get_trending_articles", {
          p_limit: 10,
          p_days: 7,
        });

        if (error) {
          console.error("Error loading trending articles:", error);
        } else {
          setArticles(data || []);
        }
      } catch (error) {
        console.error("Error loading trending articles:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTrendingArticles();
  }, [supabase]);

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="space-y-4">
      {articles.map((article) => (
        <ArticleCard key={article.id} article={article} userProfile={userProfile} />
      ))}
    </div>
  );
}

// Component for following articles
function FollowingArticles({ userProfile }: { userProfile: UserProfile }) {
  const [articles, setArticles] = useState<Article[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const supabase = createClient();

  useEffect(() => {
    const loadFollowingArticles = async () => {
      try {
        const { data, error } = await (supabase as any).rpc("get_followed_articles", {
          p_user_id: userProfile.user_id,
          p_limit: 10,
        });

        if (error) {
          console.error("Error loading following articles:", error);
        } else {
          setArticles(data || []);
        }
      } catch (error) {
        console.error("Error loading following articles:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadFollowingArticles();
  }, [userProfile.user_id, supabase]);

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="space-y-4">
      {articles.map((article) => (
        <ArticleCard key={article.id} article={article} userProfile={userProfile} />
      ))}
    </div>
  );
}
