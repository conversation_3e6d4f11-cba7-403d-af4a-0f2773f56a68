"use client";

import React, { useState, useCallback } from "react";
import { Static<PERSON><PERSON> } from "./StaticChart";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useLazyChartData } from "@/utils/lazy-real-time-data";
import { exportChartData } from "@/utils/export-utils";
import {
  Calendar,
  Download,
  Maximize2,
  Minimize2,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus,
} from "lucide-react";

interface ChartDataPoint {
  time: string;
  value: number;
}

interface InteractiveMetricChartProps {
  title: string;
  description: string;
  data: ChartDataPoint[];
  color: string;
  icon: React.ReactNode;
  unit?: string;
  change24h?: number;
  loading?: boolean;
  isActive?: boolean; // For lazy loading
  onTimeRangeChange?: (range: string) => void;
  onRefresh?: () => void;
  onExport?: () => void;
}

const TIME_RANGES = [
  { value: "24h", label: "24H" },
  { value: "7d", label: "7D" },
  { value: "30d", label: "30D" },
  { value: "90d", label: "90D" },
  { value: "1y", label: "1Y" },
  { value: "all", label: "ALL" },
];

export function InteractiveMetricChart({
  title,
  description,
  data,
  color,
  icon,
  unit = "",
  change24h = 0,
  loading = false,
  isActive = false,
  onTimeRangeChange,
  onRefresh,
  onExport,
}: InteractiveMetricChartProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState("7d");

  // Real-time data hooks - only when active for lazy loading
  const [realTimeData, isRealTimeLoading, refreshRealTime] =
    useLazyChartData(isActive);
  const [isConnected, setIsConnected] = useState(true); // Simple connection state

  // Generate mock historical data if not provided - now using centralized service
  const generateMockData = async (range: string): Promise<ChartDataPoint[]> => {
    try {
      // Try to get realistic base value from centralized service
      const { getAllCoinsData } = await import("@/utils/mock-data-service");
      const centralizedData = await getAllCoinsData();
      const baseValue =
        centralizedData.length > 0
          ? centralizedData[0].price
          : data.length > 0
            ? data[data.length - 1].value
            : 1000;

      const now = new Date();
      const points = range === "24h" ? 24 : range === "7d" ? 168 : 720; // 24h, 7d, 30d
      const interval =
        range === "24h" ? 3600000 : range === "7d" ? 3600000 : 3600000; // 1 hour intervals

      const mockData: ChartDataPoint[] = [];

      for (let i = points; i >= 0; i--) {
        const time = new Date(now.getTime() - i * interval);
        const volatility = 0.02; // 2% volatility
        const trend = Math.sin(i / 10) * 0.1; // Slight trending
        const randomChange = (Math.random() - 0.5) * volatility;
        const value = baseValue * (1 + trend + randomChange);

        mockData.push({
          time: time.toISOString().split("T")[0],
          value: Math.max(0, value),
        });
      }

      return mockData;
    } catch (error) {
      console.warn(
        "⚠️ Centralized mock service failed for chart data, using legacy fallback:",
        error
      );

      // Legacy fallback
      const now = new Date();
      const points = range === "24h" ? 24 : range === "7d" ? 168 : 720;
      const interval = 3600000; // 1 hour intervals
      const baseValue = data.length > 0 ? data[data.length - 1].value : 1000;

      const mockData: ChartDataPoint[] = [];
      for (let i = points; i >= 0; i--) {
        const time = new Date(now.getTime() - i * interval);
        const volatility = 0.02;
        const trend = Math.sin(i / 10) * 0.1;
        const randomChange = (Math.random() - 0.5) * volatility;
        const value = baseValue * (1 + trend + randomChange);

        mockData.push({
          time: time.toISOString().split("T")[0],
          value: Math.max(0, value),
        });
      }

      return mockData;
    }
  };

  // Use real-time data if available, otherwise fallback to provided data or mock data
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);

  React.useEffect(() => {
    const loadChartData = async () => {
      // Only load data when component is active (lazy loading)
      if (!isActive) {
        console.log("📈 Chart not active, skipping data load");
        return;
      }

      if (realTimeData && realTimeData.length > 0) {
        // Convert real-time data to chart format
        console.log("📊 Using real-time chart data");
        const convertedData = realTimeData.map((point: any) => ({
          time: new Date(point.time).toISOString().split("T")[0],
          value: point.close,
        }));
        setChartData(convertedData);
      } else if (data.length > 0) {
        console.log("📈 Using provided chart data");
        setChartData(data);
      } else {
        // Generate mock data asynchronously using static data
        console.log("📋 Loading static chart data from centralized service");
        const mockData = await generateMockData(selectedTimeRange);
        setChartData(mockData);
      }
    };

    loadChartData();
  }, [realTimeData, data, selectedTimeRange, isActive]);

  // Format value based on unit
  const formatValue = useCallback((value: number) => {
    if (unit === "$") {
      return `$${value.toLocaleString(undefined, { maximumFractionDigits: 2 })}`;
    } else if (unit === "%") {
      return `${value.toFixed(2)}%`;
    }
    return `${value.toLocaleString(undefined, { maximumFractionDigits: 2 })}${unit}`;
  }, [unit]);

  const handleTimeRangeChange = useCallback(
    (range: string) => {
      setSelectedTimeRange(range);
      onTimeRangeChange?.(range);
    },
    [onTimeRangeChange]
  );

  const getChangeColor = useCallback((change: number) => {
    if (change > 0) return "text-green-600";
    if (change < 0) return "text-red-600";
    return "text-gray-600";
  }, []);

  const getChangeIcon = useCallback((change: number) => {
    if (change > 0) return <TrendingUp className="h-3 w-3" />;
    if (change < 0) return <TrendingDown className="h-3 w-3" />;
    return <Minus className="h-3 w-3" />;
  }, []);

  const currentValue = chartData[chartData.length - 1]?.value || 0;

  // Show placeholder when not active
  if (!isActive) {
    return (
      <Card className="transition-all duration-300">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {icon}
              <div>
                <CardTitle className="text-base">{title}</CardTitle>
                <CardDescription className="text-sm">
                  {description}
                </CardDescription>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center justify-center h-48 text-muted-foreground">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 mx-auto mb-2" />
              <p>Chart will load when active</p>
              <p className="text-xs mt-1">Optimized for performance</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className={`${isExpanded ? "col-span-full" : ""} transition-all duration-300`}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {icon}
            <div>
              <CardTitle className="text-base">{title}</CardTitle>
              <CardDescription className="text-sm">
                {description}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                refreshRealTime();
                onRefresh?.();
              }}
              disabled={loading || isRealTimeLoading}
            >
              <RefreshCw
                className={`h-4 w-4 ${loading || isRealTimeLoading ? "animate-spin" : ""}`}
              />
            </Button>

            {/* Connection Status Indicator */}
            <div className="flex items-center gap-1">
              {isConnected ? (
                <div
                  className="w-2 h-2 bg-green-500 rounded-full animate-pulse"
                  title="Connected"
                />
              ) : (
                <div
                  className="w-2 h-2 bg-red-500 rounded-full"
                  title="Disconnected"
                />
              )}
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                exportChartData(chartData, title);
                onExport?.();
              }}
              disabled={!chartData.length}
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Current Value and Change */}
        <div className="flex items-center justify-between mt-2">
          <div>
            <div className="text-2xl font-bold">
              {unit === "$" && "$"}
              {currentValue.toLocaleString(undefined, {
                maximumFractionDigits: 2,
              })}
              {unit !== "$" && unit}
            </div>
            {change24h !== undefined && (
              <div
                className={`flex items-center gap-1 text-sm ${getChangeColor(change24h)}`}
              >
                {getChangeIcon(change24h)}
                <span>
                  {change24h > 0 ? "+" : ""}
                  {change24h.toFixed(2)}% (24h)
                </span>
              </div>
            )}
          </div>

          {/* Time Range Selector */}
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div className="flex gap-1">
              {TIME_RANGES.map((range) => (
                <Button
                  key={range.value}
                  variant={
                    selectedTimeRange === range.value ? "default" : "ghost"
                  }
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={() => handleTimeRangeChange(range.value)}
                >
                  {range.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {chartData.length > 0 ? (
          <StaticChart
            data={chartData}
            type="line"
            color={color}
            height={isExpanded ? 400 : 200}
            formatValue={formatValue}
            formatDate={(date) => new Date(date).toLocaleDateString()}
            showGrid={true}
            showTooltip={true}
          />
        ) : (
          <div className="flex items-center justify-center h-48 text-muted-foreground">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 mx-auto mb-2 animate-spin" />
              <p>Loading chart data...</p>
            </div>
          </div>
        )}

        {/* Chart Stats */}
        <div className="flex justify-between items-center mt-2 text-xs text-muted-foreground">
          <span>Data points: {chartData.length}</span>
          <span>Updated: {new Date().toLocaleTimeString()}</span>
        </div>
      </CardContent>
    </Card>
  );
}

// Chart Container for multiple metrics
interface MetricChartsGridProps {
  metrics: Array<{
    id: string;
    title: string;
    description: string;
    data: ChartDataPoint[];
    color: string;
    icon: React.ReactNode;
    unit?: string;
    change24h?: number;
  }>;
  onTimeRangeChange?: (metricId: string, range: string) => void;
  onRefresh?: (metricId: string) => void;
  onExport?: (metricId: string) => void;
}

export function MetricChartsGrid({
  metrics,
  onTimeRangeChange,
  onRefresh,
  onExport,
}: MetricChartsGridProps) {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {metrics.map((metric) => (
        <InteractiveMetricChart
          key={metric.id}
          title={metric.title}
          description={metric.description}
          data={metric.data}
          color={metric.color}
          icon={metric.icon}
          unit={metric.unit}
          change24h={metric.change24h}
          onTimeRangeChange={(range) => onTimeRangeChange?.(metric.id, range)}
          onRefresh={() => onRefresh?.(metric.id)}
          onExport={() => onExport?.(metric.id)}
        />
      ))}
    </div>
  );
}
