"use client";

import { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { ArticleCardProps } from "@/types/articleTypes";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Heart,
  MessageSquare,
  Share2,
  Bookmark,
  Loader2,
  Clock,
} from "lucide-react";
import { AuthorProfileDialog } from "./AuthorProfileDialog";
import { estimateReadTime, formatTimeAgo } from "@/utils/articleUtils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { createClient } from "@/utils/supabase/client";
import { toast } from "@/hooks/use-toast";

export function ArticleCard({
  article,
  userProfile,
  onLike,
  onBookmark,
  onShare,
  onError,
  onTagClick,
  className = "",
}: ArticleCardProps) {
  const [isAuthorProfileOpen, setIsAuthorProfileOpen] = useState(false);
  const [isLiking, setIsLiking] = useState(false);
  const [isBookmarking, setIsBookmarking] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const supabase = createClient();

  const readTime = estimateReadTime(article.content || "");

  const handleLikeClick = async () => {
    if (!userProfile) {
      toast({
        title: "Authentication required",
        description: "Please sign in to like articles",
        variant: "destructive",
      });
      return;
    }

    if (isLiking) return;
    setIsLiking(true);

    try {
      await onLike?.(article.id, article.is_liked!, userProfile.user_id);
    } catch (error) {
      console.error("Error handling like:", error);
      onError?.(error as Error);
      toast({
        title: "Error",
        description: "Failed to update like status",
        variant: "destructive",
      });
    } finally {
      setIsLiking(false);
    }
  };

  const handleBookmarkClick = async () => {
    if (!userProfile) {
      toast({
        title: "Authentication required",
        description: "Please sign in to bookmark articles",
        variant: "destructive",
      });
      return;
    }

    if (isBookmarking) return;
    setIsBookmarking(true);

    try {
      await onBookmark?.(article.id);
    } catch (error) {
      console.error("Error handling bookmark:", error);
      onError?.(error as Error);
      toast({
        title: "Error",
        description: "Failed to update bookmark status",
        variant: "destructive",
      });
    } finally {
      setIsBookmarking(false);
    }
  };

  const handleShareClick = async () => {
    if (isSharing) return;
    setIsSharing(true);

    try {
      await onShare?.(article);

      // Track share interaction
      await supabase.rpc("track_article_share", {
        p_article_id: article.id,
        p_user_id: userProfile?.user_id,
      });
    } catch (error) {
      console.error("Error sharing article:", error);
      onError?.(error as Error);
      toast({
        title: "Error",
        description: "Failed to share article",
        variant: "destructive",
      });
    } finally {
      setIsSharing(false);
    }
  };

  // Keyboard navigation
  useEffect(() => {
    const card = cardRef.current;
    if (!card) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (document.activeElement !== card) return;

      switch (e.key) {
        case "Enter":
          window.location.href = `/ctn/articles/${article.id}`;
          break;
        case "l":
          handleLikeClick();
          break;
        case "b":
          handleBookmarkClick();
          break;
        case "s":
          handleShareClick();
          break;
      }
    };

    card.addEventListener("keydown", handleKeyDown);
    return () => card.removeEventListener("keydown", handleKeyDown);
  }, [article.id]);

  return (
    <TooltipProvider>
      <div
      ref={cardRef}
      tabIndex={0}
      className={`p-4 border rounded-md shadow-2xs hover:shadow-md transition-all focus:ring-2 focus:ring-primary/50 ${className}`}
    >
      {/* Article header */}
      <div className="flex justify-between items-center mb-4">
        <div
          className="flex items-center space-x-2 cursor-pointer"
          onClick={() => setIsAuthorProfileOpen(true)}
        >
          <Avatar>
            <AvatarImage
              src={article.author_avatar || "/default-avatar.jpg"}
              alt={article.author || "Unknown Author"}
            />
            <AvatarFallback>
              {article.author?.[0]?.toUpperCase() || "U"}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-semibold hover:underline">
              {article.author || "Unknown Author"}
            </p>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Tooltip>
                <TooltipTrigger>
                  <span>
                    {formatTimeAgo(
                      article.timestamp || article.created_at || ""
                    )}
                  </span>
                </TooltipTrigger>
                <TooltipContent>
                  {new Date(
                    article.timestamp || article.created_at || ""
                  ).toLocaleString()}
                </TooltipContent>
              </Tooltip>
              <span>·</span>
              <div className="flex items-center gap-1">
                <Clock className="w-3 h-3" />
                <span>{readTime} min read</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Tags */}
      {article.tags && article.tags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-3">
          {article.tags.map((tag) => (
            <Badge
              key={tag}
              variant="secondary"
              className="text-xs cursor-pointer hover:bg-accent/50"
              onClick={(e) => {
                e.preventDefault(); // Prevent link navigation if inside a Link
                onTagClick?.(tag);
              }}
            >
              #{tag}
            </Badge>
          ))}
        </div>
      )}

      {/* Article content */}
      <Link href={`/ctn/articles/${article.id}`} className="block group">
        <h3 className="text-2xl font-semibold text-accent group-hover:underline mb-2">
          {article.title}
        </h3>

        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1 space-y-2">
            <p className="line-clamp-3 text-ellipsis overflow-hidden">
              {article.content || "No content available"}
            </p>
          </div>
          {article.image_url && (
            <img
              src={article.image_url}
              alt={article.title || "Article image"}
              className="w-32 h-32 object-cover rounded-lg"
              loading="lazy"
            />
          )}
        </div>
      </Link>
      {/* Action buttons */}
      <div className="flex items-center space-x-4 mt-4">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLikeClick}
              disabled={isLiking}
              className="group"
            >
              {isLiking ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Heart
                  className={`w-4 h-4 ${
                    article.is_liked
                      ? "fill-red-500 text-red-500"
                      : "group-hover:text-red-500"
                  }`}
                />
              )}
              <span className="ml-1">{article.likes ?? 0}</span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {article.is_liked ? "Unlike" : "Like"} (Press 'L')
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBookmarkClick}
              disabled={isBookmarking}
            >
              {isBookmarking ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Bookmark
                  className={`w-4 h-4 ${
                    article.is_bookmarked ? "fill-current" : ""
                  }`}
                />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {article.is_bookmarked ? "Remove Bookmark" : "Bookmark"} (Press 'B')
          </TooltipContent>
        </Tooltip>

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShareClick}
              disabled={isSharing}
            >
              {isSharing ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Share2 className="w-4 h-4" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>Share (Press 'S')</TooltipContent>
        </Tooltip>

        <Link href={`/ctn/articles/${article.id}#comments`}>
          <Button variant="ghost" size="sm" className="gap-2">
            <MessageSquare className="w-4 h-4" />
            <span>{article.comments_count ?? 0}</span>
          </Button>
        </Link>
      </div>

      {/* Author Profile Dialog */}
      {article.author_id && (
        <AuthorProfileDialog
          authorId={article.author_id}
          isOpen={isAuthorProfileOpen}
          onClose={() => setIsAuthorProfileOpen(false)}
          currentUserId={userProfile?.user_id}
        />
      )}
      </div>
    </TooltipProvider>
  );
}
