// components/FormMessage.tsx
export interface Message {
  success?: string;
  error?: string;
  message?: string;
}

export function FormMessage({ message }: { message: Message }) {
  // Type guards to narrow down the specific message type
  if ("success" in message) {
    return (
      <div className="text-foreground border-l-2 border-foreground px-4">
        {message.success}
      </div>
    );
  }

  if ("error" in message) {
    return (
      <div className="text-destructive-foreground border-l-2 border-destructive-foreground px-4">
        {message.error}
      </div>
    );
  }

  if ("message" in message) {
    return (
      <div className="text-foreground border-l-2 px-4">{message.message}</div>
    );
  }

  return null; // Return null if none of the above conditions are met
}
