"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { InteractiveMetricChart } from '@/components/InteractiveMetricChart';
import { 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  Percent,
  ArrowUp,
  ArrowDown,
  Minus,
  RefreshCw,
  Download
} from 'lucide-react';

interface ChainMetrics {
  name: string;
  symbol: string;
  inflation: { mom: number; yoy: number };
  circulatingSupply: number;
  price: number;
}

interface InflationChartsProps {
  selectedChain: ChainMetrics;
  onRefresh?: () => void;
}

// Month-over-Month Inflation Chart
function MoMInflationChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const generateMoMData = () => {
    const data = [];
    const currentMoM = selectedChain.inflation.mom;
    
    for (let i = 0; i < 12; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      
      // Generate realistic MoM inflation data with some variation
      const variation = (Math.random() - 0.5) * 0.5;
      const momInflation = currentMoM + variation;
      
      data.push({
        time: date.toISOString().split('T')[0],
        value: momInflation
      });
    }
    
    return data.reverse();
  };

  const momData = generateMoMData();
  const currentMoM = selectedChain.inflation.mom;
  const isPositive = currentMoM > 0;
  const trend = momData.length > 1 ? momData[momData.length - 1].value - momData[momData.length - 2].value : 0;

  const getInflationLevel = (value: number) => {
    if (Math.abs(value) < 0.5) return { level: 'Low', color: 'text-green-500' };
    if (Math.abs(value) < 2) return { level: 'Moderate', color: 'text-yellow-500' };
    return { level: 'High', color: 'text-red-500' };
  };

  const inflationLevel = getInflationLevel(currentMoM);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5 text-blue-500" />
          Month-over-Month Inflation
        </CardTitle>
        <CardDescription>
          Monthly inflation rate for {selectedChain.name} ({selectedChain.symbol})
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <div className={`text-2xl font-bold ${isPositive ? 'text-red-500' : 'text-green-500'}`}>
              {isPositive ? '+' : ''}{currentMoM.toFixed(2)}%
            </div>
            <div className="text-xs text-muted-foreground">Current MoM Rate</div>
          </div>
          <div>
            <Badge className={inflationLevel.color}>
              {inflationLevel.level} Inflation
            </Badge>
            <div className="flex items-center gap-1 mt-1">
              {trend > 0 ? (
                <ArrowUp className="h-3 w-3 text-red-500" />
              ) : trend < 0 ? (
                <ArrowDown className="h-3 w-3 text-green-500" />
              ) : (
                <Minus className="h-3 w-3 text-gray-500" />
              )}
              <span className="text-xs text-muted-foreground">
                {Math.abs(trend).toFixed(2)}% vs last month
              </span>
            </div>
          </div>
        </div>
        
        <InteractiveMetricChart
          title="MoM Inflation Rate"
          description="Monthly inflation rate over the past 12 months"
          data={momData}
          color={isPositive ? "#ef4444" : "#10b981"}
          icon={<Calendar className="h-5 w-5" />}
          unit="%"
          change24h={trend}
        />
        
        <div className="mt-4 text-xs text-muted-foreground">
          <p>
            Positive values indicate inflationary pressure, negative values indicate deflationary pressure.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// Year-over-Year Inflation Chart
function YoYInflationChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const generateYoYData = () => {
    const data = [];
    const currentYoY = selectedChain.inflation.yoy;
    
    for (let i = 0; i < 24; i++) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      
      // Generate realistic YoY inflation data with gradual changes
      const variation = (Math.random() - 0.5) * 2;
      const yoyInflation = currentYoY + variation;
      
      data.push({
        time: date.toISOString().split('T')[0],
        value: yoyInflation
      });
    }
    
    return data.reverse();
  };

  const yoyData = generateYoYData();
  const currentYoY = selectedChain.inflation.yoy;
  const isPositive = currentYoY > 0;
  const trend = yoyData.length > 1 ? yoyData[yoyData.length - 1].value - yoyData[yoyData.length - 2].value : 0;

  // Calculate inflation impact on supply
  const annualSupplyIncrease = (selectedChain.circulatingSupply * currentYoY) / 100;
  const inflationValueUSD = annualSupplyIncrease * selectedChain.price;

  const getYoYInflationLevel = (value: number) => {
    if (Math.abs(value) < 2) return { level: 'Low', color: 'text-green-500', bg: 'bg-green-50' };
    if (Math.abs(value) < 5) return { level: 'Moderate', color: 'text-yellow-500', bg: 'bg-yellow-50' };
    if (Math.abs(value) < 10) return { level: 'High', color: 'text-orange-500', bg: 'bg-orange-50' };
    return { level: 'Very High', color: 'text-red-500', bg: 'bg-red-50' };
  };

  const inflationLevel = getYoYInflationLevel(currentYoY);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-purple-500" />
          Year-over-Year Inflation
        </CardTitle>
        <CardDescription>
          Annual inflation rate and supply impact for {selectedChain.name}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <div className={`text-2xl font-bold ${isPositive ? 'text-red-500' : 'text-green-500'}`}>
              {isPositive ? '+' : ''}{currentYoY.toFixed(2)}%
            </div>
            <div className="text-xs text-muted-foreground">Annual Rate</div>
          </div>
          <div>
            <Badge className={`${inflationLevel.color} ${inflationLevel.bg}`}>
              {inflationLevel.level}
            </Badge>
            <div className="flex items-center gap-1 mt-1">
              {trend > 0 ? (
                <ArrowUp className="h-3 w-3 text-red-500" />
              ) : trend < 0 ? (
                <ArrowDown className="h-3 w-3 text-green-500" />
              ) : (
                <Minus className="h-3 w-3 text-gray-500" />
              )}
              <span className="text-xs text-muted-foreground">
                {Math.abs(trend).toFixed(2)}% vs last year
              </span>
            </div>
          </div>
        </div>

        {/* Supply Impact Metrics */}
        <div className="grid grid-cols-2 gap-4 mb-4 p-3 bg-muted/50 rounded-lg">
          <div>
            <div className="text-lg font-semibold">
              {(annualSupplyIncrease / 1000000).toFixed(2)}M
            </div>
            <div className="text-xs text-muted-foreground">Annual Supply Increase</div>
          </div>
          <div>
            <div className="text-lg font-semibold">
              ${(inflationValueUSD / 1000000).toFixed(1)}M
            </div>
            <div className="text-xs text-muted-foreground">Inflation Value (USD)</div>
          </div>
        </div>
        
        <InteractiveMetricChart
          title="YoY Inflation Rate"
          description="Annual inflation rate over the past 24 months"
          data={yoyData}
          color="#8b5cf6"
          icon={<TrendingUp className="h-5 w-5" />}
          unit="%"
          change24h={trend}
        />
        
        <div className="mt-4 space-y-2 text-xs text-muted-foreground">
          <p>
            <strong>Supply Dilution:</strong> {currentYoY > 0 ? 'Increasing' : 'Decreasing'} supply by {Math.abs(currentYoY).toFixed(2)}% annually
          </p>
          <p>
            <strong>Impact:</strong> {currentYoY > 0 ? 'Inflationary pressure on price' : 'Deflationary support for price'}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// Combined Inflation Overview
function InflationOverview({ selectedChain }: { selectedChain: ChainMetrics }) {
  const momInflation = selectedChain.inflation.mom;
  const yoyInflation = selectedChain.inflation.yoy;
  
  // Calculate projected annual inflation based on MoM
  const projectedAnnual = Math.pow(1 + momInflation / 100, 12) - 1;
  const projectedVsActual = projectedAnnual * 100 - yoyInflation;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Percent className="h-5 w-5 text-indigo-500" />
          Inflation Overview
        </CardTitle>
        <CardDescription>
          Comprehensive inflation analysis for {selectedChain.name}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {momInflation.toFixed(2)}%
            </div>
            <div className="text-sm text-muted-foreground">Monthly Rate</div>
          </div>
          
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {yoyInflation.toFixed(2)}%
            </div>
            <div className="text-sm text-muted-foreground">Annual Rate</div>
          </div>
          
          <div className="text-center p-4 bg-indigo-50 dark:bg-indigo-950/20 rounded-lg">
            <div className="text-2xl font-bold text-indigo-600">
              {(projectedAnnual * 100).toFixed(2)}%
            </div>
            <div className="text-sm text-muted-foreground">Projected Annual</div>
          </div>
        </div>

        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="font-semibold mb-2">Analysis</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Inflation Trend:</span>
              <span className={projectedVsActual > 0 ? 'text-red-500' : 'text-green-500'}>
                {projectedVsActual > 0 ? 'Accelerating' : 'Decelerating'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Monthly vs Annual:</span>
              <span>{Math.abs(projectedVsActual).toFixed(2)}% difference</span>
            </div>
            <div className="flex justify-between">
              <span>Supply Pressure:</span>
              <span className={yoyInflation > 5 ? 'text-red-500' : yoyInflation > 2 ? 'text-yellow-500' : 'text-green-500'}>
                {yoyInflation > 5 ? 'High' : yoyInflation > 2 ? 'Moderate' : 'Low'}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function InflationCharts({ selectedChain, onRefresh }: InflationChartsProps) {
  const [selectedView, setSelectedView] = useState('overview');

  const views = [
    { id: 'overview', name: 'Overview', component: InflationOverview },
    { id: 'mom', name: 'Month-over-Month', component: MoMInflationChart },
    { id: 'yoy', name: 'Year-over-Year', component: YoYInflationChart },
  ];

  const SelectedViewComponent = views.find(v => v.id === selectedView)?.component || InflationOverview;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold">Inflation Analysis</h3>
          <p className="text-sm text-muted-foreground">
            Monthly and yearly inflation trends for {selectedChain.name}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedView} onValueChange={setSelectedView}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {views.map(view => (
                <SelectItem key={view.id} value={view.id}>
                  {view.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <SelectedViewComponent selectedChain={selectedChain} />
    </div>
  );
}
