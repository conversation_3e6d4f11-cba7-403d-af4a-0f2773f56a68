'use client';

import { UserProvider } from '@/context/UserContext';
import { createClient } from '@/utils/supabase/client';
import { User } from '@supabase/supabase-js';
import { useEffect, useState } from 'react';

interface ProvidersProps {
  children: React.ReactNode;
}

export function Providers({ children }: ProvidersProps) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const supabase = createClient();
    
    const getUser = async () => {
      try {
        const { data: { user }, error } = await (supabase as any).auth.getUser();
        if (error && error.message !== 'Auth session missing!') {
          console.error('Error fetching user:', error);
        }
        setUser(user);
      } catch (error) {
        // Silently handle authentication errors for guest users
        console.debug('Auth session not available (guest user)');
        setUser(null);
      } finally {
        setLoading(false);
      }
    };
    
    getUser();
    
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (_event, session) => {
        setUser(session?.user ?? null);
      }
    );
    
    return () => subscription.unsubscribe();
  }, []);
  
  if (loading) {
    return <div>Loading...</div>;
  }
  
  return (
    <UserProvider initialUserId={user?.id || null}>
      {children}
    </UserProvider>
  );
}
