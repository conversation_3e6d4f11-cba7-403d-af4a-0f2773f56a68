"use client";

import { useState, useEffect, useCallback } from "react";
import { createClient } from "@/utils/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Shield, 
  Flag, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Eye,
  MessageSquare,
  User,
  Clock
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface ModerationReport {
  id: number;
  reporter_id: string;
  reported_content_id: number;
  content_type: "article" | "comment" | "user";
  reason: string;
  description: string;
  status: "pending" | "reviewed" | "resolved" | "dismissed";
  created_at: string;
  updated_at: string;
  content_title?: string;
  content_excerpt?: string;
  reporter_username?: string;
}

interface ContentModerationProps {
  userRole: "admin" | "moderator" | "user";
  userId: string;
}

export default function ContentModeration({ userRole, userId }: ContentModerationProps) {
  const [reports, setReports] = useState<ModerationReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedReport, setSelectedReport] = useState<ModerationReport | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [moderationNote, setModerationNote] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const supabase = createClient();

  // Only admins and moderators can access this
  const canModerate = userRole === "admin" || userRole === "moderator";

  const loadReports = useCallback(async () => {
    if (!canModerate) return;

    try {
      setIsLoading(true);
      const { data, error } = await (supabase as any).rpc("get_moderation_reports", {
        p_status_filter: filterStatus === "all" ? null : filterStatus,
        p_limit: 50,
      });

      if (error) {
        console.error("Error loading reports:", error);
        toast({
          title: "Error",
          description: "Failed to load moderation reports",
          variant: "destructive",
        });
        return;
      }

      setReports(data || []);
    } catch (error) {
      console.error("Error loading reports:", error);
    } finally {
      setIsLoading(false);
    }
  }, [canModerate, filterStatus, supabase]);

  useEffect(() => {
    loadReports();
  }, [loadReports]);

  const handleReportAction = async (reportId: number, action: "approve" | "dismiss" | "escalate") => {
    if (!canModerate) return;

    setIsProcessing(true);
    try {
      const { error } = await (supabase as any).rpc("process_moderation_report", {
        p_report_id: reportId,
        p_action: action,
        p_moderator_id: userId,
        p_note: moderationNote,
      });

      if (error) {
        console.error("Error processing report:", error);
        toast({
          title: "Error",
          description: "Failed to process report",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Success",
        description: `Report ${action}d successfully`,
      });

      setModerationNote("");
      setSelectedReport(null);
      loadReports();
    } catch (error) {
      console.error("Error processing report:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-800";
      case "reviewed": return "bg-blue-100 text-blue-800";
      case "resolved": return "bg-green-100 text-green-800";
      case "dismissed": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending": return <Clock className="h-4 w-4" />;
      case "reviewed": return <Eye className="h-4 w-4" />;
      case "resolved": return <CheckCircle className="h-4 w-4" />;
      case "dismissed": return <XCircle className="h-4 w-4" />;
      default: return <Flag className="h-4 w-4" />;
    }
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case "article": return <MessageSquare className="h-4 w-4" />;
      case "comment": return <MessageSquare className="h-4 w-4" />;
      case "user": return <User className="h-4 w-4" />;
      default: return <Flag className="h-4 w-4" />;
    }
  };

  if (!canModerate) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            You don't have permission to access content moderation features.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold flex items-center">
              <Shield className="h-6 w-6 mr-2" />
              Content Moderation
            </h1>
            <p className="text-muted-foreground">
              Review and moderate reported content
            </p>
          </div>
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Reports</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="reviewed">Reviewed</SelectItem>
              <SelectItem value="resolved">Resolved</SelectItem>
              <SelectItem value="dismissed">Dismissed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Reports</p>
                  <p className="text-2xl font-bold">{reports.length}</p>
                </div>
                <Flag className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Pending</p>
                  <p className="text-2xl font-bold">
                    {reports.filter(r => r.status === "pending").length}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Resolved</p>
                  <p className="text-2xl font-bold">
                    {reports.filter(r => r.status === "resolved").length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Dismissed</p>
                  <p className="text-2xl font-bold">
                    {reports.filter(r => r.status === "dismissed").length}
                  </p>
                </div>
                <XCircle className="h-8 w-8 text-gray-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Reports List */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Reports List */}
          <Card>
            <CardHeader>
              <CardTitle>Moderation Reports</CardTitle>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="p-4 border rounded-lg animate-pulse">
                      <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {reports.map((report) => (
                    <div
                      key={report.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedReport?.id === report.id
                          ? "border-primary bg-primary/5"
                          : "hover:bg-muted/50"
                      }`}
                      onClick={() => setSelectedReport(report)}
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          {getContentTypeIcon(report.content_type)}
                          <span className="font-medium capitalize">
                            {report.content_type} Report
                          </span>
                        </div>
                        <Badge className={getStatusColor(report.status)}>
                          {getStatusIcon(report.status)}
                          <span className="ml-1 capitalize">{report.status}</span>
                        </Badge>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-2">
                        Reason: {report.reason}
                      </p>
                      
                      {report.content_title && (
                        <p className="text-sm font-medium line-clamp-1">
                          "{report.content_title}"
                        </p>
                      )}
                      
                      <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                        <span>By {report.reporter_username}</span>
                        <span>{new Date(report.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ))}
                  
                  {reports.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      No reports found
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Report Details */}
          <Card>
            <CardHeader>
              <CardTitle>Report Details</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedReport ? (
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold mb-2">Content Information</h3>
                    <div className="bg-muted p-3 rounded-lg">
                      <p className="text-sm">
                        <strong>Type:</strong> {selectedReport.content_type}
                      </p>
                      {selectedReport.content_title && (
                        <p className="text-sm mt-1">
                          <strong>Title:</strong> {selectedReport.content_title}
                        </p>
                      )}
                      {selectedReport.content_excerpt && (
                        <p className="text-sm mt-1">
                          <strong>Excerpt:</strong> {selectedReport.content_excerpt}
                        </p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-semibold mb-2">Report Details</h3>
                    <div className="space-y-2">
                      <p className="text-sm">
                        <strong>Reason:</strong> {selectedReport.reason}
                      </p>
                      <p className="text-sm">
                        <strong>Description:</strong> {selectedReport.description}
                      </p>
                      <p className="text-sm">
                        <strong>Reported by:</strong> {selectedReport.reporter_username}
                      </p>
                      <p className="text-sm">
                        <strong>Date:</strong> {new Date(selectedReport.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  {selectedReport.status === "pending" && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Moderation Note
                        </label>
                        <Textarea
                          value={moderationNote}
                          onChange={(e) => setModerationNote(e.target.value)}
                          placeholder="Add a note about your decision..."
                          className="min-h-[100px]"
                        />
                      </div>

                      <div className="flex space-x-2">
                        <Button
                          onClick={() => handleReportAction(selectedReport.id, "approve")}
                          disabled={isProcessing}
                          variant="destructive"
                          size="sm"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Take Action
                        </Button>
                        <Button
                          onClick={() => handleReportAction(selectedReport.id, "dismiss")}
                          disabled={isProcessing}
                          variant="outline"
                          size="sm"
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Dismiss
                        </Button>
                        <Button
                          onClick={() => handleReportAction(selectedReport.id, "escalate")}
                          disabled={isProcessing}
                          variant="secondary"
                          size="sm"
                        >
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Escalate
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Select a report to view details
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
