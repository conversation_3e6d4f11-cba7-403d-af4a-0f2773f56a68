'use client';

import { useState, useEffect, useRef } from 'react';
import { PublicKey } from '@solana/web3.js';
import { encodeURL, createQR } from '@solana/pay';
import BigNumber from 'bignumber.js';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { QrCode, Wallet, Smartphone, Zap, Crown, Star, Shield, Check, RefreshCw, CreditCard, CheckCircle2, AlertCircle, ExternalLink } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { createClient } from '@/utils/supabase/client';
import { getTreasuryWallet, generatePaymentMemo, formatSolAmount, isValidSolanaAddress } from '@/utils/solana-utils';

interface TierPlan {
  id: string;
  name: string;
  display_name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  icon: React.ComponentType<any>;
  popular?: boolean;
}

const TIER_PLANS: TierPlan[] = [
  {
    id: 'tier1',
    name: 'tier1',
    display_name: 'Member',
    description: 'Perfect for getting started with crypto trading',
    price_monthly: 10.00,
    price_yearly: 100.00,
    features: [
      'Basic Analytics Dashboard',
      'Article Creation Tools',
      'Community Access',
      'Ad-free Experience',
      'Basic Chart Tools'
    ],
    icon: Star
  },
  {
    id: 'tier2',
    name: 'tier2',
    display_name: 'Contributor',
    description: 'For active community contributors and traders',
    price_monthly: 25.00,
    price_yearly: 250.00,
    features: [
      'Advanced Analytics Dashboard',
      'Premium Article Creation Tools',
      'Priority Support',
      'Ad-free Experience',
      'Advanced Chart Tools'
    ],
    icon: Zap
  },
  {
    id: 'tier3',
    name: 'tier3',
    display_name: 'Analyst',
    description: 'For serious traders and market analysts',
    price_monthly: 50.00,
    price_yearly: 500.00,
    features: [
      'All Contributor Features',
      'Real-time Market Data',
      'Advanced Portfolio Tracking',
      'Exclusive Community Access',
      'Custom Indicators',
      'API Access'
    ],
    icon: Crown,
    popular: true
  },
  {
    id: 'tier4',
    name: 'tier4',
    display_name: 'Baller',
    description: 'Complete solution for trading professionals',
    price_monthly: 100.00,
    price_yearly: 1000.00,
    features: [
      'All Analyst Features',
      'White-label Solutions',
      'Custom Integrations',
      'Dedicated Account Manager',
      'Advanced Security Features',
      'Custom Branding'
    ],
    icon: Shield
  }
];

interface EnhancedSolanaPaymentProps {
  recipientUserId?: string;
  recipientUsername?: string;
  paymentType: 'donation' | 'subscription';
  selectedTier?: string; // Pre-selected tier from subscription page
  billingCycle?: 'monthly' | 'yearly'; // Pre-selected billing cycle
  isActive?: boolean; // For lazy loading
  onPaymentSuccess?: (transactionId: string, plan?: string, billingCycle?: string) => void;
}

export default function EnhancedSolanaPayment({
  recipientUserId,
  recipientUsername,
  paymentType,
  selectedTier,
  billingCycle = 'monthly',
  isActive = true,
  onPaymentSuccess,
}: EnhancedSolanaPaymentProps) {
  const [activeTab, setActiveTab] = useState(paymentType === 'subscription' ? 'solana-pay' : 'solana-pay');
  
  // Solana Pay state
  const [selectedPlan, setSelectedPlan] = useState<TierPlan | null>(null);
  const [currentBillingCycle, setCurrentBillingCycle] = useState(billingCycle);
  const [solanaPrice, setSolanaPrice] = useState<number>(0);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isPolling, setIsPolling] = useState(false);
  const [recipientWallet, setRecipientWallet] = useState<string | null>(null);
  
  // Donation-specific state
  const [donationAmount, setDonationAmount] = useState('');
  const [donationQrCodeUrl, setDonationQrCodeUrl] = useState<string | null>(null);
  const [isDonationGenerating, setIsDonationGenerating] = useState(false);
  const [isDonationPolling, setIsDonationPolling] = useState(false);
  
  const qrRef = useRef<HTMLDivElement>(null);
  const donationQrRef = useRef<HTMLDivElement>(null);
  const pollingRef = useRef<NodeJS.Timeout | null>(null);
  const donationPollingRef = useRef<NodeJS.Timeout | null>(null);

  const supabase = createClient();

  // Initialize with pre-selected tier
  useEffect(() => {
    if (selectedTier) {
      const plan = TIER_PLANS.find(p => p.name === selectedTier || p.id === selectedTier);
      if (plan) {
        setSelectedPlan(plan);
      }
    }
  }, [selectedTier]);

  // Fetch current SOL price (only when active)
  useEffect(() => {
    if (isActive) {
      fetchSolanaPrice();
      const priceInterval = setInterval(fetchSolanaPrice, 30000);
      return () => clearInterval(priceInterval);
    }
  }, [isActive]);

  // Fetch recipient wallet address or use treasury (only when active)
  useEffect(() => {
    if (isActive) {
      if (recipientUserId) {
        fetchRecipientWallet();
      } else {
        setRecipientWallet(getTreasuryWallet());
      }
    }
  }, [recipientUserId, isActive]);

  // Auto-generate QR code when plan or billing cycle changes
  useEffect(() => {
    if (selectedPlan && recipientWallet && solanaPrice > 0) {
      generateQRCode();
    }
  }, [selectedPlan, currentBillingCycle, recipientWallet, solanaPrice]);

  const fetchSolanaPrice = async () => {
    try {
      const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd');
      const data = await response.json();
      setSolanaPrice(data.solana.usd);
    } catch (error) {
      console.error('Error fetching SOL price:', error);
      setSolanaPrice(100); // Fallback price
    }
  };

  const fetchRecipientWallet = async () => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('solana_wallet_address')
        .eq('user_id', recipientUserId)
        .single();

      if (error || !data?.solana_wallet_address) {
        setRecipientWallet(getTreasuryWallet());
        return;
      }

      setRecipientWallet(data.solana_wallet_address);
    } catch (error) {
      console.error('Error fetching recipient wallet:', error);
      setRecipientWallet(getTreasuryWallet());
    }
  };

  const calculateSolanaAmount = (usdPrice: number): number => {
    if (solanaPrice === 0) return 0;
    return usdPrice / solanaPrice;
  };

  const generateQRCode = async () => {
    if (!selectedPlan || !recipientWallet) return;

    setIsGenerating(true);

    try {
      const usdPrice = currentBillingCycle === 'yearly' ? selectedPlan.price_yearly : selectedPlan.price_monthly;
      const solAmount = calculateSolanaAmount(usdPrice);
      
      const recipient = new PublicKey(recipientWallet);
      const amountSOL = new BigNumber(solAmount);
      
      const memo = generatePaymentMemo('subscription', selectedPlan.name, currentBillingCycle, recipientUserId);
      const reference = new PublicKey('********************************');
      
      const url = encodeURL({
        recipient,
        amount: amountSOL,
        reference,
        label: `CryptoTalks ${selectedPlan.display_name} Subscription`,
        message: `Subscribe to ${selectedPlan.display_name} (${currentBillingCycle})`,
        memo,
      });

      setQrCodeUrl(url.toString());

      if (qrRef.current) {
        qrRef.current.innerHTML = '';
        const qr = createQR(url, 256, 'transparent');
        qr.append(qrRef.current);
      }

      startPaymentPolling(memo);
      toast.success('QR code generated! Scan with your Solana wallet.');
    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error('Failed to generate payment QR code');
    } finally {
      setIsGenerating(false);
    }
  };

  const startPaymentPolling = (memo: string) => {
    setIsPolling(true);
    let attempts = 0;
    const maxAttempts = 60;

    pollingRef.current = setInterval(async () => {
      attempts++;
      
      if (attempts >= maxAttempts) {
        stopPaymentPolling();
        toast.error('Payment timeout. Please try again if you completed the payment.');
        return;
      }

      // TODO: Implement actual blockchain transaction verification
      console.log(`Polling for payment ${attempts}/${maxAttempts}`);
    }, 5000);
  };

  const stopPaymentPolling = () => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
    setIsPolling(false);
  };

  const handleManualConfirmation = () => {
    if (!selectedPlan) return;
    
    stopPaymentPolling();
    const mockTransactionId = `mock_tx_${Date.now()}`;
    toast.success('Payment confirmed! Subscription activated.');
    onPaymentSuccess?.(mockTransactionId, selectedPlan.name, currentBillingCycle);
  };

  const handlePaymentSuccess = (transactionId: string, plan?: string, billingCycle?: string) => {
    onPaymentSuccess?.(transactionId, plan, billingCycle);
  };

  // Donation-specific functions
  const generateDonationQRCode = async () => {
    if (!donationAmount || parseFloat(donationAmount) <= 0) {
      toast.error('Please enter a valid amount');
      return;
    }

    if (!recipientWallet) {
      toast.error('Recipient wallet not found');
      return;
    }

    setIsDonationGenerating(true);

    try {
      const recipient = new PublicKey(recipientWallet);
      const amountSOL = new BigNumber(donationAmount);
      
      // Create payment URL with memo for tracking
      const memo = `CTT-${paymentType}-${recipientUserId}-${Date.now()}`;
      const reference = new PublicKey('********************************'); // System program as reference
      
      const url = encodeURL({
        recipient,
        amount: amountSOL,
        reference,
        label: `CryptoTalks ${paymentType === 'donation' ? 'Donation' : 'Subscription'}`,
        message: `${paymentType === 'donation' ? 'Donate' : 'Subscribe'} to ${recipientUsername || 'user'}`,
        memo,
      });

      setDonationQrCodeUrl(url.toString());

      // Generate QR code visual
      if (donationQrRef.current) {
        donationQrRef.current.innerHTML = '';
        const qr = createQR(url, 256, 'transparent');
        qr.append(donationQrRef.current);
      }

      // Start polling for payment confirmation
      startDonationPaymentPolling(reference.toString(), memo);

    } catch (error) {
      console.error('Error generating QR code:', error);
      toast.error('Failed to generate payment QR code');
    } finally {
      setIsDonationGenerating(false);
    }
  };

  const startDonationPaymentPolling = (reference: string, memo: string) => {
    setIsDonationPolling(true);
    
    // Poll every 3 seconds for payment confirmation
    donationPollingRef.current = setInterval(async () => {
      try {
        // Check for confirmed transaction via our webhook
        const response = await fetch(`/api/solana-pay?signature=${reference}&paymentType=${paymentType}&recipientUserId=${recipientUserId}`);
        
        if (response.ok) {
          const data = await response.json();
          if (data.confirmed) {
            // Payment confirmed!
            stopDonationPaymentPolling();
            toast.success('Payment confirmed! Thank you!');
            setDonationAmount('');
            setDonationQrCodeUrl(null);
            if (donationQrRef.current) {
              donationQrRef.current.innerHTML = '';
            }
            onPaymentSuccess?.(data.signature || reference);
          }
        }
      } catch (error) {
        // Continue polling if there's an error
        console.error('Payment polling error:', error);
      }
    }, 3000);

    // Stop polling after 10 minutes
    setTimeout(() => {
      if (donationPollingRef.current) {
        stopDonationPaymentPolling();
        toast.info('Payment polling timed out. Please try again if payment was made.');
      }
    }, 600000);
  };

  const stopDonationPaymentPolling = () => {
    if (donationPollingRef.current) {
      clearInterval(donationPollingRef.current);
      donationPollingRef.current = null;
    }
    setIsDonationPolling(false);
  };

  const copyDonationToClipboard = () => {
    if (donationQrCodeUrl) {
      navigator.clipboard.writeText(donationQrCodeUrl);
      toast.success('Payment URL copied to clipboard!');
    }
  };

  // Cleanup polling on unmount
  useEffect(() => {
    return () => {
      if (donationPollingRef.current) {
        clearInterval(donationPollingRef.current);
      }
    };
  }, []);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="text-center text-2xl">
          {paymentType === 'donation' ? 'Send Donation' : 'Subscribe'} with Solana
        </CardTitle>
        <CardDescription className="text-center">
          Choose your preferred payment method
        </CardDescription>
      </CardHeader>
      <CardContent className="max-h-[70vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">{/* Added scrollable content with custom scrollbar */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className={`grid w-full ${paymentType === 'subscription' ? 'grid-cols-2' : 'grid-cols-2'}`}>
            <TabsTrigger value="stripe" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              <span className="hidden sm:inline">Stripe</span>
              <span className="sm:hidden">Card</span>
            </TabsTrigger>
            <TabsTrigger value="solana-pay" className="flex items-center gap-2">
              <QrCode className="h-4 w-4" />
              <span className="hidden sm:inline">Solana Pay</span>
              <span className="sm:hidden">SOL</span>
              <Badge variant="secondary" className="text-xs">Crypto</Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="stripe" className="space-y-4">
            <div className="text-center space-y-2 mb-4">
              <div className="flex items-center justify-center gap-2 text-blue-600">
                <CreditCard className="h-4 w-4" />
                <span className="font-medium">Credit Card Payment</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Pay with your credit or debit card through Stripe
              </p>
            </div>

            <div className="text-center py-8 text-muted-foreground">
              <CreditCard className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">Stripe Integration Coming Soon</p>
              <p className="text-sm">
                Credit card payments will be available soon. For now, please use Solana Pay.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="solana-pay" className="space-y-4">
            {/* Tier Selection Dropdown */}
            <div className="space-y-4">
              <div className="text-center space-y-2">
                <div className="flex items-center justify-center gap-2 text-purple-600">
                  <Crown className="h-4 w-4" />
                  <span className="font-medium">Choose Your Subscription Tier</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Select a tier to see pricing and generate payment QR code
                </p>
              </div>

              <div className="max-w-md mx-auto">
                <Select 
                  value={selectedPlan?.name || ''} 
                  onValueChange={(value) => {
                    const plan = TIER_PLANS.find(p => p.name === value);
                    setSelectedPlan(plan || null);
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select a subscription tier" />
                  </SelectTrigger>
                  <SelectContent>
                    {TIER_PLANS.map((plan) => {
                      const Icon = plan.icon;
                      return (
                        <SelectItem key={plan.name} value={plan.name}>
                          <div className="flex items-center gap-2 w-full">
                            <Icon className="h-4 w-4 text-purple-600" />
                            <span className="font-medium">{plan.display_name}</span>
                            {plan.popular && (
                              <Badge variant="secondary" className="text-xs ml-auto">Popular</Badge>
                            )}
                            <span className="text-sm text-muted-foreground ml-auto">
                              ${currentBillingCycle === 'yearly' ? plan.price_yearly : plan.price_monthly}
                              /{currentBillingCycle === 'yearly' ? 'yr' : 'mo'}
                            </span>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>

              {/* Billing Cycle Toggle */}
              <div className="flex items-center justify-center gap-4">
                <Button
                  variant={currentBillingCycle === 'monthly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setCurrentBillingCycle('monthly')}
                >
                  Monthly
                </Button>
                <Button
                  variant={currentBillingCycle === 'yearly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setCurrentBillingCycle('yearly')}
                >
                  Yearly
                  <Badge variant="secondary" className="ml-2 text-xs">Save 17%</Badge>
                </Button>
              </div>
            </div>

            {selectedPlan ? (
              <div className="mt-6 space-y-4">
                {/* Selected Plan Details */}
                <div className="text-center space-y-2 p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center justify-center gap-2 text-purple-600">
                    <Crown className="h-4 w-4" />
                    <span className="font-medium">{selectedPlan.display_name} Subscription</span>
                    {selectedPlan.popular && (
                      <Badge variant="secondary" className="text-xs ml-2">Popular</Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {selectedPlan.description}
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="text-center">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold">
                        ${(currentBillingCycle === 'yearly' ? selectedPlan.price_yearly : selectedPlan.price_monthly).toFixed(2)} USD
                      </div>
                      <div className="text-lg text-blue-600">
                        ≈ {formatSolAmount(calculateSolanaAmount(currentBillingCycle === 'yearly' ? selectedPlan.price_yearly : selectedPlan.price_monthly))} SOL
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Current SOL price: ${solanaPrice.toFixed(2)}
                      </div>
                    </div>
                  </div>

                  {qrCodeUrl ? (
                    <div className="space-y-4">
                      <div className="flex justify-center">
                        <div className="p-4 bg-white rounded-lg shadow-lg">
                          <div ref={qrRef} className="flex justify-center" />
                        </div>
                      </div>
                      
                      <div className="text-center space-y-2">
                        <p className="text-sm text-muted-foreground">
                          Scan this QR code with any Solana wallet to complete payment
                        </p>
                        {isPolling && (
                          <div className="flex items-center justify-center gap-2 text-blue-600">
                            <RefreshCw className="w-4 h-4 animate-spin" />
                            <span className="text-sm">Waiting for payment...</span>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2 justify-center">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setQrCodeUrl(null);
                            stopPaymentPolling();
                            generateQRCode();
                          }}
                        >
                          Refresh QR Code
                        </Button>
                        <Button
                          variant="secondary"
                          onClick={handleManualConfirmation}
                        >
                          I've Completed Payment
                        </Button>
                      </div>
                    </div>
                  ) : isGenerating ? (
                    <div className="text-center py-8">
                      <div className="flex items-center justify-center gap-2 text-blue-600">
                        <RefreshCw className="w-6 h-6 animate-spin" />
                        <span className="text-lg">Generating QR Code...</span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        Please wait while we prepare your payment
                      </p>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">
                        QR code will appear here when ready
                      </p>
                    </div>
                  )}
                </div>

                {/* Show plan features */}
                <div className="bg-gray-50 dark:bg-gray-900/50 p-4 rounded-lg border">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                    Included with {selectedPlan.display_name}:
                  </div>
                  <div className="grid grid-cols-1 gap-1">
                    {selectedPlan.features.slice(0, 4).map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                        <Check className="h-3 w-3 text-green-600" />
                        <span>{feature}</span>
                      </div>
                    ))}
                    {selectedPlan.features.length > 4 && (
                      <div className="text-xs text-muted-foreground mt-1">
                        +{selectedPlan.features.length - 4} more features
                      </div>
                    )}
                  </div>
                </div>

                {/* Payment Instructions */}
                <div className="bg-purple-50 dark:bg-purple-950/20 p-3 rounded-lg border border-purple-200 dark:border-purple-800">
                  <div className="flex items-start gap-2">
                    <Smartphone className="h-4 w-4 mt-0.5 text-purple-600" />
                    <div className="text-sm">
                      <p className="font-medium text-purple-900 dark:text-purple-100 mb-1">
                        How to Pay:
                      </p>
                      <ul className="text-purple-700 dark:text-purple-300 space-y-1 text-xs">
                        <li>• Open your Solana wallet app (Phantom, Solflare, etc.)</li>
                        <li>• Tap "Scan QR Code" or use camera</li>
                        <li>• Review payment details and confirm</li>
                        <li>• Payment will be detected automatically</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}

            {/* For donations when no specific tier, show custom amount input */}
            {!selectedPlan && paymentType === 'donation' && (
              <div className="text-center space-y-4 mb-4">
                <div className="flex items-center justify-center gap-2 text-green-600">
                  <Zap className="h-4 w-4" />
                  <span className="font-medium">Custom Donation Amount</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Enter any amount you'd like to donate
                </p>
                
                {/* Inline Donation Payment UI */}
                <Card className="w-full max-w-md mx-auto">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <QrCode className="h-5 w-5" />
                      Solana Pay - Easy Payment
                    </CardTitle>
                    <CardDescription>
                      Scan QR code with any Solana wallet to pay instantly
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="donation-amount">Amount (SOL)</Label>
                      <Input
                        id="donation-amount"
                        type="number"
                        placeholder="0.1"
                        value={donationAmount}
                        onChange={(e) => setDonationAmount(e.target.value)}
                        step="0.001"
                        min="0"
                        disabled={isDonationPolling}
                      />
                    </div>

                    {!donationQrCodeUrl ? (
                      <Button
                        onClick={generateDonationQRCode}
                        disabled={isDonationGenerating || !donationAmount || !recipientWallet}
                        className="w-full"
                      >
                        {isDonationGenerating ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Generating QR Code...
                          </>
                        ) : (
                          <>
                            <QrCode className="h-4 w-4 mr-2" />
                            Generate Payment QR Code
                          </>
                        )}
                      </Button>
                    ) : (
                      <div className="space-y-4">
                        <div className="text-center">
                          <div 
                            ref={donationQrRef} 
                            className="inline-block p-4 bg-white rounded-lg border-2 border-dashed border-gray-300"
                          />
                        </div>

                        {isDonationPolling && (
                          <div className="text-center text-sm text-muted-foreground">
                            <RefreshCw className="h-4 w-4 mx-auto mb-1 animate-spin" />
                            Waiting for payment confirmation...
                          </div>
                        )}

                        <div className="space-y-2">
                          <Button
                            onClick={copyDonationToClipboard}
                            variant="outline"
                            size="sm"
                            className="w-full"
                          >
                            <Smartphone className="h-4 w-4 mr-2" />
                            Copy Payment URL
                          </Button>
                          
                          <Button
                            onClick={() => {
                              setDonationQrCodeUrl(null);
                              stopDonationPaymentPolling();
                              if (donationQrRef.current) {
                                donationQrRef.current.innerHTML = '';
                              }
                            }}
                            variant="ghost"
                            size="sm"
                            className="w-full"
                          >
                            Cancel Payment
                          </Button>
                        </div>

                        <div className="text-xs text-muted-foreground space-y-1">
                          <p>📱 <strong>Mobile:</strong> Tap "Copy Payment URL" and open in your Solana wallet</p>
                          <p>💻 <strong>Desktop:</strong> Scan QR code with your mobile Solana wallet</p>
                          <p>⚡ Payment will be automatically detected</p>
                        </div>
                      </div>
                    )}

                    <Separator />

                    <div className="text-center text-sm text-muted-foreground">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Wallet className="h-4 w-4" />
                        <span>Works with any Solana wallet</span>
                      </div>
                      <p className="text-xs">
                        Phantom, Solflare, Backpack, Glow, and more
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
