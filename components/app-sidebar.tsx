"use client";

import * as React from "react";
import {
  SquareTerminal,
  Bot,
  Settings2,
  PenSquare,
  Home,
  Users,
  Menu,
  X,
  Bell,
  BarChart3,
  Search,
  Hash,
  TrendingUp,
  Activity,
  FileText,
  Gift,
  Crown,
  CreditCard,
  QrCode,
} from "lucide-react";
import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";
import { NavToolsAccordion } from "./nav-tools-accordion";
import { NavMainAccordion } from "./nav-main-accordion";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { ThemeSwitcher } from "./theme-switcher";
import { LucideIcon } from "lucide-react";
import { User } from "@supabase/supabase-js";
import Link from "next/link";
import { UserProfile as Profile } from "@/types";
import { useIsMobile } from "@/hooks/use-mobile";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface NavItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  isActive?: boolean;
  tierRequired?: string;
  items?: NavItem[];
}

interface AppSidebarProps {
  user: User;
  profile: Profile;
}

// Navigation data
const getNavMainData = (userId: string): NavItem[] => [
  {
    title: "Home",
    url: "/ctn",
    icon: Home,
    isActive: true,
  },
  {
    title: "Search",
    url: "/ctn/search",
    icon: Search,
  },
  {
    title: "Hashtags",
    url: "/ctn/hashtag",
    icon: Hash,
  },
  {
    title: "Analytics Dashboard",
    url: "/ctn/analytics",
    icon: BarChart3,
  },
  {
    title: "Research Papers",
    url: "/ctn/research",
    icon: FileText,
    tierRequired: "tier1",
  },
  {
    title: "Daily Claim",
    url: "/ctn/daily-claim",
    icon: Gift,
    tierRequired: "tier1",
  },
  {
    title: "Leaderboards",
    url: "/ctn/leaderboards",
    icon: Crown,
  },
  {
    title: "Publish Article",
    url: "/ctn/articles/publish",
    icon: PenSquare,
  },
  {
    title: "Notifications",
    url: "/ctn/notifications",
    icon: Bell,
  },
  {
    title: "My Profile",
    url: `/ctn/profile/${userId}`,
    icon: Bot,
  },
  {
    title: "Upgrade Plan",
    url: "/ctn/subscription/plans",
    icon: CreditCard,
  },
  {
    title: "Settings",
    url: "/ctn/settings",
    icon: Settings2,
    items: [
      { title: "Account Settings", url: "/ctn/settings/account" },
      { title: "Notifications", url: "/ctn/settings/notifications" },
      { title: "Privacy", url: "/ctn/settings/privacy" },
    ],
  },
];

export function AppSidebar({ user, profile }: AppSidebarProps) {
  const navMainData = React.useMemo(() => getNavMainData(user.id), [user.id]);
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = React.useState(!isMobile);

  React.useEffect(() => {
    if (isMobile) {
      const handleRouteChange = () => setIsOpen(false);
      window.addEventListener("popstate", handleRouteChange);
      return () => window.removeEventListener("popstate", handleRouteChange);
    }
  }, [isMobile]);

  return (
    <>
      {isMobile && (
        <Button
          variant="ghost"
          size="icon"
          className="fixed top-4 left-4 z-50"
          onClick={() => setIsOpen(!isOpen)}
        >
          {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
        </Button>
      )}

      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-gradient-to-br from-black/40 via-black/25 to-black/10 backdrop-blur-xl backdrop-saturate-150 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      <div
        className={cn(
          "h-full transition-transform duration-300 ease-in-out",
          isMobile && "fixed top-0 left-0 z-50",
          isMobile && !isOpen && "-translate-x-full"
        )}
      >
        <Sidebar
          side="left"
          collapsible={isMobile ? "none" : "icon"}
          className="h-full text-foreground border-r border-white/30 dark:border-white/20 bg-gradient-to-br from-white/80 via-white/60 to-white/40 dark:from-gray-900/90 dark:via-gray-800/80 dark:to-gray-900/70 backdrop-blur-lg shadow-2xl shadow-black/10 dark:shadow-black/30 z-20"
        >
          <SidebarHeader>{!isMobile && <SidebarTrigger />}</SidebarHeader>

          <SidebarContent>
            <div className="flex items-center justify-center m-2">
              <Link
                href="/ctn"
                className="flex items-center space-x-2"
                onClick={() => isMobile && setIsOpen(false)}
              >
                <img
                  src="/ct_logoclr.png"
                  alt="CTN Logo"
                  className="w-12 h-auto object-cover rounded-lg transition-transform hover:scale-105"
                />
                <span className="font-bold text-lg hidden md:inline">CTN</span>
              </Link>
            </div>

            <React.Suspense fallback={<div>Loading navigation...</div>}>
              {/* Enhanced Main Navigation */}
              <NavMainAccordion
                onItemClick={() => isMobile && setIsOpen(false)}
                userId={user.id}
              />

              {/* Separator */}
              <div className="px-4 py-2">
                <div className="border-t border-border/50"></div>
              </div>

              {/* Enhanced Tools Navigation */}
              <NavToolsAccordion
                onItemClick={() => isMobile && setIsOpen(false)}
              />
            </React.Suspense>
          </SidebarContent>

          <div className="m-auto">
            <ThemeSwitcher />
          </div>

          <SidebarFooter>
            <React.Suspense fallback={<div>Loading user data...</div>}>
              {user && profile ? (
                <NavUser user={user} profile={profile} />
              ) : (
                <div className="p-4 text-center text-sm text-muted-foreground">
                  No user data available
                </div>
              )}
            </React.Suspense>
          </SidebarFooter>

          {!isMobile && <SidebarRail />}
        </Sidebar>
      </div>
    </>
  );
}
