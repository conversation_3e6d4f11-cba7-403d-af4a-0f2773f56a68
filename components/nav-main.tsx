import { LucideIcon } from "lucide-react";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { NotificationBadge } from "./NotificationBadge";
import { ExperienceBadge } from "./ExperienceSystem";
import { Badge } from "./ui/badge";
import { useTierAccess } from "@/hooks/use-tier-access";

interface NavMainProps {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    tierRequired?: string;
    items?: {
      title: string;
      url: string;
      tierRequired?: string;
    }[];
  }[];
  onItemClick?: () => void;
  userId?: string;
}

export function NavMain({ items, onItemClick, userId }: NavMainProps) {
  const { userExperience, getTierLevel } = useTierAccess();
  const { state } = useSidebar();
  
  // Check if user has access to a tier-protected item
  const hasAccess = (tierRequired?: string) => {
    if (!tierRequired) return true;
    if (!userExperience) return false;
    return getTierLevel(userExperience.tier) >= getTierLevel(tierRequired);
  };

  return (
    <SidebarGroup>
      {/* Experience Badge - hidden when collapsed */}
      {userExperience && state === "expanded" && (
        <div className="px-2 mb-4">
          <ExperienceBadge />
        </div>
      )}
      
      <SidebarMenu>
        {items.map((item) => {
          const itemHasAccess = hasAccess(item.tierRequired);
          
          // Hide tier-protected items if user doesn't have access
          if (item.tierRequired && !itemHasAccess) {
            return null;
          }
          
          return item.title === "Settings" && item.items ? (
            // Popover for "Settings" menu
            <SidebarMenuItem key={item.title}>
              <Popover>
                <PopoverTrigger asChild>
                  <SidebarMenuButton tooltip={item.title}>
                    {item.icon && <item.icon />}
                    <span>{item.title}</span>
                  </SidebarMenuButton>
                </PopoverTrigger>
                <PopoverContent align="start" sideOffset={8} className="w-48 border border-white/30 dark:border-white/20 bg-gradient-to-br from-white/90 via-white/75 to-white/60 dark:from-white/15 dark:via-white/10 dark:to-white/5 backdrop-blur-xl backdrop-saturate-150 shadow-2xl shadow-black/10 dark:shadow-black/30 before:absolute before:inset-0 before:rounded-lg before:bg-gradient-to-br before:from-white/20 before:to-transparent before:opacity-60 before:pointer-events-none relative overflow-hidden">
                  <ul className="space-y-1 relative z-10">
                    {item.items.map((subItem) => {
                      const subItemHasAccess = hasAccess(subItem.tierRequired);
                      
                      // Hide tier-protected sub-items if user doesn't have access
                      if (subItem.tierRequired && !subItemHasAccess) {
                        return null;
                      }
                      
                      return (
                        <li key={subItem.title}>
                          <Link
                            href={subItem.url}
                            className="block px-4 py-2 hover:bg-muted rounded-sm"
                            onClick={onItemClick}
                          >
                            {subItem.title}
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                </PopoverContent>
              </Popover>
            </SidebarMenuItem>
          ) : (
            // Standard navigation item
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild tooltip={item.title}>
                <Link
                  href={item.url}
                  className="flex items-center space-x-2"
                  onClick={onItemClick}
                >
                  {item.icon && <item.icon />}
                  <span>{item.title}</span>
                  {item.title === "Notifications" && userId && (
                    <NotificationBadge userId={userId} />
                  )}
                  {/* Tier badge */}
                  {item.tierRequired && (
                    <Badge variant="outline" className="ml-auto">
                      {item.tierRequired}
                    </Badge>
                  )}
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
