"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { InteractiveMetricChart } from '@/components/InteractiveMetricChart';
import { 
  Wallet, 
  Users, 
  TrendingUp, 
  TrendingDown, 
  PieChart,
  BarChart3,
  Crown,
  Fish,
  Anchor,
  RefreshCw,
  Download
} from 'lucide-react';

interface ChainMetrics {
  name: string;
  symbol: string;
  price: number;
  circulatingSupply: number;
}

interface WalletBalanceAnalysisProps {
  selectedChain: ChainMetrics;
  isActive?: boolean;
  onRefresh?: () => void;
}

interface WalletTier {
  name: string;
  icon: React.ReactNode;
  minBalance: number;
  maxBalance: number;
  count: number;
  percentage: number;
  totalBalance: number;
  color: string;
  description: string;
}

// Generate wallet tier data based on chain
function generateWalletTiers(chain: ChainMetrics): WalletTier[] {
  const price = chain.price;
  const symbol = chain.symbol;
  
  // Define tiers based on USD value ranges
  const baseTiers = [
    { name: 'Whales', icon: <Crown className="h-4 w-4" />, minUSD: 1000000, maxUSD: Infinity, color: '#8b5cf6', description: 'Large institutional holders' },
    { name: 'Sharks', icon: <Fish className="h-4 w-4" />, minUSD: 100000, maxUSD: 1000000, color: '#3b82f6', description: 'High net worth individuals' },
    { name: 'Dolphins', icon: <Anchor className="h-4 w-4" />, minUSD: 10000, maxUSD: 100000, color: '#10b981', description: 'Serious investors' },
    { name: 'Fish', icon: <Fish className="h-4 w-4" />, minUSD: 1000, maxUSD: 10000, color: '#f59e0b', description: 'Regular investors' },
    { name: 'Shrimp', icon: <Users className="h-4 w-4" />, minUSD: 100, maxUSD: 1000, color: '#ef4444', description: 'Small holders' },
    { name: 'Dust', icon: <Users className="h-4 w-4" />, minUSD: 0, maxUSD: 100, color: '#6b7280', description: 'Minimal holdings' },
  ];

  return baseTiers.map((tier, index) => {
    const minBalance = tier.minUSD / price;
    const maxBalance = tier.maxUSD === Infinity ? Infinity : tier.maxUSD / price;
    
    // Generate realistic distribution data
    const totalWallets = 1000000; // Assume 1M total wallets
    let count: number;
    let percentage: number;
    
    switch (index) {
      case 0: // Whales
        count = Math.floor(totalWallets * 0.001); // 0.1%
        percentage = 0.1;
        break;
      case 1: // Sharks  
        count = Math.floor(totalWallets * 0.01); // 1%
        percentage = 1;
        break;
      case 2: // Dolphins
        count = Math.floor(totalWallets * 0.05); // 5%
        percentage = 5;
        break;
      case 3: // Fish
        count = Math.floor(totalWallets * 0.15); // 15%
        percentage = 15;
        break;
      case 4: // Shrimp
        count = Math.floor(totalWallets * 0.30); // 30%
        percentage = 30;
        break;
      default: // Dust
        count = Math.floor(totalWallets * 0.489); // 48.9%
        percentage = 48.9;
        break;
    }
    
    // Calculate total balance for this tier (simplified)
    const avgBalance = (minBalance + (maxBalance === Infinity ? minBalance * 10 : maxBalance)) / 2;
    const totalBalance = count * avgBalance;
    
    return {
      ...tier,
      minBalance,
      maxBalance,
      count,
      percentage,
      totalBalance,
    };
  });
}

// Wallet Distribution Chart
function WalletDistributionChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const walletTiers = generateWalletTiers(selectedChain);
  const totalWallets = walletTiers.reduce((sum, tier) => sum + tier.count, 0);
  const totalSupply = walletTiers.reduce((sum, tier) => sum + tier.totalBalance, 0);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <PieChart className="h-5 w-5 text-blue-500" />
          Wallet Balance Distribution
        </CardTitle>
        <CardDescription>
          Distribution of {selectedChain.symbol} holdings across wallet tiers
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {walletTiers.map((tier, index) => (
            <div key={tier.name} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div style={{ color: tier.color }}>
                    {tier.icon}
                  </div>
                  <span className="font-medium">{tier.name}</span>
                  <Badge variant="outline" className="text-xs">
                    {tier.percentage}%
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground">
                  {tier.count.toLocaleString()} wallets
                </div>
              </div>
              
              <Progress 
                value={tier.percentage} 
                className="h-2"
                style={{ 
                  backgroundColor: `${tier.color}20`,
                }}
              />
              
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>
                  {tier.minBalance === 0 ? '0' : tier.minBalance.toFixed(0)} - {' '}
                  {tier.maxBalance === Infinity ? '∞' : tier.maxBalance.toFixed(0)} {selectedChain.symbol}
                </span>
                <span>
                  ${(tier.minBalance * selectedChain.price).toLocaleString()} - {' '}
                  {tier.maxBalance === Infinity ? '∞' : `$${(tier.maxBalance * selectedChain.price).toLocaleString()}`}
                </span>
              </div>
              
              <div className="text-xs text-muted-foreground">
                {tier.description}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-6 grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
          <div>
            <div className="text-lg font-semibold">
              {totalWallets.toLocaleString()}
            </div>
            <div className="text-xs text-muted-foreground">Total Wallets</div>
          </div>
          <div>
            <div className="text-lg font-semibold">
              {(totalSupply / 1000000).toFixed(1)}M
            </div>
            <div className="text-xs text-muted-foreground">Total Supply Tracked</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Whale Activity Chart
function WhaleActivityChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const generateWhaleActivity = () => {
    const data = [];
    for (let i = 0; i < 30; i++) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const activity = Math.floor(Math.random() * 50) + 10; // 10-60 whale transactions
      
      data.push({
        time: date.toISOString().split('T')[0],
        value: activity
      });
    }
    return data.reverse();
  };

  const whaleData = generateWhaleActivity();
  const currentActivity = whaleData[whaleData.length - 1]?.value || 0;
  const avgActivity = whaleData.reduce((sum, d) => sum + d.value, 0) / whaleData.length;
  const trend = currentActivity - avgActivity;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Crown className="h-5 w-5 text-purple-500" />
          Whale Activity
        </CardTitle>
        <CardDescription>
          Large holder transaction activity (1M+ USD)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <div className="text-2xl font-bold">
              {currentActivity}
            </div>
            <div className="text-xs text-muted-foreground">Whale Transactions Today</div>
          </div>
          <div>
            <div className={`text-lg font-semibold ${trend > 0 ? 'text-green-500' : 'text-red-500'}`}>
              {trend > 0 ? '+' : ''}{trend.toFixed(0)}
            </div>
            <div className="text-xs text-muted-foreground">vs 30-day Average</div>
          </div>
        </div>
        
        <InteractiveMetricChart
          title="Daily Whale Transactions"
          description="Number of transactions > $1M USD"
          data={whaleData}
          color="#8b5cf6"
          icon={<Crown className="h-5 w-5" />}
          unit=""
          change24h={trend}
        />
        
        <div className="mt-4 text-xs text-muted-foreground">
          <p>
            High whale activity often indicates significant market movements or institutional interest.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}

// Balance Concentration Analysis
function BalanceConcentrationChart({ selectedChain }: { selectedChain: ChainMetrics }) {
  const walletTiers = generateWalletTiers(selectedChain);
  const totalSupply = walletTiers.reduce((sum, tier) => sum + tier.totalBalance, 0);
  
  // Calculate concentration metrics
  const top1PercentHoldings = walletTiers.slice(0, 2).reduce((sum, tier) => sum + tier.totalBalance, 0);
  const top10PercentHoldings = walletTiers.slice(0, 3).reduce((sum, tier) => sum + tier.totalBalance, 0);
  
  const concentrationData = walletTiers.map(tier => ({
    name: tier.name,
    percentage: (tier.totalBalance / totalSupply) * 100,
    color: tier.color
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-indigo-500" />
          Supply Concentration
        </CardTitle>
        <CardDescription>
          How concentrated is the {selectedChain.symbol} supply?
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-950/20 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {((top1PercentHoldings / totalSupply) * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">Top 1% Holdings</div>
          </div>
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {((top10PercentHoldings / totalSupply) * 100).toFixed(1)}%
            </div>
            <div className="text-sm text-muted-foreground">Top 10% Holdings</div>
          </div>
        </div>

        <div className="space-y-3">
          {concentrationData.map((tier, index) => (
            <div key={tier.name} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium">{tier.name}</span>
                <span className="text-sm text-muted-foreground">
                  {tier.percentage.toFixed(1)}%
                </span>
              </div>
              <Progress 
                value={tier.percentage} 
                className="h-2"
                style={{ backgroundColor: `${tier.color}20` }}
              />
            </div>
          ))}
        </div>

        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="font-semibold mb-2">Concentration Analysis</h4>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span>Decentralization Score:</span>
              <span className={top1PercentHoldings / totalSupply < 0.5 ? 'text-green-500' : 'text-red-500'}>
                {top1PercentHoldings / totalSupply < 0.3 ? 'High' : top1PercentHoldings / totalSupply < 0.5 ? 'Medium' : 'Low'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Whale Dominance:</span>
              <span>{((top1PercentHoldings / totalSupply) * 100).toFixed(1)}%</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function WalletBalanceAnalysis({ selectedChain, onRefresh }: WalletBalanceAnalysisProps) {
  const [selectedView, setSelectedView] = useState('distribution');

  const views = [
    { id: 'distribution', name: 'Distribution', component: WalletDistributionChart },
    { id: 'whale-activity', name: 'Whale Activity', component: WhaleActivityChart },
    { id: 'concentration', name: 'Concentration', component: BalanceConcentrationChart },
  ];

  const SelectedViewComponent = views.find(v => v.id === selectedView)?.component || WalletDistributionChart;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-xl font-semibold">Wallet Balance Analysis</h3>
          <p className="text-sm text-muted-foreground">
            Distribution and concentration of {selectedChain.name} holdings
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedView} onValueChange={setSelectedView}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {views.map(view => (
                <SelectItem key={view.id} value={view.id}>
                  {view.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <SelectedViewComponent selectedChain={selectedChain} />
    </div>
  );
}
