"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Crown, 
  Star, 
  TrendingUp, 
  MessageSquare, 
  Heart, 
  Share2, 
  FileText, 
  BookOpen, 
  Trophy,
  Users,
  Zap,
  Award,
  Shield
} from "lucide-react";
import { UserProfile, UserAward } from "@/types";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface UserStatsCardProps {
  profile: UserProfile;
  isOwnProfile: boolean;
}

// Use the existing tier system from CachedTierProtection
const tierNames = {
  tier0: "Free",
  tier1: "Contributor", 
  tier2: "Analyst",
  tier3: "Baller",
  tier4: "Elite",
};

const tierIcons = {
  tier0: Users,
  tier1: Zap,
  tier2: Star,
  tier3: Shield,
  tier4: Crown,
};

const tierColors = {
  tier0: "text-gray-600",
  tier1: "text-yellow-500",
  tier2: "text-purple-500", 
  tier3: "text-blue-500",
  tier4: "text-green-500",
};

// Helper function to calculate level progress
const getLevelProgress = (exp: number = 0, level: number = 1) => {
  const expForCurrentLevel = level * 1000;
  const expForNextLevel = (level + 1) * 1000;
  const currentLevelExp = exp - expForCurrentLevel;
  const expNeededForNext = expForNextLevel - expForCurrentLevel;
  return Math.max(0, Math.min(100, (currentLevelExp / expNeededForNext) * 100));
};

// Helper function to get reputation tier
const getReputationTier = (score: number = 0) => {
  if (score >= 10000) return { name: 'Legendary', color: 'text-purple-500' };
  if (score >= 5000) return { name: 'Expert', color: 'text-blue-500' };
  if (score >= 1000) return { name: 'Advanced', color: 'text-green-500' };
  if (score >= 500) return { name: 'Intermediate', color: 'text-yellow-500' };
  if (score >= 100) return { name: 'Beginner', color: 'text-orange-500' };
  return { name: 'Newcomer', color: 'text-gray-500' };
};

// Award rarity colors
const getAwardRarityColor = (rarity: UserAward['rarity']) => {
  switch (rarity) {
    case 'legendary':
      return 'text-purple-500 bg-purple-50 border-purple-200 dark:bg-purple-950/20 dark:border-purple-800';
    case 'epic':
      return 'text-blue-500 bg-blue-50 border-blue-200 dark:bg-blue-950/20 dark:border-blue-800';
    case 'rare':
      return 'text-green-500 bg-green-50 border-green-200 dark:bg-green-950/20 dark:border-green-800';
    case 'common':
      return 'text-gray-500 bg-gray-50 border-gray-200 dark:bg-gray-950/20 dark:border-gray-800';
    default:
      return 'text-gray-500 bg-gray-50 border-gray-200 dark:bg-gray-950/20 dark:border-gray-800';
  }
};

export function UserStatsCard({ profile, isOwnProfile }: UserStatsCardProps) {
  const userTier = profile.account_tier || 'tier0';
  const TierIcon = tierIcons[userTier];
  const tierColor = tierColors[userTier];
  const levelProgress = getLevelProgress(profile.account_exp, profile.account_level);
  const reputationTier = getReputationTier(profile.reputation_score);

  return (
    <TooltipProvider>
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            {isOwnProfile ? "Your Stats" : `${profile.username}'s Stats`}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Account Tier & Level */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Account Tier</span>
                <Badge variant="outline" className={`${tierColor} border-current`}>
                  <TierIcon className="h-3 w-3 mr-1" />
                  {tierNames[userTier]}
                </Badge>
              </div>
              
              <div className="space-y-1">
                <div className="flex items-center justify-between text-sm">
                  <span>Level {profile.account_level || 1}</span>
                  <span className="text-muted-foreground">
                    {profile.account_exp || 0} XP
                  </span>
                </div>
                <Progress value={levelProgress} className="h-2" />
                <div className="text-xs text-muted-foreground">
                  {Math.round((100 - levelProgress))}% to next level
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Discord Level</span>
                <Badge variant="secondary">
                  Level {profile.discord_level || 0}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">XP Boost</span>
                <div className="flex items-center gap-1">
                  <Zap className="h-3 w-3 text-yellow-500" />
                  <span className="text-sm font-bold text-yellow-600">
                    +{profile.total_exp_boost || 0}%
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Social Stats */}
          <div>
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <Users className="h-4 w-4" />
              Social Stats
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="text-center space-y-1 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-center">
                      <Users className="h-4 w-4 text-blue-500" />
                    </div>
                    <div className="text-lg font-bold">{profile.followers_count || 0}</div>
                    <div className="text-xs text-muted-foreground">Followers</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Number of users following this profile</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="text-center space-y-1 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-center">
                      <Heart className="h-4 w-4 text-red-500" />
                    </div>
                    <div className="text-lg font-bold">{profile.likes_count || 0}</div>
                    <div className="text-xs text-muted-foreground">Likes</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Total likes received on content</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="text-center space-y-1 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-center">
                      <MessageSquare className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="text-lg font-bold">{profile.comments_count || 0}</div>
                    <div className="text-xs text-muted-foreground">Comments</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Total comments made</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="text-center space-y-1 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="flex items-center justify-center">
                      <Share2 className="h-4 w-4 text-purple-500" />
                    </div>
                    <div className="text-lg font-bold">{profile.shares_count || 0}</div>
                    <div className="text-xs text-muted-foreground">Shares</div>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Total shares received</p>
                </TooltipContent>
              </Tooltip>
            </div>
          </div>

          {/* Content Stats */}
          <div>
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Content Published
            </h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center space-y-1 p-3 rounded-lg bg-muted/50">
                <div className="flex items-center justify-center">
                  <FileText className="h-5 w-5 text-blue-500" />
                </div>
                <div className="text-xl font-bold">{profile.article_count || 0}</div>
                <div className="text-sm text-muted-foreground">Articles</div>
              </div>
              
              <div className="text-center space-y-1 p-3 rounded-lg bg-muted/50">
                <div className="flex items-center justify-center">
                  <BookOpen className="h-5 w-5 text-green-500" />
                </div>
                <div className="text-xl font-bold">{profile.research_papers_count || 0}</div>
                <div className="text-sm text-muted-foreground">Research Papers</div>
              </div>
            </div>
          </div>

          {/* Reputation */}
          <div>
            <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
              <Star className="h-4 w-4" />
              Reputation
            </h4>
            <div className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
              <div>
                <div className="text-2xl font-bold">{profile.reputation_score || 0}</div>
                <Badge variant="outline" className={reputationTier.color}>
                  {reputationTier.name}
                </Badge>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500 opacity-60" />
            </div>
          </div>

          {/* Awards/Badges */}
          {profile.awards && profile.awards.length > 0 && (
            <div>
              <h4 className="text-sm font-medium mb-3 flex items-center gap-2">
                <Award className="h-4 w-4" />
                Awards & Badges
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {profile.awards.slice(0, 6).map((award) => (
                  <Tooltip key={award.id}>
                    <TooltipTrigger asChild>
                      <div className={`p-2 rounded-lg border text-center hover:scale-105 transition-transform cursor-pointer ${getAwardRarityColor(award.rarity)}`}>
                        <div className="text-lg mb-1">{award.icon}</div>
                        <div className="text-xs font-medium truncate">{award.name}</div>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-center">
                        <p className="font-medium">{award.name}</p>
                        <p className="text-sm text-muted-foreground">{award.description}</p>
                        <p className="text-xs mt-1 capitalize">{award.rarity} • {new Date(award.earned_at).toLocaleDateString()}</p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                ))}
              </div>
              {profile.awards.length > 6 && (
                <div className="text-center mt-2">
                  <Badge variant="secondary">
                    +{profile.awards.length - 6} more awards
                  </Badge>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}