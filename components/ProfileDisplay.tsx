"use client";

import { useState, useCallback } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  <PERSON>,
  CardHeader,
  CardFooter,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { ProfileEdit } from "@/components/ProfileEdit";
import { Button } from "./ui/button";
import Link from "next/link";
import { User } from "@supabase/supabase-js";
import { Badge } from "@/components/ui/badge";
import {
  CalendarDays,
  Heart,
  MessageSquare,
  Users,
  UserPlus,
  UserMinus,
  Share2,
  Pencil,
} from "lucide-react";
import { createClient } from "@/utils/supabase/client";
import { toast } from "@/hooks/use-toast";
import { Separator } from "@/components/ui/separator";
import { UserProfile, Article } from "@/types"; // Import your types
import { UserStatsCard } from "@/components/UserStatsCard";

interface ProfileDisplayProps {
  currentUser: User | null;
  profile: UserProfile;
  articles: Article[];
  isOwnProfile: boolean;
}

interface FollowStatus {
  isLoading: boolean;
  error: string | null;
}

const formatDate = (date: string | null | undefined) => {
  if (!date) return "Unknown date";
  return new Date(date).toLocaleDateString(undefined, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

export function ProfileDisplay({
  currentUser,
  profile,
  articles,
  isOwnProfile,
}: ProfileDisplayProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [updatedProfile, setUpdatedProfile] = useState<UserProfile>(profile);
  const [isLoading, setIsLoading] = useState(false);
  const [isFollowing, setIsFollowing] = useState(profile.is_followed || false);
  const [followersCount, setFollowersCount] = useState(
    profile.followers_count || 0
  );
  const [followStatus, setFollowStatus] = useState<FollowStatus>({
    isLoading: false,
    error: null,
  });
  const supabase = createClient();

  const handleEditToggle = () => setIsEditing(!isEditing);

  const handleProfileUpdated = (newProfile: UserProfile) => {
    setUpdatedProfile(newProfile);
    setIsEditing(false);
    toast({
      title: "Profile Updated",
      description:
        newProfile.username !== profile.username
          ? "Profile and username updated successfully."
          : "Profile updated successfully.",
    });
  };

  const handleFollow = useCallback(async () => {
    if (!currentUser) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to follow users.",
        variant: "destructive",
      });
      return;
    }

    setFollowStatus((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const { error: followError } = isFollowing
        ? await (supabase as any)
            .from("follows")
            .delete()
            .eq("follower_id", currentUser.id)
            .eq("following_id", profile.user_id)
        : await (supabase as any).from("follows").insert({
            follower_id: currentUser.id,
            following_id: profile.user_id,
            created_at: new Date().toISOString(),
          });

      if (followError) throw followError;

      setIsFollowing(!isFollowing);
      setFollowersCount((prev) => (isFollowing ? prev - 1 : prev + 1));

      toast({
        title: isFollowing ? "Unfollowed" : "Following",
        description: isFollowing
          ? `You have unfollowed @${profile.username}`
          : `You are now following @${profile.username}`,
      });
    } catch (error: any) {
      console.error("Follow error:", error);
      setFollowStatus((prev) => ({
        ...prev,
        error: error.message || "Failed to update follow status",
      }));
      toast({
        title: "Action Failed",
        description:
          error.message || "Failed to update follow status. Please try again.",
        variant: "destructive",
      });
    } finally {
      setFollowStatus((prev) => ({ ...prev, isLoading: false }));
    }
  }, [currentUser, isFollowing, profile.user_id, profile.username, supabase]);

  const handleShare = async () => {
    const shareUrl = window.location.href;
    const shareText = `Check out ${profile.username}'s profile on CTN`;

    try {
      if (navigator.canShare?.({ url: shareUrl })) {
        await navigator.share({
          title: `${profile.username}'s Profile`,
          text: shareText,
          url: shareUrl,
        });

        toast({
          title: "Shared Successfully",
          description: "Profile shared successfully",
        });
      } else {
        await navigator.clipboard.writeText(shareUrl);
        toast({
          title: "Link Copied",
          description: "Profile link copied to clipboard",
        });
      }
    } catch (error) {
      console.error("Share error:", error);
      toast({
        title: "Share Failed",
        description: "Failed to share profile",
        variant: "destructive",
      });
    }
  };

  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>) => {
    e.currentTarget.src = "/default-avatar.jpg";
  };

  const FollowButton = () => (
    <Button
      onClick={handleFollow}
      variant={isFollowing ? "outline" : "default"}
      className="gap-2"
      disabled={followStatus.isLoading}
    >
      {followStatus.isLoading ? (
        <span className="flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          {isFollowing ? "Unfollowing..." : "Following..."}
        </span>
      ) : isFollowing ? (
        <>
          <UserMinus className="w-4 h-4" />
          Following
        </>
      ) : (
        <>
          <UserPlus className="w-4 h-4" />
          Follow
        </>
      )}
    </Button>
  );

  return (
    <div className="space-y-6 py-6 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {isLoading ? (
        <div className="flex items-center justify-center h-96">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin" />
        </div>
      ) : isEditing && isOwnProfile ? (
        <ProfileEdit
          profile={updatedProfile}
          user={currentUser!}
          onCancel={handleEditToggle}
          onSave={handleProfileUpdated}
        />
      ) : (
        <>
          {/* Banner Section */}
          <div className="relative w-full h-[300px] rounded-xl overflow-hidden bg-linear-to-r from-primary/10 to-secondary/10">
            {updatedProfile.banner_url && (
              <img
                src={updatedProfile.banner_url}
                alt="Profile Banner"
                className="w-full h-full object-cover"
              />
            )}
            <div className="absolute bottom-0 left-0 right-0 h-24 bg-linear-to-t from-black/60 to-transparent" />
          </div>

          {/* Profile Info Section */}
          <div className="relative z-10 -mt-20 px-4">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
              {/* Avatar */}
              <Avatar className="w-32 h-32 border-4 border-background rounded-full shadow-xl">
                <AvatarImage
                  src={updatedProfile.avatar_url || "/default-avatar.jpg"}
                  alt={updatedProfile.username || "User Avatar"}
                  className="object-cover"
                  onError={handleImageError}
                />
                <AvatarFallback className="text-2xl">
                  {updatedProfile.username?.[0]?.toUpperCase() || "?"}
                </AvatarFallback>
              </Avatar>

              {/* Profile Info */}
              <div className="flex-1 md:mt-16 space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <h1 className="text-2xl font-bold">
                        @{updatedProfile.username}
                      </h1>
                      {isOwnProfile && <Badge variant="outline">You</Badge>}
                    </div>
                    <p className="text-muted-foreground">
                      {updatedProfile.bio || "No bio available."}
                    </p>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 sm:ml-auto">
                    {isOwnProfile ? (
                      <Button
                        onClick={handleEditToggle}
                        variant="outline"
                        className="gap-2"
                      >
                        <Pencil className="w-4 h-4" />
                        Edit Profile
                      </Button>
                    ) : (
                      <FollowButton />
                    )}
                    <Button
                      onClick={handleShare}
                      variant="outline"
                      size="icon"
                      className="shrink-0"
                    >
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>

                {/* Stats */}
                <div className="flex gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Users className="w-4 h-4" />
                    <span>{followersCount} followers</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CalendarDays className="w-4 h-4" />
                    <span>Joined {formatDate(updatedProfile.created_at)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator className="my-8" />

          {/* Content Grid - Stats Card and Articles */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Stats Card */}
            <div className="lg:col-span-1">
              <UserStatsCard profile={updatedProfile} isOwnProfile={isOwnProfile} />
            </div>

            {/* Articles Section */}
            <div className="lg:col-span-2 space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-semibold">Articles</h2>
                <Badge variant="secondary">
                  {profile.article_count || 0}{" "}
                  {profile.article_count === 1 ? "Article" : "Articles"}
                </Badge>
              </div>

            {articles.length > 0 ? (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {articles.map((article) => (
                  <Link
                    key={article.id}
                    href={`/ctn/articles/${article.id}`}
                    className="block group"
                  >
                    <Card className="h-full transition-all duration-200 hover:shadow-lg">
                      <CardHeader>
                        <CardTitle className="line-clamp-2 group-hover:text-primary transition-colors">
                          {article.title}
                        </CardTitle>
                        <CardDescription>
                          {formatDate(article.timestamp || "")}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <p className="line-clamp-3 text-muted-foreground">
                          {article.content || "No content available"}
                        </p>
                      </CardContent>
                      <CardFooter>
                        <div className="flex gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Heart className="w-4 h-4" />
                            <span>{article.likes ?? 0}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MessageSquare className="w-4 h-4" />
                            <span>{article.comments_count ?? 0}</span>
                          </div>
                        </div>
                      </CardFooter>
                    </Card>
                  </Link>
                ))}
              </div>
            ) : (
              <Card className="bg-muted">
                <CardContent className="flex items-center justify-center h-32">
                  <p className="text-muted-foreground">
                    No articles published yet.
                  </p>
                </CardContent>
              </Card>
            )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}