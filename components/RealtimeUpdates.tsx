"use client";

import { useEffect, useRef } from "react";
import { createClient } from "@/utils/supabase/client";
import { toast } from "@/hooks/use-toast";

interface RealtimeUpdatesProps {
  userId: string;
  onArticleUpdate?: (article: any) => void;
  onLikeUpdate?: (articleId: number, liked: boolean, likesCount: number) => void;
  onCommentUpdate?: (articleId: number, commentsCount: number) => void;
  onNewNotification?: (notification: any) => void;
}

export function RealtimeUpdates({
  userId,
  onArticleUpdate,
  onLikeUpdate,
  onCommentUpdate,
  onNewNotification,
}: RealtimeUpdatesProps) {
  const supabase = createClient();
  const channelsRef = useRef<any[]>([]);

  useEffect(() => {
    // Subscribe to articles changes
    const articlesChannel = supabase
      .channel("articles-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "articles",
        },
        (payload) => {
          if (payload.eventType === "INSERT" || payload.eventType === "UPDATE") {
            onArticleUpdate?.(payload.new);
          }
        }
      )
      .subscribe();

    // Subscribe to likes changes
    const likesChannel = supabase
      .channel("likes-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "likes",
        },
        async (payload) => {
          if (payload.eventType === "INSERT" || payload.eventType === "DELETE") {
            // Get updated likes count
            const articleId = (payload.new as any)?.article_id || (payload.old as any)?.article_id;
            const { data: likesData } = await (supabase as any)
              .from("likes")
              .select("article_id")
              .eq("article_id", articleId);
            
            const likesCount = likesData?.length || 0;
            const isLiked = payload.eventType === "INSERT" && (payload.new as any)?.user_id === userId;
            
            onLikeUpdate?.(articleId, isLiked, likesCount);
          }
        }
      )
      .subscribe();

    // Subscribe to comments changes
    const commentsChannel = supabase
      .channel("comments-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "comments",
        },
        async (payload) => {
          if (payload.eventType === "INSERT" || payload.eventType === "DELETE") {
            // Get updated comments count
            const articleId = (payload.new as any)?.article_id || (payload.old as any)?.article_id;
            const { data: commentsData } = await (supabase as any)
              .from("comments")
              .select("article_id")
              .eq("article_id", articleId);
            
            const commentsCount = commentsData?.length || 0;
            
            onCommentUpdate?.(articleId, commentsCount);
          }
        }
      )
      .subscribe();

    // Subscribe to notifications for current user
    const notificationsChannel = supabase
      .channel("notifications-changes")
      .on(
        "postgres_changes",
        {
          event: "INSERT",
          schema: "public",
          table: "notifications",
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          onNewNotification?.(payload.new);
          
          // Show toast notification
          toast({
            title: payload.new.title,
            description: payload.new.message,
          });
        }
      )
      .subscribe();

    // Store channels for cleanup
    channelsRef.current = [
      articlesChannel,
      likesChannel,
      commentsChannel,
      notificationsChannel,
    ];

    return () => {
      // Cleanup all channels
      channelsRef.current.forEach((channel) => {
        supabase.removeChannel(channel);
      });
    };
  }, [userId, onArticleUpdate, onLikeUpdate, onCommentUpdate, onNewNotification, supabase]);

  return null; // This component doesn't render anything
}

// Hook for using real-time updates
export function useRealtimeUpdates(userId: string) {
  const supabase = createClient();

  const subscribeToArticleUpdates = (callback: (article: any) => void) => {
    return supabase
      .channel("article-updates")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "articles",
        },
        (payload) => {
          callback(payload.new);
        }
      )
      .subscribe();
  };

  const subscribeToEngagementUpdates = (
    articleId: number,
    callback: (engagement: any) => void
  ) => {
    return supabase
      .channel(`engagement-${articleId}`)
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "likes",
          filter: `article_id=eq.${articleId}`,
        },
        callback
      )
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "comments",
          filter: `article_id=eq.${articleId}`,
        },
        callback
      )
      .subscribe();
  };

  return {
    subscribeToArticleUpdates,
    subscribeToEngagementUpdates,
  };
}
