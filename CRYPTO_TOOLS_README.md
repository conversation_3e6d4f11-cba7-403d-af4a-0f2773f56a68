# Crypto Tools - Real-Time Cryptocurrency Analytics Platform

A comprehensive cryptocurrency analysis and portfolio management platform built with Next.js, featuring real-time market data, advanced analytics, and AI-powered insights.

## 🚀 Quick Start with Real API Data

This project includes full API integrations for real cryptocurrency data! Follow these steps to get started:

### 1. <PERSON><PERSON> and Install
```bash
git clone <repository-url>
cd crypto-talks
npm install
```

### 2. Configure API Keys
Copy the example environment file and add your API keys:
```bash
cp .env.example .env
```

Edit `.env` and add your API keys:
- **🚀 Binance API**: FREE! No API key needed for market data (1,200 req/min)
- **CryptoNews API**: Get key at https://cryptonews-api.com/register
- **NewsAPI**: Get free key at https://newsapi.org/register
- **CoinGecko API**: Optional fallback at https://www.coingecko.com/en/api/pricing

### 3. Test API Integrations
```bash
npx tsx scripts/test-api-integrations.ts
```

### 4. Start Development Server
```bash
npm run dev
```

Visit http://localhost:3000 to see real cryptocurrency data in action!

## 📊 Features with Real Data

### Market Data & Analytics
- **Real-time Market Data**: Live prices from CoinGecko API
- **WebSocket Price Feeds**: Real-time updates via Binance WebSocket
- **Market Heatmap**: Live market visualization with 100+ cryptocurrencies
- **Technical Indicators**: Real OHLCV data for chart analysis
- **Trending Coins**: Real-time trending cryptocurrency data

### Portfolio Management
- **Portfolio Tracking**: Real-time portfolio valuation
- **Performance Analytics**: Track gains/losses with real market data
- **Asset Allocation**: Visual breakdown of portfolio composition
- **Historical Performance**: Track portfolio value over time

### News & Insights
- **Crypto News**: Latest news from specialized crypto sources
- **Sentiment Analysis**: AI-powered sentiment scoring
- **Market Insights**: Automated analysis and recommendations

### On-Chain Analytics
- **Blockchain Metrics**: Enhanced with real market data
- **DeFi Analytics**: Total Value Locked (TVL) data from DefiLlama
- **Network Statistics**: Real transaction and address data
- **Cross-Chain Comparison**: Compare different blockchain networks

## 🏗️ Architecture

### API Integrations (Smart Cost-Optimized Strategy!)
- **🥇 Binance API**: Primary market data (FREE, 1,200 calls/min!)
- **🥇 Binance WebSocket**: Real-time price streams (FREE)
- **🥈 CoinGecko API**: Fallback for coins not on Binance (30 calls/min free)
- **🥉 CryptoNews API**: Crypto-specific news and sentiment
- **🥉 DefiLlama API**: DeFi protocol TVL data (free)
- **🥉 NewsAPI**: Backup news source (1,000 calls/month free)

### Core Components
- **API Client**: Centralized HTTP client with rate limiting and caching
- **WebSocket Service**: Real-time data streaming with auto-reconnection
- **Data Transformers**: Normalize data across different API sources
- **Real-Time Service**: Orchestrates data from multiple sources
- **Error Handling**: Comprehensive fallback mechanisms

### Technology Stack
- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API routes, Server-side rendering
- **Database**: Supabase (PostgreSQL)
- **Real-time**: WebSocket connections, Server-sent events
- **Charts**: Recharts, TradingView widgets
- **UI Components**: shadcn/ui, Radix UI

## 📖 Documentation

- [API Integration Guide](docs/API_INTEGRATION_GUIDE.md) - Complete setup and usage guide
- [Environment Configuration](.env.example) - All available configuration options

## 🔧 Configuration

### Environment Variables

```bash
# 🚀 PRIMARY: Binance API (FREE - No key needed for market data!)
# Provides 1,200 requests/minute for free - 40x more than CoinGecko!
BINANCE_API_KEY=optional_for_trading_features_only
BINANCE_API_SECRET=optional_for_trading_features_only

# News APIs (Required for news features)
NEWSDATA_API_KEY=your_NEWSDATA_API_key_here
NEWS_API_KEY=your_news_api_key_here

# Optional Fallback APIs
COINGECKO_API_KEY=your_coingecko_api_key_here_optional
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here_optional

# Feature Flags
ENABLE_REAL_TIME_DATA=true
ENABLE_WEBSOCKET_CONNECTIONS=true
FALLBACK_TO_MOCK_DATA=true

# Rate Limiting
API_RATE_LIMIT_REQUESTS_PER_MINUTE=30
API_CACHE_TTL_SECONDS=300
```

### Feature Flags
- `ENABLE_REAL_TIME_DATA`: Enable real-time data fetching
- `ENABLE_WEBSOCKET_CONNECTIONS`: Enable WebSocket price streams
- `FALLBACK_TO_MOCK_DATA`: Use mock data when APIs fail

## 🧪 Testing

### API Integration Tests
```bash
# Test all API integrations
npx tsx scripts/test-api-integrations.ts

# Test specific components
npm run test
```

### Manual Testing Checklist
- [ ] Market data loads with real prices
- [ ] WebSocket connections establish successfully
- [ ] News articles load from crypto sources
- [ ] Portfolio calculator uses real prices
- [ ] Heatmap displays live market data
- [ ] Error handling works when APIs fail

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Environment Setup for Production
- Set all required API keys
- Enable real-time features
- Configure rate limiting based on API plans
- Set up monitoring and alerts

## 📊 API Usage & Costs

### 💰 Cost-Optimized API Limits (Binance-First Strategy!)
- **🚀 Binance REST API**: 1,200 calls/min (FREE!) - Our primary source
- **🚀 Binance WebSocket**: Unlimited real-time data (FREE!)
- **DefiLlama**: Unlimited (FREE!)
- **CryptoNews**: Rate limited (check their docs)
- **NewsAPI**: 1,000 calls/month (FREE!)
- **CoinGecko**: 30 calls/min, 10,000/month (FREE!) - Only used as fallback

### 💡 Why This Strategy Saves Money
- **Before**: CoinGecko Pro needed at $129/month for decent limits
- **Now**: Binance provides 40x more requests for FREE!
- **Result**: Dramatically reduced API costs while improving performance

### Optional Upgrades (Only if Needed)
- **CoinGecko Pro**: $129/month (only if you need many non-Binance coins)
- **NewsAPI Pro**: $449/month (only for high-volume news needs)
- **Most users won't need any paid plans!**

## 🔍 Monitoring

### Health Checks
- API endpoint health monitoring
- WebSocket connection status
- Rate limit tracking
- Error rate monitoring

### Debug Tools
```bash
# Check API connectivity
npx tsx scripts/test-api-integrations.ts

# Monitor WebSocket connections
# Check browser console for real-time connection status

# View API cache status
# Available in browser dev tools
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add your changes with tests
4. Update documentation
5. Submit a pull request

### Development Guidelines
- Follow TypeScript best practices
- Add error handling for all API calls
- Include fallback mechanisms
- Update tests and documentation
- Test with real API data

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Common Issues
- **Rate Limiting**: Reduce request frequency or upgrade API plan
- **API Key Errors**: Verify keys in .env file
- **WebSocket Issues**: Check network connectivity
- **CORS Errors**: API calls are server-side to avoid CORS

### Getting Help
- Check the [API Integration Guide](docs/API_INTEGRATION_GUIDE.md)
- Review console logs for error messages
- Test API connectivity with the test script
- Ensure all environment variables are set

### API Provider Support
- [CoinGecko Documentation](https://www.coingecko.com/en/api/documentation)
- [CryptoNews API Docs](https://cryptonews-api.com/)
- [Binance API Docs](https://binance-docs.github.io/apidocs/)
- [NewsAPI Documentation](https://newsapi.org/docs)

---

**Built with ❤️ for the crypto community**
