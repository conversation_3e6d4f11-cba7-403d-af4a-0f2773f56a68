// Binance API Service - Primary data source (free with high rate limits)
import { APIClient, createAPIClient } from './api-client';
import { API_CONFIG, CACHE_DURATIONS, FEATURE_FLAGS } from './api-config';
import { transformBinanceTickerData, normalizeSymbol, toBinanceSymbol, isSupportedPair } from './data-transformers';

// Binance API response types
export interface BinanceTicker24hr {
  symbol: string;
  priceChange: string;
  priceChangePercent: string;
  weightedAvgPrice: string;
  prevClosePrice: string;
  lastPrice: string;
  lastQty: string;
  bidPrice: string;
  bidQty: string;
  askPrice: string;
  askQty: string;
  openPrice: string;
  highPrice: string;
  lowPrice: string;
  volume: string;
  quoteVolume: string;
  openTime: number;
  closeTime: number;
  firstId: number;
  lastId: number;
  count: number;
}

export interface BinancePrice {
  symbol: string;
  price: string;
}

export interface BinanceKline {
  openTime: number;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
  closeTime: number;
  quoteAssetVolume: string;
  numberOfTrades: number;
  takerBuyBaseAssetVolume: string;
  takerBuyQuoteAssetVolume: string;
}

export interface BinanceExchangeInfo {
  timezone: string;
  serverTime: number;
  symbols: Array<{
    symbol: string;
    status: string;
    baseAsset: string;
    baseAssetPrecision: number;
    quoteAsset: string;
    quotePrecision: number;
    quoteAssetPrecision: number;
    baseCommissionPrecision: number;
    quoteCommissionPrecision: number;
    orderTypes: string[];
    icebergAllowed: boolean;
    ocoAllowed: boolean;
    isSpotTradingAllowed: boolean;
    isMarginTradingAllowed: boolean;
    permissions: string[];
  }>;
}

// Binance API Service
export class BinanceService {
  private client: APIClient;
  private exchangeInfo: BinanceExchangeInfo | null = null;
  private supportedSymbols: Set<string> = new Set();
  private failureCount: number = 0;
  private lastFailureTime: number = 0;
  private readonly MAX_FAILURES = 3;
  private readonly COOLDOWN_PERIOD = 2 * 60 * 1000; // 2 minutes (reduced for testing)

  constructor() {
    this.client = createAPIClient(
      API_CONFIG.BINANCE.REST_BASE_URL,
      undefined, // No API key needed for public endpoints
      API_CONFIG.BINANCE.RATE_LIMITS.REQUESTS_PER_MINUTE
    );

    // Only initialize exchange info on server-side
    if (typeof window === 'undefined') {
      this.initializeExchangeInfo();
    }
  }

  // Check if service should be used based on recent failures
  private shouldSkipService(): boolean {
    if (this.failureCount >= this.MAX_FAILURES) {
      const timeSinceLastFailure = Date.now() - this.lastFailureTime;
      if (timeSinceLastFailure < this.COOLDOWN_PERIOD) {
        console.log(`⏸️ Binance service in cooldown. ${Math.ceil((this.COOLDOWN_PERIOD - timeSinceLastFailure) / 1000)}s remaining`);
        return true;
      } else {
        // Reset failure count after cooldown
        this.failureCount = 0;
        console.log('🔄 Binance service cooldown ended, resuming');
      }
    }
    return false;
  }

  // Record a failure
  private recordFailure(): void {
    this.failureCount++;
    this.lastFailureTime = Date.now();
    console.log(`❌ Binance failure ${this.failureCount}/${this.MAX_FAILURES}`);
  }

  // Reset failure count on success
  private recordSuccess(): void {
    if (this.failureCount > 0) {
      console.log('✅ Binance service recovered');
      this.failureCount = 0;
    }
  }

  // Public method to reset the service (useful for debugging/testing)
  public resetFailures(): void {
    this.failureCount = 0;
    this.lastFailureTime = 0;
    console.log('🔄 Binance service failures manually reset');
  }

  // Get current service status
  public getServiceStatus() {
    return {
      failureCount: this.failureCount,
      lastFailureTime: this.lastFailureTime,
      inCooldown: this.shouldSkipService(),
      cooldownRemaining: this.shouldSkipService() ? 
        Math.ceil((this.COOLDOWN_PERIOD - (Date.now() - this.lastFailureTime)) / 1000) : 0
    };
  }

  // Initialize exchange information and supported symbols
  private async initializeExchangeInfo(): Promise<void> {
    try {
      this.exchangeInfo = await this.getExchangeInfo();
      if (this.exchangeInfo) {
        this.exchangeInfo.symbols.forEach(symbol => {
          if (symbol.status === 'TRADING' && symbol.quoteAsset === 'USDT') {
            this.supportedSymbols.add(symbol.baseAsset);
          }
        });
      }
    } catch (error) {
      console.error('Failed to initialize Binance exchange info:', error);
    }
  }

  // Get exchange information
  async getExchangeInfo(): Promise<BinanceExchangeInfo> {
    try {
      return await this.client.get(API_CONFIG.BINANCE.ENDPOINTS.EXCHANGE_INFO, undefined, {
        cacheTTL: CACHE_DURATIONS.STATIC_DATA, // Cache for 24 hours
      });
    } catch (error) {
      console.error('Failed to fetch Binance exchange info:', error);
      throw error;
    }
  }

  // Get 24hr ticker statistics for all symbols or specific symbols
  async get24hrTicker(symbols?: string[]): Promise<BinanceTicker24hr[]> {
    try {
      // Check if service should be skipped due to recent failures
      if (this.shouldSkipService()) {
        console.log('🚫 Skipping Binance 24hr ticker due to recent failures');
        if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
          return this.getMock24hrTicker(symbols);
        }
        // Return empty array instead of throwing during cooldown
        return [];
      }

      const params: any = {};
      
      if (symbols && symbols.length > 0) {
        // Filter and map symbols to supported pairs
        const supportedSymbols = symbols
          .map(s => toBinanceSymbol(s))
          .filter((s): s is string => s !== null); // Filter out null values
        
        if (supportedSymbols.length === 0) {
          console.log('⚠️ No supported Binance symbols found for 24hr ticker');
          return []; // Return empty array if no supported symbols
        }

        const binanceSymbols = supportedSymbols.map(s => `${s}USDT`);
        console.log(`🎯 Requesting 24hr ticker for supported symbols: ${binanceSymbols.join(', ')}`);
        
        if (binanceSymbols.length === 1) {
          params.symbol = binanceSymbols[0];
        } else {
          params.symbols = JSON.stringify(binanceSymbols);
        }
      }

      const response = await this.client.get(API_CONFIG.BINANCE.ENDPOINTS.TICKER_24HR, params, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA, // Cache for 1 minute
      });

      this.recordSuccess(); // Record success to reset failure count
      // Ensure response is an array
      return Array.isArray(response) ? response : [response];
    } catch (error) {
      this.recordFailure(); // Record failure for cooldown logic
      console.error('Failed to fetch Binance 24hr ticker:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return this.getMock24hrTicker(symbols);
      }
      throw error;
    }
  }

  // Get latest prices for all symbols or specific symbols
  async getPrices(symbols?: string[]): Promise<BinancePrice[]> {
    try {
      // Check if service should be skipped due to recent failures
      if (this.shouldSkipService()) {
        console.log('🚫 Skipping Binance due to recent failures');
        if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
          return this.getMockPrices(symbols);
        }
        // Return empty array instead of throwing during cooldown
        return [];
      }

      const params: any = {};
      
      if (symbols && symbols.length > 0) {
        // Filter and map symbols to supported pairs
        const supportedSymbols = symbols
          .map(s => toBinanceSymbol(s))
          .filter((s): s is string => s !== null); // Filter out null values
        
        if (supportedSymbols.length === 0) {
          console.log('⚠️ No supported Binance symbols found in request');
          return []; // Return empty array if no supported symbols
        }

        const binanceSymbols = supportedSymbols.map(s => `${s}USDT`);
        console.log(`🎯 Requesting supported Binance symbols: ${binanceSymbols.join(', ')}`);
        
        if (binanceSymbols.length === 1) {
          params.symbol = binanceSymbols[0];
        } else {
          params.symbols = JSON.stringify(binanceSymbols);
        }
      }

      const response = await this.client.get(API_CONFIG.BINANCE.ENDPOINTS.TICKER_PRICE, params, {
        cacheTTL: CACHE_DURATIONS.PRICE_DATA, // Cache for 30 seconds
      });

      this.recordSuccess(); // Record success to reset failure count
      return Array.isArray(response) ? response : [response];
    } catch (error) {
      this.recordFailure(); // Record failure for cooldown logic
      console.error('Failed to fetch Binance prices:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return this.getMockPrices(symbols);
      }
      throw error;
    }
  }

  // Get kline/candlestick data
  async getKlines(
    symbol: string,
    interval: string = '1h',
    limit: number = 100,
    startTime?: number,
    endTime?: number
  ): Promise<BinanceKline[]> {
    try {
      const params: any = {
        symbol: `${symbol.toUpperCase()}USDT`,
        interval,
        limit,
      };

      if (startTime) params.startTime = startTime;
      if (endTime) params.endTime = endTime;

      const response = await this.client.get(API_CONFIG.BINANCE.ENDPOINTS.KLINES, params, {
        cacheTTL: CACHE_DURATIONS.HISTORICAL_DATA, // Cache for 1 hour
      });

      // Transform array response to objects
      return response.map((kline: any[]) => ({
        openTime: kline[0],
        open: kline[1],
        high: kline[2],
        low: kline[3],
        close: kline[4],
        volume: kline[5],
        closeTime: kline[6],
        quoteAssetVolume: kline[7],
        numberOfTrades: kline[8],
        takerBuyBaseAssetVolume: kline[9],
        takerBuyQuoteAssetVolume: kline[10],
      }));
    } catch (error) {
      console.error(`Failed to fetch Binance klines for ${symbol}:`, error);
      throw error;
    }
  }

  // Check if a symbol is supported on Binance
  isSymbolSupported(symbol: string): boolean {
    return this.supportedSymbols.has(symbol.toUpperCase());
  }

  // Get all supported symbols
  getSupportedSymbols(): string[] {
    return Array.from(this.supportedSymbols);
  }

  // Transform Binance 24hr ticker to our market data format
  transformToMarketData(tickers: BinanceTicker24hr[]): any[] {
    return tickers.map(ticker => ({
      symbol: ticker.symbol.replace('USDT', ''), // Remove USDT suffix
      price: parseFloat(ticker.lastPrice),
      change24h: parseFloat(ticker.priceChangePercent),
      change7d: 0, // Not available in 24hr ticker
      volume24h: parseFloat(ticker.quoteVolume), // Quote volume (in USDT)
      marketCap: 0, // Not available from Binance
      high24h: parseFloat(ticker.highPrice),
      low24h: parseFloat(ticker.lowPrice),
      lastUpdated: ticker.closeTime,
    }));
  }

  // Transform to portfolio prices format
  transformToPortfolioPrices(prices: BinancePrice[]): Record<string, number> {
    const portfolioPrices: Record<string, number> = {};
    
    prices.forEach(price => {
      const symbol = price.symbol.replace('USDT', '');
      portfolioPrices[symbol] = parseFloat(price.price);
    });
    
    return portfolioPrices;
  }

  // Health check
  async ping(): Promise<boolean> {
    try {
      const response = await this.client.get('/ping', undefined, {
        cache: false,
        retries: 1,
        timeout: 5000,
      });
      return response !== null;
    } catch {
      return false;
    }
  }

  // Mock data fallbacks
  private getMock24hrTicker(symbols?: string[]): BinanceTicker24hr[] {
    const mockSymbols = symbols || ['BTC', 'ETH', 'SOL', 'ADA', 'DOT'];
    
    return mockSymbols.map(symbol => ({
      symbol: `${symbol}USDT`,
      priceChange: (Math.random() * 2000 - 1000).toFixed(2),
      priceChangePercent: (Math.random() * 20 - 10).toFixed(2),
      weightedAvgPrice: (Math.random() * 50000 + 1000).toFixed(2),
      prevClosePrice: (Math.random() * 50000 + 1000).toFixed(2),
      lastPrice: (Math.random() * 50000 + 1000).toFixed(2),
      lastQty: '1.00000000',
      bidPrice: (Math.random() * 50000 + 1000).toFixed(2),
      bidQty: '1.00000000',
      askPrice: (Math.random() * 50000 + 1000).toFixed(2),
      askQty: '1.00000000',
      openPrice: (Math.random() * 50000 + 1000).toFixed(2),
      highPrice: (Math.random() * 55000 + 1000).toFixed(2),
      lowPrice: (Math.random() * 45000 + 1000).toFixed(2),
      volume: (Math.random() * 1000000).toFixed(8),
      quoteVolume: (Math.random() * 1000000000).toFixed(8),
      openTime: Date.now() - 86400000,
      closeTime: Date.now(),
      firstId: 1,
      lastId: 1000,
      count: 1000,
    }));
  }

  private getMockPrices(symbols?: string[]): BinancePrice[] {
    const mockSymbols = symbols || ['BTC', 'ETH', 'SOL', 'ADA', 'DOT'];
    
    return mockSymbols.map(symbol => ({
      symbol: `${symbol}USDT`,
      price: (Math.random() * 50000 + 1000).toFixed(2),
    }));
  }
}

// Create singleton instance
export const binanceService = new BinanceService();
