// News scheduler for twice-daily cache refresh
import { cacheService, createCacheKey } from './cache-service';

export class NewsScheduler {
  private refreshInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // Start the twice-daily news refresh (every 12 hours)
  startScheduler(): void {
    if (this.isRunning) {
      console.log('📰 News scheduler already running');
      return;
    }

    console.log('📰 Starting news scheduler (twice daily refresh)');
    
    // Refresh every 12 hours (43,200,000 ms)
    this.refreshInterval = setInterval(() => {
      this.refreshNewsCache();
    }, 12 * 60 * 60 * 1000);

    // Also do an initial refresh
    this.refreshNewsCache();
    this.isRunning = true;
  }

  stopScheduler(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
      this.isRunning = false;
      console.log('📰 News scheduler stopped');
    }
  }

  // Manually trigger news cache refresh
  async refreshNewsCache(): Promise<void> {
    console.log('📰 Starting scheduled news cache refresh...');
    
    try {
      // Pre-warm cache with popular news categories
      const newsCategories = [
        { tickers: ['BTC', 'ETH'], items: 20 },
        { tickers: ['SOL', 'ADA', 'DOT'], items: 15 },
        { tickers: null, items: 30 }, // General crypto news
      ];

      const refreshPromises = newsCategories.map(async ({ tickers, items }) => {
        try {
          const baseUrl = process.env.NODE_ENV === 'production' 
            ? process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
            : 'http://localhost:3000';
          
          const tickersParam = tickers ? `&tickers=${tickers.join(',')}` : '';
          const url = `${baseUrl}/api/crypto-news?items=${items}${tickersParam}&refresh=true`;
          
          const response = await fetch(url);
          if (response.ok) {
            const data = await response.json();
            console.log(`✅ Refreshed news cache: ${data.totalResults} articles for ${tickers?.join(',') || 'general'}`);
          } else {
            console.warn(`⚠️ Failed to refresh news cache for ${tickers?.join(',') || 'general'}: ${response.statusText}`);
          }
        } catch (error) {
          console.error(`❌ Error refreshing news cache for ${tickers?.join(',') || 'general'}:`, error);
        }
      });

      await Promise.allSettled(refreshPromises);
      console.log('📰 Scheduled news cache refresh completed');
      
    } catch (error) {
      console.error('❌ News cache refresh failed:', error);
    }
  }

  // Get refresh status
  getStatus(): { isRunning: boolean; nextRefresh?: number } {
    return {
      isRunning: this.isRunning,
      nextRefresh: this.refreshInterval ? Date.now() + (12 * 60 * 60 * 1000) : undefined,
    };
  }

  // Clear all news cache
  clearNewsCache(): number {
    const deletedCount = cacheService.invalidateBySource('crypto-news');
    console.log(`🗑️ Cleared ${deletedCount} news cache entries`);
    return deletedCount;
  }

  // Get news cache statistics
  getNewsCacheStats(): { entries: number; sources: string[] } {
    const newsEntries = cacheService.getEntriesBySource('crypto-news');
    const sources = Array.from(new Set(newsEntries.map(entry => entry.entry.source)));
    
    return {
      entries: newsEntries.length,
      sources,
    };
  }
}

// Create singleton instance
export const newsScheduler = new NewsScheduler();

// Auto-start in production or when explicitly enabled
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_NEWS_SCHEDULER === 'true') {
  // Start after a short delay to allow app initialization
  setTimeout(() => {
    newsScheduler.startScheduler();
  }, 30000); // 30 seconds delay
}

// Graceful shutdown
process.on('SIGTERM', () => {
  newsScheduler.stopScheduler();
});

process.on('SIGINT', () => {
  newsScheduler.stopScheduler();
});