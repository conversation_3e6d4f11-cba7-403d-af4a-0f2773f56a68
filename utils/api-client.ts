// Core API Client with rate limiting, caching, and error handling
import { APIErrorType, RATE_LIMITS, CACHE_DURATIONS, FEATURE_FLAGS } from './api-config';
import { cacheService, createCache<PERSON>ey, CACHE_CONFIGS, type <PERSON><PERSON>Entry } from './cache-service';

// Rate limiter class
class RateLimiter {
  private requests: number[] = [];
  private readonly maxRequests: number;
  private readonly windowMs: number;

  constructor(maxRequests: number = RATE_LIMITS.REQUESTS_PER_MINUTE, windowMs: number = 60000) {
    this.maxRequests = maxRequests;
    this.windowMs = windowMs;
  }

  canMakeRequest(): boolean {
    const now = Date.now();
    // Remove requests outside the current window
    this.requests = this.requests.filter(time => now - time < this.windowMs);
    
    if (this.requests.length < this.maxRequests) {
      this.requests.push(now);
      return true;
    }
    return false;
  }

  getTimeUntilNextRequest(): number {
    if (this.requests.length < this.maxRequests) return 0;
    const oldestRequest = Math.min(...this.requests);
    return this.windowMs - (Date.now() - oldestRequest);
  }
}

// Cache implementation
class APICache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = CACHE_DURATIONS.MARKET_DATA): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl * 1000, // Convert to milliseconds
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    const isExpired = Date.now() - item.timestamp > item.ttl;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }
}

// API Error class
export class APIError extends Error {
  constructor(
    public type: APIErrorType,
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Main API Client class
export class APIClient {
  private rateLimiter: RateLimiter;
  private cache: APICache;
  private baseURL: string;
  private defaultHeaders: Record<string, string>;

  constructor(
    baseURL: string,
    apiKey?: string,
    maxRequestsPerMinute: number = RATE_LIMITS.REQUESTS_PER_MINUTE
  ) {
    this.baseURL = baseURL;
    this.rateLimiter = new RateLimiter(maxRequestsPerMinute);
    this.cache = new APICache();
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'User-Agent': 'CryptoTalks/1.0',
    };

    if (apiKey) {
      this.defaultHeaders['Authorization'] = `Bearer ${apiKey}`;
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getCacheKey(url: string, params?: Record<string, any>): string {
    const paramString = params ? JSON.stringify(params) : '';
    return `${url}${paramString}`;
  }

  private async handleResponse(response: Response): Promise<any> {
    if (!response.ok) {
      let errorType: APIErrorType;
      
      switch (response.status) {
        case 429:
          errorType = APIErrorType.RATE_LIMIT_EXCEEDED;
          break;
        case 401:
        case 403:
          errorType = APIErrorType.API_KEY_INVALID;
          break;
        case 503:
        case 504:
          errorType = APIErrorType.SERVICE_UNAVAILABLE;
          break;
        default:
          errorType = APIErrorType.NETWORK_ERROR;
      }

      const errorText = await response.text().catch(() => 'Unknown error');
      throw new APIError(
        errorType,
        `API request failed: ${response.status} ${response.statusText}`,
        response.status,
        errorText
      );
    }

    try {
      return await response.json();
    } catch (error) {
      throw new APIError(
        APIErrorType.INVALID_RESPONSE,
        'Failed to parse JSON response',
        response.status
      );
    }
  }

  async get(
    endpoint: string,
    params?: Record<string, any>,
    options: {
      cache?: boolean;
      cacheTTL?: number;
      retries?: number;
      timeout?: number;
      cacheSource?: string;
    } = {}
  ): Promise<any> {
    const {
      cache = true,
      cacheTTL = CACHE_DURATIONS.MARKET_DATA,
      retries = 3,
      timeout = 10000,
      cacheSource = 'api',
    } = options;

    const url = new URL(endpoint, this.baseURL);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    const cacheKey = createCacheKey('api', this.baseURL, endpoint, JSON.stringify(params || {}));

    // Check enhanced cache first
    if (cache) {
      const cachedData = cacheService.get(cacheKey);
      if (cachedData !== null) {
        console.log(`🎯 Cache hit: ${cacheKey}`);
        return cachedData;
      }
      console.log(`❌ Cache miss: ${cacheKey}`);
    }

    // Rate limiting
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilNextRequest();
      console.warn(`Rate limit exceeded. Waiting ${waitTime}ms before next request.`);
      await this.delay(waitTime);
    }

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: this.defaultHeaders,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        const data = await this.handleResponse(response);

        // Cache successful response using enhanced cache service
        if (cache) {
          cacheService.set(cacheKey, data, cacheTTL, cacheSource);
          console.log(`💾 Cached: ${cacheKey} (TTL: ${cacheTTL}s)`);
        }

        return data;
      } catch (error) {
        lastError = error as Error;
        
        if (error instanceof APIError) {
          // Don't retry on certain error types
          if (error.type === APIErrorType.API_KEY_INVALID) {
            throw error;
          }
          
          // For rate limit errors, wait and retry
          if (error.type === APIErrorType.RATE_LIMIT_EXCEEDED && attempt < retries) {
            const waitTime = Math.pow(2, attempt) * 1000; // Exponential backoff
            console.warn(`Rate limited. Retrying in ${waitTime}ms (attempt ${attempt + 1}/${retries + 1})`);
            await this.delay(waitTime);
            continue;
          }
        }

        // For network errors, retry with exponential backoff
        if (attempt < retries) {
          const waitTime = Math.pow(2, attempt) * 1000;
          console.warn(`Request failed. Retrying in ${waitTime}ms (attempt ${attempt + 1}/${retries + 1})`);
          await this.delay(waitTime);
          continue;
        }
      }
    }

    throw lastError || new APIError(APIErrorType.NETWORK_ERROR, 'All retry attempts failed');
  }

  async post(
    endpoint: string,
    data?: any,
    options: {
      retries?: number;
      timeout?: number;
    } = {}
  ): Promise<any> {
    const { retries = 3, timeout = 10000 } = options;

    const url = new URL(endpoint, this.baseURL);

    // Rate limiting
    if (!this.rateLimiter.canMakeRequest()) {
      const waitTime = this.rateLimiter.getTimeUntilNextRequest();
      await this.delay(waitTime);
    }

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url.toString(), {
          method: 'POST',
          headers: this.defaultHeaders,
          body: data ? JSON.stringify(data) : undefined,
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        return await this.handleResponse(response);
      } catch (error) {
        lastError = error as Error;

        if (attempt < retries) {
          const waitTime = Math.pow(2, attempt) * 1000;
          await this.delay(waitTime);
          continue;
        }
      }
    }

    throw lastError || new APIError(APIErrorType.NETWORK_ERROR, 'All retry attempts failed');
  }

  // Health check method
  async healthCheck(): Promise<boolean> {
    try {
      await this.get('/ping', undefined, { cache: false, retries: 1, timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  // Cache management
  clearCache(): void {
    this.cache.clear();
  }

  getCacheSize(): number {
    return this.cache.size();
  }

  // Rate limiter status
  getRateLimiterStatus(): { canMakeRequest: boolean; timeUntilNext: number } {
    return {
      canMakeRequest: this.rateLimiter.canMakeRequest(),
      timeUntilNext: this.rateLimiter.getTimeUntilNextRequest(),
    };
  }
}

// Utility function to create API clients
export function createAPIClient(
  baseURL: string,
  apiKey?: string,
  maxRequestsPerMinute?: number
): APIClient {
  return new APIClient(baseURL, apiKey, maxRequestsPerMinute);
}

// Export types
export type { APIErrorType };
