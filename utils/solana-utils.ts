import { PublicKey } from '@solana/web3.js';

/**
 * Validates if a string is a valid Solana wallet address
 */
export function isValidSolanaAddress(address: string): boolean {
  try {
    new PublicKey(address);
    return true;
  } catch {
    return false;
  }
}

/**
 * Gets the appropriate treasury wallet for the current environment
 */
export function getTreasuryWallet(): string {
  const isProduction = process.env.NODE_ENV === 'production';
  
  if (isProduction) {
    const prodWallet = process.env.NEXT_PUBLIC_SOLANA_TREASURY_WALLET_PROD;
    if (!prodWallet || !isValidSolanaAddress(prodWallet)) {
      console.error('Production Solana treasury wallet not configured or invalid');
      throw new Error('Production treasury wallet not configured');
    }
    return prodWallet;
  } else {
    const devWallet = process.env.NEXT_PUBLIC_SOLANA_TREASURY_WALLET_DEV;
    if (!devWallet || !isValidSolanaAddress(devWallet)) {
      console.warn('Development Solana treasury wallet not configured, using placeholder');
      // Return a valid devnet wallet address for development (you should replace this)
      return 'DEVtEST1111111111111111111111111111111111111111111';
    }
    return devWallet;
  }
}

/**
 * Generates a unique memo for Solana Pay transactions
 */
export function generatePaymentMemo(
  type: 'subscription' | 'donation',
  planName?: string,
  billingCycle?: string,
  userId?: string
): string {
  const timestamp = Date.now();
  const userPart = userId || 'guest';
  
  if (type === 'subscription' && planName && billingCycle) {
    return `CTT-subscription-${planName}-${billingCycle}-${userPart}-${timestamp}`;
  } else {
    return `CTT-${type}-${userPart}-${timestamp}`;
  }
}

/**
 * Formats SOL amount for display
 */
export function formatSolAmount(amount: number): string {
  if (amount < 0.0001) {
    return amount.toExponential(2);
  }
  return amount.toFixed(4);
}

/**
 * Treasury wallet addresses for different environments
 */
export const TREASURY_WALLETS = {
  development: process.env.NEXT_PUBLIC_SOLANA_TREASURY_WALLET_DEV,
  production: process.env.NEXT_PUBLIC_SOLANA_TREASURY_WALLET_PROD,
} as const;
