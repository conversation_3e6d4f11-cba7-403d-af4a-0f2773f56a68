// DexScreener API Service - Free DEX data for on-chain analytics
// DexScreener provides free access to DEX pair data, TVL, volume, and price analytics
import { APIClient, createAPIClient } from './api-client';
import { CACHE_DURATIONS, FEATURE_FLAGS } from './api-config';

export interface DexPair {
  chainId: string;
  dexId: string;
  url: string;
  pairAddress: string;
  baseToken: {
    address: string;
    name: string;
    symbol: string;
  };
  quoteToken: {
    address: string;
    name: string;
    symbol: string;
  };
  priceNative: string;
  priceUsd: string;
  txns: {
    m5: { buys: number; sells: number };
    h1: { buys: number; sells: number };
    h6: { buys: number; sells: number };
    h24: { buys: number; sells: number };
  };
  volume: {
    h24: number;
    h6: number;
    h1: number;
    m5: number;
  };
  priceChange: {
    m5: number;
    h1: number;
    h6: number;
    h24: number;
  };
  liquidity?: {
    usd: number;
    base: number;
    quote: number;
  };
  fdv?: number;
  marketCap?: number;
  pairCreatedAt?: number;
}

export interface DexTokenInfo {
  address: string;
  name: string;
  symbol: string;
  pairs: DexPair[];
}

// DexScreener API Service - Completely free with generous rate limits
export class DexScreenerService {
  private client: APIClient;
  private baseUrl = 'https://api.dexscreener.com';

  constructor() {
    // DexScreener is completely free with no API key required
    this.client = createAPIClient(this.baseUrl, undefined, 300); // 300 requests per minute (generous)
  }

  // Get pairs for a specific token across all DEXes
  async getTokenPairs(tokenAddress: string): Promise<{ pairs: DexPair[] }> {
    try {
      const response = await this.client.get(`/latest/dex/tokens/${tokenAddress}`, undefined, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
      
      // Ensure we return a proper structure
      if (response && response.pairs && Array.isArray(response.pairs)) {
        return { pairs: response.pairs };
      } else {
        console.warn(`Invalid response structure for token ${tokenAddress}:`, response);
        return { pairs: [] };
      }
    } catch (error) {
      console.error(`Failed to fetch token pairs for ${tokenAddress}:`, error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { pairs: this.getMockPairs(tokenAddress) };
      }
      return { pairs: [] }; // Return empty array instead of throwing
    }
  }

  // Search for pairs by token symbols
  async searchPairs(query: string): Promise<{ pairs: DexPair[] }> {
    try {
      const response = await this.client.get(`/latest/dex/search/?q=${encodeURIComponent(query)}`, undefined, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
      
      // Ensure we return a proper structure
      if (response && response.pairs && Array.isArray(response.pairs)) {
        return { pairs: response.pairs };
      } else {
        console.warn(`Invalid search response structure for query ${query}:`, response);
        return { pairs: [] };
      }
    } catch (error) {
      console.error(`Failed to search pairs for ${query}:`, error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { pairs: this.getMockPairs() };
      }
      return { pairs: [] }; // Return empty array instead of throwing
    }
  }

  // Get pairs by specific pair addresses
  async getPairsByAddresses(pairAddresses: string[]): Promise<{ pairs: DexPair[] }> {
    try {
      const addresses = pairAddresses.join(',');
      const response = await this.client.get(`/latest/dex/pairs/${addresses}`, undefined, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
      
      // Ensure we return a proper structure
      if (response && response.pairs && Array.isArray(response.pairs)) {
        return { pairs: response.pairs };
      } else {
        console.warn(`Invalid pairs response structure for addresses ${addresses}:`, response);
        return { pairs: [] };
      }
    } catch (error) {
      console.error(`Failed to fetch pairs by addresses:`, error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { pairs: this.getMockPairs() };
      }
      return { pairs: [] }; // Return empty array instead of throwing
    }
  }

  // Get trending pairs across all DEXes
  async getTrendingPairs(): Promise<{ pairs: DexPair[] }> {
    try {
      // DexScreener doesn't have a specific trending endpoint, but we can simulate it
      // by getting the most traded pairs from popular DEXes
      const popularTokens = [
        '******************************************', // WETH
        '******************************************', // SOL (bridged)
        '******************************************', // DAI
      ];

      const allPairs: DexPair[] = [];
      for (const token of popularTokens) {
        try {
          const { pairs } = await this.getTokenPairs(token);
          if (pairs && Array.isArray(pairs)) {
            allPairs.push(...pairs.slice(0, 5)); // Top 5 pairs per token
          }
        } catch (error) {
          console.warn(`Failed to get pairs for ${token}:`, error);
        }
      }

      // Sort by volume and return top pairs
      const sortedPairs = allPairs
        .filter(pair => pair.volume.h24 > 0)
        .sort((a, b) => b.volume.h24 - a.volume.h24)
        .slice(0, 20);

      return { pairs: sortedPairs };
    } catch (error) {
      console.error('Failed to get trending pairs:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { pairs: this.getMockPairs() };
      }
      throw error;
    }
  }

  // Transform DexScreener data for on-chain analytics
  transformToOnChainMetrics(pairs: DexPair[]): any {
    const totalVolume24h = pairs.reduce((sum, pair) => sum + (pair.volume.h24 || 0), 0);
    const totalLiquidity = pairs.reduce((sum, pair) => sum + (pair.liquidity?.usd || 0), 0);
    const avgPriceChange24h = pairs.length > 0 ? 
      pairs.reduce((sum, pair) => sum + (pair.priceChange.h24 || 0), 0) / pairs.length : 0;

    const totalTransactions24h = pairs.reduce((sum, pair) => {
      return sum + (pair.txns.h24.buys || 0) + (pair.txns.h24.sells || 0);
    }, 0);

    return {
      totalVolume24h,
      totalLiquidity,
      avgPriceChange24h,
      totalTransactions24h,
      activePairs: pairs.length,
      timestamp: Date.now(),
    };
  }

  // Transform to portfolio data format
  transformToPortfolioData(pairs: DexPair[]): Record<string, any> {
    const portfolioData: Record<string, any> = {};

    pairs.forEach(pair => {
      const symbol = pair.baseToken.symbol;
      if (!portfolioData[symbol]) {
        portfolioData[symbol] = {
          price: parseFloat(pair.priceUsd),
          volume24h: pair.volume.h24,
          change24h: pair.priceChange.h24,
          liquidity: pair.liquidity?.usd || 0,
          lastUpdated: Date.now(),
        };
      }
    });

    return portfolioData;
  }

  // Get token profiles using the v1 endpoint
  async getTokenProfiles(): Promise<any> {
    try {
      const response = await this.client.get('/token-profiles/latest/v1', undefined, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
      return response;
    } catch (error) {
      console.error('Failed to fetch token profiles:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { data: [] };
      }
      throw error;
    }
  }

  // Health check
  async ping(): Promise<boolean> {
    try {
      // Try the token profiles endpoint to test connectivity
      await this.getTokenProfiles();
      return true;
    } catch {
      return false;
    }
  }

  // Mock data for development/fallback
  private getMockPairs(tokenAddress?: string): DexPair[] {
    return [
      {
        chainId: 'ethereum',
        dexId: 'uniswap',
        url: 'https://dexscreener.com/ethereum/0x...',
        pairAddress: '0x...',
        baseToken: {
          address: tokenAddress || '0x...',
          name: 'Mock Token',
          symbol: 'MOCK',
        },
        quoteToken: {
          address: '******************************************',
          name: 'Wrapped Ether',
          symbol: 'WETH',
        },
        priceNative: '1.5',
        priceUsd: '3750.50',
        txns: {
          m5: { buys: 12, sells: 8 },
          h1: { buys: 156, sells: 98 },
          h6: { buys: 789, sells: 612 },
          h24: { buys: 2156, sells: 1834 },
        },
        volume: {
          h24: 1250000,
          h6: 312500,
          h1: 52083,
          m5: 4340,
        },
        priceChange: {
          m5: 0.25,
          h1: 1.2,
          h6: -0.8,
          h24: 5.4,
        },
        liquidity: {
          usd: 2500000,
          base: 666.67,
          quote: 1000,
        },
        fdv: 37505000000,
        marketCap: 25003333333,
        pairCreatedAt: Date.now() - 86400000 * 30, // 30 days ago
      },
    ];
  }
}

// Create singleton instance
export const dexScreenerService = new DexScreenerService();

// Utility functions
export function calculateDEXMetrics(pairs: DexPair[]): Record<string, number> {
  const metrics: Record<string, number> = {};

  if (pairs.length === 0) return metrics;

  // Volume metrics
  metrics.totalVolume24h = pairs.reduce((sum, pair) => sum + (pair.volume.h24 || 0), 0);
  metrics.avgVolume24h = metrics.totalVolume24h / pairs.length;

  // Liquidity metrics
  const liquidityPairs = pairs.filter(p => p.liquidity?.usd);
  if (liquidityPairs.length > 0) {
    metrics.totalLiquidity = liquidityPairs.reduce((sum, pair) => sum + (pair.liquidity?.usd || 0), 0);
    metrics.avgLiquidity = metrics.totalLiquidity / liquidityPairs.length;
  }

  // Transaction metrics
  metrics.totalTransactions24h = pairs.reduce((sum, pair) => {
    return sum + (pair.txns.h24.buys || 0) + (pair.txns.h24.sells || 0);
  }, 0);

  // Price change metrics
  const priceChanges = pairs.map(p => p.priceChange.h24).filter(change => change !== undefined);
  if (priceChanges.length > 0) {
    metrics.avgPriceChange24h = priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length;
    metrics.maxPriceChange24h = Math.max(...priceChanges);
    metrics.minPriceChange24h = Math.min(...priceChanges);
  }

  // Market cap metrics
  const marketCaps = pairs.map(p => p.marketCap).filter(mc => mc !== undefined) as number[];
  if (marketCaps.length > 0) {
    metrics.totalMarketCap = marketCaps.reduce((sum, mc) => sum + mc, 0);
    metrics.avgMarketCap = metrics.totalMarketCap / marketCaps.length;
  }

  // Activity metrics
  metrics.activePairs = pairs.length;
  metrics.liquidityPairs = liquidityPairs.length;

  return metrics;
}