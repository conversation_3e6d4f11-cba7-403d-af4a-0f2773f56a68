// Strategy accuracy calculation utilities

export interface StrategyMarker {
  time: number;
  position: 'belowBar' | 'aboveBar';
  color: string;
  shape: 'arrowUp' | 'arrowDown';
  text: string;
  id: string;
  price?: number;
  type: 'buy' | 'sell';
}

export interface AccuracyResult {
  totalSignals: number;
  accurateSignals: number;
  accuracy: number;
  details: Array<{
    marker: StrategyMarker;
    nextMarker: StrategyMarker | null;
    isAccurate: boolean;
    reason: string;
  }>;
}

/**
 * Calculate strategy accuracy based on marker placement relative to each other
 * 
 * Rules for ZStrategy (alternating long/short positions):
 * - Buy long signal lower than previous sell short signal = GOOD (buying at lower price)
 * - Sell short signal higher than previous buy long signal = GOOD (selling at higher price)
 * - Buy long signal higher than previous sell short signal = BAD (buying at higher price)
 * - Sell short signal lower than previous buy long signal = BAD (selling at lower price)
 */
export function calculateStrategyAccuracy(
  markers: StrategyMarker[],
  priceData?: Array<{ time: number; close: number }>
): AccuracyResult {
  if (!markers || markers.length < 2) {
    return {
      totalSignals: markers?.length || 0,
      accurateSignals: 0,
      accuracy: 0,
      details: []
    };
  }

  // Sort markers by time to ensure proper sequence
  const sortedMarkers = markers
    .map(marker => {
      // Extract price from text if not provided directly
      let price = marker.price;
      if (!price && marker.text) {
        const priceMatch = marker.text.match(/[\d,]+\.?\d*/);
        if (priceMatch) {
          price = parseFloat(priceMatch[0].replace(/,/g, ''));
        }
      }
      
      // If still no price, try to get it from price data
      if (!price && priceData) {
        const pricePoint = priceData.find(p => Math.abs(p.time - marker.time) < 60); // Within 1 minute
        price = pricePoint?.close;
      }

      return {
        ...marker,
        price,
        type: marker.shape === 'arrowUp' || marker.color === '#10b981' ? 'buy' as const : 'sell' as const
      };
    })
    .filter(marker => marker.price && marker.price > 0) // Only include markers with valid prices
    .sort((a, b) => a.time - b.time);

  console.log('📊 Calculating accuracy for', sortedMarkers.length, 'markers');

  let accurateSignals = 0;
  const details: AccuracyResult['details'] = [];

  // Analyze each signal compared to the previous one (starting from index 1)
  for (let i = 1; i < sortedMarkers.length; i++) {
    const currentMarker = sortedMarkers[i];
    const previousMarker = sortedMarkers[i - 1];

    if (!currentMarker.price || !previousMarker.price) {
      continue;
    }

    let isAccurate = false;
    let reason = '';

    // Apply accuracy rules: compare current signal to the immediately previous signal
    if (currentMarker.type === 'buy' && previousMarker.type === 'sell') {
      // Green (buy) marker after red (sell) marker
      if (currentMarker.price < previousMarker.price) {
        isAccurate = true;
        reason = `Buy signal (${currentMarker.price.toFixed(2)}) lower than previous sell signal (${previousMarker.price.toFixed(2)}) - GOOD`;
      } else {
        isAccurate = false;
        reason = `Buy signal (${currentMarker.price.toFixed(2)}) higher than previous sell signal (${previousMarker.price.toFixed(2)}) - BAD`;
      }
    } else if (currentMarker.type === 'sell' && previousMarker.type === 'buy') {
      // Red (sell) marker after green (buy) marker
      if (currentMarker.price > previousMarker.price) {
        isAccurate = true;
        reason = `Sell signal (${currentMarker.price.toFixed(2)}) higher than previous buy signal (${previousMarker.price.toFixed(2)}) - GOOD`;
      } else {
        isAccurate = false;
        reason = `Sell signal (${currentMarker.price.toFixed(2)}) lower than previous buy signal (${previousMarker.price.toFixed(2)}) - BAD`;
      }
    } else {
      // Same type signals in sequence - neutral (don't count toward accuracy)
      reason = `Sequential ${currentMarker.type} signals (${currentMarker.price.toFixed(2)} after ${previousMarker.price.toFixed(2)}) - NEUTRAL`;
      continue; // Skip counting this signal
    }

    if (isAccurate) {
      accurateSignals++;
    }

    details.push({
      marker: currentMarker,
      nextMarker: null, // Not used in this approach since we're looking backwards
      isAccurate,
      reason
    });

    console.log(`📊 Signal ${i}: ${reason}`);
  }

  const totalEvaluatedSignals = details.length;
  const accuracy = totalEvaluatedSignals > 0 ? (accurateSignals / totalEvaluatedSignals) * 100 : 0;

  console.log(`📊 Accuracy calculation complete:`, {
    totalMarkers: sortedMarkers.length,
    evaluatedPairs: totalEvaluatedSignals,
    accurateSignals,
    accuracy: accuracy.toFixed(2) + '%'
  });

  return {
    totalSignals: totalEvaluatedSignals,
    accurateSignals,
    accuracy: Math.round(accuracy),
    details
  };
}

/**
 * Calculate accuracy for multiple strategies
 */
export function calculateMultiStrategyAccuracy(
  strategiesWithMarkers: Array<{
    id: string;
    name: string;
    markers: StrategyMarker[];
  }>,
  priceData?: Array<{ time: number; close: number }>
): Array<{
  strategyId: string;
  strategyName: string;
  accuracy: AccuracyResult;
}> {
  return strategiesWithMarkers.map(strategy => ({
    strategyId: strategy.id,
    strategyName: strategy.name,
    accuracy: calculateStrategyAccuracy(strategy.markers, priceData)
  }));
}

/**
 * Get average accuracy across all strategies
 */
export function getAverageAccuracy(accuracyResults: AccuracyResult[]): number {
  if (accuracyResults.length === 0) return 0;
  
  const validResults = accuracyResults.filter(result => result.totalSignals > 0);
  if (validResults.length === 0) return 0;
  
  const totalAccuracy = validResults.reduce((sum, result) => sum + result.accuracy, 0);
  return Math.round(totalAccuracy / validResults.length);
}

/**
 * Convert existing markers to the StrategyMarker format
 */
export function convertMarkersToStrategyMarkers(markers: any[]): StrategyMarker[] {
  return markers.map(marker => ({
    time: marker.time,
    position: marker.position,
    color: marker.color,
    shape: marker.shape,
    text: marker.text,
    id: marker.id,
    price: marker.price,
    type: marker.shape === 'arrowUp' || marker.color === '#10b981' ? 'buy' as const : 'sell' as const
  }));
}