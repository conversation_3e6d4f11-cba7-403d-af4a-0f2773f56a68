// API Services for different data sources
// PRIORITY: Binance (free, high limits) -> CoinGecko (fallback) -> Mock data
import { APIClient, createAPIClient, APIError, APIErrorType } from './api-client';
import {
  API_CONFIG,
  CRYPTO_SYMBOLS,
  SYMBOL_TO_COINGECKO_ID,
  COINGECKO_ID_TO_SYMBOL,
  DEFAULT_PARAMS,
  CACHE_DURATIONS,
  FEATURE_FLAGS
} from './api-config';
import {
  transformCoinGeckoToMarketData,
  transformSimplePricesToPortfolio,
  transformCoinGeckoToHeatmap,
  transformNewsApiToArticles,
  transformCryptoNewsToArticles,
  validateAndSanitizeMarketData,
  handleAPIError,
  mergeMarketDataSources
} from './data-transformers';
import { binanceService, type BinanceTicker24hr } from './binance-service';

// Types for API responses
export interface CoinPrice {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation?: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  price_change_percentage_7d_in_currency?: number;
  circulating_supply: number;
  total_supply?: number;
  max_supply?: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

export interface SimplePriceResponse {
  [coinId: string]: {
    [key: string]: number; // For price, market_cap, 24h_vol, etc.
  };
}

export interface NewsArticle {
  title: string;
  description: string;
  url: string;
  urlToImage?: string;
  publishedAt: string;
  source?: {
    id?: string;
    name: string;
  };
  sentiment?: 'positive' | 'negative' | 'neutral';
}

export interface TrendingCoin {
  id: string;
  coin_id: number;
  name: string;
  symbol: string;
  market_cap_rank: number;
  thumb: string;
  small: string;
  large: string;
  slug: string;
  price_btc: number;
  score: number;
}

// CoinGecko API Service
export class CoinGeckoService {
  private client: APIClient;

  constructor() {
    this.client = createAPIClient(
      API_CONFIG.COINGECKO.BASE_URL,
      API_CONFIG.COINGECKO.API_KEY,
      API_CONFIG.COINGECKO.RATE_LIMITS.FREE_TIER
    );
  }

  async getSimplePrices(
    coinIds: string[],
    vsCurrencies: string[] = ['usd'],
    includeMarketCap: boolean = true,
    include24hrVol: boolean = true,
    include24hrChange: boolean = true
  ): Promise<SimplePriceResponse> {
    try {
      const params = {
        ids: coinIds.join(','),
        vs_currencies: vsCurrencies.join(','),
        include_market_cap: includeMarketCap,
        include_24hr_vol: include24hrVol,
        include_24hr_change: include24hrChange,
        include_last_updated_at: true,
      };

      return await this.client.get(API_CONFIG.COINGECKO.ENDPOINTS.SIMPLE_PRICE, params, {
        cacheTTL: CACHE_DURATIONS.PRICE_DATA,
      });
    } catch (error) {
      console.error('Failed to fetch simple prices:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return this.getMockSimplePrices(coinIds, vsCurrencies);
      }
      throw error;
    }
  }

  async getCoinsMarkets(
    vsCurrency: string = 'usd',
    order: string = 'market_cap_desc',
    perPage: number = 100,
    page: number = 1,
    sparkline: boolean = false,
    priceChangePercentage: string = '1h,24h,7d'
  ): Promise<CoinPrice[]> {
    try {
      const params = {
        vs_currency: vsCurrency,
        order,
        per_page: perPage,
        page,
        sparkline,
        price_change_percentage: priceChangePercentage,
      };

      // Check if we have an API key for CoinGecko
      if (!API_CONFIG.COINGECKO.API_KEY || API_CONFIG.COINGECKO.API_KEY === 'your_coingecko_api_key_here') {
        console.warn('⚠️ CoinGecko API key not configured, using mock data');
        return this.getMockCoinsMarkets();
      }

      return await this.client.get(API_CONFIG.COINGECKO.ENDPOINTS.COINS_MARKETS, params, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
    } catch (error) {
      console.error('Failed to fetch coins markets:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return this.getMockCoinsMarkets();
      }
      throw error;
    }
  }

  async getTrending(): Promise<{ coins: TrendingCoin[] }> {
    try {
      return await this.client.get(API_CONFIG.COINGECKO.ENDPOINTS.TRENDING, undefined, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
    } catch (error) {
      console.error('Failed to fetch trending coins:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { coins: [] };
      }
      throw error;
    }
  }

  async getCoinHistory(
    coinId: string,
    date: string, // Format: dd-mm-yyyy
    localization: boolean = false
  ): Promise<any> {
    try {
      const endpoint = API_CONFIG.COINGECKO.ENDPOINTS.COIN_HISTORY.replace('{id}', coinId);
      const params = {
        date,
        localization,
      };

      return await this.client.get(endpoint, params, {
        cacheTTL: CACHE_DURATIONS.HISTORICAL_DATA,
      });
    } catch (error) {
      console.error(`Failed to fetch coin history for ${coinId}:`, error);
      throw error;
    }
  }

  async getCoinMarketChart(
    coinId: string,
    vsCurrency: string = 'usd',
    days: number = 7,
    interval?: string
  ): Promise<any> {
    try {
      const endpoint = API_CONFIG.COINGECKO.ENDPOINTS.COIN_MARKET_CHART.replace('{id}', coinId);
      const params: any = {
        vs_currency: vsCurrency,
        days,
      };

      if (interval) {
        params.interval = interval;
      }

      return await this.client.get(endpoint, params, {
        cacheTTL: CACHE_DURATIONS.HISTORICAL_DATA,
      });
    } catch (error) {
      console.error(`Failed to fetch market chart for ${coinId}:`, error);
      throw error;
    }
  }

  async ping(): Promise<boolean> {
    try {
      const response = await this.client.get(API_CONFIG.COINGECKO.ENDPOINTS.PING, undefined, {
        cache: false,
        retries: 1,
        timeout: 5000,
      });
      return response && response.gecko_says === '(V3) To the Moon!';
    } catch {
      return false;
    }
  }

  // Mock data fallbacks
  private getMockSimplePrices(coinIds: string[], vsCurrencies: string[]): SimplePriceResponse {
    const mockPrices: SimplePriceResponse = {};
    
    coinIds.forEach(coinId => {
      mockPrices[coinId] = {};
      vsCurrencies.forEach(currency => {
        // Generate mock prices based on coin - Updated to current market prices
        const basePrice = coinId === 'bitcoin' ? 118953.53 :
                         coinId === 'ethereum' ? 3737.52 :
                         coinId === 'solana' ? 188.42 :
                         coinId === 'binancecoin' ? 779.13 :
                         coinId === 'cardano' ? 0.8206 :
                         Math.random() * 1000 + 10;
        
        mockPrices[coinId][currency] = basePrice;
        mockPrices[coinId][`${currency}_market_cap`] = basePrice * 19000000;
        mockPrices[coinId][`${currency}_24h_vol`] = basePrice * 1000000;
        mockPrices[coinId][`${currency}_24h_change`] = (Math.random() - 0.5) * 10;
      });
    });

    return mockPrices;
  }

  private getMockCoinsMarkets(): CoinPrice[] {
    return CRYPTO_SYMBOLS.slice(0, 10).map((coinId, index) => ({
      id: coinId,
      symbol: COINGECKO_ID_TO_SYMBOL[coinId] || coinId.toUpperCase(),
      name: coinId.charAt(0).toUpperCase() + coinId.slice(1),
      current_price: Math.random() * 50000 + 100,
      market_cap: Math.random() * 100000000000,
      market_cap_rank: index + 1,
      total_volume: Math.random() * 10000000000,
      high_24h: Math.random() * 55000 + 100,
      low_24h: Math.random() * 45000 + 100,
      price_change_24h: (Math.random() - 0.5) * 2000,
      price_change_percentage_24h: (Math.random() - 0.5) * 10,
      price_change_percentage_7d_in_currency: (Math.random() - 0.5) * 20,
      circulating_supply: Math.random() * 1000000000,
      total_supply: Math.random() * 1000000000,
      max_supply: Math.random() * 1000000000,
      ath: Math.random() * 60000 + 100,
      ath_change_percentage: (Math.random() - 1) * 50,
      ath_date: new Date().toISOString(),
      atl: Math.random() * 100,
      atl_change_percentage: Math.random() * 1000,
      atl_date: new Date().toISOString(),
      last_updated: new Date().toISOString(),
    }));
  }
}

// NewsData.io API Service (Crypto News)
export class CryptoNewsService {
  private client: APIClient;

  constructor() {
    this.client = createAPIClient(
      API_CONFIG.CRYPTO_NEWS.BASE_URL,
      API_CONFIG.CRYPTO_NEWS.API_KEY
    );
  }

  async getNews(
    tickers?: string[],
    items: number = 50,
    sortBy: string = 'published_desc'
  ): Promise<{ data: NewsArticle[] }> {
    try {
      // NewsData.io API parameters
      const params: any = {
        apikey: API_CONFIG.CRYPTO_NEWS.API_KEY,
        language: 'en',
        size: Math.min(items, 50), // NewsData.io max is 50
        category: 'business,technology',
        timeframe: '7', // Last 7 days
      };

      // Add crypto-related keywords
      let query = 'cryptocurrency OR bitcoin OR ethereum OR crypto OR blockchain';
      
      if (tickers && tickers.length > 0) {
        // Add specific ticker searches
        const tickerQueries = tickers.map(ticker => {
          const coinNames = {
            'BTC': 'bitcoin',
            'ETH': 'ethereum',
            'SOL': 'solana',
            'ADA': 'cardano',
            'XRP': 'ripple',
            'SUI': 'sui',
            'SEI': 'sei',
            'RAY': 'raydium',
            'JUP': 'jupiter',
            'BNB': 'binance',
            'PYTH': 'pyth',
            'NATIX': 'natix',
            'RENDER': 'render',
            'ZEUS': 'zeus',
            'APE': 'apecoin',
            'BONK': 'bonk',
            'DOGE': 'dogecoin',
            'FLOKI': 'floki',
            'PENGU': 'penguin',
            'PEPE': 'pepe',
            'SHIB': 'shiba',
            'WIF': 'dogwifhat'
          };
          const coinName = coinNames[ticker as keyof typeof coinNames] || ticker.toLowerCase();
          return `${ticker} OR ${coinName}`;
        }).join(' OR ');
        
        query = `(${query}) AND (${tickerQueries})`;
      }
      
      params.q = query;

      const response = await this.client.get(API_CONFIG.CRYPTO_NEWS.ENDPOINTS.NEWS, params, {
        cacheTTL: CACHE_DURATIONS.NEWS_DATA,
      });

      // Transform NewsData.io response to our format
      const articles: NewsArticle[] = (response.results || []).map((article: any) => ({
        title: article.title,
        description: article.description || article.content?.substring(0, 200) + '...',
        url: article.link,
        urlToImage: article.image_url,
        publishedAt: article.pubDate,
        source: { 
          id: article.source_id,
          name: article.source_name || article.source_id 
        },
        sentiment: article.sentiment, // NewsData.io provides sentiment analysis
      }));

      return { data: articles };
    } catch (error) {
      console.error('Failed to fetch crypto news from NewsData.io:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { data: this.getMockNews() };
      }
      throw error;
    }
  }

  private getMockNews(): NewsArticle[] {
    return [
      {
        title: "Bitcoin Reaches New Heights",
        description: "Bitcoin continues its upward trajectory as institutional adoption grows.",
        url: "https://example.com/bitcoin-news",
        urlToImage: "https://via.placeholder.com/300x200",
        publishedAt: new Date().toISOString(),
        source: { name: "CryptoNews" },
        sentiment: "positive",
      },
      {
        title: "Ethereum 2.0 Updates",
        description: "Latest developments in Ethereum's transition to proof-of-stake.",
        url: "https://example.com/ethereum-news",
        urlToImage: "https://via.placeholder.com/300x200",
        publishedAt: new Date(Date.now() - 3600000).toISOString(),
        source: { name: "CryptoNews" },
        sentiment: "neutral",
      },
      {
        title: "DeFi Market Analysis",
        description: "Comprehensive analysis of the current DeFi landscape and trends.",
        url: "https://example.com/defi-news",
        urlToImage: "https://via.placeholder.com/300x200",
        publishedAt: new Date(Date.now() - 7200000).toISOString(),
        source: { name: "CryptoNews" },
        sentiment: "neutral",
      },
    ];
  }
}

// Unified Market Data Service - Prioritizes Binance, falls back to CoinGecko
export class UnifiedMarketDataService {
  private coinGeckoService: CoinGeckoService;

  constructor() {
    this.coinGeckoService = new CoinGeckoService();
  }

  // Get market data with Binance priority
  async getMarketData(symbols?: string[], limit: number = 100): Promise<any[]> {
    try {
      // Step 1: Try to get data from Binance (free, high limits)
      console.log('Fetching market data from Binance (primary)...');
      const binanceTickers = await binanceService.get24hrTicker(symbols);
      const binanceData = binanceService.transformToMarketData(binanceTickers);

      // If we got good data from Binance and it covers our needs, use it
      if (binanceData.length > 0) {
        console.log(`✅ Got ${binanceData.length} coins from Binance`);

        // If we need more coins or specific coins not on Binance, supplement with CoinGecko
        if (!symbols || binanceData.length < limit) {
          try {
            console.log('Supplementing with CoinGecko data...');
            const coinGeckoData = await this.coinGeckoService.getCoinsMarkets('usd', 'market_cap_desc', limit);
            const transformedCoinGeckoData = transformCoinGeckoToMarketData(coinGeckoData);

            // Merge data sources (Binance takes priority)
            const mergedData = mergeMarketDataSources(binanceData, transformedCoinGeckoData);
            console.log(`✅ Merged data: ${mergedData.length} total coins`);
            return mergedData.slice(0, limit);
          } catch (coinGeckoError) {
            console.warn('CoinGecko supplement failed, using Binance data only:', coinGeckoError);
            return binanceData.slice(0, limit);
          }
        }

        return binanceData.slice(0, limit);
      }
    } catch (binanceError) {
      console.warn('Binance primary fetch failed, falling back to CoinGecko:', binanceError);
    }

    // Step 2: Fallback to CoinGecko if Binance fails
    try {
      console.log('Using CoinGecko as fallback...');
      const coinGeckoData = await this.coinGeckoService.getCoinsMarkets('usd', 'market_cap_desc', limit);
      return transformCoinGeckoToMarketData(coinGeckoData);
    } catch (coinGeckoError) {
      console.error('Both Binance and CoinGecko failed:', coinGeckoError);

      // Step 3: Final fallback to mock data
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        console.log('Using mock data as final fallback...');
        return this.getMockMarketData(symbols, limit);
      }

      throw new Error('All market data sources failed');
    }
  }

  // Get simple prices with Binance priority
  async getSimplePrices(symbols: string[]): Promise<Record<string, number>> {
    try {
      // Try Binance first
      const binancePrices = await binanceService.getPrices(symbols);
      const prices = binanceService.transformToPortfolioPrices(binancePrices);

      if (Object.keys(prices).length > 0) {
        console.log(`✅ Got prices for ${Object.keys(prices).length} symbols from Binance`);
        return prices;
      }
    } catch (binanceError) {
      console.warn('Binance prices failed, falling back to CoinGecko:', binanceError);
    }

    // Fallback to CoinGecko
    try {
      const coinIds = symbols.map(symbol => convertSymbolToCoinGeckoId(symbol));
      const coinGeckoPrices = await this.coinGeckoService.getSimplePrices(coinIds);
      return transformSimplePricesToPortfolio(coinGeckoPrices, symbols);
    } catch (error) {
      console.error('Both price sources failed:', error);

      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return this.getMockPrices(symbols);
      }

      throw error;
    }
  }

  // Get trending coins (CoinGecko only, as Binance doesn't have this)
  async getTrending(): Promise<{ coins: any[] }> {
    try {
      return await this.coinGeckoService.getTrending();
    } catch (error) {
      console.error('Failed to get trending coins:', error);
      return { coins: [] };
    }
  }

  // Health check for both services
  async healthCheck(): Promise<{ binance: boolean; coinGecko: boolean }> {
    const [binanceHealth, coinGeckoHealth] = await Promise.allSettled([
      binanceService.ping(),
      this.coinGeckoService.ping(),
    ]);

    return {
      binance: binanceHealth.status === 'fulfilled' && binanceHealth.value,
      coinGecko: coinGeckoHealth.status === 'fulfilled' && coinGeckoHealth.value,
    };
  }

  // Mock data fallbacks - now using centralized service
  private async getMockMarketData(symbols?: string[], limit: number = 100): Promise<any[]> {
    try {
      // Import centralized service dynamically to avoid circular dependencies
      const { getTradingData } = await import('./mock-data-service');
      const centralizedData = await getTradingData();

      // Filter by symbols if provided, otherwise return all
      let filteredData = centralizedData;
      if (symbols && symbols.length > 0) {
        filteredData = centralizedData.filter(coin =>
          symbols.some(symbol => symbol.toLowerCase() === coin.symbol.toLowerCase())
        );
      }

      return filteredData.slice(0, limit).map(coin => ({
        symbol: coin.symbol,
        price: coin.price,
        change24h: coin.change24h,
        change7d: Math.random() * 40 - 20, // 7d change not in trading format
        volume24h: coin.volume24h,
        marketCap: coin.marketCap,
        lastUpdated: coin.lastUpdated,
      }));
    } catch (error) {
      console.warn('⚠️ Centralized mock service failed, using legacy fallback:', error);

      // Legacy fallback - only use BASE coins
      const { BASE_COINS } = await import('./mock-data-service');
      const mockSymbols = symbols || BASE_COINS.map(coin => coin.symbol);
      return mockSymbols.slice(0, limit).map((symbol, index) => ({
        symbol,
        price: Math.random() * 50000 + 100,
        change24h: (Math.random() - 0.5) * 20,
        change7d: (Math.random() - 0.5) * 40,
        volume24h: Math.random() * 1000000000,
        marketCap: Math.random() * 100000000000,
        lastUpdated: Date.now(),
      }));
    }
  }

  private async getMockPrices(symbols: string[]): Promise<Record<string, number>> {
    try {
      const { getAllCoinsData } = await import('./mock-data-service');
      const centralizedData = await getAllCoinsData();

      const prices: Record<string, number> = {};
      symbols.forEach(symbol => {
        const coinData = centralizedData.find(coin =>
          coin.symbol.toLowerCase() === symbol.toLowerCase()
        );
        prices[symbol] = coinData?.price || Math.random() * 50000 + 100;
      });
      return prices;
    } catch (error) {
      console.warn('⚠️ Centralized mock service failed for prices, using legacy fallback:', error);

      // Legacy fallback
      const prices: Record<string, number> = {};
      symbols.forEach(symbol => {
        prices[symbol] = Math.random() * 50000 + 100;
      });
      return prices;
    }
  }
}

// Create singleton instances
export const coinGeckoService = new CoinGeckoService();
export const cryptoNewsService = new CryptoNewsService();
export const unifiedMarketDataService = new UnifiedMarketDataService();

// Utility functions
export function convertSymbolToCoinGeckoId(symbol: string): string {
  return SYMBOL_TO_COINGECKO_ID[symbol.toUpperCase()] || symbol.toLowerCase();
}

export function convertCoinGeckoIdToSymbol(coinId: string): string {
  return COINGECKO_ID_TO_SYMBOL[coinId] || coinId.toUpperCase();
}
