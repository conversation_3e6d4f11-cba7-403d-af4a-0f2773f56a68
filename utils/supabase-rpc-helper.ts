import { createClient } from '@/utils/supabase/client';

// Utility for handling RPC calls with fallback to mock data
export class SupabaseRPCHelper {
  private supabase = createClient();

  async callWithFallback<T>(
    functionName: string, 
    params: Record<string, any> = {},
    mockData: T,
    options: { requireAuth?: boolean } = {}
  ): Promise<{ data: T; error: null } | { data: null; error: any }> {
    try {
      // Check authentication if required
      if (options.requireAuth) {
        const { data: { user }, error: authError } = await this.supabase.auth.getUser();
        if (authError || !user) {
          console.warn(`Authentication required for ${functionName}, using mock data`);
          return { data: mockData, error: null };
        }
      }

      console.log(`Calling RPC function: ${functionName}`, params);
      
      const { data, error } = await this.supabase.rpc(functionName, params);
      
      if (error) {
        console.error(`RPC error for ${functionName}:`, error);
        console.log(`Falling back to mock data for ${functionName}`);
        return { data: mockData, error: null };
      }

      console.log(`RPC success for ${functionName}:`, data);
      return { data: data || mockData, error: null };
      
    } catch (err) {
      console.error(`RPC call failed for ${functionName}:`, err);
      console.log(`Using mock data for ${functionName}`);
      return { data: mockData, error: null };
    }
  }

  // Specific helper for experience leaderboard
  async getExperienceLeaderboard(limit: number = 50) {
    const mockData = [
      {
        user_id: '1',
        username: 'crypto_master',
        display_name: 'Crypto Master',
        avatar_url: '/default-avatar.jpg',
        tier: 'tier4',
        experience_points: 15420,
        level: 25,
        reputation_score: 98,
        rank: 1
      },
      {
        user_id: '2',
        username: 'defi_guru',
        display_name: 'DeFi Guru',
        avatar_url: '/default-avatar.jpg',
        tier: 'tier3',
        experience_points: 12850,
        level: 22,
        reputation_score: 95,
        rank: 2
      },
      {
        user_id: '3',
        username: 'nft_explorer',
        display_name: 'NFT Explorer',
        avatar_url: '/default-avatar.jpg',
        tier: 'tier3',
        experience_points: 11200,
        level: 20,
        reputation_score: 89,
        rank: 3
      },
      {
        user_id: '4',
        username: 'blockchain_dev',
        display_name: 'Blockchain Dev',
        avatar_url: '/default-avatar.jpg',
        tier: 'tier2',
        experience_points: 9800,
        level: 18,
        reputation_score: 85,
        rank: 4
      },
      {
        user_id: '5',
        username: 'smart_trader',
        display_name: 'Smart Trader',
        avatar_url: '/default-avatar.jpg',
        tier: 'tier2',
        experience_points: 8500,
        level: 16,
        reputation_score: 78,
        rank: 5
      }
    ].slice(0, limit);

    return this.callWithFallback(
      'get_experience_leaderboard',
      { p_limit: limit },
      mockData
    );
  }

  // Helper for trending articles
  async getTrendingArticles(limit: number = 10, timeRange: string = '7d') {
    const mockData = [
      {
        id: 1,
        title: 'Understanding DeFi: A Comprehensive Guide',
        content: 'DeFi has revolutionized...',
        author_username: 'crypto_expert',
        created_at: new Date().toISOString(),
        view_count: 1250,
        like_count: 89,
        comment_count: 23
      },
      {
        id: 2,
        title: 'Bitcoin Price Analysis for 2025',
        content: 'Bitcoin continues to show...',
        author_username: 'market_analyst',
        created_at: new Date(Date.now() - 86400000).toISOString(),
        view_count: 980,
        like_count: 67,
        comment_count: 18
      }
    ];

    return this.callWithFallback(
      'get_trending_articles',
      { p_limit: limit, p_time_range: timeRange },
      mockData
    );
  }

  // Helper for popular tags
  async getPopularTags(limit: number = 20) {
    const mockData = [
      { id: 1, name: 'defi', article_count: 152 },
      { id: 2, name: 'bitcoin', article_count: 134 },
      { id: 3, name: 'ethereum', article_count: 98 },
      { id: 4, name: 'nft', article_count: 87 },
      { id: 5, name: 'web3', article_count: 76 },
      { id: 6, name: 'blockchain', article_count: 65 },
      { id: 7, name: 'staking', article_count: 54 },
      { id: 8, name: 'trading', article_count: 43 },
      { id: 9, name: 'crypto', article_count: 38 },
      { id: 10, name: 'layer2', article_count: 29 },
      { id: 11, name: 'dao', article_count: 25 },
      { id: 12, name: 'yield', article_count: 22 },
      { id: 13, name: 'protocol', article_count: 20 },
      { id: 14, name: 'security', article_count: 18 },
      { id: 15, name: 'audit', article_count: 15 },
      { id: 16, name: 'governance', article_count: 12 },
      { id: 17, name: 'liquidity', article_count: 10 },
      { id: 18, name: 'mining', article_count: 8 },
      { id: 19, name: 'bridge', article_count: 6 },
      { id: 20, name: 'oracle', article_count: 4 }
    ].slice(0, limit);

    return this.callWithFallback(
      'get_popular_tags',
      {},
      mockData
    );
  }

  // Helper for trending hashtags (uses article_hashtags table)
  async getTrendingHashtags(limit: number = 15) {
    try {
      console.log(`Fetching trending hashtags from article_hashtags table`);
      
      // Get all hashtags from article_hashtags table
      const { data: hashtagData, error } = await this.supabase
        .from('article_hashtags')
        .select('article_tags');
      
      if (error) {
        console.error('Error fetching from article_hashtags:', error);
        throw error;
      }

      // Count hashtag occurrences
      const hashtagCounts = new Map<string, number>();
      
      hashtagData?.forEach(row => {
        row.article_tags?.forEach((tag: string) => {
          const count = hashtagCounts.get(tag) || 0;
          hashtagCounts.set(tag, count + 1);
        });
      });

      // Convert to sorted array and format for display
      const trendingHashtags = Array.from(hashtagCounts.entries())
        .sort(([,a], [,b]) => b - a)
        .slice(0, limit)
        .map(([name, usage_count]) => ({
          name,
          usage_count,
          recent_usage_count: Math.floor(usage_count * 0.8), // Estimate recent usage
          created_at: new Date().toISOString()
        }));

      console.log(`Found ${trendingHashtags.length} trending hashtags from article_hashtags`);
      return { data: trendingHashtags, error: null };
      
    } catch (err) {
      console.error('Error fetching from article_hashtags, falling back to get_popular_tags:', err);
      
      // Fallback to get_popular_tags RPC function
      const result = await this.callWithFallback(
        'get_popular_tags',
        {},
        []
      );

      if (result.data && result.data.length > 0) {
        const transformedData = result.data.map((tag: any) => ({
          name: tag.name,
          usage_count: tag.article_count || 0,
          recent_usage_count: Math.floor((tag.article_count || 0) * 0.3),
          created_at: new Date().toISOString()
        }));
        
        return { data: transformedData.slice(0, limit), error: null };
      }

      // Final fallback to mock data
      const mockData = [
        { name: 'defi', usage_count: 152, recent_usage_count: 45, created_at: new Date().toISOString() },
        { name: 'bitcoin', usage_count: 134, recent_usage_count: 40, created_at: new Date().toISOString() },
        { name: 'ethereum', usage_count: 98, recent_usage_count: 30, created_at: new Date().toISOString() },
        { name: 'nft', usage_count: 87, recent_usage_count: 26, created_at: new Date().toISOString() },
        { name: 'web3', usage_count: 76, recent_usage_count: 23, created_at: new Date().toISOString() },
        { name: 'blockchain', usage_count: 65, recent_usage_count: 19, created_at: new Date().toISOString() },
        { name: 'staking', usage_count: 54, recent_usage_count: 16, created_at: new Date().toISOString() },
        { name: 'trading', usage_count: 43, recent_usage_count: 13, created_at: new Date().toISOString() },
        { name: 'crypto', usage_count: 38, recent_usage_count: 11, created_at: new Date().toISOString() },
        { name: 'layer2', usage_count: 29, recent_usage_count: 9, created_at: new Date().toISOString() }
      ].slice(0, limit);

      return { data: mockData, error: null };
    }
  }
}

export const rpcHelper = new SupabaseRPCHelper();
