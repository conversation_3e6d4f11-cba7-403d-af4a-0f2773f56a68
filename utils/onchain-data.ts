// On-chain data integration utilities for production-level analytics
import { coinGeckoService, convertSymbolToCoinGeckoId } from './api-services';
import { messariService, convertSymbolToMessariKey } from './messari-service';
import { dexScreenerService } from './dexscreener-service';
import { FEATURE_FLAGS } from './api-config';

export interface ChainMetrics {
  name: string;
  symbol: string;
  logo: string;
  dailyActiveAddresses: number;
  dailyTransactions: number;
  tvl: number;
  fees: number;
  stakedSupply: number;
  newAddresses: number;
  transactionVolume: number;
  fdmc: number;
  marketCap: number;
  inflation: { mom: number; yoy: number };
  circulatingSupply: number;
  maxTps: number;
  realTimeTps: number;
  theoreticalTps: number;
  blockSize: number;
  avgFee: number;
  finalityTime: number;
  energyPerTx: number;
  validators: number;
  activeDevelopers: number;
  price: number;
  timestamp: number;
}

export interface OnChainModelData {
  id: string;
  name: string;
  data: any;
  timestamp: number;
}

// Data source configuration
const DATA_SOURCES = {
  COINGECKO: 'https://api.coingecko.com/api/v3',
  DEFILLAMA: 'https://api.llama.fi',
  MESSARI: 'https://data.messari.io/api/v1',
  GLASSNODE: 'https://api.glassnode.com/v1',
  CHAINSPECT: 'https://api.chainspect.app/v1', // hypothetical
  // Add more data sources as needed
};

// Rate limiting and caching
const REQUEST_CACHE = new Map<string, { data: any; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const RATE_LIMIT_DELAY = 1000; // 1 second between requests

// Utility function to delay requests for rate limiting
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Cache management
function getCachedData(key: string): any | null {
  const cached = REQUEST_CACHE.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  return null;
}

function setCachedData(key: string, data: any): void {
  REQUEST_CACHE.set(key, { data, timestamp: Date.now() });
}

// API request wrapper with error handling and caching
async function apiRequest(url: string, options?: RequestInit): Promise<any> {
  const cacheKey = `${url}_${JSON.stringify(options)}`;
  const cached = getCachedData(cacheKey);
  
  if (cached) {
    return cached;
  }

  try {
    await delay(RATE_LIMIT_DELAY);
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'CryptoTalksNetwork/1.0',
        ...options?.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    setCachedData(cacheKey, data);
    return data;
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}

// Enhanced market data fetching with multiple sources
export async function fetchMarketData(coinId: string): Promise<Partial<ChainMetrics>> {
  // Try multiple sources for comprehensive data
  let marketData: Partial<ChainMetrics> = {};

  // Step 1: Try Messari first (more comprehensive on-chain data)
  try {
    const messariKey = convertSymbolToMessariKey(coinId);
    const messariData = await messariService.getAssetMetrics(messariKey);
    const enhanced = messariService.transformToOnChainMetrics(messariData.data);
    marketData = { ...marketData, ...enhanced };
    console.log(`✅ Enhanced ${coinId} with Messari data`);
  } catch (error) {
    console.warn(`Messari data unavailable for ${coinId}:`, error);
  }

  // Step 2: Supplement with CoinGecko data (only if API key is configured)
  try {
    if (!process.env.COINGECKO_API_KEY || process.env.COINGECKO_API_KEY === 'your_coingecko_api_key_here') {
      console.warn(`⚠️ CoinGecko API key not configured, skipping CoinGecko data for ${coinId}`);
    } else {
      const coinsData = await coinGeckoService.getCoinsMarkets('usd', 'market_cap_desc', 250);
      const coinData = coinsData.find(coin => coin.id === coinId || coin.symbol.toLowerCase() === coinId.toLowerCase());

      if (coinData) {
        const coinGeckoData = {
          name: coinData.name,
          symbol: coinData.symbol.toUpperCase(),
          price: coinData.current_price,
          marketCap: coinData.market_cap,
          fdmc: coinData.fully_diluted_valuation || 0,
          circulatingSupply: coinData.circulating_supply,
          transactionVolume: coinData.total_volume,
          timestamp: Date.now(),
        };

        // Merge data, preferring Messari where available
        marketData = { ...coinGeckoData, ...marketData };
        console.log(`✅ Enhanced ${coinId} with CoinGecko data`);
      }
    }
  } catch (error) {
    console.warn(`CoinGecko data unavailable for ${coinId}:`, error);
  }

  // Step 3: Try DexScreener for DEX-specific data
  try {
    if (coinId.startsWith('0x') || coinId.length === 42) {
      // Looks like a contract address
      const dexData = await dexScreenerService.getTokenPairs(coinId);
      if (dexData.pairs.length > 0) {
        const dexMetrics = dexScreenerService.transformToOnChainMetrics(dexData.pairs);
        marketData = {
          ...marketData,
          tvl: dexMetrics.totalLiquidity,
          transactionVolume: dexMetrics.totalVolume24h,
          dailyTransactions: dexMetrics.totalTransactions24h,
        };
        console.log(`✅ Enhanced ${coinId} with DexScreener data`);
      }
    }
  } catch (error) {
    console.warn(`DexScreener data unavailable for ${coinId}:`, error);
  }

  if (Object.keys(marketData).length === 0) {
    console.warn(`No market data found for ${coinId}`);
    
    // Fallback to mock data if enabled
    if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
      return generateMockMarketData(coinId);
    }
  }

  return marketData;
}

// Generate mock market data for fallback - now using centralized service
async function generateMockMarketData(coinId: string): Promise<Partial<ChainMetrics>> {
  try {
    // Import centralized service dynamically to avoid circular dependencies
    const { getCoinData } = await import('./mock-data-service');

    // Map coinId to symbol for centralized service lookup - only BASE coins
    const { BASE_COINS_MAPPING } = await import('./mock-data-service');

    // Create reverse mapping from coinId to symbol using BASE_COINS_MAPPING
    const symbolMapping: Record<string, string> = {};
    Object.values(BASE_COINS_MAPPING).forEach(coin => {
      symbolMapping[coin.id] = coin.symbol;
    });

    const symbol = symbolMapping[coinId] || coinId.toUpperCase();
    const coinData = await getCoinData(symbol);

    if (coinData) {
      return {
        name: coinData.name,
        symbol: coinData.symbol,
        price: coinData.price,
        marketCap: coinData.marketCap,
        fdmc: coinData.marketCap * 1.2,
        circulatingSupply: coinData.marketCap / coinData.price,
        transactionVolume: coinData.volume24h,
        timestamp: coinData.lastUpdated,
      };
    }
  } catch (error) {
    console.warn('⚠️ Centralized mock service failed for onchain data, using legacy fallback:', error);
  }

  // Legacy fallback for emergency cases
  const mockData = {
    bitcoin: { name: 'Bitcoin', symbol: 'BTC', price: 118953.53, marketCap: 2366967578877 },
    ethereum: { name: 'Ethereum', symbol: 'ETH', price: 3737.52, marketCap: 451138108044 },
    solana: { name: 'Solana', symbol: 'SOL', price: 188.42, marketCap: 101408443821 },
    cardano: { name: 'Cardano', symbol: 'ADA', price: 0.8206, marketCap: 29057503815 },
    polkadot: { name: 'Polkadot', symbol: 'DOT', price: 6.85, marketCap: 9500000000 },
    binancecoin: { name: 'BNB', symbol: 'BNB', price: 779.13, marketCap: 108525324692 },
  };

  const mock = mockData[coinId as keyof typeof mockData] || {
    name: coinId.charAt(0).toUpperCase() + coinId.slice(1),
    symbol: coinId.toUpperCase(),
    price: Math.random() * 1000 + 10,
    marketCap: Math.random() * 10000000000,
  };

  return {
    ...mock,
    fdmc: mock.marketCap * 1.2,
    circulatingSupply: mock.marketCap / mock.price,
    transactionVolume: mock.marketCap * 0.1,
    timestamp: Date.now(),
  };
}

// Fetch TVL data from DefiLlama
export async function fetchTVLData(protocol: string): Promise<number> {
  try {
    // Map chain names to DefiLlama protocol names
    const protocolMapping: Record<string, string> = {
      ethereum: 'ethereum',
      solana: 'solana',
      cardano: 'cardano', // Note: Cardano has limited DeFi
      polkadot: 'polkadot', // Note: Polkadot ecosystem
      avalanche: 'avalanche',
      'avalanche-2': 'avalanche',
      polygon: 'polygon',
      binance: 'bsc', // Binance Smart Chain
    };

    const mappedProtocol = protocolMapping[protocol.toLowerCase()] || protocol;

    // Use DefiLlama chains API for better data
    const response = await fetch(`${DATA_SOURCES.DEFILLAMA}/chains`);
    if (!response.ok) {
      throw new Error(`DefiLlama API failed: ${response.statusText}`);
    }

    const chains = await response.json();
    const chainData = chains.find((chain: any) =>
      chain.name.toLowerCase() === mappedProtocol.toLowerCase() ||
      chain.gecko_id === mappedProtocol
    );

    if (chainData) {
      return chainData.tvl || 0;
    }

    // Fallback to mock TVL data
    console.warn(`No TVL data found for ${protocol}, using mock data`);
    const mockTVL = {
      ethereum: 25000000000,
      solana: 1200000000,
      cardano: 150000000,
      polkadot: 800000000,
      avalanche: 2000000000,
      polygon: 3000000000,
      binance: 5000000000,
    };
    return mockTVL[protocol as keyof typeof mockTVL] || Math.random() * 1000000000;

  } catch (error) {
    console.error(`Failed to fetch TVL for ${protocol}:`, error);

    // Fallback to mock TVL data
    if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
      const mockTVL = {
        ethereum: 25000000000,
        solana: 1200000000,
        cardano: 150000000,
        polkadot: 800000000,
        avalanche: 2000000000,
        polygon: 3000000000,
        binance: 5000000000,
      };
      return mockTVL[protocol as keyof typeof mockTVL] || Math.random() * 1000000000;
    }

    return 0;
  }
}

// Fetch on-chain metrics from various sources
export async function fetchOnChainMetrics(chainId: string): Promise<Partial<ChainMetrics>> {
  const metrics: Partial<ChainMetrics> = {};

  try {
    // This would integrate with various APIs like Glassnode, Messari, etc.
    // For now, we'll use mock data but structure it for real API integration
    
    const [marketData, tvlData] = await Promise.allSettled([
      fetchMarketData(chainId),
      fetchTVLData(chainId),
    ]);

    if (marketData.status === 'fulfilled') {
      Object.assign(metrics, marketData.value);
    }

    if (tvlData.status === 'fulfilled') {
      metrics.tvl = tvlData.value;
    }

    // Additional on-chain metrics would be fetched here
    // metrics.dailyActiveAddresses = await fetchActiveAddresses(chainId);
    // metrics.dailyTransactions = await fetchTransactionCount(chainId);
    // etc.

    return metrics;
  } catch (error) {
    console.error(`Failed to fetch on-chain metrics for ${chainId}:`, error);
    return {};
  }
}

// Calculate derived metrics based on raw data
export function calculateDerivedMetrics(metrics: ChainMetrics): Record<string, number> {
  const derived: Record<string, number> = {};

  // All the metrics from tools.md specification
  if (metrics.fdmc && metrics.dailyActiveAddresses) {
    derived.fdmc_daa = metrics.fdmc / metrics.dailyActiveAddresses;
  }

  if (metrics.fdmc && metrics.dailyTransactions) {
    derived.fdmc_transactions = metrics.fdmc / metrics.dailyTransactions;
  }

  if (metrics.marketCap && metrics.tvl) {
    derived.mc_tvl = metrics.marketCap / Math.max(metrics.tvl, 1);
  }

  if (metrics.fees && metrics.dailyTransactions) {
    derived.fees_transactions = metrics.fees / metrics.dailyTransactions;
  }

  if (metrics.marketCap && metrics.activeDevelopers) {
    derived.mc_developers = metrics.marketCap / metrics.activeDevelopers;
  }

  if (metrics.dailyTransactions && metrics.dailyActiveAddresses) {
    derived.tx_daa = metrics.dailyTransactions / metrics.dailyActiveAddresses;
  }

  if (metrics.stakedSupply && metrics.circulatingSupply) {
    derived.staked_circulating = (metrics.stakedSupply / metrics.circulatingSupply) * 100;
  }

  if (metrics.fdmc && metrics.newAddresses) {
    derived.fdmc_new_addresses = metrics.fdmc / (metrics.newAddresses * 7); // 7DMA
  }

  if (metrics.tvl && metrics.dailyActiveAddresses) {
    derived.tvl_daa = Math.max(metrics.tvl, 1) / metrics.dailyActiveAddresses;
  }

  if (metrics.marketCap && metrics.dailyActiveAddresses) {
    derived.mc_daa = metrics.marketCap / metrics.dailyActiveAddresses;
  }

  if (metrics.transactionVolume && metrics.dailyActiveAddresses) {
    derived.volume_daa = metrics.transactionVolume / metrics.dailyActiveAddresses;
  }

  if (metrics.fees && metrics.dailyActiveAddresses) {
    derived.fees_daa = metrics.fees / metrics.dailyActiveAddresses;
  }

  if (metrics.marketCap && metrics.dailyTransactions) {
    derived.mc_transactions = metrics.marketCap / metrics.dailyTransactions;
  }

  if (metrics.avgFee && metrics.realTimeTps) {
    derived.fee_tps = metrics.avgFee / metrics.realTimeTps;
  }

  if (metrics.theoreticalTps && metrics.realTimeTps) {
    derived.theoretical_real_tps = metrics.theoreticalTps / metrics.realTimeTps;
  }

  if (metrics.energyPerTx && metrics.realTimeTps) {
    derived.energy_tps = metrics.energyPerTx / metrics.realTimeTps;
  }

  if (metrics.fdmc && metrics.finalityTime) {
    derived.fdmc_finality = metrics.fdmc / metrics.finalityTime;
  }

  if (metrics.fdmc && metrics.realTimeTps) {
    derived.fdmc_tps = metrics.fdmc / metrics.realTimeTps;
  }

  if (metrics.fdmc && metrics.transactionVolume) {
    derived.fdmc_volume = metrics.fdmc / metrics.transactionVolume;
  }

  // Validator-related metrics
  if (metrics.validators) {
    if (metrics.dailyActiveAddresses) {
      derived.daa_validators = metrics.dailyActiveAddresses / metrics.validators;
    }
    if (metrics.marketCap) {
      derived.mc_validators = metrics.marketCap / metrics.validators;
    }
    if (metrics.transactionVolume) {
      derived.volume_validators = metrics.transactionVolume / metrics.validators;
    }
    if (metrics.dailyTransactions) {
      derived.tx_validators = metrics.dailyTransactions / metrics.validators;
    }
    if (metrics.fees) {
      derived.fees_validators = metrics.fees / metrics.validators;
    }
  }

  // Missing metrics from tools.md specification

  // Transaction Fee per block / Blocksize
  if (metrics.avgFee && metrics.blockSize && metrics.dailyTransactions) {
    const blocksPerDay = metrics.dailyTransactions / (metrics.realTimeTps * 86400); // Approximate blocks per day
    const feePerBlock = metrics.avgFee * (metrics.dailyTransactions / blocksPerDay);
    derived.fee_per_block_blocksize = feePerBlock / metrics.blockSize;
  }

  // Transaction volume per block / Block Size
  if (metrics.transactionVolume && metrics.blockSize && metrics.dailyTransactions && metrics.realTimeTps) {
    const blocksPerDay = metrics.dailyTransactions / (metrics.realTimeTps * 86400);
    const volumePerBlock = metrics.transactionVolume / blocksPerDay;
    derived.volume_per_block_blocksize = volumePerBlock / metrics.blockSize;
  }

  // Total Yearly Inflation(USD) / DAA
  if (metrics.inflation && metrics.circulatingSupply && metrics.price && metrics.dailyActiveAddresses) {
    const yearlyInflationUSD = (metrics.inflation.yoy / 100) * metrics.circulatingSupply * metrics.price;
    derived.yearly_inflation_usd_daa = yearlyInflationUSD / metrics.dailyActiveAddresses;
  }

  return derived;
}

// Score calculation for automated comparison
export function calculateComparisonScore(
  metrics: ChainMetrics,
  weights: Record<string, number> = {}
): number {
  const defaultWeights = {
    tps_efficiency: 0.2,
    fee_efficiency: 0.15,
    finality: 0.1,
    decentralization: 0.15,
    developer_activity: 0.1,
    market_maturity: 0.1,
    energy_efficiency: 0.1,
    user_adoption: 0.1,
  };

  const finalWeights = { ...defaultWeights, ...weights };
  let totalScore = 0;

  // TPS Efficiency (higher real TPS relative to theoretical is better)
  const tpsEfficiency = Math.min((metrics.realTimeTps / metrics.theoreticalTps) * 100, 100);
  totalScore += (tpsEfficiency / 100) * finalWeights.tps_efficiency;

  // Fee Efficiency (lower fees are better, normalized)
  const feeEfficiency = Math.max(100 - (metrics.avgFee * 10), 0); // Normalize fees
  totalScore += (feeEfficiency / 100) * finalWeights.fee_efficiency;

  // Finality (faster finality is better)
  const finalityScore = Math.max(100 - metrics.finalityTime, 0);
  totalScore += (finalityScore / 100) * finalWeights.finality;

  // Decentralization (more validators is better, but with diminishing returns)
  const decentralizationScore = Math.min(Math.log10(metrics.validators) * 25, 100);
  totalScore += (decentralizationScore / 100) * finalWeights.decentralization;

  // Developer Activity
  const devScore = Math.min(Math.log10(metrics.activeDevelopers) * 30, 100);
  totalScore += (devScore / 100) * finalWeights.developer_activity;

  // Market Maturity (higher market cap indicates maturity)
  const maturityScore = Math.min(Math.log10(metrics.marketCap / 1e9) * 25, 100);
  totalScore += (maturityScore / 100) * finalWeights.market_maturity;

  // Energy Efficiency (lower energy per transaction is better)
  const energyScore = Math.max(100 - Math.log10(metrics.energyPerTx) * 10, 0);
  totalScore += (energyScore / 100) * finalWeights.energy_efficiency;

  // User Adoption (more daily active addresses is better)
  const adoptionScore = Math.min(Math.log10(metrics.dailyActiveAddresses) * 20, 100);
  totalScore += (adoptionScore / 100) * finalWeights.user_adoption;

  return Math.round(totalScore * 100);
}

// Export comprehensive chain comparison
export async function generateChainComparison(
  chainIds: string[],
  analysisType: 'technical' | 'economic' | 'comprehensive' = 'comprehensive'
): Promise<{
  chains: Array<ChainMetrics & { score: number; derivedMetrics: Record<string, number> }>;
  insights: string[];
  timestamp: number;
}> {
  const chains = [];
  const insights = [];

  for (const chainId of chainIds) {
    try {
      const metrics = await fetchOnChainMetrics(chainId);
      const derivedMetrics = calculateDerivedMetrics(metrics as ChainMetrics);
      const score = calculateComparisonScore(metrics as ChainMetrics);

      chains.push({
        ...metrics,
        derivedMetrics,
        score,
      } as ChainMetrics & { score: number; derivedMetrics: Record<string, number> });
    } catch (error) {
      console.error(`Failed to analyze ${chainId}:`, error);
    }
  }

  // Sort by score
  chains.sort((a, b) => b.score - a.score);

  // Generate insights
  if (chains.length > 0) {
    const topChain = chains[0];
    insights.push(`${topChain.name} leads with a score of ${topChain.score}/100`);

    if (topChain.realTimeTps > 1000) {
      insights.push(`${topChain.name} excels in transaction throughput with ${topChain.realTimeTps} TPS`);
    }

    if (topChain.avgFee < 0.01) {
      insights.push(`${topChain.name} offers extremely low transaction fees`);
    }

    if (topChain.validators > 1000) {
      insights.push(`${topChain.name} demonstrates strong decentralization with ${topChain.validators} validators`);
    }
  }

  return {
    chains,
    insights,
    timestamp: Date.now(),
  };
}

// Utility function to format numbers for display
export function formatNumber(num: number, type: 'currency' | 'number' | 'percentage' = 'number'): string {
  if (type === 'currency') {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD', 
      notation: 'compact',
      maximumFractionDigits: 2
    }).format(num);
  }
  if (type === 'percentage') {
    return `${num > 0 ? '+' : ''}${num.toFixed(2)}%`;
  }
  return new Intl.NumberFormat('en-US', { 
    notation: 'compact',
    maximumFractionDigits: 2
  }).format(num);
}

// Error handling for missing metrics
export function handleMissingMetrics(chainName: string, missingMetrics: string[]): string {
  if (missingMetrics.length === 0) {
    return '';
  }

  return `These metrics couldn't be found for ${chainName}: ${missingMetrics.join(', ')}. Results shown are based on available data only.`;
}