import { createClient } from '@supabase/supabase-js'
import type { Database } from '../supabase_types'

// Database utility functions for optimized queries and monitoring
export class DatabaseUtils {
  private supabase: ReturnType<typeof createClient<Database>>

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient<Database>(supabaseUrl, supabaseKey)
  }

  // Optimized query for articles with engagement metrics
  async getArticlesWithEngagement(options: {
    limit?: number
    offset?: number
    userId?: string
    sortBy?: 'latest' | 'trending' | 'popular'
  }) {
    const { limit = 20, offset = 0, userId, sortBy = 'latest' } = options

    let query = (this.supabase as any)
      .from('articles')
      .select(`
        *,
        article_hashtags(hashtags(name)),
        comments(count)
      `)
      .range(offset, offset + limit - 1)

    // Apply sorting
    switch (sortBy) {
      case 'trending':
        query = query.order('created_at', { ascending: false })
        break
      case 'popular':
        query = query.order('likes', { ascending: false })
        break
      default:
        query = query.order('created_at', { ascending: false })
    }

    return query
  }

  // Optimized notifications query with batching
  async getUserNotifications(userId: string, options: {
    limit?: number
    unreadOnly?: boolean
    types?: Database['public']['Tables']['notifications']['Row']['type'][]
  }) {
    const { limit = 50, unreadOnly = false, types } = options

    let query: any = (this.supabase as any)
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (unreadOnly) {
      query = query.is('read_at', null)
    }

    if (types && types.length > 0) {
      query = query.in('type', types)
    }

    return query
  }

  // Bulk mark notifications as read
  async markNotificationsAsRead(userId: string, notificationIds?: string[]) {
    let query = ((this.supabase as any) as any)
      .from('notifications')
      .update({ read_at: new Date().toISOString() })
      .eq('user_id', userId)

    if (notificationIds && notificationIds.length > 0) {
      query = query.in('id', notificationIds)
    } else {
      query = query.is('read_at', null)
    }

    return query
  }

  // Search with hashtags and mentions
  async searchContent(searchQuery: string, options: {
    contentTypes?: ('article' | 'user' | 'hashtag')[]
    limit?: number
    userId?: string
  }) {
    const { contentTypes = ['article', 'user', 'hashtag'], limit = 20, userId } = options
    const results: any = {}

    if (contentTypes.includes('article')) {
      const articlesQuery = (this.supabase as any)
        .rpc('search_articles', { search_query: searchQuery })
        .limit(limit)
      
      results.articles = await articlesQuery
    }

    if (contentTypes.includes('hashtag')) {
      const hashtagsQuery = (this.supabase as any)
        .rpc('search_hashtags', { p_query: searchQuery, p_limit: limit })
      
      results.hashtags = await hashtagsQuery
    }

    if (contentTypes.includes('user')) {
      const usersQuery = (this.supabase as any)
        .rpc('search_users_for_mentions', { p_query: searchQuery, p_limit: limit })
      
      results.users = await usersQuery
    }

    return results
  }

  // Analytics aggregation
  async getPlatformAnalytics(startDate?: string, endDate?: string) {
    return ((this.supabase as any) as any).rpc('get_platform_analytics', {
      p_start_date: startDate,
      p_end_date: endDate
    })
  }

  // Performance monitoring
  async getSlowQueries() {
    // This would integrate with Supabase's pg_stat_statements if available
    return (this.supabase as any)
      .from('analytics_events')
      .select('*')
      .eq('event_type', 'slow_query')
      .order('created_at', { ascending: false })
      .limit(100)
  }

  // User engagement tracking
  async trackUserEngagement(userId: string, action: string, metadata?: Record<string, any>) {
    const eventData = {
      event_type: `user_${action}`,
      user_id: userId,
      data: metadata || {},
      created_at: new Date().toISOString()
    }

    return ((this.supabase as any) as any).from('analytics_events').insert(eventData)
  }

  // Content moderation helpers
  async getContentForModeration(options: {
    flagType?: Database['public']['Tables']['content_flags']['Row']['flag_type']
    reviewed?: boolean
    limit?: number
  }) {
    const { flagType, reviewed = false, limit = 50 } = options

    let query = (this.supabase as any)
      .from('content_flags')
      .select('*')
      .eq('moderator_reviewed', reviewed)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (flagType) {
      query = query.eq('flag_type', flagType)
    }

    return query
  }

  // Cleanup expired data
  async cleanupExpiredData() {
    const results = await Promise.allSettled([
      ((this.supabase as any) as any).rpc('cleanup_expired_notifications'),
      ((this.supabase as any) as any).rpc('cleanup_expired_typing_indicators')
    ])

    return {
      notifications: results[0],
      typingIndicators: results[1]
    }
  }
}

// Export singleton instance
export const db = new DatabaseUtils(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)
