// WebSocket service for real-time cryptocurrency data
import { API_CONFIG, WEBSOCKET_CONFIG, FEATURE_FLAGS } from './api-config';

export interface WebSocketMessage {
  type: 'price_update' | 'market_update' | 'error' | 'connection_status';
  data: any;
  timestamp: number;
}

export interface PriceUpdate {
  symbol: string;
  price: number;
  change24h: number;
  volume24h: number;
  timestamp: number;
}

export type WebSocketEventHandler = (message: WebSocketMessage) => void;

export class CryptoWebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private pingTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private eventHandlers: Set<WebSocketEventHandler> = new Set();
  private subscribedSymbols: Set<string> = new Set();

  constructor() {
    if (typeof window !== 'undefined' && FEATURE_FLAGS.ENABLE_WEBSOCKET_CONNECTIONS) {
      this.connect();
    }
  }

  // Add event handler
  addEventHandler(handler: WebSocketEventHandler): void {
    this.eventHandlers.add(handler);
  }

  // Remove event handler
  removeEventHandler(handler: WebSocketEventHandler): void {
    this.eventHandlers.delete(handler);
  }

  // Emit event to all handlers
  private emit(message: WebSocketMessage): void {
    this.eventHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('Error in WebSocket event handler:', error);
      }
    });
  }

  // Connect to Binance WebSocket
  private connect(): void {
    if (this.isConnecting || this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    this.isConnecting = true;
    
    try {
      // Create stream URL for multiple symbols
      const symbols = Array.from(this.subscribedSymbols);
      const streams = symbols.map(symbol => `${symbol.toLowerCase()}usdt@ticker`).join('/');
      const wsUrl = streams 
        ? `${API_CONFIG.BINANCE.WEBSOCKET_BASE_URL}/${streams}`
        : `${API_CONFIG.BINANCE.WEBSOCKET_BASE_URL}/btcusdt@ticker`;

      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket connected to Binance');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.startPing();
        
        this.emit({
          type: 'connection_status',
          data: { status: 'connected', source: 'binance' },
          timestamp: Date.now(),
        });
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        this.isConnecting = false;
        this.stopPing();
        
        this.emit({
          type: 'connection_status',
          data: { status: 'disconnected', code: event.code, reason: event.reason },
          timestamp: Date.now(),
        });

        // Attempt to reconnect
        this.scheduleReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.isConnecting = false;
        
        this.emit({
          type: 'error',
          data: { error: 'WebSocket connection error' },
          timestamp: Date.now(),
        });
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  // Handle incoming WebSocket messages
  private handleMessage(data: any): void {
    if (data.e === '24hrTicker') {
      // Binance 24hr ticker data
      const symbol = data.s.replace('USDT', ''); // Remove USDT suffix
      
      const priceUpdate: PriceUpdate = {
        symbol,
        price: parseFloat(data.c), // Current price
        change24h: parseFloat(data.P), // 24hr price change percentage
        volume24h: parseFloat(data.v), // 24hr volume
        timestamp: data.E, // Event time
      };

      this.emit({
        type: 'price_update',
        data: priceUpdate,
        timestamp: Date.now(),
      });
    }
  }

  // Start ping to keep connection alive
  private startPing(): void {
    this.pingTimer = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        // Binance WebSocket doesn't require manual ping, but we can check connection
        this.emit({
          type: 'connection_status',
          data: { status: 'ping', timestamp: Date.now() },
          timestamp: Date.now(),
        });
      }
    }, WEBSOCKET_CONFIG.PING_INTERVAL);
  }

  // Stop ping timer
  private stopPing(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  // Schedule reconnection attempt
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS) {
      console.error('Max reconnection attempts reached');
      this.emit({
        type: 'error',
        data: { error: 'Max reconnection attempts reached' },
        timestamp: Date.now(),
      });
      return;
    }

    const delay = Math.min(
      WEBSOCKET_CONFIG.RECONNECT_INTERVAL * Math.pow(2, this.reconnectAttempts),
      30000 // Max 30 seconds
    );

    console.log(`Scheduling reconnect in ${delay}ms (attempt ${this.reconnectAttempts + 1})`);

    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect();
    }, delay);
  }

  // Subscribe to symbol updates
  subscribe(symbols: string[]): void {
    symbols.forEach(symbol => this.subscribedSymbols.add(symbol));
    
    // If we're already connected, we need to reconnect with new symbols
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.disconnect();
      setTimeout(() => this.connect(), 100);
    } else if (!this.isConnecting) {
      this.connect();
    }
  }

  // Unsubscribe from symbol updates
  unsubscribe(symbols: string[]): void {
    symbols.forEach(symbol => this.subscribedSymbols.delete(symbol));
    
    // Reconnect with updated symbol list
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.disconnect();
      setTimeout(() => this.connect(), 100);
    }
  }

  // Get connection status
  getConnectionStatus(): 'connecting' | 'connected' | 'disconnected' {
    if (this.isConnecting) return 'connecting';
    if (this.ws?.readyState === WebSocket.OPEN) return 'connected';
    return 'disconnected';
  }

  // Get subscribed symbols
  getSubscribedSymbols(): string[] {
    return Array.from(this.subscribedSymbols);
  }

  // Disconnect WebSocket
  disconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    this.stopPing();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  // Cleanup
  destroy(): void {
    this.disconnect();
    this.eventHandlers.clear();
    this.subscribedSymbols.clear();
  }
}

// Create singleton instance
export const cryptoWebSocketService = new CryptoWebSocketService();

// React hook for using WebSocket service
export function useWebSocketService() {
  return cryptoWebSocketService;
}
