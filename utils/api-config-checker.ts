// API Configuration Checker - Helps identify missing API keys and configuration issues

export interface APIConfigStatus {
  service: string;
  configured: boolean;
  required: boolean;
  status: 'ok' | 'missing' | 'invalid' | 'optional';
  message: string;
  setupUrl?: string;
}

export function checkAPIConfiguration(): APIConfigStatus[] {
  const results: APIConfigStatus[] = [];

  // Check Binance API (optional for public data)
  const binanceKey = process.env.BINANCE_API_KEY;
  results.push({
    service: 'Binance API',
    configured: !!(binanceKey && binanceKey !== 'your_binance_api_key_here_optional'),
    required: false,
    status: 'optional',
    message: 'Binance API key is optional. Public endpoints work without authentication.',
    setupUrl: 'https://www.binance.com/en/binance-api',
  });

  // Check CoinGecko API (recommended for fallback)
  const coinGeckoKey = process.env.COINGECKO_API_KEY;
  const coinGeckoConfigured = !!(coinGeckoKey && coinGeckoKey !== 'your_coingecko_api_key_here');
  results.push({
    service: 'CoinGecko API',
    configured: coinGeckoConfigured,
    required: false,
    status: coinGeckoConfigured ? 'ok' : 'missing',
    message: coinGeckoConfigured 
      ? 'CoinGecko API key configured - provides fallback market data'
      : 'CoinGecko API key missing - will use mock data for coins not on Binance',
    setupUrl: 'https://www.coingecko.com/en/api/pricing',
  });

  // Check Messari API (optional for enhanced on-chain data)
  const messariKey = process.env.MESSARI_API_KEY;
  const messariConfigured = !!(messariKey && messariKey !== 'your_messari_api_key_here');
  results.push({
    service: 'Messari API',
    configured: messariConfigured,
    required: false,
    status: messariConfigured ? 'ok' : 'optional',
    message: messariConfigured
      ? 'Messari API key configured - provides enhanced on-chain analytics'
      : 'Messari API key not configured - will use basic market data only',
    setupUrl: 'https://messari.io/api',
  });

  // Check CryptoNews API (for news features)
  const cryptoNewsKey = process.env.NEWSDATA_API_KEY;
  const cryptoNewsConfigured = !!(cryptoNewsKey && cryptoNewsKey !== 'your_NEWSDATA_API_key_here');
  results.push({
    service: 'NewsData.io API',
    configured: cryptoNewsConfigured,
    required: true,
    status: cryptoNewsConfigured ? 'ok' : 'missing',
    message: cryptoNewsConfigured
      ? 'NewsData.io API key configured - provides crypto news'
      : 'NewsData.io API key missing - news features will use mock data',
    setupUrl: 'https://newsdata.io/',
  });

  // Check NewsAPI (backup news source)
  const newsApiKey = process.env.NEWS_API_KEY;
  const newsApiConfigured = !!(newsApiKey && newsApiKey !== 'your_news_api_key_here');
  results.push({
    service: 'NewsAPI',
    configured: newsApiConfigured,
    required: false,
    status: newsApiConfigured ? 'ok' : 'optional',
    message: newsApiConfigured
      ? 'NewsAPI key configured - provides backup news source'
      : 'NewsAPI key not configured - will rely on primary news source only',
    setupUrl: 'https://newsapi.org/register',
  });

  return results;
}

export function getConfigurationSummary(): {
  totalServices: number;
  configuredServices: number;
  missingRequired: number;
  recommendations: string[];
} {
  const status = checkAPIConfiguration();
  
  const totalServices = status.length;
  const configuredServices = status.filter(s => s.configured).length;
  const missingRequired = status.filter(s => s.required && !s.configured).length;
  
  const recommendations: string[] = [];
  
  // Add specific recommendations based on configuration
  if (missingRequired > 0) {
    recommendations.push('⚠️ Some required API keys are missing. The application will use mock data for these services.');
  }
  
  const missingCoinGecko = status.find(s => s.service === 'CoinGecko API' && !s.configured);
  if (missingCoinGecko) {
    recommendations.push('💡 Consider adding a CoinGecko API key for better market data coverage.');
  }
  
  const missingMessari = status.find(s => s.service === 'Messari API' && !s.configured);
  if (missingMessari) {
    recommendations.push('💡 Consider adding a Messari API key for enhanced on-chain analytics.');
  }
  
  if (configuredServices === totalServices) {
    recommendations.push('✅ All API services are properly configured!');
  }
  
  return {
    totalServices,
    configuredServices,
    missingRequired,
    recommendations,
  };
}

export function logConfigurationStatus(): void {
  console.log('\n🔧 API Configuration Status:');
  console.log('================================');
  
  const status = checkAPIConfiguration();
  const summary = getConfigurationSummary();
  
  status.forEach(service => {
    const icon = service.configured ? '✅' : 
                 service.required ? '❌' : '⚪';
    const statusText = service.configured ? 'CONFIGURED' :
                      service.required ? 'MISSING (REQUIRED)' : 'NOT CONFIGURED (OPTIONAL)';
    
    console.log(`${icon} ${service.service}: ${statusText}`);
    console.log(`   ${service.message}`);
    if (!service.configured && service.setupUrl) {
      console.log(`   Setup: ${service.setupUrl}`);
    }
    console.log('');
  });
  
  console.log('📊 Summary:');
  console.log(`   Configured: ${summary.configuredServices}/${summary.totalServices} services`);
  console.log(`   Missing Required: ${summary.missingRequired} services`);
  console.log('');
  
  summary.recommendations.forEach(rec => {
    console.log(rec);
  });
  
  console.log('\n💡 Quick Setup:');
  console.log('1. Copy .env.example to .env');
  console.log('2. Add your API keys to the .env file');
  console.log('3. Restart the development server');
  console.log('4. Visit /test-api to verify configuration');
}

// Environment-specific checks
export function checkProductionReadiness(): {
  ready: boolean;
  issues: string[];
  warnings: string[];
} {
  const status = checkAPIConfiguration();
  const issues: string[] = [];
  const warnings: string[] = [];
  
  // Check for required services in production
  const missingRequired = status.filter(s => s.required && !s.configured);
  missingRequired.forEach(service => {
    issues.push(`Missing required API key: ${service.service}`);
  });
  
  // Check for recommended services
  const missingRecommended = status.filter(s => 
    !s.required && !s.configured && 
    ['CoinGecko API', 'Messari API'].includes(s.service)
  );
  missingRecommended.forEach(service => {
    warnings.push(`Recommended API key missing: ${service.service}`);
  });
  
  // Check environment variables
  if (!process.env.NODE_ENV) {
    warnings.push('NODE_ENV not set');
  }
  
  if (process.env.FALLBACK_TO_MOCK_DATA !== 'false') {
    warnings.push('FALLBACK_TO_MOCK_DATA should be false in production');
  }
  
  return {
    ready: issues.length === 0,
    issues,
    warnings,
  };
}

// Export for use in API routes
export function getAPIConfigurationForClient() {
  const status = checkAPIConfiguration();
  
  // Return only safe information (no API keys)
  return status.map(service => ({
    service: service.service,
    configured: service.configured,
    required: service.required,
    status: service.status,
    message: service.message,
  }));
}
