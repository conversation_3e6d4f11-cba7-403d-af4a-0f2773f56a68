'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { useTabLazyData, LazyDataState, LazyDataActions } from './lazy-loading-manager';

// Import the existing real-time data service but don't auto-subscribe
import { MarketData, ChartDataPoint } from './real-time-data';
import { clientAPIService } from './client-api-service';

// Lazy real-time data service that doesn't auto-start
export class LazyRealTimeDataService {
  private cache: Map<string, any> = new Map();
  private subscribers: Map<string, Set<(data: any) => void>> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private isStarted: Map<string, boolean> = new Map();

  // Get cached data without starting updates
  getCachedData<T>(dataType: string): T | null {
    return this.cache.get(dataType) || null;
  }

  // Start updates for a specific data type
  startUpdates(dataType: string): void {
    if (this.isStarted.get(dataType)) return;

    console.log(`🔄 Starting lazy updates for ${dataType}`);
    this.isStarted.set(dataType, true);

    // Initial fetch
    this.fetchData(dataType);

    // Set up interval based on data type
    const intervals = {
      'market-data': 30000,     // 30 seconds for market data
      'chart-data': 60000,      // 1 minute for chart data
      'heatmap-data': 15000,    // 15 seconds for heatmap data
      'portfolio-prices': 45000, // 45 seconds for portfolio prices
    };

    const interval = intervals[dataType as keyof typeof intervals] || 60000;
    
    const timer = setInterval(() => {
      this.fetchData(dataType);
    }, interval);

    this.intervals.set(dataType, timer);
  }

  // Stop updates for a specific data type
  stopUpdates(dataType: string): void {
    console.log(`⏹️ Stopping lazy updates for ${dataType}`);
    
    const timer = this.intervals.get(dataType);
    if (timer) {
      clearInterval(timer);
      this.intervals.delete(dataType);
    }
    
    this.isStarted.set(dataType, false);
  }

  // Subscribe to data updates (doesn't auto-start)
  subscribe(dataType: string, callback: (data: any) => void): () => void {
    if (!this.subscribers.has(dataType)) {
      this.subscribers.set(dataType, new Set());
    }
    
    this.subscribers.get(dataType)!.add(callback);

    // Return unsubscribe function
    return () => {
      const subs = this.subscribers.get(dataType);
      if (subs) {
        subs.delete(callback);
        
        // If no more subscribers, stop updates
        if (subs.size === 0) {
          this.stopUpdates(dataType);
        }
      }
    };
  }

  // Manually refresh data
  async refresh(dataType: string): Promise<void> {
    await this.fetchData(dataType);
  }

  // Fetch data for a specific type
  private async fetchData(dataType: string): Promise<void> {
    try {
      let data: any;

      switch (dataType) {
        case 'market-data':
          data = await this.fetchMarketData();
          break;
        case 'chart-data':
          data = await this.fetchChartData();
          break;
        case 'heatmap-data':
          data = await this.fetchHeatmapData();
          break;
        case 'portfolio-prices':
          data = await this.fetchPortfolioPrices();
          break;
        default:
          console.warn(`Unknown data type: ${dataType}`);
          return;
      }

      // Cache the data
      this.cache.set(dataType, data);

      // Notify all subscribers
      this.notifySubscribers(dataType, data);
    } catch (error) {
      console.error(`Failed to fetch ${dataType}:`, error);
    }
  }

  // Notify subscribers
  private notifySubscribers(dataType: string, data: any): void {
    const subscribers = this.subscribers.get(dataType);
    if (subscribers) {
      subscribers.forEach(callback => callback(data));
    }
  }

  // Data fetching methods (public so they can be used by hooks)
  async fetchMarketData(): Promise<MarketData[]> {
    try {
      console.log('🔄 Fetching market data via lazy client API service...');
      const response = await clientAPIService.getMarketData(
        ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'AVAX', 'MATIC', 'UNI', 'ATOM'],
        50
      );
      return response.data || [];
    } catch (error) {
      console.error('Market data fetch failed:', error);
      return [];
    }
  }

  async fetchChartData(): Promise<ChartDataPoint[]> {
    try {
      console.log('🔄 Generating chart data for lazy loading...');
      // Generate mock OHLCV data for charts
      const data: ChartDataPoint[] = [];
      const now = Date.now();
      const basePrice = 40000;

      for (let i = 99; i >= 0; i--) {
        const time = now - (i * 60000); // 1-minute intervals
        const open = basePrice + (Math.random() - 0.5) * 1000;
        const close = open + (Math.random() - 0.5) * 500;
        const high = Math.max(open, close) + Math.random() * 200;
        const low = Math.min(open, close) - Math.random() * 200;
        const volume = Math.random() * 1000000;

        data.push({ time, open, high, low, close, volume });
      }

      return data;
    } catch (error) {
      console.error('Chart data generation failed:', error);
      return [];
    }
  }

  async fetchHeatmapData(): Promise<any[]> {
    try {
      console.log('🔄 Fetching heatmap data via lazy client API service...');
      const response = await clientAPIService.getMarketData(
        undefined, // Get all available coins
        100 // More coins for heatmap
      );

      // Transform to heatmap format
      const heatmapData = response.data.map(coin => ({
        symbol: coin.symbol,
        change24h: coin.change24h || 0,
        volume: coin.volume24h,
        marketCap: coin.marketCap,
        price: coin.price,
        lastUpdated: coin.lastUpdated || Date.now(),
      }));

      console.log(`✅ Fetched ${heatmapData.length} coins for heatmap from [${response.sources?.join(', ') || response.source}]`);
      return heatmapData;
    } catch (error) {
      console.error('Heatmap data fetch failed:', error);

      // Fallback to centralized mock data
      try {
        const { getHeatmapData } = await import('./mock-data-service');
        const centralizedData = await getHeatmapData();
        return centralizedData.map(coin => ({
          symbol: coin.symbol,
          change24h: coin.change24h,
          volume: coin.volume24h,
          marketCap: coin.marketCap,
          price: coin.price,
          lastUpdated: Date.now(),
        }));
      } catch (mockError) {
        console.warn('⚠️ Centralized mock service also failed, using BASE coins emergency fallback:', mockError);

        // Emergency fallback - only use BASE coins
        const { BASE_COINS } = await import('./mock-data-service');
        const coins = BASE_COINS.map(coin => coin.symbol);

        return coins.map(symbol => ({
          symbol,
          change24h: (Math.random() - 0.5) * 20,
          volume: Math.random() * 1000000000,
          marketCap: Math.random() * 10000000000,
          price: Math.random() * 10000,
          lastUpdated: Date.now(),
        }));
      }
    }
  }

  async fetchPortfolioPrices(): Promise<Record<string, number>> {
    try {
      console.log('🔄 Fetching portfolio prices via lazy client API service...');
      const response = await clientAPIService.getPortfolioPrices(['BTC', 'ETH', 'SOL']);
      return response.data || {};
    } catch (error) {
      console.error('Portfolio prices fetch failed:', error);
      return {};
    }
  }

  // Cleanup all intervals and subscriptions
  cleanup(): void {
    this.intervals.forEach(timer => clearInterval(timer));
    this.intervals.clear();
    this.subscribers.clear();
    this.cache.clear();
    this.isStarted.clear();
  }
}

// Global lazy real-time data service instance
export const lazyRealTimeDataService = new LazyRealTimeDataService();

// Lazy real-time data hook that doesn't auto-start
export function useLazyRealTimeData<T>(
  dataType: string,
  isActive: boolean = false,
  initialData?: T
): [T | null, boolean, () => Promise<void>, () => void, () => void] {
  const [data, setData] = useState<T | null>(
    initialData || lazyRealTimeDataService.getCachedData(dataType) || null
  );
  const [isLoading, setIsLoading] = useState(false);
  const mountedRef = useRef(true);

  // Start data updates
  const start = useCallback(() => {
    if (!mountedRef.current) return;
    
    setIsLoading(true);
    lazyRealTimeDataService.startUpdates(dataType);
    
    // Set loading to false after a short delay to show initial loading state
    setTimeout(() => {
      if (mountedRef.current) {
        setIsLoading(false);
      }
    }, 1000);
  }, [dataType]);

  // Stop data updates
  const stop = useCallback(() => {
    lazyRealTimeDataService.stopUpdates(dataType);
  }, [dataType]);

  // Refresh data manually
  const refresh = useCallback(async () => {
    if (!mountedRef.current) return;
    
    setIsLoading(true);
    await lazyRealTimeDataService.refresh(dataType);
    setIsLoading(false);
  }, [dataType]);

  // Subscribe to data updates
  useEffect(() => {
    const unsubscribe = lazyRealTimeDataService.subscribe(dataType, (newData: T) => {
      if (mountedRef.current) {
        setData(newData);
        setIsLoading(false);
      }
    });

    return unsubscribe;
  }, [dataType]);

  // Auto-start when active
  useEffect(() => {
    if (isActive) {
      start();
    } else {
      stop();
    }
  }, [isActive, start, stop]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return [data, isLoading, refresh, start, stop];
}

// Specific lazy hooks for different data types
export function useLazyMarketData(isActive: boolean = false): [MarketData[] | null, boolean, () => Promise<void>, () => void, () => void] {
  return useLazyRealTimeData<MarketData[]>('market-data', isActive);
}

export function useLazyChartData(isActive: boolean = false): [ChartDataPoint[] | null, boolean, () => Promise<void>, () => void, () => void] {
  return useLazyRealTimeData<ChartDataPoint[]>('chart-data', isActive);
}

export function useLazyHeatmapData(isActive: boolean = false): [any[] | null, boolean, () => Promise<void>, () => void, () => void] {
  return useLazyRealTimeData<any[]>('heatmap-data', isActive);
}

export function useLazyPortfolioPrices(isActive: boolean = false): [Record<string, number> | null, boolean, () => Promise<void>, () => void, () => void] {
  return useLazyRealTimeData<Record<string, number>>('portfolio-prices', isActive);
}

// Tab-based lazy data hooks using the lazy loading manager
export function useTabLazyMarketData(isActiveTab: boolean, tabId: string): [LazyDataState<MarketData[]>, LazyDataActions] {
  return useTabLazyData(
    () => lazyRealTimeDataService.fetchMarketData(),
    isActiveTab,
    tabId
  );
}

export function useTabLazyHeatmapData(isActiveTab: boolean, tabId: string): [LazyDataState<any[]>, LazyDataActions] {
  return useTabLazyData(
    () => lazyRealTimeDataService.fetchHeatmapData(),
    isActiveTab,
    tabId
  );
}

export function useTabLazyChartData(isActiveTab: boolean, tabId: string): [LazyDataState<ChartDataPoint[]>, LazyDataActions] {
  return useTabLazyData(
    () => lazyRealTimeDataService.fetchChartData(),
    isActiveTab,
    tabId
  );
}

// Cleanup function for global cleanup
export function cleanupLazyRealTimeData(): void {
  lazyRealTimeDataService.cleanup();
}
