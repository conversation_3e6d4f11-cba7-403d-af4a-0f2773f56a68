// API Configuration and Constants
// This file centralizes all API-related configuration

export const API_CONFIG = {
  // CoinGecko API Configuration
  COINGECKO: {
    BASE_URL: process.env.COINGECKO_API_BASE_URL || 'https://api.coingecko.com/api/v3',
    API_KEY: process.env.COINGECKO_API_KEY,
    ENDPOINTS: {
      SIMPLE_PRICE: '/simple/price',
      COINS_MARKETS: '/coins/markets',
      COINS_LIST: '/coins/list',
      TRENDING: '/search/trending',
      GLOBAL: '/global',
      PING: '/ping',
      COIN_HISTORY: '/coins/{id}/history',
      COIN_MARKET_CHART: '/coins/{id}/market_chart',
      EXCHANGES: '/exchanges',
      EXCHANGE_RATES: '/exchange_rates',
    },
    RATE_LIMITS: {
      FREE_TIER: 30, // requests per minute
      PRO_TIER: 500, // requests per minute
    },
  },

  // NewsData.io API Configuration (Crypto News)
  CRYPTO_NEWS: {
    BASE_URL: 'https://newsdata.io/api/1',
    API_KEY: process.env.NEWSDATA_API_KEY,
    ENDPOINTS: {
      NEWS: '/news',
      LATEST: '/latest',
      ARCHIVE: '/archive',
    },
  },

  // NewsAPI Configuration (Backup)
  NEWS_API: {
    BASE_URL: 'https://newsapi.org/v2',
    API_KEY: process.env.NEWS_API_KEY,
    ENDPOINTS: {
      EVERYTHING: '/everything',
      TOP_HEADLINES: '/top-headlines',
    },
  },

  // CoinMarketCap API Configuration (Backup)
  COINMARKETCAP: {
    BASE_URL: 'https://pro-api.coinmarketcap.com/v1',
    API_KEY: process.env.COINMARKETCAP_API_KEY,
    ENDPOINTS: {
      LISTINGS: '/cryptocurrency/listings/latest',
      QUOTES: '/cryptocurrency/quotes/latest',
      INFO: '/cryptocurrency/info',
      GLOBAL_METRICS: '/global-metrics/quotes/latest',
    },
  },

  // Binance API Configuration (PRIMARY - Free with high limits)
  BINANCE: {
    REST_BASE_URL: 'https://api.binance.com/api/v3',
    WEBSOCKET_BASE_URL: 'wss://stream.binance.com:9443/ws',
    API_KEY: process.env.BINANCE_API_KEY, // Optional - not needed for public endpoints
    API_SECRET: process.env.BINANCE_API_SECRET, // Optional - not needed for public endpoints
    ENDPOINTS: {
      KLINES: '/klines',
      TICKER_24HR: '/ticker/24hr', // 24hr price change statistics
      TICKER_PRICE: '/ticker/price', // Latest price for all symbols
      EXCHANGE_INFO: '/exchangeInfo', // Exchange trading rules and symbol information
      DEPTH: '/depth', // Order book
      TRADES: '/trades', // Recent trades list
      AVG_PRICE: '/avgPrice', // Current average price
    },
    RATE_LIMITS: {
      REQUESTS_PER_MINUTE: 1200, // Much higher than CoinGecko!
      WEIGHT_PER_MINUTE: 6000, // Request weight limits
    },
  },
};

// Rate limiting configuration
export const RATE_LIMITS = {
  REQUESTS_PER_MINUTE: parseInt(process.env.API_RATE_LIMIT_REQUESTS_PER_MINUTE || '30'),
  CACHE_TTL_SECONDS: parseInt(process.env.API_CACHE_TTL_SECONDS || '300'),
};

// Feature flags
export const FEATURE_FLAGS = {
  ENABLE_REAL_TIME_DATA: process.env.ENABLE_REAL_TIME_DATA === 'true',
  ENABLE_WEBSOCKET_CONNECTIONS: process.env.ENABLE_WEBSOCKET_CONNECTIONS === 'true',
  FALLBACK_TO_MOCK_DATA: process.env.FALLBACK_TO_MOCK_DATA === 'true',
};

// Common cryptocurrency symbols for consistent data fetching - only BASE coins
export const CRYPTO_SYMBOLS = [
  'bitcoin',
  'ethereum',
  'solana',
  'cardano',
  'ripple',
  'sui',
  'sei-network',
  'raydium',
  'jupiter-exchange-solana',
  'binancecoin',
  'pyth-network',
  'natix-network',
  'render-token',
  'zeus-network',
  'apecoin',
  'bonk',
  'dogecoin',
  'floki',
  'pudgy-penguins',
  'pepe',
  'shiba-inu',
  'dogwifcoin',
] as const;

// Mapping of display symbols to CoinGecko IDs - only BASE coins
export const SYMBOL_TO_COINGECKO_ID: Record<string, string> = {
  BTC: 'bitcoin',
  ETH: 'ethereum',
  SOL: 'solana',
  ADA: 'cardano',
  XRP: 'ripple',
  SUI: 'sui',
  SEI: 'sei-network',
  RAY: 'raydium',
  JUP: 'jupiter-exchange-solana',
  BNB: 'binancecoin',
  PYTH: 'pyth-network',
  NATIX: 'natix-network',
  RENDER: 'render-token',
  ZEUS: 'zeus-network',
  APE: 'apecoin',
  BONK: 'bonk',
  DOGE: 'dogecoin',
  FLOKI: 'floki',
  PENGU: 'pudgy-penguins',
  PEPE: 'pepe',
  SHIB: 'shiba-inu',
  WIF: 'dogwifcoin',
};

// Reverse mapping for display purposes
export const COINGECKO_ID_TO_SYMBOL: Record<string, string> = Object.fromEntries(
  Object.entries(SYMBOL_TO_COINGECKO_ID).map(([symbol, id]) => [id, symbol])
);

// API Error types
export enum APIErrorType {
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  API_KEY_INVALID = 'API_KEY_INVALID',
  NETWORK_ERROR = 'NETWORK_ERROR',
  INVALID_RESPONSE = 'INVALID_RESPONSE',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
}

// Default cache durations for different data types
export const CACHE_DURATIONS = {
  PRICE_DATA: 30, // 30 seconds
  MARKET_DATA: 60, // 1 minute
  NEWS_DATA: 300, // 5 minutes
  HISTORICAL_DATA: 3600, // 1 hour
  STATIC_DATA: 86400, // 24 hours
};

// WebSocket configuration
export const WEBSOCKET_CONFIG = {
  RECONNECT_INTERVAL: 5000, // 5 seconds
  MAX_RECONNECT_ATTEMPTS: 10,
  PING_INTERVAL: 30000, // 30 seconds
  PONG_TIMEOUT: 5000, // 5 seconds
};

// Supported vs currencies for price data
export const SUPPORTED_VS_CURRENCIES = [
  'usd',
  'eur',
  'gbp',
  'jpy',
  'btc',
  'eth',
] as const;

// Default parameters for API calls
export const DEFAULT_PARAMS = {
  VS_CURRENCY: 'usd',
  ORDER: 'market_cap_desc',
  PER_PAGE: 100,
  PAGE: 1,
  SPARKLINE: false,
  PRICE_CHANGE_PERCENTAGE: '1h,24h,7d',
};

// Environment check
export const isProduction = process.env.NODE_ENV === 'production';
export const isDevelopment = process.env.NODE_ENV === 'development';

// API health check endpoints
export const HEALTH_CHECK_ENDPOINTS = {
  COINGECKO: `${API_CONFIG.COINGECKO.BASE_URL}/ping`,
  BINANCE: `${API_CONFIG.BINANCE.REST_BASE_URL}/ping`,
};
