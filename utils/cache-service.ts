// Enhanced caching service to maximize free tier API usage
// Implements multiple caching strategies with intelligent cache invalidation

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  source: string;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheStats {
  totalEntries: number;
  memoryUsage: number;
  hitRate: number;
  totalHits: number;
  totalMisses: number;
  oldestEntry: number;
  newestEntry: number;
}

// Advanced caching service with LRU eviction and analytics
export class CacheService {
  private cache: Map<string, CacheEntry> = new Map();
  private maxEntries: number;
  private totalHits: number = 0;
  private totalMisses: number = 0;
  private cleanupInterval: NodeJS.Timeout;

  constructor(maxEntries: number = 1000) {
    this.maxEntries = maxEntries;
    
    // Run cleanup every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  // Get data from cache with hit/miss tracking
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.totalMisses++;
      return null;
    }

    // Check if entry is expired
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key);
      this.totalMisses++;
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.totalHits++;
    
    return entry.data as T;
  }

  // Set data in cache with metadata
  set<T>(key: string, data: T, ttl: number = 300, source: string = 'unknown'): void {
    // Implement LRU eviction if cache is full
    if (this.cache.size >= this.maxEntries && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl,
      source,
      accessCount: 0,
      lastAccessed: Date.now(),
    };

    this.cache.set(key, entry);
  }

  // Check if key exists and is not expired
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;
    
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  // Delete specific key
  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  // Clear all cache entries
  clear(): void {
    this.cache.clear();
    this.totalHits = 0;
    this.totalMisses = 0;
  }

  // Get cache statistics
  getStats(): CacheStats {
    const entries = Array.from(this.cache.values());
    const now = Date.now();
    
    return {
      totalEntries: this.cache.size,
      memoryUsage: this.estimateMemoryUsage(),
      hitRate: this.totalHits + this.totalMisses > 0 ? 
        (this.totalHits / (this.totalHits + this.totalMisses)) * 100 : 0,
      totalHits: this.totalHits,
      totalMisses: this.totalMisses,
      oldestEntry: entries.length > 0 ? Math.min(...entries.map(e => e.timestamp)) : now,
      newestEntry: entries.length > 0 ? Math.max(...entries.map(e => e.timestamp)) : now,
    };
  }

  // Get cache entries by source
  getEntriesBySource(source: string): Array<{ key: string; entry: CacheEntry }> {
    const result: Array<{ key: string; entry: CacheEntry }> = [];
    
    this.cache.forEach((entry, key) => {
      if (entry.source === source) {
        result.push({ key, entry });
      }
    });
    
    return result;
  }

  // Invalidate cache entries by source
  invalidateBySource(source: string): number {
    let deletedCount = 0;
    
    this.cache.forEach((entry, key) => {
      if (entry.source === source) {
        this.cache.delete(key);
        deletedCount++;
      }
    });
    
    return deletedCount;
  }

  // Invalidate cache entries by pattern
  invalidateByPattern(pattern: RegExp): number {
    let deletedCount = 0;
    
    this.cache.forEach((entry, key) => {
      if (pattern.test(key)) {
        this.cache.delete(key);
        deletedCount++;
      }
    });
    
    return deletedCount;
  }

  // Preload cache with data
  preload<T>(entries: Array<{ key: string; data: T; ttl?: number; source?: string }>): void {
    entries.forEach(({ key, data, ttl = 300, source = 'preload' }) => {
      this.set(key, data, ttl, source);
    });
  }

  // Export cache for persistence
  export(): Record<string, CacheEntry> {
    const exported: Record<string, CacheEntry> = {};
    this.cache.forEach((entry, key) => {
      exported[key] = entry;
    });
    return exported;
  }

  // Import cache from persistence
  import(data: Record<string, CacheEntry>): number {
    let importedCount = 0;
    const now = Date.now();
    
    Object.entries(data).forEach(([key, entry]) => {
      // Only import non-expired entries
      if (now - entry.timestamp < entry.ttl * 1000) {
        this.cache.set(key, entry);
        importedCount++;
      }
    });
    
    return importedCount;
  }

  // Private methods
  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();
    
    this.cache.forEach((entry, key) => {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    });
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    this.cache.forEach((entry, key) => {
      if (now - entry.timestamp > entry.ttl * 1000) {
        expiredKeys.push(key);
      }
    });
    
    expiredKeys.forEach(key => this.cache.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`🧹 Cache cleanup: removed ${expiredKeys.length} expired entries`);
    }
  }

  private estimateMemoryUsage(): number {
    let size = 0;
    this.cache.forEach((entry, key) => {
      size += key.length * 2; // UTF-16 characters
      size += JSON.stringify(entry).length * 2;
    });
    return size;
  }

  // Cleanup on shutdown
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Specialized cache configurations for different data types
export const CACHE_CONFIGS = {
  PRICE_DATA: { ttl: 30, source: 'price' },        // 30 seconds
  MARKET_DATA: { ttl: 60, source: 'market' },      // 1 minute
  ON_CHAIN_DATA: { ttl: 300, source: 'onchain' },  // 5 minutes
  NEWS_DATA: { ttl: 900, source: 'news' },         // 15 minutes
  STATIC_DATA: { ttl: 3600, source: 'static' },    // 1 hour
  DEX_DATA: { ttl: 120, source: 'dex' },          // 2 minutes
  TRENDING_DATA: { ttl: 180, source: 'trending' }, // 3 minutes
} as const;

// Create singleton cache instance
export const cacheService = new CacheService(2000); // Increased capacity

// Helper functions for common caching patterns
export function createCacheKey(...parts: (string | number)[]): string {
  return parts.join(':');
}

export function withCache<T>(
  cacheKey: string,
  fetchFn: () => Promise<T>,
  config: { ttl: number; source: string }
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    // Try cache first
    const cached = cacheService.get<T>(cacheKey);
    if (cached !== null) {
      resolve(cached);
      return;
    }

    try {
      // Fetch data
      const data = await fetchFn();
      
      // Cache the result
      cacheService.set(cacheKey, data, config.ttl, config.source);
      
      resolve(data);
    } catch (error) {
      reject(error);
    }
  });
}

// Cache warming strategies
export class CacheWarmer {
  private warmupTasks: Array<() => Promise<void>> = [];

  addWarmupTask(task: () => Promise<void>): void {
    this.warmupTasks.push(task);
  }

  async warmup(): Promise<void> {
    console.log(`🔥 Starting cache warmup with ${this.warmupTasks.length} tasks...`);
    
    const results = await Promise.allSettled(this.warmupTasks.map(task => task()));
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    console.log(`🔥 Cache warmup completed: ${successful} successful, ${failed} failed`);
  }
}

// Export cache warmer instance
export const cacheWarmer = new CacheWarmer();

// Predefined warmup tasks for common data
export function addCommonWarmupTasks(): void {
  // Warm up popular crypto prices
  cacheWarmer.addWarmupTask(async () => {
    const { binanceService } = await import('./binance-service');
    try {
      const symbols = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT'];
      const prices = await binanceService.getPrices(symbols);
      const cacheKey = createCacheKey('prices', 'popular');
      cacheService.set(cacheKey, prices, CACHE_CONFIGS.PRICE_DATA.ttl, CACHE_CONFIGS.PRICE_DATA.source);
    } catch (error) {
      console.warn('Failed to warm up popular prices:', error);
    }
  });

  // Warm up trending data
  cacheWarmer.addWarmupTask(async () => {
    try {
      const response = await fetch('/api/trending?source=unified&limit=10');
      if (response.ok) {
        const data = await response.json();
        const cacheKey = createCacheKey('trending', 'popular');
        cacheService.set(cacheKey, data, CACHE_CONFIGS.TRENDING_DATA.ttl, CACHE_CONFIGS.TRENDING_DATA.source);
      }
    } catch (error) {
      console.warn('Failed to warm up trending data:', error);
    }
  });
}

// Performance monitoring
export function logCachePerformance(): void {
  const stats = cacheService.getStats();
  console.log('📊 Cache Performance:', {
    entries: stats.totalEntries,
    hitRate: `${stats.hitRate.toFixed(2)}%`,
    memoryUsage: `${(stats.memoryUsage / 1024 / 1024).toFixed(2)} MB`,
    totalOperations: stats.totalHits + stats.totalMisses,
  });
}