"use client";

// Export utilities for downloadable spreadsheets and data files

export interface ExportData {
  [key: string]: any;
}

export interface ExportOptions {
  filename?: string;
  format?: 'csv' | 'json' | 'xlsx';
  includeTimestamp?: boolean;
  customHeaders?: string[];
}

// Convert array of objects to CSV format
export function convertToCSV(data: ExportData[], headers?: string[]): string {
  if (!data.length) return '';

  // Get headers from first object if not provided
  const csvHeaders = headers || Object.keys(data[0]);
  
  // Create CSV header row
  const headerRow = csvHeaders.join(',');
  
  // Create data rows
  const dataRows = data.map(row =>
    csvHeaders.map(header => {
      const value = row[header];
      // Handle special cases for CSV formatting
      if (value === null || value === undefined) return '';
      if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return String(value);
    }).join(',')
  );

  return [headerRow, ...dataRows].join('\n');
}

// Convert array of objects to JSON format
export function convertToJSON(data: ExportData[]): string {
  return JSON.stringify(data, null, 2);
}

// Download file with given content
export function downloadFile(content: string, filename: string, mimeType: string): void {
  const blob = new Blob([content], { type: mimeType });
  const url = URL.createObjectURL(blob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  link.style.display = 'none';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  // Clean up
  URL.revokeObjectURL(url);
}

// Generate filename with timestamp
export function generateFilename(baseName: string, format: string, includeTimestamp = true): string {
  const timestamp = includeTimestamp 
    ? new Date().toISOString().slice(0, 19).replace(/:/g, '-')
    : '';
  const timestampSuffix = timestamp ? `_${timestamp}` : '';
  return `${baseName}${timestampSuffix}.${format}`;
}

// Main export function
export function exportData(data: ExportData[], options: ExportOptions = {}): void {
  const {
    filename = 'export',
    format = 'csv',
    includeTimestamp = true,
    customHeaders
  } = options;

  if (!data.length) {
    console.warn('No data to export');
    return;
  }

  let content: string;
  let mimeType: string;

  switch (format) {
    case 'csv':
      content = convertToCSV(data, customHeaders);
      mimeType = 'text/csv;charset=utf-8;';
      break;
    case 'json':
      content = convertToJSON(data);
      mimeType = 'application/json;charset=utf-8;';
      break;
    case 'xlsx':
      // For XLSX, we'd need a library like xlsx or exceljs
      // For now, fallback to CSV
      console.warn('XLSX format not implemented, falling back to CSV');
      content = convertToCSV(data, customHeaders);
      mimeType = 'text/csv;charset=utf-8;';
      break;
    default:
      throw new Error(`Unsupported format: ${format}`);
  }

  const fullFilename = generateFilename(filename, format, includeTimestamp);
  downloadFile(content, fullFilename, mimeType);
}

// Specific export functions for different data types

// Export portfolio data
export function exportPortfolioData(holdings: any[]): void {
  const exportableData = holdings.map(holding => ({
    'Symbol': holding.symbol,
    'Name': holding.name,
    'Amount': holding.amount,
    'Average Buy Price ($)': holding.avgBuyPrice,
    'Current Price ($)': holding.currentPrice,
    'Total Value ($)': (holding.amount * holding.currentPrice).toFixed(2),
    'Total Invested ($)': (holding.amount * holding.avgBuyPrice).toFixed(2),
    'P&L ($)': ((holding.amount * holding.currentPrice) - (holding.amount * holding.avgBuyPrice)).toFixed(2),
    'P&L (%)': ((((holding.amount * holding.currentPrice) - (holding.amount * holding.avgBuyPrice)) / (holding.amount * holding.avgBuyPrice)) * 100).toFixed(2),
    'Last Updated': new Date(holding.lastUpdated).toLocaleString(),
  }));

  exportData(exportableData, {
    filename: 'portfolio_holdings',
    format: 'csv',
    includeTimestamp: true,
  });
}

// Export heatmap/market data
export function exportHeatmapData(coins: any[]): void {
  const exportableData = coins.map(coin => ({
    'Rank': coin.rank || '',
    'Symbol': coin.symbol,
    'Name': coin.name || coin.symbol,
    'Price ($)': coin.price?.toFixed(4) || '',
    '24h Change (%)': coin.change24h?.toFixed(2) || '',
    '7d Change (%)': coin.change7d?.toFixed(2) || '',
    'Market Cap ($)': coin.marketCap?.toLocaleString() || '',
    '24h Volume ($)': coin.volume24h?.toLocaleString() || '',
    'Last Updated': new Date().toLocaleString(),
  }));

  exportData(exportableData, {
    filename: 'crypto_heatmap',
    format: 'csv',
    includeTimestamp: true,
  });
}

// Export chart data
export function exportChartData(chartData: any[], title: string): void {
  const exportableData = chartData.map(point => ({
    'Date/Time': point.time,
    'Value': point.value || point.close,
    'Open': point.open || '',
    'High': point.high || '',
    'Low': point.low || '',
    'Volume': point.volume || '',
  })).filter(point => point.Value !== undefined);

  exportData(exportableData, {
    filename: `chart_data_${title.toLowerCase().replace(/\s+/g, '_')}`,
    format: 'csv',
    includeTimestamp: true,
  });
}

// Export coin discovery data
export function exportCoinDiscoveryData(coins: any[]): void {
  const exportableData = coins.map(coin => ({
    'Symbol': coin.symbol,
    'Name': coin.name,
    'Price ($)': coin.price?.toFixed(4) || '',
    '24h Change (%)': coin.change24h?.toFixed(2) || '',
    '7d Change (%)': coin.change7d?.toFixed(2) || '',
    'Market Cap ($)': coin.marketCap?.toLocaleString() || '',
    'Volume 24h ($)': coin.volume24h?.toLocaleString() || '',
    'Rank': coin.rank || '',
    'Category': coin.category || '',
    'Tags': coin.tags?.join(', ') || '',
    'Is New': coin.isNew ? 'Yes' : 'No',
    'Is Trending': coin.isTrending ? 'Yes' : 'No',
    'Social Score': coin.socialScore || '',
    'Technical Score': coin.technicalScore || '',
    'Fundamental Score': coin.fundamentalScore || '',
    'Launch Date': coin.launchDate || '',
  }));

  exportData(exportableData, {
    filename: 'coin_discovery',
    format: 'csv',
    includeTimestamp: true,
  });
}

// Export analytics summary
export function exportAnalyticsSummary(data: any): void {
  const summaryData = [{
    'Total Portfolio Value ($)': data.totalValue?.toFixed(2) || '',
    'Total Invested ($)': data.totalInvested?.toFixed(2) || '',
    'Total P&L ($)': data.totalPnL?.toFixed(2) || '',
    'Total P&L (%)': data.totalPnLPercentage?.toFixed(2) || '',
    'Diversity Score': data.diversityScore || '',
    'Best Performer': data.bestPerformer?.symbol || '',
    'Worst Performer': data.worstPerformer?.symbol || '',
    'Number of Holdings': data.holdingsCount || '',
    'Export Date': new Date().toLocaleString(),
  }];

  exportData(summaryData, {
    filename: 'portfolio_summary',
    format: 'csv',
    includeTimestamp: true,
  });
}

// Helper function to create export button with loading state
export function createExportButton(
  data: any[], 
  exportFunction: (data: any[]) => void,
  disabled = false
): {
  onClick: () => void;
  disabled: boolean;
} {
  return {
    onClick: () => {
      try {
        exportFunction(data);
      } catch (error) {
        console.error('Export failed:', error);
        // In a real app, you'd show a toast notification
      }
    },
    disabled: disabled || !data.length,
  };
}

export default {
  exportData,
  exportPortfolioData,
  exportHeatmapData,
  exportChartData,
  exportCoinDiscoveryData,
  exportAnalyticsSummary,
  createExportButton,
};