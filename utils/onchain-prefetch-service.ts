// On-chain data prefetching service for tab-aware caching
// Fetches all needed data upfront and caches it to avoid API calls during tab switches

import { cacheService, createCacheKey } from './cache-service';
import { CACHE_DURATIONS } from './api-config';

export interface OnChainDataBundle {
  chains: {
    [chainId: string]: {
      metrics: any;
      derivedMetrics: any;
      marketData: any;
      lastUpdated: number;
    };
  };
  comparison: {
    results: any[];
    insights: string[];
    lastUpdated: number;
  };
  dexScreener: {
    tokenProfiles: any[];
    trendingPairs: any[];
    lastUpdated: number;
  };
  general: {
    availableChains: any[];
    lastUpdated: number;
  };
  bundleTimestamp: number;
}

export class OnChainPrefetchService {
  private static instance: OnChainPrefetchService;
  private prefetchPromise: Promise<OnChainDataBundle> | null = null;
  private lastPrefetchTime = 0;
  private readonly PREFETCH_INTERVAL = 10 * 60 * 1000; // 10 minutes

  private constructor() {}

  public static getInstance(): OnChainPrefetchService {
    if (!OnChainPrefetchService.instance) {
      OnChainPrefetchService.instance = new OnChainPrefetchService();
    }
    return OnChainPrefetchService.instance;
  }

  // Main prefetch method - fetches all data needed for on-chain tabs
  async prefetchAllOnChainData(forceRefresh = false): Promise<OnChainDataBundle> {
    const now = Date.now();
    const shouldRefresh = forceRefresh || (now - this.lastPrefetchTime) > this.PREFETCH_INTERVAL;

    if (!shouldRefresh && this.prefetchPromise) {
      return this.prefetchPromise;
    }

    console.log('🔄 Starting comprehensive on-chain data prefetch...');

    this.prefetchPromise = this.performPrefetch();
    this.lastPrefetchTime = now;

    return this.prefetchPromise;
  }

  private async performPrefetch(): Promise<OnChainDataBundle> {
    const bundleCacheKey = createCacheKey('onchain-bundle', 'complete', 'all-data');
    
    // Define the chains we need data for
    const targetChains = ['bitcoin', 'ethereum', 'solana', 'cardano', 'polkadot'];
    
    // Check if we have a cached bundle first
    if (!this.shouldForceRefresh()) {
      const cachedBundle = cacheService.get<OnChainDataBundle>(bundleCacheKey);
      if (cachedBundle) {
        console.log('✅ Using cached on-chain data bundle');
        return cachedBundle;
      }
    }

    try {
      
      console.log(`📊 Prefetching data for ${targetChains.length} chains...`);

      // Batch fetch all chain metrics
      const chainPromises = targetChains.map(async (chainId) => {
        try {
          const [metricsResponse, marketDataResponse] = await Promise.all([
            this.fetchChainMetrics(chainId),
            this.fetchChainMarketData(chainId),
          ]);

          return {
            chainId,
            metrics: metricsResponse?.chain || {},
            derivedMetrics: metricsResponse?.derivedMetrics || {},
            marketData: marketDataResponse || {},
            lastUpdated: Date.now(),
          };
        } catch (error) {
          console.warn(`⚠️ Failed to fetch data for ${chainId}:`, error);
          return {
            chainId,
            metrics: this.getMockChainData(chainId),
            derivedMetrics: {},
            marketData: {},
            lastUpdated: Date.now(),
          };
        }
      });

      // Fetch additional data in parallel
      const [chainResults, comparisonData, dexData, availableChains] = await Promise.all([
        Promise.all(chainPromises),
        this.fetchChainComparison(targetChains),
        this.fetchDexScreenerData(),
        this.fetchAvailableChains(),
      ]);

      // Structure the data bundle
      const chains: OnChainDataBundle['chains'] = {};
      chainResults.forEach(result => {
        chains[result.chainId] = {
          metrics: result.metrics,
          derivedMetrics: result.derivedMetrics,
          marketData: result.marketData,
          lastUpdated: result.lastUpdated,
        };
      });

      const bundle: OnChainDataBundle = {
        chains,
        comparison: {
          results: comparisonData?.chains || [],
          insights: comparisonData?.insights || [],
          lastUpdated: Date.now(),
        },
        dexScreener: {
          tokenProfiles: dexData?.tokenProfiles || [],
          trendingPairs: dexData?.trendingPairs?.pairs || [],
          lastUpdated: Date.now(),
        },
        general: {
          availableChains: availableChains?.chains || [],
          lastUpdated: Date.now(),
        },
        bundleTimestamp: Date.now(),
      };

      // Cache the complete bundle
      cacheService.set(bundleCacheKey, bundle, CACHE_DURATIONS.MARKET_DATA, 'onchain-bundle');
      
      console.log(`✅ Prefetch completed: ${Object.keys(chains).length} chains, ${bundle.comparison.results.length} comparisons`);
      
      return bundle;

    } catch (error) {
      console.error('❌ Prefetch failed:', error);
      
      // Return minimal bundle with mock data
      return this.createFallbackBundle(targetChains);
    }
  }

  // Individual fetch methods
  private async fetchChainMetrics(chainId: string): Promise<any> {
    const response = await fetch(`/api/onchain-data?action=metrics&chain=${chainId}`);
    if (!response.ok) throw new Error(`Chain metrics fetch failed: ${response.statusText}`);
    return response.json();
  }

  private async fetchChainMarketData(chainId: string): Promise<any> {
    try {
      const response = await fetch(`/api/market-data?symbols=${chainId}&limit=1`);
      if (!response.ok) throw new Error(`Market data fetch failed: ${response.statusText}`);
      const data = await response.json();
      return data.data?.[0] || {};
    } catch (error) {
      console.warn(`Market data failed for ${chainId}:`, error);
      return {};
    }
  }

  private async fetchChainComparison(chains: string[]): Promise<any> {
    try {
      const response = await fetch(`/api/onchain-data?action=compare&chains=${chains.join(',')}&analysisType=comprehensive`);
      if (!response.ok) throw new Error(`Comparison fetch failed: ${response.statusText}`);
      return response.json();
    } catch (error) {
      console.warn('Comparison fetch failed:', error);
      return { chains: [], insights: [] };
    }
  }

  private async fetchDexScreenerData(): Promise<any> {
    try {
      const response = await fetch('/api/onchain-data?action=dexscreener');
      if (!response.ok) throw new Error(`DexScreener fetch failed: ${response.statusText}`);
      return response.json();
    } catch (error) {
      console.warn('DexScreener fetch failed:', error);
      return { tokenProfiles: [], trendingPairs: { pairs: [] } };
    }
  }

  private async fetchAvailableChains(): Promise<any> {
    try {
      const response = await fetch('/api/onchain-data?action=list');
      if (!response.ok) throw new Error(`Chain list fetch failed: ${response.statusText}`);
      return response.json();
    } catch (error) {
      console.warn('Chain list fetch failed:', error);
      return { chains: [] };
    }
  }

  // Utility methods
  private shouldForceRefresh(): boolean {
    return process.env.NODE_ENV === 'development';
  }

  private createFallbackBundle(chains: string[]): OnChainDataBundle {
    const fallbackChains: OnChainDataBundle['chains'] = {};
    
    chains.forEach(chainId => {
      fallbackChains[chainId] = {
        metrics: this.getMockChainData(chainId),
        derivedMetrics: {},
        marketData: {},
        lastUpdated: Date.now(),
      };
    });

    return {
      chains: fallbackChains,
      comparison: { results: [], insights: [], lastUpdated: Date.now() },
      dexScreener: { tokenProfiles: [], trendingPairs: [], lastUpdated: Date.now() },
      general: { availableChains: [], lastUpdated: Date.now() },
      bundleTimestamp: Date.now(),
    };
  }

  private getMockChainData(chainId: string): any {
    const mockData: Record<string, any> = {
      bitcoin: {
        name: 'Bitcoin', symbol: 'BTC', logo: '₿',
        dailyActiveAddresses: 1200000, dailyTransactions: 350000,
        price: 118953.53, marketCap: 2366967578877,
      },
      ethereum: {
        name: 'Ethereum', symbol: 'ETH', logo: 'Ξ',
        dailyActiveAddresses: 520000, dailyTransactions: 1400000,
        price: 3737.52, marketCap: 451138108044,
      },
      solana: {
        name: 'Solana', symbol: 'SOL', logo: '◎',
        dailyActiveAddresses: 220000, dailyTransactions: 32000000,
        price: 188.42, marketCap: 101408443821,
      },
      binancecoin: {
        name: 'BNB', symbol: 'BNB', logo: 'BNB',
        dailyActiveAddresses: 180000, dailyTransactions: 850000,
        price: 779.13, marketCap: 108525324692,
      },
    };

    return mockData[chainId] || { name: chainId, symbol: chainId.toUpperCase() };
  }

  // Public methods for component consumption
  async getChainData(chainId: string): Promise<any> {
    const bundle = await this.prefetchAllOnChainData();
    return bundle.chains[chainId] || null;
  }

  async getComparisonData(): Promise<any> {
    const bundle = await this.prefetchAllOnChainData();
    return bundle.comparison;
  }

  async getDexScreenerData(): Promise<any> {
    const bundle = await this.prefetchAllOnChainData();
    return bundle.dexScreener;
  }

  async getAvailableChains(): Promise<any[]> {
    const bundle = await this.prefetchAllOnChainData();
    return bundle.general.availableChains;
  }

  // Force refresh method for manual triggers
  async forceRefresh(): Promise<OnChainDataBundle> {
    console.log('🔄 Forcing on-chain data refresh...');
    return this.prefetchAllOnChainData(true);
  }

  // Get bundle status
  getBundleStatus(): { 
    isLoaded: boolean; 
    lastUpdate: number; 
    needsRefresh: boolean; 
  } {
    const now = Date.now();
    const needsRefresh = (now - this.lastPrefetchTime) > this.PREFETCH_INTERVAL;
    
    return {
      isLoaded: this.prefetchPromise !== null,
      lastUpdate: this.lastPrefetchTime,
      needsRefresh,
    };
  }
}

// Export singleton instance
export const onChainPrefetchService = OnChainPrefetchService.getInstance();

// Auto-prefetch disabled for lazy loading - components will trigger prefetch when needed
// if (typeof window !== 'undefined') {
//   // Delay initial prefetch to avoid blocking page load
//   setTimeout(() => {
//     onChainPrefetchService.prefetchAllOnChainData().catch(error => {
//       console.warn('Initial prefetch failed:', error);
//     });
//   }, 2000);
// }