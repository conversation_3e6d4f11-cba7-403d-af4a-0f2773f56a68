// Client-side API service - calls our Next.js API routes (no CORS issues)
import { CACHE_DURATIONS } from './api-config';

// Types for API responses
export interface MarketDataResponse {
  data: any[];
  sources?: string[];
  source?: string;
  timestamp: number;
  count: number;
}

export interface PricesResponse {
  prices: Record<string, number>;
  sources?: string[];
  source?: string;
  timestamp: number;
  count: number;
  requested?: number;
}

export interface HealthResponse {
  binance: boolean;
  coingecko: boolean;
  timestamp: number;
}

// Client-side cache
class ClientCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = CACHE_DURATIONS.MARKET_DATA): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttl * 1000, // Convert to milliseconds
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    const isExpired = Date.now() - item.timestamp > item.ttl;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }
}

// Client API Service
export class ClientAPIService {
  private cache = new ClientCache();
  private baseURL = '/api';

  // Get market data
  async getMarketData(
    symbols?: string[],
    limit: number = 50,
    source: 'binance' | 'coingecko' | 'unified' = 'unified'
  ): Promise<MarketDataResponse> {
    const cacheKey = `market-data-${symbols?.join(',') || 'all'}-${limit}-${source}`;
    
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached) {
      console.log('📦 Using cached market data');
      return cached;
    }

    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        source,
      });
      
      if (symbols && symbols.length > 0) {
        params.append('symbols', symbols.join(','));
      }

      console.log(`🔄 Fetching market data: ${source} source`);
      const response = await fetch(`${this.baseURL}/market-data?${params}`);
      
      if (!response.ok) {
        throw new Error(`Market data API failed: ${response.status} ${response.statusText}`);
      }

      const data: MarketDataResponse = await response.json();
      
      // Cache the response
      this.cache.set(cacheKey, data, CACHE_DURATIONS.MARKET_DATA);
      
      console.log(`✅ Got market data: ${data.count} coins from [${data.sources?.join(', ') || data.source}]`);
      return data;
    } catch (error) {
      console.error('Failed to fetch market data:', error);
      throw error;
    }
  }

  // Get simple prices
  async getPrices(
    symbols: string[],
    source: 'binance' | 'coingecko' | 'unified' = 'unified'
  ): Promise<PricesResponse> {
    const cacheKey = `prices-${symbols.join(',')}-${source}`;
    
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached) {
      console.log('📦 Using cached prices');
      return cached;
    }

    try {
      const params = new URLSearchParams({
        symbols: symbols.join(','),
        source,
      });

      console.log(`💰 Fetching prices for ${symbols.length} symbols: ${source} source`);
      const response = await fetch(`${this.baseURL}/prices?${params}`);
      
      if (!response.ok) {
        throw new Error(`Prices API failed: ${response.status} ${response.statusText}`);
      }

      const data: PricesResponse = await response.json();
      
      // Cache the response
      this.cache.set(cacheKey, data, CACHE_DURATIONS.PRICE_DATA);
      
      console.log(`✅ Got prices: ${data.count}/${data.requested || symbols.length} from [${data.sources?.join(', ') || data.source}]`);
      return data;
    } catch (error) {
      console.error('Failed to fetch prices:', error);
      throw error;
    }
  }

  // Get batch prices (for real-time updates)
  async getBatchPrices(symbols: string[]): Promise<PricesResponse> {
    try {
      console.log(`🔄 Batch price update for ${symbols.length} symbols`);
      const response = await fetch(`${this.baseURL}/prices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'batch_prices',
          symbols,
        }),
      });
      
      if (!response.ok) {
        throw new Error(`Batch prices API failed: ${response.status} ${response.statusText}`);
      }

      const data: PricesResponse = await response.json();
      console.log(`✅ Batch prices: ${data.count}/${symbols.length} updated`);
      return data;
    } catch (error) {
      console.error('Failed to fetch batch prices:', error);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<HealthResponse> {
    try {
      console.log('🏥 Checking API health...');
      const response = await fetch(`${this.baseURL}/market-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'health' }),
      });
      
      if (!response.ok) {
        throw new Error(`Health check failed: ${response.status} ${response.statusText}`);
      }

      const data: HealthResponse = await response.json();
      console.log('🏥 Health check results:', data);
      return data;
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }

  // Get crypto news
  async getNews(query?: string, items: number = 20): Promise<any> {
    const cacheKey = `news-${query || 'default'}-${items}`;
    
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached) {
      console.log('📦 Using cached news');
      return cached;
    }

    try {
      const params = new URLSearchParams({
        items: items.toString(),
      });
      
      if (query) {
        params.append('query', query);
      }

      console.log(`📰 Fetching crypto news`);
      const response = await fetch(`${this.baseURL}/crypto-news?${params}`);
      
      if (!response.ok) {
        throw new Error(`News API failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // Cache the response
      this.cache.set(cacheKey, data, CACHE_DURATIONS.NEWS_DATA);
      
      console.log(`✅ Got news: ${data.totalResults} articles`);
      return data;
    } catch (error) {
      console.error('Failed to fetch news:', error);
      throw error;
    }
  }

  // Get on-chain data
  async getOnChainData(action: string, params: Record<string, any> = {}): Promise<any> {
    const cacheKey = `onchain-${action}-${JSON.stringify(params)}`;
    
    // Check cache first
    const cached = this.cache.get(cacheKey);
    if (cached) {
      console.log('📦 Using cached on-chain data');
      return cached;
    }

    try {
      const searchParams = new URLSearchParams({
        action,
        ...params,
      });

      console.log(`⛓️ Fetching on-chain data: ${action}`);
      const response = await fetch(`${this.baseURL}/onchain-data?${searchParams}`);
      
      if (!response.ok) {
        throw new Error(`On-chain API failed: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      // Cache the response
      this.cache.set(cacheKey, data, CACHE_DURATIONS.MARKET_DATA);
      
      console.log(`✅ Got on-chain data for ${action}`);
      return data;
    } catch (error) {
      console.error('Failed to fetch on-chain data:', error);
      throw error;
    }
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
    console.log('🗑️ Cache cleared');
  }

  // Get cache stats
  getCacheStats(): { size: number } {
    return {
      size: this.cache['cache'].size,
    };
  }
}

// Create singleton instance
export const clientAPIService = new ClientAPIService();

// Convenience functions
export const getMarketData = (symbols?: string[], limit?: number) => 
  clientAPIService.getMarketData(symbols, limit);

export const getPrices = (symbols: string[]) => 
  clientAPIService.getPrices(symbols);

export const getNews = (query?: string, items?: number) => 
  clientAPIService.getNews(query, items);

export const getOnChainData = (action: string, params?: Record<string, any>) => 
  clientAPIService.getOnChainData(action, params);

export const healthCheck = () => 
  clientAPIService.healthCheck();
