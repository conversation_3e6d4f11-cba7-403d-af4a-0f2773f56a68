'use client';

import { useEffect, useRef, useState, useCallback } from 'react';

// Types for lazy loading
export interface LazyLoadingOptions {
  threshold?: number;
  rootMargin?: string;
  triggerOnMount?: boolean;
  triggerOnTabChange?: boolean;
  debounceMs?: number;
}

export interface LazyDataState<T> {
  data: T | null;
  isLoading: boolean;
  isError: boolean;
  error: string | null;
  hasLoaded: boolean;
  lastUpdated: number;
}

export interface LazyDataActions {
  trigger: () => Promise<void>;
  reset: () => void;
  refresh: () => Promise<void>;
}

// Default options
const DEFAULT_OPTIONS: LazyLoadingOptions = {
  threshold: 0.1,
  rootMargin: '50px 0px',
  triggerOnMount: false,
  triggerOnTabChange: true,
  debounceMs: 100,
};

// Lazy loading manager class
export class LazyLoadingManager {
  private observers: Map<string, IntersectionObserver> = new Map();
  private loadingStates: Map<string, boolean> = new Map();
  private debounceTimers: Map<string, NodeJS.Timeout> = new Map();

  // Create intersection observer for a specific element
  createObserver(
    elementId: string,
    callback: () => void,
    options: LazyLoadingOptions = DEFAULT_OPTIONS
  ): IntersectionObserver | null {
    if (typeof window === 'undefined') return null;

    // Clean up existing observer
    this.cleanupObserver(elementId);

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !this.loadingStates.get(elementId)) {
            this.debounceCallback(elementId, callback, options.debounceMs || 100);
          }
        });
      },
      {
        threshold: options.threshold || 0.1,
        rootMargin: options.rootMargin || '50px 0px',
      }
    );

    this.observers.set(elementId, observer);
    return observer;
  }

  // Debounce callback execution
  private debounceCallback(elementId: string, callback: () => void, debounceMs: number) {
    const existingTimer = this.debounceTimers.get(elementId);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    const timer = setTimeout(() => {
      callback();
      this.debounceTimers.delete(elementId);
    }, debounceMs);

    this.debounceTimers.set(elementId, timer);
  }

  // Observe an element
  observe(elementId: string, element: Element) {
    const observer = this.observers.get(elementId);
    if (observer) {
      observer.observe(element);
    }
  }

  // Unobserve an element
  unobserve(elementId: string, element: Element) {
    const observer = this.observers.get(elementId);
    if (observer) {
      observer.unobserve(element);
    }
  }

  // Set loading state
  setLoading(elementId: string, isLoading: boolean) {
    this.loadingStates.set(elementId, isLoading);
  }

  // Get loading state
  isLoading(elementId: string): boolean {
    return this.loadingStates.get(elementId) || false;
  }

  // Cleanup observer
  cleanupObserver(elementId: string) {
    const observer = this.observers.get(elementId);
    if (observer) {
      observer.disconnect();
      this.observers.delete(elementId);
    }

    const timer = this.debounceTimers.get(elementId);
    if (timer) {
      clearTimeout(timer);
      this.debounceTimers.delete(elementId);
    }

    this.loadingStates.delete(elementId);
  }

  // Cleanup all observers
  cleanup() {
    this.observers.forEach((observer) => observer.disconnect());
    this.debounceTimers.forEach((timer) => clearTimeout(timer));
    this.observers.clear();
    this.debounceTimers.clear();
    this.loadingStates.clear();
  }
}

// Global lazy loading manager instance
export const lazyLoadingManager = new LazyLoadingManager();

// Hook for lazy data loading with intersection observer
export function useLazyData<T>(
  dataFetcher: () => Promise<T>,
  elementId: string,
  options: LazyLoadingOptions = DEFAULT_OPTIONS
): [LazyDataState<T>, LazyDataActions, React.RefObject<HTMLDivElement>] {
  const [state, setState] = useState<LazyDataState<T>>({
    data: null,
    isLoading: false,
    isError: false,
    error: null,
    hasLoaded: false,
    lastUpdated: 0,
  });

  const elementRef = useRef<HTMLDivElement>(null);
  const mountedRef = useRef(true);

  // Trigger data loading
  const trigger = useCallback(async () => {
    if (state.isLoading || !mountedRef.current) return;

    setState(prev => ({ ...prev, isLoading: true, isError: false, error: null }));
    lazyLoadingManager.setLoading(elementId, true);

    try {
      const data = await dataFetcher();
      
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          data,
          isLoading: false,
          hasLoaded: true,
          lastUpdated: Date.now(),
        }));
      }
    } catch (error) {
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          isError: true,
          error: error instanceof Error ? error.message : 'Unknown error',
        }));
      }
    } finally {
      lazyLoadingManager.setLoading(elementId, false);
    }
  }, [dataFetcher, elementId, state.isLoading]);

  // Reset state
  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      isError: false,
      error: null,
      hasLoaded: false,
      lastUpdated: 0,
    });
  }, []);

  // Refresh data (same as trigger but can be called even if already loaded)
  const refresh = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, isError: false, error: null }));
    lazyLoadingManager.setLoading(elementId, true);

    try {
      const data = await dataFetcher();
      
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          data,
          isLoading: false,
          hasLoaded: true,
          lastUpdated: Date.now(),
        }));
      }
    } catch (error) {
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          isError: true,
          error: error instanceof Error ? error.message : 'Unknown error',
        }));
      }
    } finally {
      lazyLoadingManager.setLoading(elementId, false);
    }
  }, [dataFetcher, elementId]);

  // Setup intersection observer
  useEffect(() => {
    if (!elementRef.current) return;

    const observer = lazyLoadingManager.createObserver(elementId, trigger, options);
    
    if (observer && elementRef.current) {
      lazyLoadingManager.observe(elementId, elementRef.current);
    }

    // Trigger on mount if specified
    if (options.triggerOnMount) {
      trigger();
    }

    return () => {
      if (elementRef.current) {
        lazyLoadingManager.unobserve(elementId, elementRef.current);
      }
    };
  }, [elementId, trigger, options]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      lazyLoadingManager.cleanupObserver(elementId);
    };
  }, [elementId]);

  return [state, { trigger, reset, refresh }, elementRef];
}

// Hook for tab-based lazy loading
export function useTabLazyData<T>(
  dataFetcher: () => Promise<T>,
  isActiveTab: boolean,
  tabId: string
): [LazyDataState<T>, LazyDataActions] {
  const [state, setState] = useState<LazyDataState<T>>({
    data: null,
    isLoading: false,
    isError: false,
    error: null,
    hasLoaded: false,
    lastUpdated: 0,
  });

  const mountedRef = useRef(true);

  // Trigger data loading
  const trigger = useCallback(async () => {
    if (state.isLoading || !mountedRef.current) return;

    setState(prev => ({ ...prev, isLoading: true, isError: false, error: null }));

    try {
      const data = await dataFetcher();
      
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          data,
          isLoading: false,
          hasLoaded: true,
          lastUpdated: Date.now(),
        }));
      }
    } catch (error) {
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          isError: true,
          error: error instanceof Error ? error.message : 'Unknown error',
        }));
      }
    }
  }, [dataFetcher, state.isLoading]);

  // Reset state
  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      isError: false,
      error: null,
      hasLoaded: false,
      lastUpdated: 0,
    });
  }, []);

  // Refresh data
  const refresh = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, isError: false, error: null }));

    try {
      const data = await dataFetcher();
      
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          data,
          isLoading: false,
          hasLoaded: true,
          lastUpdated: Date.now(),
        }));
      }
    } catch (error) {
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          isError: true,
          error: error instanceof Error ? error.message : 'Unknown error',
        }));
      }
    }
  }, [dataFetcher]);

  // Trigger when tab becomes active
  useEffect(() => {
    if (isActiveTab && !state.hasLoaded && !state.isLoading) {
      trigger();
    }
  }, [isActiveTab, state.hasLoaded, state.isLoading, trigger]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
    };
  }, []);

  return [state, { trigger, reset, refresh }];
}
