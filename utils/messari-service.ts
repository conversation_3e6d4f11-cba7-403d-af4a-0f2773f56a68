// Messari API Service - Free tier for on-chain and fundamental data
// Messari provides free access to metrics, news, and fundamental data
import { APIClient, createAPIClient } from './api-client';
import { CACHE_DURATIONS, FEATURE_FLAGS } from './api-config';

export interface MessariAsset {
  id: string;
  name: string;
  symbol: string;
  slug: string;
  contract_addresses?: {
    platform: string;
    contract_address: string;
  }[];
  _internal_temp_agora_id?: string;
}

export interface MessariMetrics {
  id: string;
  symbol: string;
  name: string;
  slug: string;
  contract_addresses?: any[];
  market_data?: {
    price_usd: number;
    price_btc: number;
    price_eth: number;
    volume_last_24_hours: number;
    real_volume_last_24_hours: number;
    volume_last_24_hours_overstatement_multiple: number;
    percent_change_usd_last_1_hour: number;
    percent_change_usd_last_24_hours: number;
    percent_change_usd_last_7_days: number;
    percent_change_usd_last_30_days: number;
    percent_change_usd_last_1_year: number;
    ohlcv_last_1_hour?: {
      open: number;
      high: number;
      low: number;
      close: number;
      volume: number;
    };
    ohlcv_last_24_hour?: {
      open: number;
      high: number;
      low: number;
      close: number;
      volume: number;
    };
    last_trade_at: string;
  };
  marketcap?: {
    rank: number;
    marketcap_dominance_percent: number;
    current_marketcap_usd: number;
    y_2050_marketcap_usd: number;
    y_plus10_marketcap_usd: number;
    liquid_marketcap_usd: number;
    volume_turnover_last_24_hours_percent: number;
  };
  supply?: {
    y_2050: number;
    y_plus10: number;
    liquid: number;
    circulating: number;
    total: number;
    max: number;
    annual_inflation_percent: number;
    stock_to_flow: number;
  };
  blockchain_stats_24_hours?: {
    count_of_active_addresses: number;
    count_of_transactions: number;
    count_of_payments: number;
    median_transaction_value: number;
    median_fee: number;
    total_fees: number;
    average_difficulty: number;
    kilobytes_added: number;
    count_of_blocks_added: number;
  };
  all_time_high?: {
    price: number;
    at: string;
    days_since: number;
    percent_down: number;
    breakeven_multiple: number;
  };
  cycle_low?: {
    price: number;
    at: string;
    percent_up: number;
    days_since: number;
  };
  token_sale_stats?: {
    sale_proceeds_usd: number;
    sale_start_date: string;
    sale_end_date: string;
    roi_since_sale_usd_percent: number;
    roi_since_sale_btc_percent: number;
    roi_since_sale_eth_percent: number;
  };
  staking_stats?: {
    staking_yield_percent: number;
    staking_type: string;
    staking_minimum: number;
    tokens_staked: number;
    tokens_staked_percent: number;
    real_reward_rate: number;
  };
  mining_stats?: {
    mining_algo: string;
    network_hash_rate: string;
    available_on_nicehash_percent: number;
    one_hour_attack_cost: number;
    twentyfour_hours_attack_cost: number;
    attack_appeal: number;
    hash_rate_30d_average: number;
    hash_rate_90d_average: number;
    mining_revenue_per_hash_per_second: number;
    mining_revenue_per_hash_per_second_30d_average: number;
    mining_revenue_per_hash_per_second_90d_average: number;
    mining_revenue_native_units: number;
    mining_revenue_per_hash_native_units: number;
    mining_revenue_total: number;
    mining_revenue_usd: number;
  };
  developer_activity?: {
    stars: number;
    watchers: number;
    commits_last_3_months: number;
    commits_last_1_year: number;
    lines_added_last_3_months: number;
    lines_added_last_1_year: number;
    lines_deleted_last_3_months: number;
    lines_deleted_last_1_year: number;
  };
  roi_data?: {
    percent_change_last_1_week: number;
    percent_change_last_1_month: number;
    percent_change_last_3_months: number;
    percent_change_last_1_year: number;
  };
  roi_by_year?: {
    [year: string]: {
      usd_returns: number;
      btc_returns: number;
      eth_returns: number;
    };
  };
  risk_metrics?: {
    sharpe_ratios: {
      last_30_days: number;
      last_90_days: number;
      last_1_year: number;
      last_3_years: number;
    };
    volatility_stats: {
      volatility_last_30_days: number;
      volatility_last_90_days: number;
      volatility_last_1_year: number;
      volatility_last_3_years: number;
    };
  };
  misc_data?: {
    vladimir_club_cost: number;
    btc_current_normalized_supply_price_usd: number;
    btc_y2050_normalized_supply_price_usd: number;
    asset_created_at: string;
    asset_age_days: number;
    categories: string[];
    sectors: string[];
    tags: string[];
  };
  reddit?: {
    active_user_count: number;
    subscribers: number;
  };
  on_chain_data?: {
    addresses_count: number;
    active_addresses: number;
    addresses_balance_greater_001_native_units: number;
    addresses_balance_greater_01_native_units: number;
    addresses_balance_greater_1_native_units: number;
    addresses_balance_greater_10_native_units: number;
    addresses_balance_greater_100_native_units: number;
    addresses_balance_greater_1k_native_units: number;
    addresses_balance_greater_10k_native_units: number;
    gini_coefficient: number;
    balance_concentration_1pct: number;
    balance_concentration_5pct: number;
    balance_concentration_10pct: number;
  };
  exchange_flows?: {
    flow_in_exchange_native_units: number;
    flow_in_exchange_usd: number;
    flow_in_exchange_usd_1mo: number;
    flow_out_exchange_native_units: number;
    flow_out_exchange_usd: number;
    flow_out_exchange_usd_1mo: number;
    flow_net_exchange_native_units: number;
    flow_net_exchange_usd: number;
    flow_net_exchange_usd_1mo: number;
    supply_exchange_native_units: number;
    supply_exchange_percent: number;
    supply_exchange_usd: number;
  };
}

export interface MessariNews {
  id: string;
  title: string;
  content: string;
  author: {
    name: string;
  };
  tags: string[];
  url: string;
  references?: {
    name: string;
    slug: string;
  }[];
  published_at: string;
  updated_at: string;
}

// Messari API Service - Free tier available
export class MessariService {
  private client: APIClient;
  private baseUrl = 'https://data.messari.io/api';

  constructor() {
    // Messari has a free tier with reasonable limits - API key optional
    const apiKey = process.env.MESSARI_API_KEY || undefined;
    this.client = createAPIClient(
      this.baseUrl,
      apiKey, // Optional for free tier
      20 // 20 requests per minute for free tier
    );
  }

  // Get all assets
  async getAllAssets(limit: number = 500): Promise<{ data: MessariAsset[] }> {
    try {
      const response = await this.client.get('/v1/assets', {
        limit,
        fields: 'id,name,symbol,slug,contract_addresses',
      }, {
        cacheTTL: CACHE_DURATIONS.STATIC_DATA,
      });
      return response;
    } catch (error) {
      console.error('Failed to fetch Messari assets:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { data: this.getMockAssets() };
      }
      throw error;
    }
  }

  // Get asset profile
  async getAssetProfile(assetKey: string): Promise<{ data: any }> {
    try {
      const response = await this.client.get(`/v1/assets/${assetKey}/profile`, undefined, {
        cacheTTL: CACHE_DURATIONS.STATIC_DATA,
      });
      return response;
    } catch (error) {
      console.error(`Failed to fetch asset profile for ${assetKey}:`, error);
      throw error;
    }
  }

  // Get asset metrics (comprehensive data)
  async getAssetMetrics(assetKey: string): Promise<{ data: MessariMetrics }> {
    try {
      const response = await this.client.get(`/v1/assets/${assetKey}/metrics`, undefined, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
      return response;
    } catch (error) {
      console.error(`Failed to fetch asset metrics for ${assetKey}:`, error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { data: this.getMockMetrics(assetKey) };
      }
      throw error;
    }
  }

  // Get multiple asset metrics
  async getMultipleAssetMetrics(assetKeys: string[]): Promise<{ data: MessariMetrics[] }> {
    try {
      const assets = assetKeys.join(',');
      const response = await this.client.get('/v1/assets/metrics', {
        assets,
        fields: 'id,name,symbol,market_data,marketcap,supply,blockchain_stats_24_hours,developer_activity,on_chain_data',
      }, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
      return response;
    } catch (error) {
      console.error('Failed to fetch multiple asset metrics:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { data: assetKeys.map(key => this.getMockMetrics(key)) };
      }
      throw error;
    }
  }

  // Get market data for multiple assets
  async getMarketData(assetKeys: string[]): Promise<{ data: any[] }> {
    try {
      const assets = assetKeys.join(',');
      const response = await this.client.get('/v1/assets/metrics', {
        assets,
        fields: 'id,name,symbol,market_data,marketcap',
      }, {
        cacheTTL: CACHE_DURATIONS.MARKET_DATA,
      });
      return response;
    } catch (error) {
      console.error('Failed to fetch market data:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { data: this.getMockMarketData(assetKeys) };
      }
      throw error;
    }
  }

  // Get news
  async getNews(limit: number = 50): Promise<{ data: MessariNews[] }> {
    try {
      const response = await this.client.get('/v1/news', {
        limit,
        fields: 'id,title,content,author,tags,url,references,published_at',
      }, {
        cacheTTL: CACHE_DURATIONS.NEWS_DATA,
      });
      return response;
    } catch (error) {
      console.error('Failed to fetch Messari news:', error);
      if (FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        return { data: this.getMockNews() };
      }
      throw error;
    }
  }

  // Transform Messari data to our on-chain format
  transformToOnChainMetrics(metrics: MessariMetrics): Partial<any> {
    const result: any = {};

    if (metrics.market_data) {
      result.price = metrics.market_data.price_usd;
      result.transactionVolume = metrics.market_data.volume_last_24_hours;
    }

    if (metrics.marketcap) {
      result.marketCap = metrics.marketcap.current_marketcap_usd;
      result.fdmc = metrics.marketcap.y_2050_marketcap_usd;
    }

    if (metrics.supply) {
      result.circulatingSupply = metrics.supply.circulating;
      result.inflation = {
        yoy: metrics.supply.annual_inflation_percent || 0,
        mom: (metrics.supply.annual_inflation_percent || 0) / 12,
      };
    }

    if (metrics.blockchain_stats_24_hours) {
      result.dailyActiveAddresses = metrics.blockchain_stats_24_hours.count_of_active_addresses;
      result.dailyTransactions = metrics.blockchain_stats_24_hours.count_of_transactions;
      result.fees = metrics.blockchain_stats_24_hours.total_fees;
      result.avgFee = metrics.blockchain_stats_24_hours.median_fee;
    }

    if (metrics.developer_activity) {
      result.activeDevelopers = metrics.developer_activity.commits_last_3_months;
    }

    if (metrics.staking_stats) {
      result.stakedSupply = metrics.staking_stats.tokens_staked;
    }

    if (metrics.on_chain_data) {
      result.totalAddresses = metrics.on_chain_data.addresses_count;
    }

    return result;
  }

  // Health check
  async ping(): Promise<boolean> {
    try {
      await this.client.get('/v1/assets', { limit: 1 }, { cache: false, timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  // Mock data methods
  private getMockAssets(): MessariAsset[] {
    return [
      {
        id: '1e31218a-e44e-4285-820c-8282ee222035',
        name: 'Bitcoin',
        symbol: 'BTC',
        slug: 'bitcoin',
      },
      {
        id: '7d4f5c9d-f8a5-4c8e-9c5b-8e5a3f7c9d8e',
        name: 'Ethereum',
        symbol: 'ETH',
        slug: 'ethereum',
      },
    ];
  }

  private getMockMetrics(_assetKey: string): MessariMetrics {
    return {
      id: '1e31218a-e44e-4285-820c-8282ee222035',
      symbol: 'BTC',
      name: 'Bitcoin',
      slug: 'bitcoin',
      market_data: {
        price_usd: 45000,
        price_btc: 1,
        price_eth: 18,
        volume_last_24_hours: 15000000000,
        real_volume_last_24_hours: 12000000000,
        volume_last_24_hours_overstatement_multiple: 1.25,
        percent_change_usd_last_1_hour: 0.5,
        percent_change_usd_last_24_hours: 2.3,
        percent_change_usd_last_7_days: -1.2,
        percent_change_usd_last_30_days: 8.7,
        percent_change_usd_last_1_year: 156.8,
        last_trade_at: new Date().toISOString(),
      },
      marketcap: {
        rank: 1,
        marketcap_dominance_percent: 42.5,
        current_marketcap_usd: ************,
        y_2050_marketcap_usd: ************,
        y_plus10_marketcap_usd: ************,
        liquid_marketcap_usd: ************,
        volume_turnover_last_24_hours_percent: 1.8,
      },
      supply: {
        y_2050: 21000000,
        y_plus10: 21000000,
        liquid: 18200000,
        circulating: 19700000,
        total: 19700000,
        max: 21000000,
        annual_inflation_percent: 1.8,
        stock_to_flow: 58.2,
      },
      blockchain_stats_24_hours: {
        count_of_active_addresses: 980000,
        count_of_transactions: 280000,
        count_of_payments: 280000,
        median_transaction_value: 1250,
        median_fee: 4.5,
        total_fees: 1250000,
        average_difficulty: 23500000000000,
        kilobytes_added: 1440000,
        count_of_blocks_added: 144,
      },
      developer_activity: {
        stars: 73000,
        watchers: 3500,
        commits_last_3_months: 847,
        commits_last_1_year: 3200,
        lines_added_last_3_months: 15000,
        lines_added_last_1_year: 58000,
        lines_deleted_last_3_months: 8500,
        lines_deleted_last_1_year: 32000,
      },
    };
  }

  private getMockMarketData(assetKeys: string[]): any[] {
    return assetKeys.map(key => ({
      id: key,
      name: key.charAt(0).toUpperCase() + key.slice(1),
      symbol: key.toUpperCase(),
      market_data: {
        price_usd: Math.random() * 50000 + 100,
        volume_last_24_hours: Math.random() * 1000000000,
        percent_change_usd_last_24_hours: (Math.random() - 0.5) * 20,
      },
      marketcap: {
        current_marketcap_usd: Math.random() * 100000000000,
        rank: Math.floor(Math.random() * 100) + 1,
      },
    }));
  }

  private getMockNews(): MessariNews[] {
    return [
      {
        id: '1',
        title: 'Bitcoin Network Hits New Hash Rate Record',
        content: 'The Bitcoin network has achieved a new all-time high in hash rate...',
        author: { name: 'Messari Research' },
        tags: ['bitcoin', 'mining', 'security'],
        url: 'https://messari.io/article/bitcoin-hash-rate-record',
        published_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
  }
}

// Create singleton instance
export const messariService = new MessariService();

// Utility function to convert common symbols to Messari asset keys
export function convertSymbolToMessariKey(symbol: string): string {
  const symbolMap: Record<string, string> = {
    BTC: 'bitcoin',
    ETH: 'ethereum',
    SOL: 'solana',
    ADA: 'cardano',
    DOT: 'polkadot',
    LINK: 'chainlink',
    AVAX: 'avalanche',
    MATIC: 'polygon',
    UNI: 'uniswap',
    ATOM: 'cosmos',
  };
  
  return symbolMap[symbol.toUpperCase()] || symbol.toLowerCase();
}