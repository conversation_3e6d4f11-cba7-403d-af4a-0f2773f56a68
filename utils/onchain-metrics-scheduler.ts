// On-chain metrics scheduler for twice-daily cache refresh
import { cacheService } from './cache-service';

export class OnChainMetricsScheduler {
  private refreshInterval: NodeJS.Timeout | null = null;
  private isRunning = false;

  // Start the twice-daily on-chain metrics refresh (every 12 hours)
  startScheduler(): void {
    if (this.isRunning) {
      console.log('⛓️ On-chain metrics scheduler already running');
      return;
    }

    console.log('⛓️ Starting on-chain metrics scheduler (twice daily refresh)');
    
    // Refresh every 12 hours (43,200,000 ms)
    this.refreshInterval = setInterval(() => {
      this.refreshOnChainMetricsCache();
    }, 12 * 60 * 60 * 1000);

    // Also do an initial refresh
    this.refreshOnChainMetricsCache();
    this.isRunning = true;
  }

  stopScheduler(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
      this.isRunning = false;
      console.log('⛓️ On-chain metrics scheduler stopped');
    }
  }

  // Manually trigger on-chain metrics cache refresh
  async refreshOnChainMetricsCache(): Promise<void> {
    console.log('⛓️ Starting scheduled on-chain metrics cache refresh...');
    
    try {
      // Pre-warm cache with popular tokens and networks
      const metricsCategories = [
        { tokens: ['bitcoin', 'ethereum'], type: 'major' },
        { tokens: ['solana', 'cardano', 'polkadot'], type: 'altcoins' },
        { tokens: ['chainlink', 'uniswap', 'avalanche'], type: 'defi' },
        { tokens: null, type: 'trending' }, // Overall trending metrics
      ];

      const refreshPromises = metricsCategories.map(async ({ tokens, type }) => {
        try {
          const baseUrl = process.env.NODE_ENV === 'production' 
            ? process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
            : 'http://localhost:3000';
          
          const tokensParam = tokens ? `&tokens=${tokens.join(',')}` : '';
          const url = `${baseUrl}/api/onchain-data?type=${type}${tokensParam}&refresh=true`;
          
          const response = await fetch(url);
          if (response.ok) {
            const data = await response.json();
            console.log(`✅ Refreshed on-chain metrics cache: ${data.tokensCount || 0} tokens for ${type}`);
          } else {
            console.warn(`⚠️ Failed to refresh on-chain metrics cache for ${type}: ${response.statusText}`);
          }
        } catch (error) {
          console.error(`❌ Error refreshing on-chain metrics cache for ${type}:`, error);
        }
      });

      // Also refresh DexScreener data
      try {
        const baseUrl = process.env.NODE_ENV === 'production' 
          ? process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
          : 'http://localhost:3000';
        
        const dexResponse = await fetch(`${baseUrl}/api/onchain-data?source=dexscreener&refresh=true`);
        if (dexResponse.ok) {
          console.log('✅ Refreshed DexScreener metrics cache');
        }
      } catch (error) {
        console.error('❌ Error refreshing DexScreener cache:', error);
      }

      await Promise.allSettled(refreshPromises);
      console.log('⛓️ Scheduled on-chain metrics cache refresh completed');
      
    } catch (error) {
      console.error('❌ On-chain metrics cache refresh failed:', error);
    }
  }

  // Get refresh status
  getStatus(): { isRunning: boolean; nextRefresh?: number } {
    return {
      isRunning: this.isRunning,
      nextRefresh: this.refreshInterval ? Date.now() + (12 * 60 * 60 * 1000) : undefined,
    };
  }

  // Clear all on-chain metrics cache
  clearOnChainMetricsCache(): number {
    const deletedCount = cacheService.invalidateBySource('onchain-metrics');
    console.log(`🗑️ Cleared ${deletedCount} on-chain metrics cache entries`);
    return deletedCount;
  }

  // Get on-chain metrics cache statistics
  getOnChainMetricsCacheStats(): { entries: number; sources: string[] } {
    const metricsEntries = cacheService.getEntriesBySource('onchain-metrics');
    const sources = Array.from(new Set(metricsEntries.map(entry => entry.entry.source)));
    
    return {
      entries: metricsEntries.length,
      sources,
    };
  }

  // Get detailed cache statistics
  getDetailedCacheStats(): {
    onchainMetrics: { entries: number };
    cacheStats: any;
  } {
    const onchainEntries = cacheService.getEntriesBySource('onchain-metrics');
    const cacheStats = cacheService.getStats();

    return {
      onchainMetrics: {
        entries: onchainEntries.length,
      },
      cacheStats,
    };
  }
}

// Create singleton instance
export const onChainMetricsScheduler = new OnChainMetricsScheduler();

// Auto-start in production or when explicitly enabled
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_ONCHAIN_SCHEDULER === 'true') {
  // Start after a short delay to allow app initialization
  setTimeout(() => {
    onChainMetricsScheduler.startScheduler();
  }, 35000); // 35 seconds delay (slightly after news scheduler)
}

// Graceful shutdown
process.on('SIGTERM', () => {
  onChainMetricsScheduler.stopScheduler();
});

process.on('SIGINT', () => {
  onChainMetricsScheduler.stopScheduler();
});