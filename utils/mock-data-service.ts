/**
 * Centralized Mock Data Service
 * 
 * This service consolidates all fallback data used throughout the application.
 * It provides realistic, up-to-date market data by fetching from CoinMarketCap/CoinGecko
 * and falls back to static mock data when APIs are unavailable.
 */

import { FEATURE_FLAGS } from './api-config';

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export interface CentralizedCoinData {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  change7d: number;
  marketCap: number;
  volume24h: number;
  rank: number;
  lastUpdated: number;
  // Comprehensive blockchain metrics
  tvl?: number;
  activeAddresses24h?: number;
  newAddresses24h?: number;
  transactions24h?: number;
  chainFees24h?: number;
  dexVolume24h?: number;
  gasPrice?: number;
  currentTPS?: number;
  averageFee?: number;
  maxTPS?: number;
  blockSize?: number;
  finalityTime?: number;
  validatorCount?: number;
  activeDevelopers?: number;
  commitActivity?: number;
  stakedSupply?: number;
  // Individual metric timestamps for granular update scheduling
  priceLastUpdated?: number;
  metricsLastUpdated?: number;
  technicalLastUpdated?: number;
  developmentLastUpdated?: number;
}

/**
 * COMPREHENSIVE BLOCKCHAIN DATA UPDATE SCHEDULE
 *
 * This configuration defines how frequently different types of blockchain metrics
 * should be updated based on their volatility and importance to users.
 *
 * RATIONALE:
 * - Real-time data (prices, gas) changes frequently and affects trading decisions
 * - Daily metrics (volumes, addresses) provide analysis insights but don't need constant updates
 * - Weekly data (development activity) changes gradually
 * - Monthly data (technical specs) rarely changes
 *
 * IMPLEMENTATION:
 * - Each metric type has individual timestamps for granular control
 * - Priority system ensures critical data is updated first
 * - Fallback strategies prevent data staleness during API failures
 * - Rate limiting considerations optimize API usage
 *
 * BENEFITS:
 * - Reduced API calls and resource usage
 * - Better user experience with fresh critical data
 * - Scalable system that can handle many cryptocurrencies
 * - Flexible scheduling allows for easy adjustments
 */
export const UPDATE_SCHEDULE = {
  // Real-time/Hourly - Critical for trading decisions
  REAL_TIME: {
    interval: 60 * 60 * 1000, // 1 hour in milliseconds
    metrics: ['price', 'change24h', 'change7d', 'gasPrice', 'currentTPS'],
    priority: 1,
    description: 'High-frequency data critical for trading decisions'
  },

  // Daily - Important for analysis
  DAILY: {
    interval: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
    metrics: [
      'volume24h', 'activeAddresses24h', 'newAddresses24h',
      'transactions24h', 'chainFees24h', 'dexVolume24h',
      'tvl', 'marketCap'
    ],
    priority: 2,
    description: 'Daily metrics for blockchain analysis'
  },

  // Weekly - Moderate importance
  WEEKLY: {
    interval: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
    metrics: ['averageFee', 'activeDevelopers', 'commitActivity', 'rank'],
    priority: 3,
    description: 'Weekly metrics for trend analysis'
  },

  // Monthly - Low volatility technical specs
  MONTHLY: {
    interval: 30 * 24 * 60 * 60 * 1000, // 30 days in milliseconds
    metrics: ['maxTPS', 'blockSize', 'finalityTime', 'validatorCount', 'stakedSupply'],
    priority: 4,
    description: 'Technical specifications that rarely change'
  }
} as const;

export interface HeatmapCoinData {
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  change7d: number;
  marketCap: number;
  volume24h: number;
  rank: number;
}

export interface TradingCoinData {
  symbol: string;
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  lastUpdated: number;
}

// ============================================================================
// COIN MAPPING - BASE COINS FROM SUPPORTED PAIRS
// ============================================================================

export const BASE_COINS_MAPPING = {
  'BTCUSDT': { id: 'bitcoin', symbol: 'BTC', name: 'Bitcoin' },
  'ETHUSDT': { id: 'ethereum', symbol: 'ETH', name: 'Ethereum' },
  'SOLUSDT': { id: 'solana', symbol: 'SOL', name: 'Solana' },
  'ADAUSDT': { id: 'cardano', symbol: 'ADA', name: 'Cardano' },
  'XRPUSDT': { id: 'ripple', symbol: 'XRP', name: 'XRP' },
  'SUIUSDT': { id: 'sui', symbol: 'SUI', name: 'Sui' },
  'SEIUSDT': { id: 'sei-network', symbol: 'SEI', name: 'Sei' },
  'RAYUSDT': { id: 'raydium', symbol: 'RAY', name: 'Raydium' },
  'JUPUSDT': { id: 'jupiter-exchange-solana', symbol: 'JUP', name: 'Jupiter' },
  'BNBUSDT': { id: 'binancecoin', symbol: 'BNB', name: 'BNB' },
  'PYTHUSDT': { id: 'pyth-network', symbol: 'PYTH', name: 'Pyth Network' },
  'NATIXUSDT': { id: 'natix-network', symbol: 'NATIX', name: 'Natix Network' },
  'RENDERUSDT': { id: 'render-token', symbol: 'RENDER', name: 'Render' },
  'ZEUSUSDT': { id: 'zeus-network', symbol: 'ZEUS', name: 'Zeus Network' },
  'APEUSDT': { id: 'apecoin', symbol: 'APE', name: 'ApeCoin' },
  'BONKUSDT': { id: 'bonk', symbol: 'BONK', name: 'Bonk' },
  'DOGEUSDT': { id: 'dogecoin', symbol: 'DOGE', name: 'Dogecoin' },
  'FLOKIUSDT': { id: 'floki', symbol: 'FLOKI', name: 'FLOKI' },
  'PENGUUSDT': { id: 'pudgy-penguins', symbol: 'PENGU', name: 'Pudgy Penguins' },
  'PEPEUSDT': { id: 'pepe', symbol: 'PEPE', name: 'Pepe' },
  'SHIBUSDT': { id: 'shiba-inu', symbol: 'SHIB', name: 'Shiba Inu' },
  'WIFUSDT': { id: 'dogwifcoin', symbol: 'WIF', name: 'dogwifhat' },
} as const;

export const BASE_COINS = Object.values(BASE_COINS_MAPPING);

// Utility functions for update scheduling
export function needsUpdate(lastUpdated: number | undefined, interval: number): boolean {
  if (!lastUpdated) return true;
  return Date.now() - lastUpdated > interval;
}

export function getMetricsToUpdate(coinData: CentralizedCoinData): {
  realTime: boolean;
  daily: boolean;
  weekly: boolean;
  monthly: boolean;
} {
  return {
    realTime: needsUpdate(coinData.priceLastUpdated, UPDATE_SCHEDULE.REAL_TIME.interval),
    daily: needsUpdate(coinData.metricsLastUpdated, UPDATE_SCHEDULE.DAILY.interval),
    weekly: needsUpdate(coinData.developmentLastUpdated, UPDATE_SCHEDULE.WEEKLY.interval),
    monthly: needsUpdate(coinData.technicalLastUpdated, UPDATE_SCHEDULE.MONTHLY.interval)
  };
}

export function getUpdatePriority(): Array<keyof typeof UPDATE_SCHEDULE> {
  return ['REAL_TIME', 'DAILY', 'WEEKLY', 'MONTHLY'];
}

// Data source configuration for different update frequencies
export const DATA_SOURCES = {
  REAL_TIME: {
    sources: ['CoinGecko API', 'CoinMarketCap'],
    rateLimit: '100 requests/minute',
    fallback: 'Use cached data if API fails'
  },
  DAILY: {
    sources: ['DeFiLlama', 'Blockchain Explorers'],
    rateLimit: '50 requests/hour',
    fallback: 'Use previous day data'
  },
  WEEKLY: {
    sources: ['GitHub API', 'CoinGecko'],
    rateLimit: '20 requests/hour',
    fallback: 'Use previous week data'
  },
  MONTHLY: {
    sources: ['Official Documentation', 'Blockchain Specs'],
    rateLimit: '10 requests/day',
    fallback: 'Use static configuration'
  }
} as const;

// ============================================================================
// STATIC FALLBACK DATA
// ============================================================================

const STATIC_FALLBACK_DATA: CentralizedCoinData[] = [
  {
    symbol: 'BTC',
    name: 'Bitcoin',
    price: 117433.46,
    change24h: 1.35,
    change7d: 2.32,
    marketCap: 2340000000000,
    volume24h: 74180000000,
    rank: 1,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics from scraped data
    tvl: 6900000000,
    activeAddresses24h: 738717,
    newAddresses24h: 45000,
    transactions24h: 350000,
    chainFees24h: 475186,
    dexVolume24h: 2190000,
    gasPrice: 0.00001,
    currentTPS: 7,
    averageFee: 1.35,
    maxTPS: 7,
    blockSize: 1,
    finalityTime: 600,
    validatorCount: 15000,
    activeDevelopers: 850,
    commitActivity: 1200,
    stakedSupply: 0,
  },
  {
    symbol: 'ETH',
    name: 'Ethereum',
    price: 3647.14,
    change24h: 0.22,
    change7d: 1.99,
    marketCap: 440250000000,
    volume24h: 41980000000,
    rank: 2,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics from scraped data
    tvl: 81767000000,
    activeAddresses24h: 505750,
    newAddresses24h: 64793,
    transactions24h: 1650000,
    chainFees24h: 1270000,
    dexVolume24h: 3305000000,
    gasPrice: 0.283,
    currentTPS: 17.3,
    averageFee: 0.77,
    maxTPS: 15,
    blockSize: 0.1,
    finalityTime: 12,
    validatorCount: 1000000,
    activeDevelopers: 1176,
    commitActivity: 16162,
    stakedSupply: 34000000,
  },
  {
    symbol: 'XRP',
    name: 'XRP',
    price: 3.11,
    change24h: 3.05,
    change7d: 14.17,
    marketCap: 184460000000,
    volume24h: 12050000000,
    rank: 3,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics (estimated for XRP Ledger)
    tvl: 2500000000,
    activeAddresses24h: 45000,
    newAddresses24h: 3500,
    transactions24h: 1200000,
    chainFees24h: 12000,
    dexVolume24h: 850000000,
    gasPrice: 0.00001,
    currentTPS: 1500,
    averageFee: 0.00001,
    maxTPS: 1500,
    blockSize: 0.002,
    finalityTime: 4,
    validatorCount: 150,
    activeDevelopers: 180,
    commitActivity: 950,
    stakedSupply: 0,
  },
  {
    symbol: 'BNB',
    name: 'BNB',
    price: 764.95,
    change24h: 2.26,
    change7d: 4.74,
    marketCap: 106550000000,
    volume24h: 2830000000,
    rank: 5,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics from scraped data (BSC)
    tvl: 6818000000,
    activeAddresses24h: 2280000,
    newAddresses24h: 120000,
    transactions24h: 12960000,
    chainFees24h: 458499,
    dexVolume24h: 5910000000,
    gasPrice: 0.000000003,
    currentTPS: 150,
    averageFee: 0.035,
    maxTPS: 2000,
    blockSize: 0.04,
    finalityTime: 3,
    validatorCount: 21,
    activeDevelopers: 450,
    commitActivity: 2800,
    stakedSupply: 160000000,
  },
  {
    symbol: 'SOL',
    name: 'Solana',
    price: 181.70,
    change24h: 4.63,
    change7d: 0.97,
    marketCap: 97810000000,
    volume24h: 8960000000,
    rank: 6,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics from scraped data
    tvl: 9945000000,
    activeAddresses24h: 3710000,
    newAddresses24h: 180000,
    transactions24h: 92800000,
    chainFees24h: 1630000,
    dexVolume24h: 3513000000,
    gasPrice: 0.00025,
    currentTPS: 1074,
    averageFee: 0.00025,
    maxTPS: 65000,
    blockSize: 0.4,
    finalityTime: 0.4,
    validatorCount: 1900,
    activeDevelopers: 2500,
    commitActivity: 8500,
    stakedSupply: 380000000,
  },
  {
    symbol: 'DOGE',
    name: 'Dogecoin',
    price: 0.2271,
    change24h: 6.90,
    change7d: 1.55,
    marketCap: 34120000000,
    volume24h: 4230000000,
    rank: 8,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics (estimated for Dogecoin)
    tvl: 150000000,
    activeAddresses24h: 125000,
    newAddresses24h: 8500,
    transactions24h: 45000,
    chainFees24h: 2500,
    dexVolume24h: 85000000,
    gasPrice: 0.001,
    currentTPS: 33,
    averageFee: 0.055,
    maxTPS: 40,
    blockSize: 1,
    finalityTime: 60,
    validatorCount: 1200,
    activeDevelopers: 45,
    commitActivity: 180,
    stakedSupply: 0,
  },
  {
    symbol: 'ADA',
    name: 'Cardano',
    price: 0.8007,
    change24h: 2.54,
    change7d: 6.21,
    marketCap: 28350000000,
    volume24h: 2090000000,
    rank: 10,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics (estimated for Cardano)
    tvl: 450000000,
    activeAddresses24h: 85000,
    newAddresses24h: 4200,
    transactions24h: 95000,
    chainFees24h: 8500,
    dexVolume24h: 125000000,
    gasPrice: 0.17,
    currentTPS: 250,
    averageFee: 0.17,
    maxTPS: 1000,
    blockSize: 0.09,
    finalityTime: 20,
    validatorCount: 3200,
    activeDevelopers: 420,
    commitActivity: 2100,
    stakedSupply: 24000000000,
  },
  {
    symbol: 'SHIB',
    name: 'Shiba Inu',
    price: 0.00001340,
    change24h: 4.18,
    change7d: 3.50,
    marketCap: 7890000000,
    volume24h: 387710000,
    rank: 20,
    lastUpdated: Date.now(),
    // Token metrics (SHIB is ERC-20 on Ethereum)
    tvl: 85000000,
    activeAddresses24h: 15000,
    newAddresses24h: 850,
    transactions24h: 8500,
    chainFees24h: 2500,
    dexVolume24h: 45000000,
    gasPrice: 0.283, // Uses Ethereum gas
    currentTPS: 17.3, // Uses Ethereum TPS
    averageFee: 0.77, // Uses Ethereum fees
    maxTPS: 15,
    blockSize: 0.1,
    finalityTime: 12,
    validatorCount: 1000000,
    activeDevelopers: 25,
    commitActivity: 120,
    stakedSupply: 0,
  },
  {
    symbol: 'PEPE',
    name: 'Pepe',
    price: 0.00001201,
    change24h: 6.63,
    change7d: 8.45,
    marketCap: 5055000000,
    volume24h: 1192000000,
    rank: 28,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'BONK',
    name: 'Bonk',
    price: 0.00003230,
    change24h: 5.24,
    change7d: 5.68,
    marketCap: 2602000000,
    volume24h: 1465000000,
    rank: 40,
    lastUpdated: Date.now(),
    // Token metrics (BONK is SPL token on Solana)
    tvl: 125000000,
    activeAddresses24h: 85000,
    newAddresses24h: 4500,
    transactions24h: 450000,
    chainFees24h: 1630000, // Uses Solana fees
    dexVolume24h: 285000000,
    gasPrice: 0.00025, // Uses Solana gas
    currentTPS: 1074, // Uses Solana TPS
    averageFee: 0.00025, // Uses Solana fees
    maxTPS: 65000,
    blockSize: 0.4,
    finalityTime: 0.4,
    validatorCount: 1900,
    activeDevelopers: 35,
    commitActivity: 180,
    stakedSupply: 0,
  },
  {
    symbol: 'SUI',
    name: 'Sui',
    price: 3.66,
    change24h: 2.89,
    change7d: 5.12,
    marketCap: 11200000000,
    volume24h: 1850000000,
    rank: 15,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics (estimated for Sui)
    tvl: 1800000000,
    activeAddresses24h: 450000,
    newAddresses24h: 25000,
    transactions24h: 15000000,
    chainFees24h: 85000,
    dexVolume24h: 650000000,
    gasPrice: 0.0001,
    currentTPS: 297,
    averageFee: 0.006,
    maxTPS: 120000,
    blockSize: 0.02,
    finalityTime: 2.5,
    validatorCount: 150,
    activeDevelopers: 320,
    commitActivity: 1800,
    stakedSupply: 1800000000,
  },
  {
    symbol: 'SEI',
    name: 'Sei',
    price: 0.31,
    change24h: 1.45,
    change7d: 3.21,
    marketCap: 1200000000,
    volume24h: 180000000,
    rank: 85,
    lastUpdated: Date.now(),
    // Comprehensive blockchain metrics (estimated for Sei)
    tvl: 180000000,
    activeAddresses24h: 85000,
    newAddresses24h: 4500,
    transactions24h: 2800000,
    chainFees24h: 12000,
    dexVolume24h: 95000000,
    gasPrice: 0.0001,
    currentTPS: 32,
    averageFee: 0.004,
    maxTPS: 20000,
    blockSize: 0.01,
    finalityTime: 0.6,
    validatorCount: 100,
    activeDevelopers: 85,
    commitActivity: 650,
    stakedSupply: 850000000,
  },
  {
    symbol: 'RAY',
    name: 'Raydium',
    price: 2.97,
    change24h: 3.12,
    change7d: 7.89,
    marketCap: 890000000,
    volume24h: 125000000,
    rank: 95,
    lastUpdated: Date.now(),
    // Token metrics (RAY is SPL token on Solana - DEX protocol)
    tvl: 850000000,
    activeAddresses24h: 25000,
    newAddresses24h: 1200,
    transactions24h: 185000,
    chainFees24h: 1630000, // Uses Solana fees
    dexVolume24h: 1200000000, // High DEX volume for Raydium
    gasPrice: 0.00025, // Uses Solana gas
    currentTPS: 1074, // Uses Solana TPS
    averageFee: 0.00025, // Uses Solana fees
    maxTPS: 65000,
    blockSize: 0.4,
    finalityTime: 0.4,
    validatorCount: 1900,
    activeDevelopers: 45,
    commitActivity: 320,
    stakedSupply: 0,
  },
  {
    symbol: 'JUP',
    name: 'Jupiter',
    price: 0.53,
    change24h: 2.34,
    change7d: 4.56,
    marketCap: 750000000,
    volume24h: 95000000,
    rank: 110,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'PYTH',
    name: 'Pyth Network',
    price: 0.12,
    change24h: 1.89,
    change7d: 2.45,
    marketCap: 420000000,
    volume24h: 45000000,
    rank: 150,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'NATIX',
    name: 'Natix Network',
    price: 0.085,
    change24h: 0.95,
    change7d: 1.23,
    marketCap: 85000000,
    volume24h: 8500000,
    rank: 350,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'RENDER',
    name: 'Render',
    price: 4.00,
    change24h: 7.69,
    change7d: 12.34,
    marketCap: 2100000000,
    volume24h: 285000000,
    rank: 45,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'ZEUS',
    name: 'Zeus Network',
    price: 0.45,
    change24h: 3.45,
    change7d: 8.90,
    marketCap: 180000000,
    volume24h: 25000000,
    rank: 280,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'APE',
    name: 'ApeCoin',
    price: 1.12,
    change24h: 2.67,
    change7d: 5.43,
    marketCap: 420000000,
    volume24h: 65000000,
    rank: 145,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'FLOKI',
    name: 'FLOKI',
    price: 0.00013351,
    change24h: 11.22,
    change7d: 15.67,
    marketCap: 1280000000,
    volume24h: 195000000,
    rank: 75,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'PENGU',
    name: 'Pudgy Penguins',
    price: 0.04,
    change24h: 14.30,
    change7d: 25.45,
    marketCap: 2800000000,
    volume24h: 450000000,
    rank: 35,
    lastUpdated: Date.now(),
  },
  {
    symbol: 'WIF',
    name: 'dogwifhat',
    price: 1.02,
    change24h: 8.45,
    change7d: 12.89,
    marketCap: 1020000000,
    volume24h: 165000000,
    rank: 90,
    lastUpdated: Date.now(),
  },
];

// ============================================================================
// CACHE MANAGEMENT
// ============================================================================

interface CacheEntry {
  data: CentralizedCoinData[];
  timestamp: number;
  source: 'api' | 'static' | 'hybrid';
}

class MockDataCache {
  private cache: Map<string, CacheEntry> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  set(key: string, data: CentralizedCoinData[], source: 'api' | 'static' | 'hybrid'): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      source,
    });
  }

  get(key: string): CacheEntry | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // Check if cache is still valid
    if (Date.now() - cached.timestamp > this.CACHE_TTL) {
      this.cache.delete(key);
      return null;
    }

    return cached;
  }

  clear(): void {
    this.cache.clear();
  }

  isExpired(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return true;
    return Date.now() - cached.timestamp > this.CACHE_TTL;
  }
}

const cache = new MockDataCache();

// ============================================================================
// CENTRALIZED MOCK DATA SERVICE
// ============================================================================

export class CentralizedMockDataService {
  private static instance: CentralizedMockDataService;

  static getInstance(): CentralizedMockDataService {
    if (!CentralizedMockDataService.instance) {
      CentralizedMockDataService.instance = new CentralizedMockDataService();
    }
    return CentralizedMockDataService.instance;
  }



  /**
   * Get comprehensive market data for all BASE coins with smart update strategy
   * Priority: Static data first -> Check if stale -> Update if needed
   */
  async getAllCoinsData(): Promise<CentralizedCoinData[]> {
    console.log('🔍 Getting coins data with smart update strategy...');

    // Start with static data as the base
    let coinsData = this.getStaticDataWithTimestamps();

    // Check each coin to see what needs updating
    const updatedCoinsData = await Promise.all(
      coinsData.map(async (coin) => {
        return await this.updateCoinIfStale(coin);
      })
    );

    // Cache the final result
    const cacheKey = 'all-coins-data';
    cache.set(cacheKey, updatedCoinsData, 'hybrid');

    return updatedCoinsData;
  }

  /**
   * Get static data with proper timestamps for update checking
   */
  private getStaticDataWithTimestamps(): CentralizedCoinData[] {
    return STATIC_FALLBACK_DATA.map(coin => ({
      ...coin,
      // Initialize timestamps if not present
      priceLastUpdated: coin.priceLastUpdated || 0,
      metricsLastUpdated: coin.metricsLastUpdated || 0,
      developmentLastUpdated: coin.developmentLastUpdated || 0,
      technicalLastUpdated: coin.technicalLastUpdated || 0,
      lastUpdated: coin.lastUpdated || Date.now(),
    }));
  }

  /**
   * Check if a coin's data is stale and update if necessary
   */
  private async updateCoinIfStale(coin: CentralizedCoinData): Promise<CentralizedCoinData> {
    const updateNeeds = getMetricsToUpdate(coin);
    let updatedCoin = { ...coin };

    // Check if any updates are needed
    const needsAnyUpdate = updateNeeds.realTime || updateNeeds.daily || updateNeeds.weekly || updateNeeds.monthly;

    if (!needsAnyUpdate) {
      console.log(`✅ ${coin.symbol}: All data is fresh`);
      return updatedCoin;
    }

    console.log(`� ${coin.symbol}: Updating stale data...`, updateNeeds);

    // Update real-time data if stale
    if (updateNeeds.realTime) {
      updatedCoin = await this.updateRealTimeData(updatedCoin);
    }

    // Update daily metrics if stale
    if (updateNeeds.daily) {
      updatedCoin = await this.updateDailyMetrics(updatedCoin);
    }

    // Update weekly metrics if stale
    if (updateNeeds.weekly) {
      updatedCoin = await this.updateWeeklyMetrics(updatedCoin);
    }

    // Update monthly metrics if stale
    if (updateNeeds.monthly) {
      updatedCoin = await this.updateMonthlyMetrics(updatedCoin);
    }

    return updatedCoin;
  }

  /**
   * Update real-time data for a coin (price, gas, TPS)
   */
  private async updateRealTimeData(coin: CentralizedCoinData): Promise<CentralizedCoinData> {
    try {
      console.log(`🔄 ${coin.symbol}: Updating real-time data...`);

      // Try to fetch real price data from CoinGecko
      const coinMapping = BASE_COINS.find(c => c.symbol === coin.symbol);
      if (coinMapping && FEATURE_FLAGS.FALLBACK_TO_MOCK_DATA) {
        const response = await fetch(
          `https://api.coingecko.com/api/v3/simple/price?ids=${coinMapping.id}&vs_currencies=usd&include_24hr_change=true&include_7d_change=true`
        );

        if (response.ok) {
          const data = await response.json();
          const coinData = data[coinMapping.id];

          if (coinData) {
            return {
              ...coin,
              price: coinData.usd || coin.price,
              change24h: coinData.usd_24h_change || coin.change24h,
              change7d: coinData.usd_7d_change || coin.change7d,
              priceLastUpdated: Date.now(),
              lastUpdated: Date.now(),
            };
          }
        }
      }

      // Fallback: Add small realistic variations to existing data
      return {
        ...coin,
        price: coin.price * (1 + (Math.random() - 0.5) * 0.02), // ±1% variation
        change24h: coin.change24h + (Math.random() - 0.5) * 2, // ±1% variation
        change7d: coin.change7d + (Math.random() - 0.5) * 4, // ±2% variation
        priceLastUpdated: Date.now(),
        lastUpdated: Date.now(),
      };
    } catch (error) {
      console.warn(`⚠️ ${coin.symbol}: Real-time update failed, using existing data:`, error);
      return {
        ...coin,
        priceLastUpdated: Date.now(),
        lastUpdated: Date.now(),
      };
    }
  }

  /**
   * Update daily metrics for a coin (volumes, addresses, transactions)
   */
  private async updateDailyMetrics(coin: CentralizedCoinData): Promise<CentralizedCoinData> {
    try {
      console.log(`📊 ${coin.symbol}: Updating daily metrics...`);

      // For now, add realistic variations to existing data
      // In a real implementation, this would fetch from DeFiLlama, blockchain explorers, etc.
      return {
        ...coin,
        volume24h: coin.volume24h * (1 + (Math.random() - 0.5) * 0.1), // ±5% variation
        activeAddresses24h: coin.activeAddresses24h ? Math.floor(coin.activeAddresses24h * (1 + (Math.random() - 0.5) * 0.05)) : undefined,
        newAddresses24h: coin.newAddresses24h ? Math.floor(coin.newAddresses24h * (1 + (Math.random() - 0.5) * 0.1)) : undefined,
        transactions24h: coin.transactions24h ? Math.floor(coin.transactions24h * (1 + (Math.random() - 0.5) * 0.08)) : undefined,
        chainFees24h: coin.chainFees24h ? coin.chainFees24h * (1 + (Math.random() - 0.5) * 0.15) : undefined,
        dexVolume24h: coin.dexVolume24h ? coin.dexVolume24h * (1 + (Math.random() - 0.5) * 0.2) : undefined,
        tvl: coin.tvl ? coin.tvl * (1 + (Math.random() - 0.5) * 0.03) : undefined,
        metricsLastUpdated: Date.now(),
        lastUpdated: Date.now(),
      };
    } catch (error) {
      console.warn(`⚠️ ${coin.symbol}: Daily metrics update failed, using existing data:`, error);
      return {
        ...coin,
        metricsLastUpdated: Date.now(),
        lastUpdated: Date.now(),
      };
    }
  }

  /**
   * Update weekly metrics for a coin (development activity, rankings)
   */
  private async updateWeeklyMetrics(coin: CentralizedCoinData): Promise<CentralizedCoinData> {
    try {
      console.log(`📈 ${coin.symbol}: Updating weekly metrics...`);

      // For now, add small variations to existing data
      // In a real implementation, this would fetch from GitHub API, etc.
      return {
        ...coin,
        averageFee: coin.averageFee ? coin.averageFee * (1 + (Math.random() - 0.5) * 0.05) : undefined,
        activeDevelopers: coin.activeDevelopers ? Math.floor(coin.activeDevelopers * (1 + (Math.random() - 0.5) * 0.02)) : undefined,
        commitActivity: coin.commitActivity ? Math.floor(coin.commitActivity * (1 + (Math.random() - 0.5) * 0.1)) : undefined,
        developmentLastUpdated: Date.now(),
        lastUpdated: Date.now(),
      };
    } catch (error) {
      console.warn(`⚠️ ${coin.symbol}: Weekly metrics update failed, using existing data:`, error);
      return {
        ...coin,
        developmentLastUpdated: Date.now(),
        lastUpdated: Date.now(),
      };
    }
  }

  /**
   * Update monthly metrics for a coin (technical specifications)
   */
  private async updateMonthlyMetrics(coin: CentralizedCoinData): Promise<CentralizedCoinData> {
    try {
      console.log(`🔧 ${coin.symbol}: Updating monthly metrics...`);

      // Technical specs rarely change, so mostly keep existing data
      // In a real implementation, this would check official documentation, etc.
      return {
        ...coin,
        validatorCount: coin.validatorCount ? Math.floor(coin.validatorCount * (1 + (Math.random() - 0.5) * 0.01)) : undefined,
        stakedSupply: coin.stakedSupply ? coin.stakedSupply * (1 + (Math.random() - 0.5) * 0.005) : undefined,
        technicalLastUpdated: Date.now(),
        lastUpdated: Date.now(),
      };
    } catch (error) {
      console.warn(`⚠️ ${coin.symbol}: Monthly metrics update failed, using existing data:`, error);
      return {
        ...coin,
        technicalLastUpdated: Date.now(),
        lastUpdated: Date.now(),
      };
    }
  }



  /**
   * Get data for a specific coin by symbol
   */
  async getCoinData(symbol: string): Promise<CentralizedCoinData | null> {
    const allData = await this.getAllCoinsData();
    return allData.find(coin => coin.symbol.toLowerCase() === symbol.toLowerCase()) || null;
  }

  /**
   * Get data formatted for heatmap component
   */
  async getHeatmapData(): Promise<HeatmapCoinData[]> {
    const allData = await this.getAllCoinsData();
    return allData.map(coin => ({
      symbol: coin.symbol,
      name: coin.name,
      price: coin.price,
      change24h: coin.change24h,
      change7d: coin.change7d,
      marketCap: coin.marketCap,
      volume24h: coin.volume24h,
      rank: coin.rank,
    }));
  }

  /**
   * Get data formatted for trading components
   */
  async getTradingData(): Promise<TradingCoinData[]> {
    const allData = await this.getAllCoinsData();
    return allData.map(coin => ({
      symbol: coin.symbol,
      price: coin.price,
      change24h: coin.change24h,
      volume24h: coin.volume24h,
      marketCap: coin.marketCap,
      lastUpdated: coin.lastUpdated,
    }));
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    cache.clear();
    console.log('🗑️ Mock data cache cleared');
  }

  /**
   * Get cache status and statistics
   */
  getCacheStatus(): { hasCache: boolean; isExpired: boolean; source?: string; lastUpdated?: number } {
    const cached = cache.get('all-coins-data');
    if (!cached) {
      return { hasCache: false, isExpired: true };
    }

    return {
      hasCache: true,
      isExpired: cache.isExpired('all-coins-data'),
      source: cached.source,
      lastUpdated: cached.timestamp,
    };
  }
}

// ============================================================================
// SINGLETON INSTANCE AND CONVENIENCE FUNCTIONS
// ============================================================================

export const mockDataService = CentralizedMockDataService.getInstance();

// Convenience functions for easy importing
export const getAllCoinsData = () => mockDataService.getAllCoinsData();
export const getCoinData = (symbol: string) => mockDataService.getCoinData(symbol);
export const getHeatmapData = () => mockDataService.getHeatmapData();
export const getTradingData = () => mockDataService.getTradingData();
export const clearMockDataCache = () => mockDataService.clearCache();
export const getMockDataCacheStatus = () => mockDataService.getCacheStatus();
