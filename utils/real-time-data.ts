"use client";

import { useEffect, useRef, useState } from 'react';
import { clientAPIService, type MarketDataResponse, type PricesResponse } from './client-api-service';
import { CRYPTO_SYMBOLS, FEATURE_FLAGS } from './api-config';
import { cryptoWebSocketService, type PriceUpdate, type WebSocketMessage } from './websocket-service';

// Types for real-time data
export interface MarketData {
  symbol: string;
  price: number;
  change24h: number;
  change7d: number;
  volume24h: number;
  marketCap: number;
  lastUpdated: number;
}

export interface ChartDataPoint {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// Real-time data service class
class RealTimeDataService {
  private subscribers: Map<string, Set<(data: any) => void>> = new Map();
  private intervals: Map<string, NodeJS.Timeout> = new Map();
  private cache: Map<string, any> = new Map();
  private webSocketPrices: Map<string, PriceUpdate> = new Map();

  constructor() {
    // Set up WebSocket event handler
    if (FEATURE_FLAGS.ENABLE_WEBSOCKET_CONNECTIONS) {
      cryptoWebSocketService.addEventHandler(this.handleWebSocketMessage.bind(this));
    }
  }

  // Handle WebSocket messages
  private handleWebSocketMessage(message: WebSocketMessage): void {
    if (message.type === 'price_update') {
      const priceUpdate = message.data as PriceUpdate;
      this.webSocketPrices.set(priceUpdate.symbol, priceUpdate);

      // Notify market data subscribers with real-time updates
      this.notifySubscribers('market-data', this.getEnhancedMarketData());
      this.notifySubscribers('portfolio-prices', this.getWebSocketPrices());
    }
  }

  // Get enhanced market data combining API and WebSocket data
  private getEnhancedMarketData(): MarketData[] {
    const cachedData = this.cache.get('market-data') || [];

    // Enhance with WebSocket data if available
    return cachedData.map((item: MarketData) => {
      const wsUpdate = this.webSocketPrices.get(item.symbol);
      if (wsUpdate) {
        return {
          ...item,
          price: wsUpdate.price,
          change24h: wsUpdate.change24h,
          volume24h: wsUpdate.volume24h,
          lastUpdated: wsUpdate.timestamp,
        };
      }
      return item;
    });
  }

  // Get WebSocket prices for portfolio
  private getWebSocketPrices(): Record<string, number> {
    const prices: Record<string, number> = {};
    this.webSocketPrices.forEach((update, symbol) => {
      prices[symbol] = update.price;
    });
    return prices;
  }

  // Enhance market data with WebSocket updates
  private enhanceDataWithWebSocket(marketData: MarketData[]): MarketData[] {
    return marketData.map(item => {
      const wsUpdate = this.webSocketPrices.get(item.symbol);
      if (wsUpdate) {
        return {
          ...item,
          price: wsUpdate.price,
          change24h: wsUpdate.change24h,
          volume24h: wsUpdate.volume24h,
          lastUpdated: wsUpdate.timestamp,
        };
      }
      return item;
    });
  }

  // Subscribe to real-time updates for a specific data type
  subscribe(dataType: string, callback: (data: any) => void): () => void {
    if (!this.subscribers.has(dataType)) {
      this.subscribers.set(dataType, new Set());
      this.startUpdates(dataType);
    }

    this.subscribers.get(dataType)!.add(callback);

    // Return unsubscribe function
    return () => {
      const subs = this.subscribers.get(dataType);
      if (subs) {
        subs.delete(callback);
        if (subs.size === 0) {
          this.stopUpdates(dataType);
          this.subscribers.delete(dataType);
        }
      }
    };
  }

  private startUpdates(dataType: string) {
    // Set different update intervals based on data type
    const intervals = {
      'market-data': 30000,    // 30 seconds for market data (WebSocket provides real-time updates)
      'chart-data': 10000,     // 10 seconds for chart data
      'heatmap-data': 15000,   // 15 seconds for heatmap data
      'portfolio-prices': 60000, // 60 seconds for portfolio prices (WebSocket provides real-time updates)
    };

    const interval = intervals[dataType as keyof typeof intervals] || 10000;

    // Subscribe to WebSocket for real-time price updates
    if ((dataType === 'market-data' || dataType === 'portfolio-prices') && FEATURE_FLAGS.ENABLE_WEBSOCKET_CONNECTIONS) {
      const symbols = ['BTC', 'ETH', 'SOL', 'ADA', 'DOT', 'LINK', 'AVAX', 'MATIC', 'UNI', 'ATOM'];
      cryptoWebSocketService.subscribe(symbols);
    }

    const updateInterval = setInterval(() => {
      this.fetchAndNotify(dataType);
    }, interval);

    this.intervals.set(dataType, updateInterval);

    // Initial fetch
    this.fetchAndNotify(dataType);
  }

  private stopUpdates(dataType: string) {
    const interval = this.intervals.get(dataType);
    if (interval) {
      clearInterval(interval);
      this.intervals.delete(dataType);
    }
  }

  // Notify subscribers with data
  private notifySubscribers(dataType: string, data: any): void {
    const subscribers = this.subscribers.get(dataType);
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in subscriber callback for ${dataType}:`, error);
        }
      });
    }
  }

  private async fetchAndNotify(dataType: string) {
    try {
      let data;

      switch (dataType) {
        case 'market-data':
          data = await this.fetchMarketData();
          // Enhance with WebSocket data if available
          if (FEATURE_FLAGS.ENABLE_WEBSOCKET_CONNECTIONS && this.webSocketPrices.size > 0) {
            data = this.enhanceDataWithWebSocket(data);
          }
          break;
        case 'chart-data':
          data = await this.fetchChartData();
          break;
        case 'heatmap-data':
          data = await this.fetchHeatmapData();
          break;
        case 'portfolio-prices':
          data = await this.fetchPortfolioPrices();
          // Enhance with WebSocket data if available
          if (FEATURE_FLAGS.ENABLE_WEBSOCKET_CONNECTIONS && this.webSocketPrices.size > 0) {
            data = { ...data, ...this.getWebSocketPrices() };
          }
          break;
        default:
          return;
      }

      // Cache the data
      this.cache.set(dataType, data);

      // Notify all subscribers
      this.notifySubscribers(dataType, data);
    } catch (error) {
      console.error(`Failed to fetch ${dataType}:`, error);
    }
  }

  private async fetchMarketData(): Promise<MarketData[]> {
    try {
      // Use client API service (calls our Next.js API routes)
      console.log('🔄 Fetching market data via client API service...');
      // Use BASE coins for market data
      const { BASE_COINS } = await import('./mock-data-service');
      const baseSymbols = BASE_COINS.map(coin => coin.symbol);

      const response: MarketDataResponse = await clientAPIService.getMarketData(
        baseSymbols, // Only BASE coins
        50 // Limit
      );

      console.log(`✅ Fetched ${response.data.length} coins from [${response.sources?.join(', ') || response.source}]`);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch market data from client API service:', error);

      // Final fallback to centralized mock data
      try {
        const { getTradingData } = await import('./mock-data-service');
        const centralizedData = await getTradingData();
        return centralizedData.map(coin => ({
          symbol: coin.symbol,
          price: coin.price,
          change24h: coin.change24h,
          change7d: Math.random() * 40 - 20, // 7d change not in trading format
          volume24h: coin.volume24h,
          marketCap: coin.marketCap,
          lastUpdated: coin.lastUpdated,
        }));
      } catch (mockError) {
        console.warn('⚠️ Centralized mock service also failed, using BASE coins emergency fallback:', mockError);

        // Emergency fallback - only use BASE coins
        const { BASE_COINS } = await import('./mock-data-service');
        return BASE_COINS.map(coin => ({
          symbol: coin.symbol,
          price: Math.random() * 50000 + 1000,
          change24h: (Math.random() - 0.5) * 20,
          change7d: (Math.random() - 0.5) * 40,
          volume24h: Math.random() * 1000000000,
          marketCap: Math.random() * 100000000000,
          lastUpdated: Date.now(),
        }));
      }
    }
  }

  private async fetchChartData(): Promise<ChartDataPoint[]> {
    // Generate mock OHLCV data for charts
    const data: ChartDataPoint[] = [];
    const now = Date.now();
    const basePrice = 40000;

    for (let i = 99; i >= 0; i--) {
      const time = now - (i * 60000); // 1-minute intervals
      const open = basePrice + (Math.random() - 0.5) * 1000;
      const close = open + (Math.random() - 0.5) * 500;
      const high = Math.max(open, close) + Math.random() * 200;
      const low = Math.min(open, close) - Math.random() * 200;
      const volume = Math.random() * 1000000;

      data.push({ time, open, high, low, close, volume });
    }

    return data;
  }

  private async fetchHeatmapData(): Promise<any[]> {
    try {
      // Use client API service for heatmap (more comprehensive data)
      console.log('🔄 Fetching heatmap data via client API service...');
      const response: MarketDataResponse = await clientAPIService.getMarketData(
        undefined, // Get all available coins
        100 // More coins for heatmap
      );

      // Transform to heatmap format
      const heatmapData = response.data.map(coin => ({
        symbol: coin.symbol,
        change24h: coin.change24h || 0,
        volume: coin.volume24h,
        marketCap: coin.marketCap,
        price: coin.price,
        lastUpdated: coin.lastUpdated || Date.now(),
      }));

      console.log(`✅ Fetched ${heatmapData.length} coins for heatmap from [${response.sources?.join(', ') || response.source}]`);
      return heatmapData;
    } catch (error) {
      console.error('Failed to fetch heatmap data from client API service:', error);

      // Fallback to BASE coins mock data
      const { BASE_COINS } = await import('./mock-data-service');

      return BASE_COINS.map(coin => ({
        symbol: coin.symbol,
        change24h: (Math.random() - 0.5) * 20,
        volume: Math.random() * 1000000000,
        marketCap: Math.random() * 10000000000,
        price: Math.random() * 10000,
        lastUpdated: Date.now(),
      }));
    }
  }

  private async fetchPortfolioPrices(): Promise<Record<string, number>> {
    try {
      // Use BASE coins for portfolio prices (Binance primary)
      const { BASE_COINS } = await import('./mock-data-service');
      const symbols = BASE_COINS.map(coin => coin.symbol);
      console.log('🔄 Fetching portfolio prices via client API service...');

      const response: PricesResponse = await clientAPIService.getPrices(symbols);
      console.log(`✅ Fetched prices for ${response.count} portfolio symbols from [${response.sources?.join(', ') || response.source}]`);

      return response.prices;
    } catch (error) {
      console.error('Failed to fetch portfolio prices from client API service:', error);

      // Fallback to BASE coins mock data
      const { BASE_COINS } = await import('./mock-data-service');
      const prices: Record<string, number> = {};

      BASE_COINS.forEach(coin => {
        prices[coin.symbol] = Math.random() * 50000 + 100;
      });

      return prices;
    }
  }

  // Get cached data if available
  getCachedData(dataType: string) {
    return this.cache.get(dataType);
  }

  // Manual refresh
  async refresh(dataType: string) {
    await this.fetchAndNotify(dataType);
  }
}

// Global instance
const realTimeDataService = new RealTimeDataService();

// React hooks for using real-time data
export function useRealTimeData<T>(dataType: string, initialData?: T): [T | null, boolean, () => Promise<void>] {
  const [data, setData] = useState<T | null>(initialData || realTimeDataService.getCachedData(dataType) || null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const unsubscribe = realTimeDataService.subscribe(dataType, (newData: T) => {
      setData(newData);
      setIsLoading(false);
    });

    return unsubscribe;
  }, [dataType]);

  const refresh = async () => {
    setIsLoading(true);
    await realTimeDataService.refresh(dataType);
  };

  return [data, isLoading, refresh];
}

// Hook for market data specifically
export function useMarketData(): [MarketData[] | null, boolean, () => Promise<void>] {
  return useRealTimeData<MarketData[]>('market-data');
}

// Hook for chart data specifically
export function useChartData(): [ChartDataPoint[] | null, boolean, () => Promise<void>] {
  return useRealTimeData<ChartDataPoint[]>('chart-data');
}

// Hook for heatmap data specifically
export function useHeatmapData(): [any[] | null, boolean, () => Promise<void>] {
  return useRealTimeData<any[]>('heatmap-data');
}

// Hook for portfolio prices specifically
export function usePortfolioPrices(): [Record<string, number> | null, boolean, () => Promise<void>] {
  return useRealTimeData<Record<string, number>>('portfolio-prices');
}

// Connection status hook
export function useConnectionStatus(): boolean {
  const [isConnected, setIsConnected] = useState(true);

  useEffect(() => {
    const handleOnline = () => setIsConnected(true);
    const handleOffline = () => setIsConnected(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return isConnected;
}

export default realTimeDataService;