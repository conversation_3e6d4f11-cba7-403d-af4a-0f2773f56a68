// Utility to seed initial hashtags for the crypto platform
import { createClient } from '@/utils/supabase/client';

const CRYPTO_HASHTAGS = [
  'bitcoin', 'ethereum', 'defi', 'nft', 'web3', 'blockchain',
  'crypto', 'altcoins', 'dao', 'smart-contracts', 'yield-farming',
  'staking', 'trading', 'hodl', 'metaverse', 'solana', 'cardano',
  'polygon', 'layer2', 'dex', 'cefi', 'tokenomics', 'governance',
  'mining', 'validators', 'consensus', 'proof-of-stake', 'proof-of-work'
];

export async function seedHashtags() {
  const supabase = createClient();
  
  try {
    console.log('Checking and seeding hashtags...');
    
    // Check if hashtags already exist in the hashtags table (if it exists)
    const { data: existingTags, error: checkError } = await supabase
      .from('hashtags')
      .select('name')
      .limit(1);
    
    if (checkError && !checkError.message?.includes('does not exist')) {
      console.error('Error checking existing hashtags:', checkError);
      return;
    }
    
    // If hashtags table exists and is empty, seed it
    if (!checkError && (!existingTags || existingTags.length === 0)) {
      console.log('No hashtags found in hashtags table, seeding...');
      
      const hashtagsToInsert = CRYPTO_HASHTAGS.map((name, index) => ({
        name,
        usage_count: Math.floor(Math.random() * 50) + 1,
        trending_score: Math.floor(Math.random() * 100) + 1,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));
      
      const { data, error } = await supabase
        .from('hashtags')
        .insert(hashtagsToInsert)
        .select();
      
      if (error) {
        console.error('Error inserting hashtags:', error);
      } else {
        console.log(`Successfully seeded ${data?.length || 0} hashtags`);
      }
    }

    // Also seed article_hashtags table if it's empty
    await seedArticleHashtags();
    
  } catch (error) {
    console.error('Error in seedHashtags:', error);
  }
}

export async function seedArticleHashtags() {
  const supabase = createClient();
  
  try {
    console.log('Checking article_hashtags table...');
    
    // Check if article_hashtags table has any data
    const { data: existingData, error: checkError } = await supabase
      .from('article_hashtags')
      .select('id')
      .limit(1);
    
    if (checkError) {
      console.warn('article_hashtags table might not exist:', checkError);
      return;
    }
    
    if (!existingData || existingData.length === 0) {
      console.log('No data in article_hashtags table, seeding sample data...');
      
      // Get some articles to associate with hashtags
      const { data: articles, error: articlesError } = await supabase
        .from('articles')
        .select('id')
        .limit(10);
      
      if (articlesError || !articles || articles.length === 0) {
        console.log('No articles found to associate with hashtags');
        return;
      }
      
      // Create sample article_hashtags entries
      const sampleEntries = articles.map(article => {
        // Randomly select 2-5 hashtags for each article
        const numTags = Math.floor(Math.random() * 4) + 2;
        const selectedTags = CRYPTO_HASHTAGS
          .sort(() => 0.5 - Math.random())
          .slice(0, numTags);
        
        return {
          article_id: article.id,
          article_tags: selectedTags
        };
      });
      
      const { data, error } = await supabase
        .from('article_hashtags')
        .insert(sampleEntries)
        .select();
      
      if (error) {
        console.error('Error inserting article_hashtags:', error);
      } else {
        console.log(`Successfully seeded ${data?.length || 0} article_hashtags entries`);
      }
    } else {
      console.log('article_hashtags table already contains data');
    }
    
  } catch (error) {
    console.error('Error in seedArticleHashtags:', error);
  }
}

export async function getHashtagStats() {
  const supabase = createClient();
  
  try {
    // Try to get stats from article_hashtags table first
    const { data: hashtagData, error } = await supabase
      .from('article_hashtags')
      .select('article_tags');
    
    if (error) {
      console.error('Error fetching hashtag stats from article_hashtags:', error);
      return [];
    }
    
    // Count hashtag occurrences
    const hashtagCounts = new Map<string, number>();
    
    hashtagData?.forEach(row => {
      row.article_tags?.forEach((tag: string) => {
        const count = hashtagCounts.get(tag) || 0;
        hashtagCounts.set(tag, count + 1);
      });
    });

    // Convert to sorted array
    const stats = Array.from(hashtagCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([name, count]) => ({
        name,
        usage_count: count,
        trending_score: count * 10
      }));
    
    console.log('Current top hashtags from article_hashtags:', stats);
    return stats;
    
  } catch (error) {
    console.error('Error in getHashtagStats:', error);
    return [];
  }
}
