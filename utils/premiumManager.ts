import { createClient } from '@/utils/supabase/client';

// Manual types for premium features (will be generated after migration)
export interface SubscriptionPlan {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  price_monthly?: number;
  price_yearly?: number;
  currency: string;
  features: string[];
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  plan_id: string;
  status: 'active' | 'cancelled' | 'expired' | 'trial' | 'suspended';
  billing_cycle: 'monthly' | 'yearly' | 'lifetime';
  trial_start_date?: string;
  trial_end_date?: string;
  current_period_start: string;
  current_period_end?: string;
  cancelled_at?: string;
  cancel_at_period_end: boolean;
  external_subscription_id?: string;
  payment_method?: any;
  created_at: string;
  updated_at: string;
}

export interface PremiumFeature {
  id: string;
  feature_key: string;
  feature_name: string;
  description?: string;
  feature_type: 'boolean' | 'limit' | 'access';
  default_value: any;
  premium_value: any;
  category?: string;
  is_active: boolean;
  created_at: string;
}

export interface SubscriptionStatus {
  status: 'tier1' | 'active' | 'trial' | 'cancelled' | 'expired';
  plan_name: string;
  plan_display_name: string;
  billing_cycle?: string;
  features: string[];
  is_premium: boolean;
  current_period_end?: string;
  cancel_at_period_end?: boolean;
  trial_end_date?: string;
}

export interface PremiumFeatureCheck {
  hasFeature: boolean;
  value: any;
  source: 'tier1' | 'subscription' | 'override';
}

export class PremiumSubscriptionManager {
  private client = createClient();

  // Get all available subscription plans
  async getSubscriptionPlans(): Promise<SubscriptionPlan[]> {
    const { data, error } = await (this.client as any)
      .from('subscription_plans')
      .select('*')
      .eq('is_active', true)
      .order('sort_order');

    if (error) {
      console.error('Error fetching subscription plans:', error);
      return [];
    }

    return data || [];
  }

  // Get user's current subscription status
  async getUserSubscriptionStatus(userId: string): Promise<SubscriptionStatus | null> {
    try {
      // Simple implementation using direct table queries instead of the missing function
      const { data: subscription, error } = await (this.client as any)
        .from('user_subscriptions')
        .select(`
          *,
          subscription_plans (
            name,
            display_name,
            billing_cycle,
            features
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .single();

      if (error || !subscription) {
        // Return default tier0 status
        return {
          status: 'tier1', // Default tier
          plan_name: 'tier0',
          plan_display_name: 'Free Tier',
          features: [],
          is_premium: false
        };
      }

      return {
        status: subscription.status,
        plan_name: subscription.subscription_plans.name,
        plan_display_name: subscription.subscription_plans.display_name,
        billing_cycle: subscription.subscription_plans.billing_cycle,
        features: subscription.subscription_plans.features || [],
        is_premium: subscription.status === 'active',
        current_period_end: subscription.current_period_end,
        cancel_at_period_end: subscription.cancel_at_period_end,
        trial_end_date: subscription.trial_end_date
      };
    } catch (error) {
      console.error('Error fetching subscription:', error);
      // Return default status if there's any error
      return {
        status: 'tier1',
        plan_name: 'tier0',
        plan_display_name: 'Free Tier',
        features: [],
        is_premium: false
      };
    }
  }

  // Check if user has a specific premium feature
  async checkPremiumFeature(userId: string, featureKey: string): Promise<PremiumFeatureCheck> {
    const { data, error } = await (this.client as any)
      .rpc('check_user_premium_feature', {
        p_user_id: userId,
        p_feature_key: featureKey
      });

    if (error) {
      console.error('Error checking premium feature:', error);
      return { hasFeature: false, value: false, source: 'tier1' };
    }

    // Determine the source of the feature value
    const subscriptionStatus = await this.getUserSubscriptionStatus(userId);
    const isFromSubscription = subscriptionStatus?.is_premium;
    
    return {
      hasFeature: data === true || (typeof data === 'number' && data > 0),
      value: data,
      source: isFromSubscription ? 'subscription' : 'tier1'
    };
  }

  // Get all premium features
  async getPremiumFeatures(): Promise<PremiumFeature[]> {
    const { data, error } = await (this.client as any)
      .from('premium_features')
      .select('*')
      .eq('is_active', true)
      .order('category', { ascending: true });

    if (error) {
      console.error('Error fetching premium features:', error);
      return [];
    }

    return data || [];
  }

  // Create a trial subscription
  async createTrialSubscription(
    userId: string, 
    planName: string = 'tier2', 
    trialDays: number = 7
  ): Promise<{ success: boolean; subscriptionId?: string; error?: string }> {
    const { data, error } = await (this.client as any)
      .rpc('create_trial_subscription', {
        p_user_id: userId,
        p_plan_name: planName,
        p_trial_days: trialDays
      });

    if (error) {
      console.error('Error creating trial subscription:', error);
      return { success: false, error: error.message };
    }

    return { success: true, subscriptionId: data };
  }

  // Upgrade user subscription
  async upgradeSubscription(
    userId: string,
    newPlanName: string,
    billingCycle: 'monthly' | 'yearly' = 'monthly'
  ): Promise<{ success: boolean; subscriptionId?: string; error?: string }> {
    const { data, error } = await (this.client as any)
      .rpc('upgrade_subscription', {
        p_user_id: userId,
        p_new_plan_name: newPlanName,
        p_billing_cycle: billingCycle
      });

    if (error) {
      console.error('Error upgrading subscription:', error);
      return { success: false, error: error.message };
    }

    return { success: true, subscriptionId: data };
  }

  // Cancel subscription
  async cancelSubscription(
    userId: string,
    cancelAtPeriodEnd: boolean = true
  ): Promise<{ success: boolean; error?: string }> {
    const { error } = await (this.client as any)
      .from('user_subscriptions')
      .update({
        cancel_at_period_end: cancelAtPeriodEnd,
        cancelled_at: cancelAtPeriodEnd ? undefined : new Date().toISOString(),
        status: cancelAtPeriodEnd ? 'active' : 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('user_id', userId)
      .eq('status', 'active');

    if (error) {
      console.error('Error cancelling subscription:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  }

  // Get billing history
  async getBillingHistory(userId: string): Promise<any[]> {
    const { data, error } = await (this.client as any)
      .from('billing_history')
      .select(`
        *,
        subscription:user_subscriptions(
          plan:subscription_plans(display_name)
        )
      `)
      .eq('user_id', userId)
      .order('billing_date', { ascending: false });

    if (error) {
      console.error('Error fetching billing history:', error);
      return [];
    }

    return data || [];
  }

  // Add premium feature override for specific user
  async addPremiumOverride(
    userId: string,
    featureKey: string,
    overrideValue: any,
    reason?: string,
    expiresAt?: string
  ): Promise<{ success: boolean; error?: string }> {
    const { error } = await (this.client as any)
      .from('user_premium_overrides')
      .upsert({
        user_id: userId,
        feature_key: featureKey,
        override_value: overrideValue,
        reason,
        expires_at: expiresAt,
        granted_by: userId // In a real app, this would be an admin user ID
      });

    if (error) {
      console.error('Error adding premium override:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  }

  // Remove premium feature override
  async removePremiumOverride(
    userId: string,
    featureKey: string
  ): Promise<{ success: boolean; error?: string }> {
    const { error } = await (this.client as any)
      .from('user_premium_overrides')
      .delete()
      .eq('user_id', userId)
      .eq('feature_key', featureKey);

    if (error) {
      console.error('Error removing premium override:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  }

  // Check multiple features at once
  async checkMultiplePremiumFeatures(
    userId: string,
    featureKeys: string[]
  ): Promise<Record<string, PremiumFeatureCheck>> {
    const results: Record<string, PremiumFeatureCheck> = {};

    await Promise.all(
      featureKeys.map(async (featureKey) => {
        results[featureKey] = await this.checkPremiumFeature(userId, featureKey);
      })
    );

    return results;
  }

  // Get user's feature limits and usage
  async getUserFeatureLimits(userId: string): Promise<{
    daily_articles: { limit: number; used: number };
    monthly_articles: { limit: number; used: number };
    media_storage: { limit: number; used: number };
    video_upload_size: { limit: number };
  }> {
    // Get feature limits
    const dailyLimit = await this.checkPremiumFeature(userId, 'daily_article_limit');
    const monthlyLimit = await this.checkPremiumFeature(userId, 'monthly_article_limit');
    const storageLimit = await this.checkPremiumFeature(userId, 'media_storage_limit');
    const videoLimit = await this.checkPremiumFeature(userId, 'video_upload_limit');

    // Get current usage
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const thisMonth = new Date();
    thisMonth.setDate(1);
    thisMonth.setHours(0, 0, 0, 0);

    // Get article counts
    const { data: dailyCount } = await (this.client as any)
      .from('articles')
      .select('id', { count: 'exact' })
      .eq('author_id', userId)
      .gte('created_at', today.toISOString());

    const { data: monthlyCount } = await (this.client as any)
      .from('articles')
      .select('id', { count: 'exact' })
      .eq('author_id', userId)
      .gte('created_at', thisMonth.toISOString());

    // Note: Media storage usage would need to be calculated from actual file sizes
    // This is a simplified version
    const { data: mediaCount } = await (this.client as any)
      .from('articles')
      .select('image_url')
      .eq('author_id', userId)
      .not('image_url', 'is', null);

    return {
      daily_articles: {
        limit: dailyLimit.value as number,
        used: dailyCount?.length || 0
      },
      monthly_articles: {
        limit: monthlyLimit.value as number,
        used: monthlyCount?.length || 0
      },
      media_storage: {
        limit: storageLimit.value as number,
        used: Math.round((mediaCount?.length || 0) * 0.5) // Estimated 0.5GB per image
      },
      video_upload_size: {
        limit: videoLimit.value as number
      }
    };
  }
}

// Feature checking utilities
export class PremiumFeatureChecker {
  private subscriptionManager = new PremiumSubscriptionManager();

  // Higher-order component for feature checking
  async withPremiumFeature<T>(
    userId: string,
    featureKey: string,
    callback: () => Promise<T>,
    fallback?: () => Promise<T>
  ): Promise<T> {
    const featureCheck = await this.subscriptionManager.checkPremiumFeature(userId, featureKey);
    
    if (featureCheck.hasFeature) {
      return await callback();
    } else if (fallback) {
      return await fallback();
    } else {
      throw new Error(`Premium feature required: ${featureKey}`);
    }
  }

  // Check if user can perform an action based on limits
  async canPerformAction(
    userId: string,
    action: 'create_article' | 'upload_video' | 'use_ai_assistant'
  ): Promise<{ allowed: boolean; reason?: string; upgradeRequired?: boolean }> {
    switch (action) {
      case 'create_article':
        const limits = await this.subscriptionManager.getUserFeatureLimits(userId);
        if (limits.daily_articles.used >= limits.daily_articles.limit) {
          return {
            allowed: false,
            reason: `Daily article limit reached (${limits.daily_articles.limit})`,
            upgradeRequired: limits.daily_articles.limit <= 3
          };
        }
        return { allowed: true };

      case 'upload_video':
        const videoFeature = await this.subscriptionManager.checkPremiumFeature(userId, 'video_upload_limit');
        if (!videoFeature.hasFeature || videoFeature.value < 100) {
          return {
            allowed: false,
            reason: 'Video upload requires premium subscription',
            upgradeRequired: true
          };
        }
        return { allowed: true };

      case 'use_ai_assistant':
        const aiFeature = await this.subscriptionManager.checkPremiumFeature(userId, 'ai_writing_assistant');
        if (!aiFeature.hasFeature) {
          return {
            allowed: false,
            reason: 'AI Writing Assistant requires premium subscription',
            upgradeRequired: true
          };
        }
        return { allowed: true };

      default:
        return { allowed: false, reason: 'Unknown action' };
    }
  }
}

// Export instances
export const premiumManager = new PremiumSubscriptionManager();
export const premiumChecker = new PremiumFeatureChecker();

// React hooks for premium features
export const usePremiumFeature = () => {
  const checkFeature = async (userId: string, featureKey: string) => {
    return await premiumManager.checkPremiumFeature(userId, featureKey);
  };

  const checkMultipleFeatures = async (userId: string, featureKeys: string[]) => {
    return await premiumManager.checkMultiplePremiumFeatures(userId, featureKeys);
  };

  const getSubscriptionStatus = async (userId: string) => {
    return await premiumManager.getUserSubscriptionStatus(userId);
  };

  return {
    checkFeature,
    checkMultipleFeatures,
    getSubscriptionStatus,
    manager: premiumManager,
    checker: premiumChecker
  };
};
