// Data transformation utilities to convert API responses to match existing interfaces
import type { CoinPrice, SimplePriceResponse, NewsArticle } from './api-services';
import type { MarketData, ChartDataPoint } from './real-time-data';
import type { ChainMetrics } from './onchain-data';

// Transform CoinGecko market data to our MarketData interface
export function transformCoinGeckoToMarketData(coinData: CoinPrice[]): MarketData[] {
  return coinData.map(coin => ({
    symbol: coin.symbol.toUpperCase(),
    price: coin.current_price,
    change24h: coin.price_change_percentage_24h || 0,
    change7d: coin.price_change_percentage_7d_in_currency || 0,
    volume24h: coin.total_volume,
    marketCap: coin.market_cap,
    lastUpdated: new Date(coin.last_updated).getTime(),
  }));
}

// Transform simple price response to portfolio prices
export function transformSimplePricesToPortfolio(
  priceData: SimplePriceResponse,
  symbols: string[]
): Record<string, number> {
  const portfolioPrices: Record<string, number> = {};
  
  Object.entries(priceData).forEach(([coinId, prices]) => {
    // Find the symbol that matches this coin ID
    const symbol = symbols.find(s => 
      coinId.includes(s.toLowerCase()) || 
      s.toLowerCase().includes(coinId)
    );
    
    if (symbol && prices.usd) {
      portfolioPrices[symbol.toUpperCase()] = prices.usd;
    }
  });
  
  return portfolioPrices;
}

// Transform CoinGecko data to heatmap format
export function transformCoinGeckoToHeatmap(coinData: CoinPrice[]): any[] {
  return coinData.map(coin => ({
    symbol: coin.symbol.toUpperCase(),
    name: coin.name,
    change24h: coin.price_change_percentage_24h || 0,
    volume: coin.total_volume,
    marketCap: coin.market_cap,
    price: coin.current_price,
    marketCapRank: coin.market_cap_rank,
    lastUpdated: new Date(coin.last_updated).getTime(),
  }));
}

// Transform Binance WebSocket ticker data
export function transformBinanceTickerData(tickerData: any): MarketData {
  return {
    symbol: tickerData.s.replace('USDT', ''), // Remove USDT suffix
    price: parseFloat(tickerData.c), // Current price
    change24h: parseFloat(tickerData.P), // 24hr price change percentage
    change7d: 0, // Not available in ticker data
    volume24h: parseFloat(tickerData.v), // 24hr volume
    marketCap: 0, // Not available in ticker data
    lastUpdated: tickerData.E, // Event time
  };
}

// Transform Binance kline data to chart data points
export function transformBinanceKlineData(klineData: any[]): ChartDataPoint[] {
  return klineData.map(kline => ({
    time: kline[0], // Open time
    open: parseFloat(kline[1]), // Open price
    high: parseFloat(kline[2]), // High price
    low: parseFloat(kline[3]), // Low price
    close: parseFloat(kline[4]), // Close price
    volume: parseFloat(kline[5]), // Volume
  }));
}

// Transform news API responses to standardized format
export function transformNewsApiToArticles(newsData: any): NewsArticle[] {
  if (!newsData.articles) return [];
  
  return newsData.articles.map((article: any) => ({
    title: article.title || '',
    description: article.description || '',
    url: article.url || '',
    urlToImage: article.urlToImage || '',
    publishedAt: article.publishedAt || new Date().toISOString(),
    source: {
      id: article.source?.id || '',
      name: article.source?.name || 'Unknown',
    },
  }));
}

// Transform CryptoNews API response
export function transformCryptoNewsToArticles(newsData: any): NewsArticle[] {
  if (!newsData.data) return [];
  
  return newsData.data.map((article: any) => ({
    title: article.title || '',
    description: article.text || article.description || '',
    url: article.news_url || article.url || '',
    urlToImage: article.image_url || article.urlToImage || '',
    publishedAt: article.date || article.publishedAt || new Date().toISOString(),
    source: {
      id: article.source_name || '',
      name: article.source_name || 'CryptoNews',
    },
    sentiment: article.sentiment || 'neutral',
  }));
}

// Transform DefiLlama TVL data
export function transformDefiLlamaTVL(tvlData: any): number {
  if (!tvlData.tvl || !Array.isArray(tvlData.tvl)) return 0;
  
  // Get the latest TVL value
  const latestTVL = tvlData.tvl[tvlData.tvl.length - 1];
  return latestTVL?.totalLiquidityUSD || 0;
}

// Transform CoinGecko coin data to ChainMetrics
export function transformCoinGeckoToChainMetrics(coinData: any): Partial<ChainMetrics> {
  if (!coinData.market_data) return {};
  
  return {
    name: coinData.name,
    symbol: coinData.symbol?.toUpperCase(),
    price: coinData.market_data.current_price?.usd || 0,
    marketCap: coinData.market_data.market_cap?.usd || 0,
    fdmc: coinData.market_data.fully_diluted_valuation?.usd || 0,
    circulatingSupply: coinData.market_data.circulating_supply || 0,
    transactionVolume: coinData.market_data.total_volume?.usd || 0,
    timestamp: Date.now(),
  };
}

// Normalize symbol names across different APIs
export function normalizeSymbol(symbol: string, fromAPI: 'coingecko' | 'binance' | 'coinmarketcap'): string {
  const symbolMap: Record<string, Record<string, string>> = {
    coingecko: {
      'bitcoin': 'BTC',
      'ethereum': 'ETH',
      'solana': 'SOL',
      'cardano': 'ADA',
      'polkadot': 'DOT',
      'chainlink': 'LINK',
      'avalanche-2': 'AVAX',
      'polygon': 'MATIC',
      'uniswap': 'UNI',
      'cosmos': 'ATOM',
    },
    binance: {
      'BTCUSDT': 'BTC',
      'ETHUSDT': 'ETH',
      'SOLUSDT': 'SOL',
      'ADAUSDT': 'ADA',
      'DOTUSDT': 'DOT',
      'LINKUSDT': 'LINK',
      'AVAXUSDT': 'AVAX',
      'MATICUSDT': 'MATIC',
      'UNIUSDT': 'UNI',
      'ATOMUSDT': 'ATOM',
    },
    coinmarketcap: {
      'BTC': 'BTC',
      'ETH': 'ETH',
      'SOL': 'SOL',
      'ADA': 'ADA',
      'XRP': 'XRP',
      'SUI': 'SUI',
      'SEI': 'SEI',
      'RAY': 'RAY',
      'JUP': 'JUP',
      'BNB': 'BNB',
      'PYTH': 'PYTH',
      'NATIX': 'NATIX',
      'RENDER': 'RENDER',
      'ZEUS': 'ZEUS',
      'APE': 'APE',
      'BONK': 'BONK',
      'DOGE': 'DOGE',
      'FLOKI': 'FLOKI',
      'PENGU': 'PENGU',
      'PEPE': 'PEPE',
      'SHIB': 'SHIB',
      'WIF': 'WIF',
    },
  };

  return symbolMap[fromAPI]?.[symbol] || symbol.toUpperCase();
}

// Supported trading pairs on Binance/KuCoin
export const SUPPORTED_PAIRS = [
  "BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "XRPUSDT", 
  "SUIUSDT", "SEIUSDT", "RAYUSDT", "JUPUSDT", "BNBUSDT",
  "PYTHUSDT", "NATIXUSDT", "RENDERUSDT", "ZEUSUSDT", "APEUSDT",
  "BONKUSDT", "DOGEUSDT", "FLOKIUSDT", "PENGUUSDT", "PEPEUSDT",
  "SHIBUSDT", "WIFUSDT"
];

// Convert common symbol names to Binance trading symbols
export function toBinanceSymbol(symbol: string): string | null {
  const symbolMap: Record<string, string> = {
    'BITCOIN': 'BTC',
    'ETHEREUM': 'ETH', 
    'SOLANA': 'SOL',
    'CARDANO': 'ADA',
    'RIPPLE': 'XRP',
    'SUI': 'SUI',
    'SEI': 'SEI', 
    'RAYDIUM': 'RAY',
    'JUPITER': 'JUP',
    'BINANCE-COIN': 'BNB',
    'PYTH': 'PYTH',
    'NATIX': 'NATIX',
    'RENDER': 'RENDER',
    'ZEUS': 'ZEUS',
    'APECOIN': 'APE',
    'BONK': 'BONK',
    'DOGECOIN': 'DOGE',
    'FLOKI': 'FLOKI',
    'PENGU': 'PENGU',
    'PEPE': 'PEPE',
    'SHIBA-INU': 'SHIB',
    'DOGWIFHAT': 'WIF',
    // Common aliases
    'BTC': 'BTC',
    'ETH': 'ETH',
    'SOL': 'SOL',
    'ADA': 'ADA',
    'XRP': 'XRP',
    'BNB': 'BNB',
    'DOGE': 'DOGE',
    'SHIB': 'SHIB',
    'APE': 'APE',
    'RAY': 'RAY',
    'JUP': 'JUP'
  };

  const upperSymbol = symbol.toUpperCase();
  const mappedSymbol = symbolMap[upperSymbol] || upperSymbol;
  
  // Validate that the mapped symbol exists in our supported pairs
  const pairSymbol = `${mappedSymbol}USDT`;
  if (SUPPORTED_PAIRS.includes(pairSymbol)) {
    return mappedSymbol;
  }
  
  // If not supported, return null to indicate unsupported pair
  return null;
}

// Check if a symbol is supported
export function isSupportedPair(symbol: string): boolean {
  const binanceSymbol = toBinanceSymbol(symbol);
  return binanceSymbol !== null && SUPPORTED_PAIRS.includes(`${binanceSymbol}USDT`);
}

// Validate and sanitize API response data
export function validateAndSanitizeMarketData(data: any): MarketData | null {
  if (!data || typeof data !== 'object') return null;
  
  try {
    return {
      symbol: String(data.symbol || '').toUpperCase(),
      price: Number(data.price || 0),
      change24h: Number(data.change24h || 0),
      change7d: Number(data.change7d || 0),
      volume24h: Number(data.volume24h || 0),
      marketCap: Number(data.marketCap || 0),
      lastUpdated: Number(data.lastUpdated || Date.now()),
    };
  } catch (error) {
    console.error('Failed to validate market data:', error);
    return null;
  }
}

// Merge data from multiple sources with priority
export function mergeMarketDataSources(
  primary: MarketData[],
  secondary: MarketData[],
  websocket?: Record<string, any>
): MarketData[] {
  const merged = new Map<string, MarketData>();
  
  // Add primary data
  primary.forEach(item => {
    merged.set(item.symbol, item);
  });
  
  // Fill gaps with secondary data
  secondary.forEach(item => {
    if (!merged.has(item.symbol)) {
      merged.set(item.symbol, item);
    }
  });
  
  // Enhance with WebSocket data (highest priority for real-time updates)
  if (websocket) {
    Object.entries(websocket).forEach(([symbol, wsData]: [string, any]) => {
      const existing = merged.get(symbol);
      if (existing && wsData.price) {
        merged.set(symbol, {
          ...existing,
          price: wsData.price,
          change24h: wsData.change24h || existing.change24h,
          volume24h: wsData.volume24h || existing.volume24h,
          lastUpdated: wsData.timestamp || Date.now(),
        });
      }
    });
  }
  
  return Array.from(merged.values());
}

// Format numbers for display
export function formatCurrency(value: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
    notation: value >= 1e9 ? 'compact' : 'standard',
    maximumFractionDigits: value >= 1 ? 2 : 6,
  }).format(value);
}

export function formatPercentage(value: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
    signDisplay: 'always',
  }).format(value / 100);
}

export function formatNumber(value: number): string {
  return new Intl.NumberFormat('en-US', {
    notation: value >= 1e6 ? 'compact' : 'standard',
    maximumFractionDigits: 2,
  }).format(value);
}

// Error handling for API responses
export function handleAPIError(error: any, source: string): Error {
  const message = error?.message || error?.toString() || 'Unknown error';
  return new Error(`${source} API Error: ${message}`);
}

// Rate limiting helper
export function createRateLimitedFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  maxCalls: number,
  windowMs: number
): T {
  const calls: number[] = [];
  
  return (async (...args: Parameters<T>) => {
    const now = Date.now();
    
    // Remove calls outside the current window
    while (calls.length > 0 && now - calls[0] > windowMs) {
      calls.shift();
    }
    
    // Check if we can make the call
    if (calls.length >= maxCalls) {
      const waitTime = windowMs - (now - calls[0]);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return createRateLimitedFunction(fn, maxCalls, windowMs)(...args);
    }
    
    calls.push(now);
    return fn(...args);
  }) as T;
}
