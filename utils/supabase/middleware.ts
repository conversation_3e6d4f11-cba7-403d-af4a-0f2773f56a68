import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  })

  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value))
          supabaseResponse = NextResponse.next({
            request,
          })
          cookiesToSet.forEach(({ name, value, options }) =>
            supabaseResponse.cookies.set(name, value, options)
          )
        },
      },
    }
  )

  try {
    const {
      data: { user },
      error,
    } = await (supabase as any).auth.getUser();

    // Only log error if it's not the expected AuthSessionMissingError for guest users
    if (error && error.message !== 'Auth session missing!') {
      console.error("Error fetching user:", error);
    }

    // Check if the current page is a protected page under /ctn
    const isProtectedPage = request.nextUrl.pathname.startsWith('/ctn');

    // If the user is not logged in and is trying to access a protected page
    if (!user && isProtectedPage) {
      const url = request.nextUrl.clone();
      url.pathname = '/'; // Redirect non-logged-in users to the homepage
      return NextResponse.redirect(url);
    }

    // If the user is logged in and trying to access the homepage or login pages
    const isAuthPage = request.nextUrl.pathname.startsWith('/signin') || request.nextUrl.pathname.startsWith('/signup') || request.nextUrl.pathname.startsWith('/auth');

    if (user && (request.nextUrl.pathname === '/' || isAuthPage)) {
      const url = request.nextUrl.clone();
      url.pathname = '/ctn'; // Redirect logged-in users to /ctn
      return NextResponse.redirect(url);
    }

  } catch (error) {
    console.error("Unexpected error:", error);
    // Handle unexpected errors (e.g., log them, redirect, etc.)
  }

  // Return the original response if the user is authorized and on non-protected pages
  return supabaseResponse;
}