// stores/settingsStore.ts
import { create } from 'zustand'
import { createClient } from '@/utils/supabase/client'
import { getSettings, updateSettings } from '@/app/actions'

interface Settings {
  notifications: {
    pushEnabled: boolean
    emailEnabled: boolean
    mentionsEnabled: boolean
    followersEnabled: boolean
  }
  privacy: {
    accountPrivate: boolean
    activityStatus: boolean
    postVisibility: 'everyone' | 'followers' | 'nobody'
  }
  profile: {
    showActivity: boolean
    allowMessages: boolean
    allowMentions: boolean
  }
}

interface SettingsState {
  settings: Settings | null
  loading: boolean
  error: Error | null
  fetchSettings: () => Promise<void>
  updateSettings: (newSettings: Partial<Settings>) => Promise<void>
  resetSettings: () => void
}

const DEFAULT_SETTINGS: Settings = {
  notifications: {
    pushEnabled: false,
    emailEnabled: false,
    mentionsEnabled: false,
    followersEnabled: false,
  },
  privacy: {
    accountPrivate: false,
    activityStatus: false,
    postVisibility: 'everyone',
  },
  profile: {
    showActivity: true,
    allowMessages: true,
    allowMentions: true,
  }
}

export const useSettingsStore = create<SettingsState>((set, get) => ({
  settings: null,
  loading: false,
  error: null,

  fetchSettings: async () => {
    set({ loading: true, error: null })
    try {
      // Try server action first
      const serverData = await getSettings()

      if (serverData) {
        set({
          settings: {
            notifications: {
              pushEnabled: serverData.push_enabled ?? DEFAULT_SETTINGS.notifications.pushEnabled,
              emailEnabled: serverData.email_enabled ?? DEFAULT_SETTINGS.notifications.emailEnabled,
              mentionsEnabled: serverData.mentions_enabled ?? DEFAULT_SETTINGS.notifications.mentionsEnabled,
              followersEnabled: serverData.followers_enabled ?? DEFAULT_SETTINGS.notifications.followersEnabled,
            },
            privacy: {
              accountPrivate: serverData.account_private ?? DEFAULT_SETTINGS.privacy.accountPrivate,
              activityStatus: serverData.activity_status ?? DEFAULT_SETTINGS.privacy.activityStatus,
              postVisibility: serverData.post_visibility ?? DEFAULT_SETTINGS.privacy.postVisibility,
            },
            profile: {
              showActivity: serverData.show_activity ?? DEFAULT_SETTINGS.profile.showActivity,
              allowMessages: serverData.allow_messages ?? DEFAULT_SETTINGS.profile.allowMessages,
              allowMentions: serverData.allow_mentions ?? DEFAULT_SETTINGS.profile.allowMentions,
            }
          },
          loading: false
        })
        return
      }

      // Fallback to client-side fetch if server action fails
      const supabase = createClient()
      const { data, error } = await (supabase as any)
        .from('user_settings')
        .select('*')
        .single()

      if (error) throw error

      set({
        settings: {
          notifications: {
            pushEnabled: data.push_enabled ?? DEFAULT_SETTINGS.notifications.pushEnabled,
            emailEnabled: data.email_enabled ?? DEFAULT_SETTINGS.notifications.emailEnabled,
            mentionsEnabled: data.mentions_enabled ?? DEFAULT_SETTINGS.notifications.mentionsEnabled,
            followersEnabled: data.followers_enabled ?? DEFAULT_SETTINGS.notifications.followersEnabled,
          },
          privacy: {
            accountPrivate: data.account_private ?? DEFAULT_SETTINGS.privacy.accountPrivate,
            activityStatus: data.activity_status ?? DEFAULT_SETTINGS.privacy.activityStatus,
            postVisibility: data.post_visibility ?? DEFAULT_SETTINGS.privacy.postVisibility,
          },
          profile: {
            showActivity: data.show_activity ?? DEFAULT_SETTINGS.profile.showActivity,
            allowMessages: data.allow_messages ?? DEFAULT_SETTINGS.profile.allowMessages,
            allowMentions: data.allow_mentions ?? DEFAULT_SETTINGS.profile.allowMentions,
          }
        },
        loading: false
      })
    } catch (error) {
      console.error('Error fetching settings:', error)
      set({ error: error as Error, loading: false })
    }
  },

  updateSettings: async (newSettings) => {
    const currentSettings = get().settings
    if (!currentSettings) return

    set({ loading: true, error: null })
    try {
      const dbFormat = {
        ...(newSettings.notifications && {
          push_enabled: newSettings.notifications.pushEnabled,
          email_enabled: newSettings.notifications.emailEnabled,
          mentions_enabled: newSettings.notifications.mentionsEnabled,
          followers_enabled: newSettings.notifications.followersEnabled,
        }),
        ...(newSettings.privacy && {
          account_private: newSettings.privacy.accountPrivate,
          activity_status: newSettings.privacy.activityStatus,
          post_visibility: newSettings.privacy.postVisibility,
        }),
        ...(newSettings.profile && {
          show_activity: newSettings.profile.showActivity,
          allow_messages: newSettings.profile.allowMessages,
          allow_mentions: newSettings.profile.allowMentions,
        })
      }

      // Try server action first
      try {
        await updateSettings(dbFormat)
      } catch (serverError) {
        // Fallback to client-side update if server action fails
        const supabase = createClient()
        const { error } = await (supabase as any)
          .from('user_settings')
          .update(dbFormat)
          .select()

        if (error) throw error
      }

      set({
        settings: {
          ...currentSettings,
          ...newSettings,
        },
        loading: false
      })
    } catch (error) {
      console.error('Error updating settings:', error)
      set({ error: error as Error, loading: false })
    }
  },

  resetSettings: () => {
    set({
      settings: DEFAULT_SETTINGS,
      loading: false,
      error: null
    })
  }
}))

// Helper function to check if settings are loaded
export const isSettingsLoaded = (settings: Settings | null): settings is Settings => {
  return settings !== null
}