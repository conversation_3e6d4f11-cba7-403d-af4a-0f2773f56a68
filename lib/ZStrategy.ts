// ZStrategy: Quantitative trading strategy with advanced filters and indicators
// Usage: Instantiate and call executeStrategy(data) with OHLCV array

export interface ZStrategyConfig {
    initialCapital?: number;
    precision?: number;
    defaultQtyPercent?: number;
    commissionPercent?: number;
    slippage?: number;
    startDate?: Date;
    endDate?: Date;
    tradeDirection?: 'long_only' | 'short_only' | 'both';
    fillParts?: number;
    delayBars?: number;
    baseTimeframe?: number;
    stMultiplier?: number;
    stPeriod?: number;
    applyVolatilityFilter?: boolean;
    minVolatility?: number;
    maxVolatility?: number;
    applyVolumeFilter?: boolean;
    volumeMultiplier?: number;
    showSignals?: boolean;
}


export class ZStrategy {
    config: Required<ZStrategyConfig>;
    positions: any[] = [];
    trades: any[] = [];
    currentCapital: number = 0;
    pendingLong: number = 0;
    pendingShort: number = 0;
    longBar: number | null = null;
    shortBar: number | null = null;
    data: Record<string, any>[] = [];
    indicators: Record<string, any>[] = [];

    constructor(config: ZStrategyConfig = {}) {
        // Strategy configuration
        this.config = {
            initialCapital: config.initialCapital ?? 1000,
            precision: config.precision ?? 6,
            defaultQtyPercent: config.defaultQtyPercent ?? 80,
            commissionPercent: config.commissionPercent ?? 0.1,
            slippage: config.slippage ?? 2,

            // Backtest date range
            startDate: config.startDate ?? new Date('2020-01-01'),
            endDate: config.endDate ?? new Date('2025-12-31'),

            // Strategy parameters
            tradeDirection: config.tradeDirection ?? 'both', // 'long_only', 'short_only', 'both'
            fillParts: config.fillParts ?? 2,
            delayBars: config.delayBars ?? 1,
            baseTimeframe: config.baseTimeframe ?? 1440, // minutes

            // SuperTrend parameters
            stMultiplier: config.stMultiplier ?? 1.0,
            stPeriod: config.stPeriod ?? 50,

            // Filters
            applyVolatilityFilter: config.applyVolatilityFilter ?? false,
            minVolatility: config.minVolatility ?? 0.3,
            maxVolatility: config.maxVolatility ?? 5.0,
            applyVolumeFilter: config.applyVolumeFilter ?? false,
            volumeMultiplier: config.volumeMultiplier ?? 0.8,

            // Visualization
            showSignals: config.showSignals ?? true
        };

        // Strategy state
        this.positions = [];
        this.trades = [];
        this.currentCapital = this.config.initialCapital;

        // Pending fill state
        this.pendingLong = 0;
        this.pendingShort = 0;
        this.longBar = null;
        this.shortBar = null;

        // Data storage
        this.data = [];
        this.indicators = [];
    }
    
    // Helper function to calculate EMA
    calculateEMA(values: number[], period: number): number[] {
        const ema: number[] = [];
        const k = 2 / (period + 1);
        if (values.length === 0) return ema;
        ema[0] = values[0];
        for (let i = 1; i < values.length; i++) {
            ema[i] = values[i] * k + ema[i - 1] * (1 - k);
        }
        return ema;
    }
    
    // Helper function to calculate SMA
    calculateSMA(values: number[], period: number): (number | null)[] {
        const sma: (number | null)[] = [];
        for (let i = 0; i < values.length; i++) {
            if (i < period - 1) {
                sma[i] = null;
            } else {
                const sum = values.slice(i - period + 1, i + 1).reduce((a: number, b: number) => a + b, 0);
                sma[i] = sum / period;
            }
        }
        return sma;
    }
    
    // Helper function to calculate ATR
    calculateATR(highs: number[], lows: number[], closes: number[], period: number): (number | null)[] {
        const trueRanges: number[] = [];
        for (let i = 1; i < highs.length; i++) {
            const tr1 = highs[i] - lows[i];
            const tr2 = Math.abs(highs[i] - closes[i - 1]);
            const tr3 = Math.abs(lows[i] - closes[i - 1]);
            trueRanges[i] = Math.max(tr1, tr2, tr3);
        }
        return this.calculateSMA(trueRanges, period);
    }
    
    // Volume-weighted EMA calculation
    calculateVolumeWeightedEMA(prices: number[], volumes: number[], period: number): (number | null)[] {
        const priceVolume = prices.map((price: number, i: number) => price * volumes[i]);
        const vwEMANumerator = this.calculateEMA(priceVolume, period);
        const vwEMADenominator = this.calculateEMA(volumes, period);
        return vwEMANumerator.map((num: number, i: number) => 
            vwEMADenominator[i] ? num / vwEMADenominator[i] : null
        );
    }
    
    // Hull Moving Average calculation
    calculateHullMA(values: number[], period: number): (number | null)[] {
        const halfPeriod = Math.floor(period / 2);
        const sqrtPeriod = Math.floor(Math.sqrt(period));
        const wma1 = this.calculateWMA(values, halfPeriod);
        const wma2 = this.calculateWMA(values, period);
        const hullValues = wma1.map((val1: number | null, i: number) => 
            val1 !== null && wma2[i] !== null ? 2 * val1 - (wma2[i] as number) : null
        );
        return this.calculateWMA(hullValues, sqrtPeriod);
    }
    
    // Weighted Moving Average calculation
    calculateWMA(values: (number | null)[], period: number): (number | null)[] {
        const wma: (number | null)[] = [];
        for (let i = 0; i < values.length; i++) {
            if (i < period - 1 || values[i] === null) {
                wma[i] = null;
            } else {
                let numerator = 0;
                let denominator = 0;
                for (let j = 0; j < period; j++) {
                    const weight = (period - j) * period;
                    const valueIndex = i - j;
                    if (values[valueIndex] !== null) {
                        numerator += (values[valueIndex] as number) * weight;
                        denominator += weight;
                    }
                }
                wma[i] = denominator > 0 ? numerator / denominator : null;
            }
        }
        return wma;
    }
    
    // SuperTrend calculation
    calculateSuperTrend(
        highs: number[],
        lows: number[],
        closes: number[],
        period: number,
        multiplier: number
    ): { stLine: (number | undefined)[]; trendValues: number[] } {
        const atr = this.calculateATR(highs, lows, closes, period);
        const upLevels = lows.map((low: number, i: number) => low - multiplier * ((atr[i] as number) || 0));
        const dnLevels = highs.map((high: number, i: number) => high + multiplier * ((atr[i] as number) || 0));
        const upTrend: number[] = [upLevels[0]];
        const downTrend: number[] = [dnLevels[0]];
        const trendValues: number[] = [1];
        for (let i = 1; i < closes.length; i++) {
            upTrend[i] = closes[i - 1] > upTrend[i - 1]
                ? Math.max(upLevels[i], upTrend[i - 1])
                : upLevels[i];
            downTrend[i] = closes[i - 1] < downTrend[i - 1]
                ? Math.min(dnLevels[i], downTrend[i - 1])
                : dnLevels[i];
            if (closes[i] > downTrend[i - 1]) {
                trendValues[i] = 1;
            } else if (closes[i] < upTrend[i - 1]) {
                trendValues[i] = -1;
            } else {
                trendValues[i] = trendValues[i - 1];
            }
        }
        const stLine = trendValues.map((trend: number, i: number) =>
            trend === 1 ? upTrend[i] : downTrend[i]
        );
        return { stLine, trendValues };
    }
    
    // Check if price is in date range
    isInDateRange(timestamp: number): boolean {
        const date = new Date(timestamp);
        return date >= this.config.startDate && date <= this.config.endDate;
    }
    
    // Apply volatility filter
    checkVolatilityFilter(high: number, low: number): boolean {
        if (!this.config.applyVolatilityFilter) return true;
        const volatility = ((high - low) / low) * 100;
        return volatility >= this.config.minVolatility && volatility <= this.config.maxVolatility;
    }
    
    // Apply volume filter
    checkVolumeFilter(volume: number, avgVolume: number): boolean {
        if (!this.config.applyVolumeFilter) return true;
        return volume >= avgVolume * this.config.volumeMultiplier;
    }
    
    // Generate trading signals
    generateSignals(data: Array<{ open: number; high: number; low: number; close: number; volume: number; timestamp: number }>): any[] {
        const closes = data.map(d => d.close);
        const highs = data.map(d => d.high);
        const lows = data.map(d => d.low);
        const opens = data.map(d => d.open);
        const volumes = data.map(d => d.volume);
        const timestamps = data.map(d => d.timestamp);
        // Calculate volume-weighted EMA
        const vwEMA = this.calculateVolumeWeightedEMA(closes, volumes, 7);
        // Calculate Hull Moving Average
        const vhma = this.calculateHullMA(vwEMA as number[], 7);
        // Calculate SuperTrend
        const { stLine, trendValues } = this.calculateSuperTrend(
            highs, lows, closes, this.config.stPeriod, this.config.stMultiplier
        );
        // Calculate average volume for filter
        const avgVolume = this.calculateSMA(volumes, 20);
        // Generate buy/sell signals
        const signals: any[] = [];
        for (let i = 1; i < closes.length; i++) {
            const signal: any = {
                timestamp: timestamps[i],
                index: i,
                price: closes[i],
                vhma: vhma[i],
                stLine: stLine[i],
                buy: false,
                sell: false
            };
            // Check for crossover signals (with null/undefined checks)
            if (
                stLine[i] !== undefined && stLine[i - 1] !== undefined &&
                closes[i] > stLine[i]! && closes[i - 1] <= stLine[i - 1]!
            ) {
                signal.buy = true;
            }
            if (
                stLine[i] !== undefined && stLine[i - 1] !== undefined &&
                closes[i] < stLine[i]! && closes[i - 1] >= stLine[i - 1]!
            ) {
                signal.sell = true;
            }
            // Apply filters
            const volatilityOk = this.checkVolatilityFilter(highs[i], lows[i]);
            const volumeOk = this.checkVolumeFilter(volumes[i], (avgVolume[i] as number) || 0);
            const dateOk = this.isInDateRange(timestamps[i]);
            signal.filtersPass = volatilityOk && volumeOk && dateOk;
            signals.push(signal);
        }
        return signals;
    }
    
    // Execute trading strategy
    executeStrategy(data: Array<{ open: number; high: number; low: number; close: number; volume: number; timestamp: number }>) {
        const signals = this.generateSignals(data);
        
        for (let i = 0; i < signals.length; i++) {
            const signal = signals[i];
            // Handle buy signals
            if (signal.buy && this.config.tradeDirection !== 'short_only') {
                this.closePosition('short');
                if (signal.filtersPass) {
                    this.pendingLong = this.config.fillParts;
                    this.longBar = signal.index;
                }
            }
            // Handle sell signals
            if (signal.sell && this.config.tradeDirection !== 'long_only') {
                this.closePosition('long');
                if (signal.filtersPass) {
                    this.pendingShort = this.config.fillParts;
                    this.shortBar = signal.index;
                }
            }
            // Execute pending fills after delay
            if (
                this.pendingLong > 0 &&
                this.longBar !== null &&
                signal.index >= this.longBar + this.config.delayBars &&
                signal.filtersPass
            ) {
                this.openPosition('long', signal.price, signal.timestamp);
                this.pendingLong--;
            }
            if (
                this.pendingShort > 0 &&
                this.shortBar !== null &&
                signal.index >= this.shortBar + this.config.delayBars &&
                signal.filtersPass
            ) {
                this.openPosition('short', signal.price, signal.timestamp);
                this.pendingShort--;
            }
        }
        return {
            signals: signals,
            trades: this.trades,
            finalCapital: this.currentCapital,
            totalReturn: ((this.currentCapital - this.config.initialCapital) / this.config.initialCapital) * 100
        };
    }
    
    // Open a new position
    openPosition(direction: 'long' | 'short', price: number, timestamp: number) {
        const positionSize = (this.currentCapital * this.config.defaultQtyPercent / 100) / price;
        const commission = positionSize * price * (this.config.commissionPercent / 100);
        
        const position = {
            direction: direction,
            entryPrice: price,
            entryTime: timestamp,
            size: positionSize,
            commission: commission
        };
        
        this.positions.push(position);
        this.currentCapital -= commission;
    }
    
    // Close positions
    closePosition(direction: 'long' | 'short') {
        this.positions = this.positions.filter(position => {
            if (position.direction === direction) {
                // Record the trade
                const trade = {
                    direction: position.direction,
                    entryPrice: position.entryPrice,
                    entryTime: position.entryTime,
                    exitPrice: null, // Would need current price
                    exitTime: new Date(),
                    size: position.size,
                    pnl: 0 // Would calculate based on exit price
                };
                
                this.trades.push(trade);
                return false; // Remove from positions
            }
            return true; // Keep position
        });
    }
    
    // Get strategy statistics
    getStatistics() {
        const totalTrades = this.trades.length;
        const winningTrades = this.trades.filter(trade => trade.pnl > 0).length;
        const losingTrades = this.trades.filter(trade => trade.pnl < 0).length;
        
        return {
            totalTrades: totalTrades,
            winningTrades: winningTrades,
            losingTrades: losingTrades,
            winRate: totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0,
            totalReturn: ((this.currentCapital - this.config.initialCapital) / this.config.initialCapital) * 100,
            finalCapital: this.currentCapital
        };
    }
}
