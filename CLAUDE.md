# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Next.js 15 application called "Crypto Talks Network" - a cryptocurrency discussion platform built with Supabase for authentication and data management. The app uses the App Router, TypeScript, Tailwind CSS, and shadcn/ui components.

## Development Commands

- `bun run dev` - Start development server with Turbo
- `bun run build` - Build for production  
- `bun run start` - Start production server
- `bun run lint` - Run ESLint
- `bun run type-check` - Run TypeScript type checking

### Testing Commands

- `bun run test` - Run Jest tests
- `bun run test:watch` - Run Jest in watch mode
- `bun run test:coverage` - Run tests with coverage
- `bun run test:e2e` - <PERSON> Playwright end-to-end tests
- `bun run test:e2e:ui` - Run Playwright with UI
- `bun run test:integration` - Run integration tests only
- `bun run test:unit` - Run unit tests only

## Architecture

### App Structure

The app uses Next.js 15 App Router with these main route groups:

- `app/(auth-pages)/` - Authentication pages (signup, forgot-password)
- `app/auth/` - Auth API routes (callback, reset-password, signout)
- `app/api/` - API routes including:
  - `crypto-news/` - News fetching
  - `solana-pay/` - Solana payment integration
  - `subscription/` - Stripe subscription handling
  - `webhooks/stripe/` - Stripe webhook handlers
- `app/ctn/` - Main authenticated app pages including:
  - `articles/` - Article management and publishing
  - `analytics/` - Platform analytics
  - `subscription/` - Subscription management
  - `profile/` - User profiles
  - `tools/`, `news/`, `research/` - Feature pages

### Key Technologies

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS v4.1, shadcn/ui components with "new-york" style
- **Database**: Supabase with SSR package for auth cookies
- **Payments**: Stripe integration with Solana Pay support
- **State Management**: Zustand for client state
- **Forms**: React Hook Form with Zod validation
- **UI Components**: Radix UI primitives via shadcn/ui

### Authentication & Database

- Uses Supabase SSR for cookie-based authentication
- Database types generated in `supabase_types.ts`
- Client/server Supabase instances in `utils/supabase/`
- Custom middleware for auth handling

### Component Architecture

- Follows shadcn/ui conventions with components in `components/ui/`
- Path aliases configured: `@/components`, `@/lib`, `@/utils`, etc.
- Context providers for global state (UserContext, TierProvider)
- Complex features like subscription tiers, analytics, and Solana payments

### Environment Setup

Environment variables needed (see `.env.example`):
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` 
- Stripe and Solana-related keys for payment features

The project includes extensive subscription/tier management, analytics tracking, content moderation, and social features like profiles, comments, and notifications.