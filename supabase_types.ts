export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      analytics_events: {
        Row: {
          created_at: string | null
          data: Json | null
          event_type: string
          id: string
          ip_address: unknown | null
          session_id: string | null
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          event_type: string
          id?: string
          ip_address?: unknown | null
          session_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          event_type?: string
          id?: string
          ip_address?: unknown | null
          session_id?: string | null
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "analytics_events_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      article_drafts: {
        Row: {
          author_id: string | null
          content: string | null
          created_at: string | null
          id: number
          image_url: string | null
          tags: string[] | null
          title: string | null
          updated_at: string | null
        }
        Insert: {
          author_id?: string | null
          content?: string | null
          created_at?: string | null
          id?: number
          image_url?: string | null
          tags?: string[] | null
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          author_id?: string | null
          content?: string | null
          created_at?: string | null
          id?: number
          image_url?: string | null
          tags?: string[] | null
          title?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "article_drafts_author_id_fkey1"
            columns: ["author_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      article_hashtags: {
        Row: {
          article_id: number
          article_tags: string[] | null
          created_at: string | null
          hashtag_id: number
          id: string
        }
        Insert: {
          article_id: number
          article_tags?: string[] | null
          created_at?: string | null
          hashtag_id: number
          id?: string
        }
        Update: {
          article_id?: number
          article_tags?: string[] | null
          created_at?: string | null
          hashtag_id?: number
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "article_hashtags_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "article_hashtags_hashtag_id_fkey"
            columns: ["hashtag_id"]
            isOneToOne: false
            referencedRelation: "hashtags"
            referencedColumns: ["id"]
          },
        ]
      }
      article_mentions: {
        Row: {
          article_id: number
          created_at: string | null
          id: string
          mentioned_user_id: string
          mentioner_user_id: string
          position_end: number | null
          position_start: number | null
        }
        Insert: {
          article_id: number
          created_at?: string | null
          id?: string
          mentioned_user_id: string
          mentioner_user_id: string
          position_end?: number | null
          position_start?: number | null
        }
        Update: {
          article_id?: number
          created_at?: string | null
          id?: string
          mentioned_user_id?: string
          mentioner_user_id?: string
          position_end?: number | null
          position_start?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "article_mentions_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "article_mentions_mentioned_user_id_fkey"
            columns: ["mentioned_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "article_mentions_mentioner_user_id_fkey"
            columns: ["mentioner_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      article_tags: {
        Row: {
          article_id: number
          created_at: string
          tag_id: number
        }
        Insert: {
          article_id: number
          created_at?: string
          tag_id: number
        }
        Update: {
          article_id?: number
          created_at?: string
          tag_id?: number
        }
        Relationships: [
          {
            foreignKeyName: "article_tags_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "article_tags_tag_id_fkey"
            columns: ["tag_id"]
            isOneToOne: false
            referencedRelation: "tags"
            referencedColumns: ["id"]
          },
        ]
      }
      article_views: {
        Row: {
          article_id: number
          viewed_at: string
        }
        Insert: {
          article_id: number
          viewed_at?: string
        }
        Update: {
          article_id?: number
          viewed_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "article_views_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
        ]
      }
      articles: {
        Row: {
          author: string | null
          author_avatar: string | null
          author_id: string | null
          comments_count: number | null
          content: string | null
          created_at: string | null
          id: number
          image_url: string | null
          likes: number | null
          shares: number | null
          tags: string[] | null
          timestamp: string | null
          title: string
          updated_at: string | null
          views: number | null
        }
        Insert: {
          author?: string | null
          author_avatar?: string | null
          author_id?: string | null
          comments_count?: number | null
          content?: string | null
          created_at?: string | null
          id?: number
          image_url?: string | null
          likes?: number | null
          shares?: number | null
          tags?: string[] | null
          timestamp?: string | null
          title: string
          updated_at?: string | null
          views?: number | null
        }
        Update: {
          author?: string | null
          author_avatar?: string | null
          author_id?: string | null
          comments_count?: number | null
          content?: string | null
          created_at?: string | null
          id?: number
          image_url?: string | null
          likes?: number | null
          shares?: number | null
          tags?: string[] | null
          timestamp?: string | null
          title?: string
          updated_at?: string | null
          views?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "articles_author_id_fkey1"
            columns: ["author_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      backtest_results: {
        Row: {
          created_at: string | null
          end_date: string
          final_capital: number
          id: string
          initial_capital: number
          max_drawdown: number
          max_drawdown_percentage: number
          portfolio_id: string | null
          sharpe_ratio: number | null
          sortino_ratio: number | null
          start_date: string
          strategy_id: string | null
          symbol: string | null
          timeframe: string | null
          total_profit: number
          total_profit_percentage: number
          total_trades: number
          updated_at: string | null
          win_rate: number
        }
        Insert: {
          created_at?: string | null
          end_date: string
          final_capital: number
          id?: string
          initial_capital: number
          max_drawdown: number
          max_drawdown_percentage: number
          portfolio_id?: string | null
          sharpe_ratio?: number | null
          sortino_ratio?: number | null
          start_date: string
          strategy_id?: string | null
          symbol?: string | null
          timeframe?: string | null
          total_profit: number
          total_profit_percentage: number
          total_trades: number
          updated_at?: string | null
          win_rate: number
        }
        Update: {
          created_at?: string | null
          end_date?: string
          final_capital?: number
          id?: string
          initial_capital?: number
          max_drawdown?: number
          max_drawdown_percentage?: number
          portfolio_id?: string | null
          sharpe_ratio?: number | null
          sortino_ratio?: number | null
          start_date?: string
          strategy_id?: string | null
          symbol?: string | null
          timeframe?: string | null
          total_profit?: number
          total_profit_percentage?: number
          total_trades?: number
          updated_at?: string | null
          win_rate?: number
        }
        Relationships: [
          {
            foreignKeyName: "backtest_results_portfolio_id_fkey"
            columns: ["portfolio_id"]
            isOneToOne: false
            referencedRelation: "portfolios"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "backtest_results_strategy_id_fkey"
            columns: ["strategy_id"]
            isOneToOne: false
            referencedRelation: "strategies"
            referencedColumns: ["id"]
          },
        ]
      }
      bot_message_logs: {
        Row: {
          id: string
          last_message_time: string | null
          message_type: string | null
        }
        Insert: {
          id?: string
          last_message_time?: string | null
          message_type?: string | null
        }
        Update: {
          id?: string
          last_message_time?: string | null
          message_type?: string | null
        }
        Relationships: []
      }
      comment_hashtags: {
        Row: {
          comment_id: number
          created_at: string | null
          hashtag_id: number
          id: string
        }
        Insert: {
          comment_id: number
          created_at?: string | null
          hashtag_id: number
          id?: string
        }
        Update: {
          comment_id?: number
          created_at?: string | null
          hashtag_id?: number
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "comment_hashtags_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comment_hashtags_hashtag_id_fkey"
            columns: ["hashtag_id"]
            isOneToOne: false
            referencedRelation: "hashtags"
            referencedColumns: ["id"]
          },
        ]
      }
      comment_likes: {
        Row: {
          comment_id: number | null
          created_at: string | null
          id: string
          user_id: string | null
        }
        Insert: {
          comment_id?: number | null
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Update: {
          comment_id?: number | null
          created_at?: string | null
          id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comment_likes_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
        ]
      }
      comment_mentions: {
        Row: {
          comment_id: number
          created_at: string | null
          id: string
          mentioned_user_id: string
          mentioner_user_id: string
          position_end: number | null
          position_start: number | null
        }
        Insert: {
          comment_id: number
          created_at?: string | null
          id?: string
          mentioned_user_id: string
          mentioner_user_id: string
          position_end?: number | null
          position_start?: number | null
        }
        Update: {
          comment_id?: number
          created_at?: string | null
          id?: string
          mentioned_user_id?: string
          mentioner_user_id?: string
          position_end?: number | null
          position_start?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "comment_mentions_comment_id_fkey"
            columns: ["comment_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comment_mentions_mentioned_user_id_fkey"
            columns: ["mentioned_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "comment_mentions_mentioner_user_id_fkey"
            columns: ["mentioner_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      comments: {
        Row: {
          article_id: number | null
          comment_timestamp: string | null
          content: string
          created_at: string | null
          id: number
          parent_id: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          article_id?: number | null
          comment_timestamp?: string | null
          content: string
          created_at?: string | null
          id?: number
          parent_id?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          article_id?: number | null
          comment_timestamp?: string | null
          content?: string
          created_at?: string | null
          id?: number
          parent_id?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "comments_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_parent_id_fkey"
            columns: ["parent_id"]
            isOneToOne: false
            referencedRelation: "comments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "comments_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      community_guidelines: {
        Row: {
          category: string
          created_at: string | null
          description: string
          id: number
          is_active: boolean | null
          severity: number | null
          title: string
          updated_at: string | null
        }
        Insert: {
          category: string
          created_at?: string | null
          description: string
          id?: number
          is_active?: boolean | null
          severity?: number | null
          title: string
          updated_at?: string | null
        }
        Update: {
          category?: string
          created_at?: string | null
          description?: string
          id?: number
          is_active?: boolean | null
          severity?: number | null
          title?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      content_flags: {
        Row: {
          confidence_score: number | null
          content_id: number
          content_type: string
          created_at: string | null
          flag_type: string
          flagged_by: string | null
          id: string
          moderator_action: string | null
          moderator_id: string | null
          moderator_notes: string | null
          moderator_reviewed: boolean | null
          rule_id: number | null
        }
        Insert: {
          confidence_score?: number | null
          content_id: number
          content_type: string
          created_at?: string | null
          flag_type: string
          flagged_by?: string | null
          id?: string
          moderator_action?: string | null
          moderator_id?: string | null
          moderator_notes?: string | null
          moderator_reviewed?: boolean | null
          rule_id?: number | null
        }
        Update: {
          confidence_score?: number | null
          content_id?: number
          content_type?: string
          created_at?: string | null
          flag_type?: string
          flagged_by?: string | null
          id?: string
          moderator_action?: string | null
          moderator_id?: string | null
          moderator_notes?: string | null
          moderator_reviewed?: boolean | null
          rule_id?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "content_flags_flagged_by_fkey"
            columns: ["flagged_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "content_flags_moderator_id_fkey"
            columns: ["moderator_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "content_flags_rule_id_fkey"
            columns: ["rule_id"]
            isOneToOne: false
            referencedRelation: "moderation_rules"
            referencedColumns: ["id"]
          },
        ]
      }
      content_recommendations: {
        Row: {
          clicked_at: string | null
          content_id: number
          content_type: string
          created_at: string | null
          id: string
          reason: string | null
          recommendation_score: number | null
          shown_at: string | null
          user_id: string
        }
        Insert: {
          clicked_at?: string | null
          content_id: number
          content_type: string
          created_at?: string | null
          id?: string
          reason?: string | null
          recommendation_score?: number | null
          shown_at?: string | null
          user_id: string
        }
        Update: {
          clicked_at?: string | null
          content_id?: number
          content_type?: string
          created_at?: string | null
          id?: string
          reason?: string | null
          recommendation_score?: number | null
          shown_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_recommendations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      content_violations: {
        Row: {
          content_id: number
          content_type: string
          created_at: string | null
          guideline_id: number | null
          id: string
          is_resolved: boolean | null
          moderator_action: string | null
          moderator_id: string | null
          moderator_notes: string | null
          resolved_at: string | null
          severity: number
          user_id: string
          violation_type: string
        }
        Insert: {
          content_id: number
          content_type: string
          created_at?: string | null
          guideline_id?: number | null
          id?: string
          is_resolved?: boolean | null
          moderator_action?: string | null
          moderator_id?: string | null
          moderator_notes?: string | null
          resolved_at?: string | null
          severity: number
          user_id: string
          violation_type: string
        }
        Update: {
          content_id?: number
          content_type?: string
          created_at?: string | null
          guideline_id?: number | null
          id?: string
          is_resolved?: boolean | null
          moderator_action?: string | null
          moderator_id?: string | null
          moderator_notes?: string | null
          resolved_at?: string | null
          severity?: number
          user_id?: string
          violation_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "content_violations_guideline_id_fkey"
            columns: ["guideline_id"]
            isOneToOne: false
            referencedRelation: "community_guidelines"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "content_violations_moderator_id_fkey"
            columns: ["moderator_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "content_violations_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      ctt_transactions: {
        Row: {
          amount: number
          balance_after: number
          created_at: string | null
          description: string | null
          from_wallet: string | null
          id: string
          reference_id: string | null
          solana_transaction_id: string | null
          status: string | null
          to_wallet: string | null
          transaction_type: string
          user_id: string | null
        }
        Insert: {
          amount: number
          balance_after: number
          created_at?: string | null
          description?: string | null
          from_wallet?: string | null
          id?: string
          reference_id?: string | null
          solana_transaction_id?: string | null
          status?: string | null
          to_wallet?: string | null
          transaction_type: string
          user_id?: string | null
        }
        Update: {
          amount?: number
          balance_after?: number
          created_at?: string | null
          description?: string | null
          from_wallet?: string | null
          id?: string
          reference_id?: string | null
          solana_transaction_id?: string | null
          status?: string | null
          to_wallet?: string | null
          transaction_type?: string
          user_id?: string | null
        }
        Relationships: []
      }
      daily_analytics: {
        Row: {
          created_at: string | null
          date: string
          id: string
          metadata: Json | null
          metric_name: string
          metric_value: number | null
        }
        Insert: {
          created_at?: string | null
          date: string
          id?: string
          metadata?: Json | null
          metric_name: string
          metric_value?: number | null
        }
        Update: {
          created_at?: string | null
          date?: string
          id?: string
          metadata?: Json | null
          metric_name?: string
          metric_value?: number | null
        }
        Relationships: []
      }
      daily_claims: {
        Row: {
          base_experience: number | null
          claim_date: string | null
          consecutive_days: number | null
          created_at: string | null
          id: string
          streak_bonus: number | null
          user_id: string | null
        }
        Insert: {
          base_experience?: number | null
          claim_date?: string | null
          consecutive_days?: number | null
          created_at?: string | null
          id?: string
          streak_bonus?: number | null
          user_id?: string | null
        }
        Update: {
          base_experience?: number | null
          claim_date?: string | null
          consecutive_days?: number | null
          created_at?: string | null
          id?: string
          streak_bonus?: number | null
          user_id?: string | null
        }
        Relationships: []
      }
      discord_users: {
        Row: {
          avatar_url: string | null
          experience: number
          id: string
          last_text_channel: string | null
          level: number
          rank: number | null
          reputation: number | null
          tx_number: string | null
          username: string | null
        }
        Insert: {
          avatar_url?: string | null
          experience?: number
          id: string
          last_text_channel?: string | null
          level?: number
          rank?: number | null
          reputation?: number | null
          tx_number?: string | null
          username?: string | null
        }
        Update: {
          avatar_url?: string | null
          experience?: number
          id?: string
          last_text_channel?: string | null
          level?: number
          rank?: number | null
          reputation?: number | null
          tx_number?: string | null
          username?: string | null
        }
        Relationships: []
      }
      equity_curves: {
        Row: {
          backtest_id: string
          created_at: string | null
          equity: number
          id: string
          timestamp: string
        }
        Insert: {
          backtest_id: string
          created_at?: string | null
          equity: number
          id?: string
          timestamp: string
        }
        Update: {
          backtest_id?: string
          created_at?: string | null
          equity?: number
          id?: string
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "equity_curves_backtest_id_fkey"
            columns: ["backtest_id"]
            isOneToOne: false
            referencedRelation: "backtest_results"
            referencedColumns: ["id"]
          },
        ]
      }
      exchange_api_keys: {
        Row: {
          created_at: string | null
          encrypted_api_key: string
          encrypted_api_secret: string
          encrypted_passphrase: string | null
          exchange: string
          id: string
          is_active: boolean | null
          label: string | null
          last_used_at: string | null
          permissions: Json | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          encrypted_api_key: string
          encrypted_api_secret: string
          encrypted_passphrase?: string | null
          exchange: string
          id?: string
          is_active?: boolean | null
          label?: string | null
          last_used_at?: string | null
          permissions?: Json | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          encrypted_api_key?: string
          encrypted_api_secret?: string
          encrypted_passphrase?: string | null
          exchange?: string
          id?: string
          is_active?: boolean | null
          label?: string | null
          last_used_at?: string | null
          permissions?: Json | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      experience_transactions: {
        Row: {
          amount: number
          created_at: string | null
          id: string
          multiplier: number | null
          source_id: number | null
          source_type: string
          user_id: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          id?: string
          multiplier?: number | null
          source_id?: number | null
          source_type: string
          user_id?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          id?: string
          multiplier?: number | null
          source_id?: number | null
          source_type?: string
          user_id?: string | null
        }
        Relationships: []
      }
      follows: {
        Row: {
          created_at: string | null
          follower_id: string
          following_id: string
          id: number
        }
        Insert: {
          created_at?: string | null
          follower_id: string
          following_id: string
          id?: number
        }
        Update: {
          created_at?: string | null
          follower_id?: string
          following_id?: string
          id?: number
        }
        Relationships: [
          {
            foreignKeyName: "follows_follower_id_fkey1"
            columns: ["follower_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "follows_following_id_fkey1"
            columns: ["following_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      hashtags: {
        Row: {
          created_at: string | null
          id: number
          name: string
          trending_score: number | null
          updated_at: string | null
          usage_count: number | null
        }
        Insert: {
          created_at?: string | null
          id?: number
          name: string
          trending_score?: number | null
          updated_at?: string | null
          usage_count?: number | null
        }
        Update: {
          created_at?: string | null
          id?: number
          name?: string
          trending_score?: number | null
          updated_at?: string | null
          usage_count?: number | null
        }
        Relationships: []
      }
      level_thresholds: {
        Row: {
          experience_required: number
          level: number
        }
        Insert: {
          experience_required: number
          level: number
        }
        Update: {
          experience_required?: number
          level?: number
        }
        Relationships: []
      }
      market_data: {
        Row: {
          close: number
          created_at: string | null
          high: number
          id: string
          low: number
          open: number
          symbol: string
          timeframe: string
          timestamp: string
          volume: number
        }
        Insert: {
          close: number
          created_at?: string | null
          high: number
          id?: string
          low: number
          open: number
          symbol: string
          timeframe: string
          timestamp: string
          volume: number
        }
        Update: {
          close?: number
          created_at?: string | null
          high?: number
          id?: string
          low?: number
          open?: number
          symbol?: string
          timeframe?: string
          timestamp?: string
          volume?: number
        }
        Relationships: []
      }
      moderation_rules: {
        Row: {
          action: string
          created_at: string | null
          description: string | null
          enabled: boolean | null
          id: number
          pattern: string
          rule_name: string
          rule_type: string
          severity: number | null
          updated_at: string | null
        }
        Insert: {
          action: string
          created_at?: string | null
          description?: string | null
          enabled?: boolean | null
          id?: number
          pattern: string
          rule_name: string
          rule_type: string
          severity?: number | null
          updated_at?: string | null
        }
        Update: {
          action?: string
          created_at?: string | null
          description?: string | null
          enabled?: boolean | null
          id?: number
          pattern?: string
          rule_name?: string
          rule_type?: string
          severity?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      monthly_returns: {
        Row: {
          backtest_id: string
          created_at: string | null
          id: string
          month: string
          return: number
        }
        Insert: {
          backtest_id: string
          created_at?: string | null
          id?: string
          month: string
          return: number
        }
        Update: {
          backtest_id?: string
          created_at?: string | null
          id?: string
          month?: string
          return?: number
        }
        Relationships: [
          {
            foreignKeyName: "monthly_returns_backtest_id_fkey"
            columns: ["backtest_id"]
            isOneToOne: false
            referencedRelation: "backtest_results"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          created_at: string | null
          data: Json | null
          expires_at: string | null
          id: string
          message: string
          priority: string | null
          read_at: string | null
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          data?: Json | null
          expires_at?: string | null
          id?: string
          message: string
          priority?: string | null
          read_at?: string | null
          title: string
          type: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          data?: Json | null
          expires_at?: string | null
          id?: string
          message?: string
          priority?: string | null
          read_at?: string | null
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      payment_history: {
        Row: {
          amount: number
          created_at: string | null
          ctt_amount: number | null
          currency: string | null
          failure_reason: string | null
          id: string
          metadata: Json | null
          payment_method: string
          processed_at: string | null
          solana_transaction_id: string | null
          status: string | null
          stripe_invoice_id: string | null
          stripe_payment_intent_id: string | null
          subscription_id: string | null
          user_id: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          ctt_amount?: number | null
          currency?: string | null
          failure_reason?: string | null
          id?: string
          metadata?: Json | null
          payment_method: string
          processed_at?: string | null
          solana_transaction_id?: string | null
          status?: string | null
          stripe_invoice_id?: string | null
          stripe_payment_intent_id?: string | null
          subscription_id?: string | null
          user_id?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          ctt_amount?: number | null
          currency?: string | null
          failure_reason?: string | null
          id?: string
          metadata?: Json | null
          payment_method?: string
          processed_at?: string | null
          solana_transaction_id?: string | null
          status?: string | null
          stripe_invoice_id?: string | null
          stripe_payment_intent_id?: string | null
          subscription_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "payment_history_subscription_id_fkey"
            columns: ["subscription_id"]
            isOneToOne: false
            referencedRelation: "user_subscriptions"
            referencedColumns: ["id"]
          },
        ]
      }
      portfolio_assets: {
        Row: {
          allocation: number
          created_at: string | null
          id: string
          portfolio_id: string
          symbol: string
          updated_at: string | null
        }
        Insert: {
          allocation: number
          created_at?: string | null
          id?: string
          portfolio_id: string
          symbol: string
          updated_at?: string | null
        }
        Update: {
          allocation?: number
          created_at?: string | null
          id?: string
          portfolio_id?: string
          symbol?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "portfolio_assets_portfolio_id_fkey"
            columns: ["portfolio_id"]
            isOneToOne: false
            referencedRelation: "portfolios"
            referencedColumns: ["id"]
          },
        ]
      }
      portfolios: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      strategies: {
        Row: {
          code: string
          created_at: string | null
          description: string | null
          id: string
          is_public: boolean | null
          name: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          code: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          name: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          code?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_public?: boolean | null
          name?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      subscription_plans: {
        Row: {
          created_at: string | null
          description: string | null
          display_name: string
          features: Json | null
          id: string
          is_active: boolean | null
          name: string
          price_monthly: number
          price_yearly: number
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          display_name: string
          features?: Json | null
          id?: string
          is_active?: boolean | null
          name: string
          price_monthly: number
          price_yearly: number
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          display_name?: string
          features?: Json | null
          id?: string
          is_active?: boolean | null
          name?: string
          price_monthly?: number
          price_yearly?: number
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: []
      }
      tags: {
        Row: {
          created_at: string
          id: number
          name: string
        }
        Insert: {
          created_at?: string
          id?: number
          name: string
        }
        Update: {
          created_at?: string
          id?: number
          name?: string
        }
        Relationships: []
      }
      tier_access_controls: {
        Row: {
          access_type: string | null
          created_at: string | null
          description: string | null
          id: string
          page_path: string
          required_tier: string
        }
        Insert: {
          access_type?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          page_path: string
          required_tier: string
        }
        Update: {
          access_type?: string | null
          created_at?: string | null
          description?: string | null
          id?: string
          page_path?: string
          required_tier?: string
        }
        Relationships: []
      }
      top_discord_users: {
        Row: {
          experience: number
          id: string
          level: number
          username: string | null
        }
        Insert: {
          experience: number
          id: string
          level: number
          username?: string | null
        }
        Update: {
          experience?: number
          id?: string
          level?: number
          username?: string | null
        }
        Relationships: []
      }
      trades: {
        Row: {
          backtest_id: string
          created_at: string | null
          entry_price: number
          entry_time: string
          exit_price: number | null
          exit_time: string | null
          id: string
          profit_loss: number | null
          profit_loss_percentage: number | null
          quantity: number
          side: string
          symbol: string
          updated_at: string | null
        }
        Insert: {
          backtest_id: string
          created_at?: string | null
          entry_price: number
          entry_time: string
          exit_price?: number | null
          exit_time?: string | null
          id?: string
          profit_loss?: number | null
          profit_loss_percentage?: number | null
          quantity: number
          side: string
          symbol: string
          updated_at?: string | null
        }
        Update: {
          backtest_id?: string
          created_at?: string | null
          entry_price?: number
          entry_time?: string
          exit_price?: number | null
          exit_time?: string | null
          id?: string
          profit_loss?: number | null
          profit_loss_percentage?: number | null
          quantity?: number
          side?: string
          symbol?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "trades_backtest_id_fkey"
            columns: ["backtest_id"]
            isOneToOne: false
            referencedRelation: "backtest_results"
            referencedColumns: ["id"]
          },
        ]
      }
      typing_indicators: {
        Row: {
          article_id: number
          expires_at: string | null
          id: string
          is_typing: boolean | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          article_id: number
          expires_at?: string | null
          id?: string
          is_typing?: boolean | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          article_id?: number
          expires_at?: string | null
          id?: string
          is_typing?: boolean | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "typing_indicators_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "typing_indicators_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_article_interactions: {
        Row: {
          article_id: number
          created_at: string
          id: number
          interaction_count: number | null
          interaction_timestamp: string | null
          is_bookmarked: boolean | null
          is_liked: boolean | null
          user_id: string
        }
        Insert: {
          article_id: number
          created_at?: string
          id?: number
          interaction_count?: number | null
          interaction_timestamp?: string | null
          is_bookmarked?: boolean | null
          is_liked?: boolean | null
          user_id: string
        }
        Update: {
          article_id?: number
          created_at?: string
          id?: number
          interaction_count?: number | null
          interaction_timestamp?: string | null
          is_bookmarked?: boolean | null
          is_liked?: boolean | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_article_interactions_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_article_interactions_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_blocks: {
        Row: {
          blocked_id: string
          blocker_id: string
          created_at: string | null
          id: string
        }
        Insert: {
          blocked_id: string
          blocker_id: string
          created_at?: string | null
          id?: string
        }
        Update: {
          blocked_id?: string
          blocker_id?: string
          created_at?: string | null
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_blocks_blocked_id_fkey"
            columns: ["blocked_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_blocks_blocker_id_fkey"
            columns: ["blocker_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_engagement_metrics: {
        Row: {
          engagement_score: number | null
          last_active: string | null
          streak_days: number | null
          total_comments: number | null
          total_likes_given: number | null
          total_likes_received: number | null
          total_posts: number | null
          total_shares: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          engagement_score?: number | null
          last_active?: string | null
          streak_days?: number | null
          total_comments?: number | null
          total_likes_given?: number | null
          total_likes_received?: number | null
          total_posts?: number | null
          total_shares?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          engagement_score?: number | null
          last_active?: string | null
          streak_days?: number | null
          total_comments?: number | null
          total_likes_given?: number | null
          total_likes_received?: number | null
          total_posts?: number | null
          total_shares?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_engagement_metrics_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_experience_interactions: {
        Row: {
          article_id: number | null
          created_at: string | null
          experience_granted: boolean | null
          giver_user_id: string | null
          id: string
          interaction_type: string
          receiver_user_id: string | null
        }
        Insert: {
          article_id?: number | null
          created_at?: string | null
          experience_granted?: boolean | null
          giver_user_id?: string | null
          id?: string
          interaction_type: string
          receiver_user_id?: string | null
        }
        Update: {
          article_id?: number | null
          created_at?: string | null
          experience_granted?: boolean | null
          giver_user_id?: string | null
          id?: string
          interaction_type?: string
          receiver_user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_experience_interactions_article_id_fkey"
            columns: ["article_id"]
            isOneToOne: false
            referencedRelation: "articles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_interests: {
        Row: {
          interest_score: number | null
          topic: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          interest_score?: number | null
          topic: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          interest_score?: number | null
          topic?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_interests_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_mutes: {
        Row: {
          created_at: string | null
          id: string
          muted_id: string
          muter_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          muted_id: string
          muter_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          muted_id?: string
          muter_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_mutes_muted_id_fkey"
            columns: ["muted_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_mutes_muter_id_fkey"
            columns: ["muter_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_presence: {
        Row: {
          last_seen: string | null
          status: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          last_seen?: string | null
          status?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          last_seen?: string | null
          status?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_presence_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_reports: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          report_type: string
          reported_id: string
          reporter_id: string
          resolution_notes: string | null
          resolved_by: string | null
          status: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          report_type: string
          reported_id: string
          reporter_id: string
          resolution_notes?: string | null
          resolved_by?: string | null
          status?: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          report_type?: string
          reported_id?: string
          reporter_id?: string
          resolution_notes?: string | null
          resolved_by?: string | null
          status?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_reports_reported_id_fkey"
            columns: ["reported_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_reports_reporter_id_fkey"
            columns: ["reporter_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
          {
            foreignKeyName: "user_reports_resolved_by_fkey"
            columns: ["resolved_by"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_settings: {
        Row: {
          account_private: boolean | null
          activity_status: boolean | null
          allow_hashtag_notifications: boolean | null
          allow_mentions: boolean | null
          allow_messages: boolean | null
          allow_recommendation_emails: boolean | null
          auto_approve_followers: boolean | null
          block_anonymous_messages: boolean | null
          content_visibility: string | null
          created_at: string
          email_enabled: boolean | null
          followers_enabled: boolean | null
          id: string
          mentions_enabled: boolean | null
          post_visibility: string | null
          push_enabled: boolean | null
          show_activity: boolean | null
          updated_at: string
          user_id: string
        }
        Insert: {
          account_private?: boolean | null
          activity_status?: boolean | null
          allow_hashtag_notifications?: boolean | null
          allow_mentions?: boolean | null
          allow_messages?: boolean | null
          allow_recommendation_emails?: boolean | null
          auto_approve_followers?: boolean | null
          block_anonymous_messages?: boolean | null
          content_visibility?: string | null
          created_at?: string
          email_enabled?: boolean | null
          followers_enabled?: boolean | null
          id?: string
          mentions_enabled?: boolean | null
          post_visibility?: string | null
          push_enabled?: boolean | null
          show_activity?: boolean | null
          updated_at?: string
          user_id: string
        }
        Update: {
          account_private?: boolean | null
          activity_status?: boolean | null
          allow_hashtag_notifications?: boolean | null
          allow_mentions?: boolean | null
          allow_messages?: boolean | null
          allow_recommendation_emails?: boolean | null
          auto_approve_followers?: boolean | null
          block_anonymous_messages?: boolean | null
          content_visibility?: string | null
          created_at?: string
          email_enabled?: boolean | null
          followers_enabled?: boolean | null
          id?: string
          mentions_enabled?: boolean | null
          post_visibility?: string | null
          push_enabled?: boolean | null
          show_activity?: boolean | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_settings_user_id_fkey1"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      user_subscriptions: {
        Row: {
          cancel_at_period_end: boolean | null
          canceled_at: string | null
          created_at: string | null
          current_period_end: string | null
          current_period_start: string | null
          id: string
          payment_method: string | null
          status: string | null
          stripe_customer_id: string | null
          stripe_subscription_id: string | null
          subscription_plan_id: string | null
          trial_end: string | null
          trial_start: string | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          payment_method?: string | null
          status?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_plan_id?: string | null
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          created_at?: string | null
          current_period_end?: string | null
          current_period_start?: string | null
          id?: string
          payment_method?: string | null
          status?: string | null
          stripe_customer_id?: string | null
          stripe_subscription_id?: string | null
          subscription_plan_id?: string | null
          trial_end?: string | null
          trial_start?: string | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_subscriptions_subscription_plan_id_fkey"
            columns: ["subscription_plan_id"]
            isOneToOne: false
            referencedRelation: "subscription_plans"
            referencedColumns: ["id"]
          },
        ]
      }
      user_subscriptions_ctt: {
        Row: {
          amount: number
          created_at: string | null
          creator_id: string | null
          currency: string | null
          id: string
          next_payment_date: string | null
          payment_frequency: string | null
          solana_transaction_id: string | null
          status: string | null
          subscriber_id: string | null
        }
        Insert: {
          amount: number
          created_at?: string | null
          creator_id?: string | null
          currency?: string | null
          id?: string
          next_payment_date?: string | null
          payment_frequency?: string | null
          solana_transaction_id?: string | null
          status?: string | null
          subscriber_id?: string | null
        }
        Update: {
          amount?: number
          created_at?: string | null
          creator_id?: string | null
          currency?: string | null
          id?: string
          next_payment_date?: string | null
          payment_frequency?: string | null
          solana_transaction_id?: string | null
          status?: string | null
          subscriber_id?: string | null
        }
        Relationships: []
      }
      user_trust_scores: {
        Row: {
          content_removed: number | null
          false_reports: number | null
          last_violation: string | null
          spam_reports: number | null
          trust_score: number | null
          updated_at: string | null
          user_id: string
          valid_contributions: number | null
        }
        Insert: {
          content_removed?: number | null
          false_reports?: number | null
          last_violation?: string | null
          spam_reports?: number | null
          trust_score?: number | null
          updated_at?: string | null
          user_id: string
          valid_contributions?: number | null
        }
        Update: {
          content_removed?: number | null
          false_reports?: number | null
          last_violation?: string | null
          spam_reports?: number | null
          trust_score?: number | null
          updated_at?: string | null
          user_id?: string
          valid_contributions?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "user_trust_scores_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["user_id"]
          },
        ]
      }
      users: {
        Row: {
          article_count: number | null
          avatar_position: Json | null
          avatar_url: string | null
          banner_position: Json | null
          banner_url: string | null
          bio: string | null
          consecutive_claim_boost: number | null
          created_at: string
          ctt_balance: number | null
          daily_claim_streak: number | null
          email: string | null
          experience_active_until: string | null
          experience_points: number | null
          followers: string[] | null
          followers_count: number | null
          following_count: number | null
          full_name: string | null
          governance_votes: number | null
          id: number
          is_following: string[] | null
          last_daily_claim: string | null
          level: number | null
          points: number | null
          premium_tier: string | null
          reputation_score: number | null
          solana_wallet_address: string | null
          updated_at: string | null
          user_id: string
          username: string | null
        }
        Insert: {
          article_count?: number | null
          avatar_position?: Json | null
          avatar_url?: string | null
          banner_position?: Json | null
          banner_url?: string | null
          bio?: string | null
          consecutive_claim_boost?: number | null
          created_at?: string
          ctt_balance?: number | null
          daily_claim_streak?: number | null
          email?: string | null
          experience_active_until?: string | null
          experience_points?: number | null
          followers?: string[] | null
          followers_count?: number | null
          following_count?: number | null
          full_name?: string | null
          governance_votes?: number | null
          id?: number
          is_following?: string[] | null
          last_daily_claim?: string | null
          level?: number | null
          points?: number | null
          premium_tier?: string | null
          reputation_score?: number | null
          solana_wallet_address?: string | null
          updated_at?: string | null
          user_id: string
          username?: string | null
        }
        Update: {
          article_count?: number | null
          avatar_position?: Json | null
          avatar_url?: string | null
          banner_position?: Json | null
          banner_url?: string | null
          bio?: string | null
          consecutive_claim_boost?: number | null
          created_at?: string
          ctt_balance?: number | null
          daily_claim_streak?: number | null
          email?: string | null
          experience_active_until?: string | null
          experience_points?: number | null
          followers?: string[] | null
          followers_count?: number | null
          following_count?: number | null
          full_name?: string | null
          governance_votes?: number | null
          id?: number
          is_following?: string[] | null
          last_daily_claim?: string | null
          level?: number | null
          points?: number | null
          premium_tier?: string | null
          reputation_score?: number | null
          solana_wallet_address?: string | null
          updated_at?: string | null
          user_id?: string
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_premium_tier_fkey"
            columns: ["premium_tier"]
            isOneToOne: false
            referencedRelation: "subscription_plans"
            referencedColumns: ["name"]
          },
        ]
      }
      voice_activity: {
        Row: {
          active: boolean
          discord_id: string
          exp: boolean | null
          id: number
          is_deafened: boolean | null
          is_muted: boolean | null
          joined_at: string | null
          muted_at: string | null
          username: string | null
          voice_channel_id: string | null
        }
        Insert: {
          active?: boolean
          discord_id: string
          exp?: boolean | null
          id?: never
          is_deafened?: boolean | null
          is_muted?: boolean | null
          joined_at?: string | null
          muted_at?: string | null
          username?: string | null
          voice_channel_id?: string | null
        }
        Update: {
          active?: boolean
          discord_id?: string
          exp?: boolean | null
          id?: never
          is_deafened?: boolean | null
          is_muted?: boolean | null
          joined_at?: string | null
          muted_at?: string | null
          username?: string | null
          voice_channel_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "voice_activity_discord_id_fkey"
            columns: ["discord_id"]
            isOneToOne: true
            referencedRelation: "discord_users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "voice_activity_username_fkey"
            columns: ["username"]
            isOneToOne: true
            referencedRelation: "discord_users"
            referencedColumns: ["username"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      award_experience_for_comment_interaction: {
        Args: {
          p_user_id: string
          p_comment_id: number
          p_comment_author_id: string
          p_interaction_type: string
        }
        Returns: Json
      }
      award_interaction_experience: {
        Args: {
          p_giver_user_id: string
          p_receiver_user_id: string
          p_article_id: number
          p_interaction_type: string
        }
        Returns: Json
      }
      check_tier_access: {
        Args: { p_user_id: string; p_page_path: string; p_access_type?: string }
        Returns: boolean
      }
      check_username_availability: {
        Args: { p_username: string; p_current_user_id: string }
        Returns: boolean
      }
      cleanup_expired_notifications: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_expired_typing_indicators: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      cleanup_stale_sessions: {
        Args: { p_hours?: number }
        Returns: number
      }
      create_article_with_tags: {
        Args: {
          p_author_id: string
          p_content: string
          p_tags: string[]
          p_image_url: string
          p_title: string
        }
        Returns: {
          id: number
          title: string
          content: string
          author_id: string
          image_url: string
          created_at: string
          tags: string[]
          views: number
          likes: number
          shares: number
          comments_count: number
        }[]
      }
      create_comment: {
        Args: {
          p_user_id: string
          p_article_id: number
          p_content: string
          p_parent_id?: number
        }
        Returns: Json
      }
      create_notification: {
        Args: {
          p_user_id: string
          p_type: string
          p_title: string
          p_message: string
          p_data?: Json
        }
        Returns: string
      }
      create_or_update_discord_user: {
        Args: { p_discord_id: string; p_username: string; p_channel_id: string }
        Returns: undefined
      }
      end_voice_session: {
        Args: { p_discord_id: string }
        Returns: undefined
      }
      fix_muted_users: {
        Args: Record<PropertyKey, never>
        Returns: {
          discord_id: string
          fixed: boolean
          issue: string
        }[]
      }
      get_active_voice_session: {
        Args: { p_discord_id: string }
        Returns: {
          id: number
          voice_channel_id: string
          joined_at: string
          is_muted: boolean
          is_deafened: boolean
          muted_at: string
          exp: boolean
        }[]
      }
      get_article_analytics: {
        Args: { p_user_id: string; p_time_range?: string; p_limit?: number }
        Returns: {
          id: number
          title: string
          views: number
          likes: number
          comments: number
          shares: number
          engagement_rate: number
          created_at: string
        }[]
      }
      get_article_comments: {
        Args: { p_article_id: number; p_limit?: number; p_offset?: number }
        Returns: {
          comments: Json
          total_count: number
        }[]
      }
      get_article_stats: {
        Args: { p_author_id: string }
        Returns: Json
      }
      get_articles_with_interactions: {
        Args: {
          p_user_id: string
          p_last_timestamp?: string
          p_limit?: number
          p_time_range?: string
          p_sort_field?: string
          p_sort_direction?: string
        }
        Returns: {
          id: number
          title: string
          content: string
          author_id: string
          author: string
          author_avatar: string
          created_at: string
          updated_at: string
          image_url: string
          likes: number
          shares: number
          comments_count: number
          is_liked: boolean
          is_bookmarked: boolean
          tags: string[]
        }[]
      }
      get_engagement_trends: {
        Args: { p_user_id: string; p_time_range?: string }
        Returns: {
          date: string
          views: number
          likes: number
          comments: number
        }[]
      }
      get_experience_leaderboard: {
        Args: { p_limit?: number }
        Returns: {
          user_id: string
          username: string
          experience_points: number
          level: number
          avatar_url: string
        }[]
      }
      get_platform_analytics: {
        Args: { p_start_date?: string; p_end_date?: string }
        Returns: {
          metric_name: string
          metric_value: number
          change_percentage: number
        }[]
      }
      get_popular_tags: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: number
          name: string
          article_count: number
        }[]
      }
      get_profile_update_status: {
        Args: { p_user_id: string }
        Returns: boolean
      }
      get_trending_articles: {
        Args: { p_hours?: number }
        Returns: {
          id: number
          title: string
          views: number
        }[]
      }
      get_user_analytics_overview: {
        Args: { p_user_id: string; p_time_range?: string }
        Returns: {
          total_articles: number
          total_views: number
          total_likes: number
          total_comments: number
          total_followers: number
          avg_engagement_rate: number
          top_performing_article: string
          most_engaging_topic: string
        }[]
      }
      get_user_experience_summary: {
        Args: { p_user_id: string }
        Returns: Json
      }
      get_user_subscription_status: {
        Args: { p_user_id: string }
        Returns: Json
      }
      get_voice_statistics: {
        Args: {
          p_discord_id: string
          p_start_date?: string
          p_end_date?: string
        }
        Returns: {
          total_time_minutes: number
          active_time_minutes: number
          muted_time_minutes: number
          deafened_time_minutes: number
        }[]
      }
      increment_article_metric: {
        Args: { p_article_id: number; p_metric: string }
        Returns: Json
      }
      increment_article_views: {
        Args: { article_id: number }
        Returns: undefined
      }
      process_ctt_transaction: {
        Args: {
          p_user_id: string
          p_transaction_type: string
          p_amount: number
          p_reference_id?: string
          p_solana_transaction_id?: string
          p_from_wallet?: string
          p_to_wallet?: string
          p_description?: string
        }
        Returns: Json
      }
      process_daily_claim: {
        Args: { p_user_id: string }
        Returns: Json
      }
      process_hashtags: {
        Args: {
          p_content: string
          p_content_type: string
          p_content_id: number
        }
        Returns: number
      }
      process_mentions: {
        Args: {
          p_content: string
          p_content_type: string
          p_content_id: number
          p_author_id: string
        }
        Returns: number
      }
      process_subscription_update: {
        Args: {
          p_user_id: string
          p_plan_name: string
          p_stripe_subscription_id?: string
          p_stripe_customer_id?: string
          p_status?: string
          p_current_period_start?: string
          p_current_period_end?: string
        }
        Returns: Json
      }
      record_article_view: {
        Args: { p_article_id: number }
        Returns: undefined
      }
      save_article_draft: {
        Args: {
          p_author_id: string
          p_title: string
          p_content: string
          p_tags: string[]
          p_image_url: string
        }
        Returns: number
      }
      search_articles: {
        Args: { search_query: string }
        Returns: {
          id: number
          title: string
          created_at: string
        }[]
      }
      search_hashtags: {
        Args: { p_query: string; p_limit?: number }
        Returns: {
          id: number
          name: string
          usage_count: number
          trending_score: number
        }[]
      }
      search_users_for_mentions: {
        Args: { p_query: string; p_limit?: number }
        Returns: {
          user_id: string
          username: string
          avatar_url: string
          follower_count: number
        }[]
      }
      should_receive_xp: {
        Args: { p_discord_id: string }
        Returns: boolean
      }
      start_voice_session: {
        Args:
          | { p_discord_id: string; p_channel_id: string }
          | {
              p_discord_id: string
              p_channel_id: string
              p_is_muted?: boolean
              p_is_deafened?: boolean
            }
        Returns: undefined
      }
      toggle_article_interaction: {
        Args: {
          p_user_id: string
          p_article_id: number
          p_interaction_type: string
        }
        Returns: Json
      }
      toggle_follow: {
        Args: { p_follower_id: string; p_following_id: string }
        Returns: Json
      }
      track_article_share: {
        Args: { p_article_id: number; p_user_id?: string }
        Returns: undefined
      }
      update_article: {
        Args: {
          p_article_id: number
          p_author_id: string
          p_title?: string
          p_content?: string
          p_tags?: string[]
          p_image_url?: string
        }
        Returns: Json
      }
      update_user_engagement: {
        Args: { p_user_id: string; p_action: string }
        Returns: undefined
      }
      update_user_profile: {
        Args: {
          p_user_id: string
          p_username: string
          p_bio: string
          p_avatar_url: string
          p_banner_url: string
          p_avatar_position: Json
          p_banner_position: Json
        }
        Returns: {
          article_count: number | null
          avatar_position: Json | null
          avatar_url: string | null
          banner_position: Json | null
          banner_url: string | null
          bio: string | null
          consecutive_claim_boost: number | null
          created_at: string
          ctt_balance: number | null
          daily_claim_streak: number | null
          email: string | null
          experience_active_until: string | null
          experience_points: number | null
          followers: string[] | null
          followers_count: number | null
          following_count: number | null
          full_name: string | null
          governance_votes: number | null
          id: number
          is_following: string[] | null
          last_daily_claim: string | null
          level: number | null
          points: number | null
          premium_tier: string | null
          reputation_score: number | null
          solana_wallet_address: string | null
          updated_at: string | null
          user_id: string
          username: string | null
        }
      }
      update_voice_status: {
        Args: {
          p_discord_id: string
          p_is_muted: boolean
          p_is_deafened: boolean
        }
        Returns: undefined
      }
      update_voice_xp: {
        Args: { p_discord_id: string; p_xp_amount?: number }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const
