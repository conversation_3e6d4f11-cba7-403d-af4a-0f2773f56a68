# Solana Payment Setup Guide

## Treasury Wallet Configuration

To properly configure Solana Pay for your CryptoTalks application, you need to set up treasury wallets to receive payments.

### 1. Generate Solana Wallets

You'll need two wallets:
- **Development Wallet**: For testing and development
- **Production Wallet**: For live payments

#### Option A: Using Solana CLI (Recommended)
```bash
# Install Solana CLI if not already installed
sh -c "$(curl -sSfL https://release.solana.com/v1.17.0/install)"

# Generate development wallet
solana-keygen new --outfile ~/solana-dev-wallet.json

# Generate production wallet  
solana-keygen new --outfile ~/solana-prod-wallet.json

# Get the public keys (wallet addresses)
solana-keygen pubkey ~/solana-dev-wallet.json
solana-keygen pubkey ~/solana-prod-wallet.json
```

#### Option B: Using Phantom/Solflare Wallet
1. Create new wallets in Phantom or Solflare
2. Export the public addresses
3. **Important**: Keep private keys secure and never commit them to version control

### 2. Environment Configuration

Create a `.env.local` file in your project root:

```bash
# Copy from .env.example
cp .env.example .env.local
```

Add your wallet addresses to `.env.local`:

```bash
# Your existing Supabase config
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Solana Treasury Wallets
NEXT_PUBLIC_SOLANA_TREASURY_WALLET_DEV=YOUR_DEV_WALLET_ADDRESS_HERE
NEXT_PUBLIC_SOLANA_TREASURY_WALLET_PROD=YOUR_PROD_WALLET_ADDRESS_HERE
```

### 3. Wallet Address Format

Solana wallet addresses are 32-44 character Base58 strings, for example:
```
HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH
```

### 4. Testing Your Setup

#### Development Testing
1. Set your development environment
2. Ensure you're using devnet/testnet for testing
3. Test with small amounts first

#### Production Deployment
1. Verify your production wallet address
2. Test on mainnet with minimal amounts
3. Monitor transactions in Solana Explorer

### 5. Security Best Practices

#### DO:
- ✅ Use environment variables for wallet addresses
- ✅ Keep private keys secure and offline
- ✅ Use different wallets for dev/prod
- ✅ Test thoroughly on devnet first
- ✅ Monitor transactions regularly

#### DON'T:
- ❌ Commit private keys to git
- ❌ Use the same wallet for dev and prod
- ❌ Share private keys in plain text
- ❌ Test with large amounts initially

### 6. Monitoring Payments

You can monitor incoming payments using:

1. **Solana Explorer**: https://explorer.solana.com/
2. **Solscan**: https://solscan.io/
3. **Solana Beach**: https://solanabeach.io/

Search for your wallet address to see all transactions.

### 7. Webhook Integration (Optional)

For production applications, consider setting up webhooks to automatically process payments:

```typescript
// Example webhook endpoint
export async function POST(request: Request) {
  const { signature, memo, amount } = await request.json();
  
  // Verify transaction on blockchain
  // Update user subscription status
  // Send confirmation email
}
```

### 8. Error Handling

The application includes automatic fallbacks:
- If wallet addresses are invalid, it will log errors
- Development mode shows warnings for missing wallets
- Production mode throws errors for invalid configurations

### 9. Testing Checklist

Before going live:
- [ ] Development wallet receives test payments
- [ ] QR codes generate correctly
- [ ] Price calculations are accurate
- [ ] Environment variables are set correctly
- [ ] Production wallet is funded for transaction fees
- [ ] Monitoring systems are in place

### 10. Support

If you encounter issues:
1. Check Solana network status
2. Verify wallet addresses are valid
3. Ensure environment variables are loaded
4. Test with minimal amounts first
5. Monitor transaction logs

### Example Valid Wallet Addresses

Here are example addresses (for reference only, generate your own):

```bash
# Development (testnet/devnet)
NEXT_PUBLIC_SOLANA_TREASURY_WALLET_DEV=HN7cABqLq46Es1jh92dQQisAq662SmxELLLsHHe4YWrH

# Production (mainnet)
NEXT_PUBLIC_SOLANA_TREASURY_WALLET_PROD=*******************************************
```

Replace these with your actual wallet addresses.
