# Update these with your Supabase details from your project settings > API
# https://app.supabase.com/project/_/settings/api
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Solana Treasury Wallets for receiving payments
# Development/Testing wallet address (replace with your actual development wallet)
NEXT_PUBLIC_SOLANA_TREASURY_WALLET_DEV=your-development-wallet-address

# Production treasury wallet address (replace with your actual production wallet)
NEXT_PUBLIC_SOLANA_TREASURY_WALLET_PROD=your-production-wallet-address

# =============================================================================
# API KEYS FOR CRYPTO DATA INTEGRATION
# =============================================================================

# 🚀 BINANCE API (PRIMARY - FREE with 1,200 requests/min!)
# No API key needed for public market data endpoints
# Get API key only if you need private trading features: https://www.binance.com/en/binance-api
# Public endpoints: FREE with 1,200 requests/minute (40x higher than CoinGecko!)
BINANCE_API_KEY=your_binance_api_key_here_optional
BINANCE_API_SECRET=your_binance_api_secret_here_optional

# CoinGecko API (Secondary/Fallback for coins not on Binance)
# Get your free API key at: https://www.coingecko.com/en/api/pricing
# Free tier: 30 calls/min, 10,000 calls/month
# Pro tier: $129/month, 500 calls/min, 500,000 calls/month
COINGECKO_API_KEY=your_coingecko_api_key_here

# CoinGecko API Base URL (Demo vs Pro)
# Demo (free): https://api.coingecko.com/api/v3
# Pro: https://pro-api.coingecko.com/api/v3
COINGECKO_API_BASE_URL=https://api.coingecko.com/api/v3

# CryptoNews API (Crypto-specific news)
# Get your API key at: https://cryptonews-api.com/register
# Free tier available with rate limits
NEWSDATA_API_KEY=your_NEWSDATA_API_key_here

# NewsAPI (Backup news source)
# Get your API key at: https://newsapi.org/register
# Free tier: 1,000 requests/month
NEWS_API_KEY=your_news_api_key_here

# CoinMarketCap API (Optional backup)
# Get your API key at: https://coinmarketcap.com/api/
# Free tier: 10,000 calls/month
# Note: Not needed since Binance provides better free data
COINMARKETCAP_API_KEY=your_coinmarketcap_api_key_here

# Messari API (Enhanced on-chain analytics)
# Get your API key at: https://messari.io/api
# Free tier: 20 calls/min, 1,000 calls/month
# Provides comprehensive on-chain metrics, developer activity, and fundamental data
MESSARI_API_KEY=your_messari_api_key_here

# DexScreener API (Free DEX data)
# No API key required - completely free
# Provides real-time DEX pair data, volume, and liquidity across all DEXes
# Rate limit: ~300 requests/minute (very generous)

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Rate limiting configuration
API_RATE_LIMIT_REQUESTS_PER_MINUTE=30
API_CACHE_TTL_SECONDS=300

# Environment (development, staging, production)
NODE_ENV=development

# Enable/disable real-time features
ENABLE_REAL_TIME_DATA=true
ENABLE_WEBSOCKET_CONNECTIONS=true

# Enable news scheduler (twice daily cache refresh)
ENABLE_NEWS_SCHEDULER=true

# Fallback to mock data when APIs are unavailable
FALLBACK_TO_MOCK_DATA=true
