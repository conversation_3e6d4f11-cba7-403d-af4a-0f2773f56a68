'use client';

import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Hook to manage lazy loading of component data
 * Only loads data when component is active and visible
 * Provides caching to avoid unnecessary re-fetches
 */
export interface LazyComponentDataOptions {
  /** Whether to load data immediately when active */
  loadOnActive?: boolean;
  /** Cache duration in milliseconds */
  cacheDuration?: number;
  /** Whether to use static data for non-real-time requirements */
  useStaticData?: boolean;
  /** Debounce delay for load triggers */
  debounceDelay?: number;
}

export interface LazyComponentDataState<T> {
  data: T | null;
  isLoading: boolean;
  isError: boolean;
  error: string | null;
  lastUpdated: number | null;
  source: 'static' | 'api' | 'cache' | null;
}

const DEFAULT_OPTIONS: LazyComponentDataOptions = {
  loadOnActive: true,
  cacheDuration: 5 * 60 * 1000, // 5 minutes
  useStaticData: true,
  debounceDelay: 100,
};

export function useLazyComponentData<T>(
  dataFetcher: () => Promise<T>,
  staticDataFetcher?: () => Promise<T> | T,
  isActive: boolean = false,
  options: LazyComponentDataOptions = {}
): [
  LazyComponentDataState<T>,
  {
    refresh: () => Promise<void>;
    reset: () => void;
    forceReload: () => Promise<void>;
  }
] {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const [state, setState] = useState<LazyComponentDataState<T>>({
    data: null,
    isLoading: false,
    isError: false,
    error: null,
    lastUpdated: null,
    source: null,
  });

  const mountedRef = useRef(true);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const cacheRef = useRef<{ data: T; timestamp: number; source: 'static' | 'api' } | null>(null);

  // Check if cached data is still valid
  const isCacheValid = useCallback(() => {
    if (!cacheRef.current) return false;
    return Date.now() - cacheRef.current.timestamp < opts.cacheDuration!;
  }, [opts.cacheDuration]);

  // Load data with prioritization: cache -> static -> api
  const loadData = useCallback(async (forceReload: boolean = false) => {
    if (!mountedRef.current) return;

    // Clear any pending debounce
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }

    // Use cache if valid and not forcing reload
    if (!forceReload && isCacheValid() && cacheRef.current) {
      console.log('📦 Using cached data');
      setState(prev => ({
        ...prev,
        data: cacheRef.current!.data,
        isLoading: false,
        isError: false,
        error: null,
        lastUpdated: cacheRef.current!.timestamp,
        source: 'cache',
      }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, isError: false, error: null }));

    try {
      let data: T;
      let source: 'static' | 'api' = 'api';

      // Try static data first if enabled and available
      if (opts.useStaticData && staticDataFetcher && !forceReload) {
        try {
          console.log('📊 Loading static data...');
          const staticResult = staticDataFetcher();
          data = staticResult instanceof Promise ? await staticResult : staticResult;
          source = 'static';
        } catch (staticError) {
          console.warn('⚠️ Static data fetch failed, falling back to API:', staticError);
          data = await dataFetcher();
          source = 'api';
        }
      } else {
        console.log('🔄 Loading data from API...');
        data = await dataFetcher();
        source = 'api';
      }

      if (mountedRef.current) {
        const timestamp = Date.now();
        
        // Update cache
        cacheRef.current = { data, timestamp, source };
        
        setState(prev => ({
          ...prev,
          data,
          isLoading: false,
          isError: false,
          error: null,
          lastUpdated: timestamp,
          source,
        }));
      }
    } catch (error) {
      if (mountedRef.current) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('❌ Data loading failed:', errorMessage);
        
        setState(prev => ({
          ...prev,
          isLoading: false,
          isError: true,
          error: errorMessage,
          source: null,
        }));
      }
    }
  }, [dataFetcher, staticDataFetcher, opts.useStaticData, isCacheValid]);

  // Debounced load function
  const debouncedLoad = useCallback((forceReload: boolean = false) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      loadData(forceReload);
    }, opts.debounceDelay);
  }, [loadData, opts.debounceDelay]);

  // Actions
  const refresh = useCallback(async () => {
    await loadData(false);
  }, [loadData]);

  const forceReload = useCallback(async () => {
    await loadData(true);
  }, [loadData]);

  const reset = useCallback(() => {
    setState({
      data: null,
      isLoading: false,
      isError: false,
      error: null,
      lastUpdated: null,
      source: null,
    });
    cacheRef.current = null;
  }, []);

  // Auto-load when component becomes active
  useEffect(() => {
    if (isActive && opts.loadOnActive) {
      debouncedLoad(false);
    }

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, [isActive, opts.loadOnActive, debouncedLoad]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  return [state, { refresh, reset, forceReload }];
}

/**
 * Hook specifically for components that need market data
 * Automatically uses static data from mock-data-service for non-real-time needs
 */
export function useLazyMarketData(isActive: boolean = false, useRealTime: boolean = false) {
  const marketDataFetcher = useCallback(async () => {
    if (useRealTime) {
      const { clientAPIService } = await import('@/utils/client-api-service');
      const response = await clientAPIService.getMarketData();
      return response.data || [];
    } else {
      throw new Error('Use static data instead for non-real-time market data');
    }
  }, [useRealTime]);

  const staticDataFetcher = useCallback(async () => {
    const { getAllCoinsData } = await import('@/utils/mock-data-service');
    return await getAllCoinsData();
  }, []);

  return useLazyComponentData(
    marketDataFetcher,
    staticDataFetcher,
    isActive,
    {
      useStaticData: !useRealTime,
      cacheDuration: useRealTime ? 30000 : 5 * 60 * 1000, // 30s for real-time, 5min for static
    }
  );
}

/**
 * Hook specifically for components that need analytics data
 * Uses static/cached data for better performance
 */
export function useLazyAnalyticsData(isActive: boolean = false, userId?: string) {
  const analyticsDataFetcher = useCallback(async () => {
    // In a real implementation, this would call Supabase
    throw new Error('Use static analytics data for development');
  }, []);

  const staticAnalyticsDataFetcher = useCallback(() => {
    // Generate consistent static analytics data
    const baseValue = userId ? parseInt(userId.slice(-3), 16) % 1000 : 500;
    return {
      totalArticles: 5 + (baseValue % 15),
      totalLikes: baseValue * 2,
      totalComments: Math.floor(baseValue * 0.3),
      totalFollowers: 100 + (baseValue % 500),
      avgEngagementRate: 3 + ((baseValue % 100) / 10),
      lastUpdated: Date.now(),
    };
  }, [userId]);

  return useLazyComponentData(
    analyticsDataFetcher,
    staticAnalyticsDataFetcher,
    isActive,
    {
      useStaticData: true,
      cacheDuration: 10 * 60 * 1000, // 10 minutes for analytics
    }
  );
}