'use client';

import { useEffect, useState } from 'react';
import { createClient } from '@/utils/supabase/client';
import { useRouter } from 'next/navigation';

// Simple interfaces without complex type dependencies
export interface TierAccessInfo {
  hasTierAccess: boolean;
  userTier: string;
  requiredTier: string;
  tierLevel: number;
  requiredLevel: number;
  canUpgrade: boolean;
}

export interface UserExperienceInfo {
  user_id: string;
  tier: string;
  experience_points: number;
  level: number;
  next_level_exp: number;
  progress_percentage: number;
  daily_claim_streak: number;
  consecutive_claim_boost: number;
  experience_active: boolean;
  experience_active_until: string;
  can_claim_daily: boolean;
  time_until_next_claim: string;
  reputation_score: number;
}

export function useTierAccess() {
  const [loading, setLoading] = useState(true);
  const [userExperience, setUserExperience] = useState<UserExperienceInfo | null>(null);
  const supabase = createClient();
  const router = useRouter();

  const getTierLevel = (tier: string): number => {
    switch (tier) {
      case 'tier1': return 1;
      case 'tier2': return 2;
      case 'tier3': return 3;
      case 'tier4': return 4;
      default: return 0;
    }
  };

  const getTierName = (tier: string): string => {
    switch (tier) {
      case 'tier1': return 'Contributor';
      case 'tier2': return 'Analyst';
      case 'tier3': return 'Baller';
      case 'tier4': return 'Elite';
      default: return 'Free';
    }
  };

  const checkTierAccess = async (
    pagePath: string, 
    accessType: 'view' | 'interact' | 'create' = 'view'
  ): Promise<TierAccessInfo> => {
    try {
      // First check if we have cached user experience data with tier info
      if (userExperience) {
        const userTier = userExperience.tier;
        const pageRequirements: Record<string, string> = {
          '/ctn/charts': 'tier1',
          '/ctn/heatmap': 'tier1',
          '/ctn/tools/calculator': 'tier1',
          '/ctn/tools/analytics': 'tier1',
          // Add more as needed
        };

        const requiredTier = pageRequirements[pagePath] || 'tier1';
        const hasAccess = getTierLevel(userTier) >= getTierLevel(requiredTier);

        return {
          hasTierAccess: Boolean(hasAccess),
          userTier,
          requiredTier,
          tierLevel: getTierLevel(userTier),
          requiredLevel: getTierLevel(requiredTier),
          canUpgrade: getTierLevel(userTier) < getTierLevel(requiredTier)
        };
      }

      // Fallback to database query if no cached data
      const { data: { user } } = await (supabase as any).auth.getUser();
      
      if (!user) {
        return {
          hasTierAccess: false,
          userTier: 'tier0',
          requiredTier: 'tier1',
          tierLevel: 0,
          requiredLevel: 1,
          canUpgrade: true
        };
      }

      // Get user subscription info directly from tables
      const { data: subscription, error: subError } = await supabase
        .from('user_subscriptions')
        .select('subscription_plan_id, status')
        .eq('user_id', user.id)
        .eq('status', 'active')
        .maybeSingle(); // Use maybeSingle instead of single to avoid errors when no record exists

      // Get user's premium tier from users table
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('premium_tier')
        .eq('user_id', user.id)
        .maybeSingle(); // Use maybeSingle instead of single

      // If there are errors, log them but continue with defaults
      if (subError && subError.code !== 'PGRST116') {
        console.warn('Subscription query error:', subError);
      }
      if (userError && userError.code !== 'PGRST116') {
        console.warn('User data query error:', userError);
      }

      const userTier = userData?.premium_tier || subscription?.subscription_plan_id || 'tier0';

      // Define tier requirements for different pages (since we don't have page_access_control table)
      const pageRequirements: Record<string, string> = {
        '/ctn/charts': 'tier1',
        '/ctn/heatmap': 'tier1',
        '/ctn/tools/calculator': 'tier1',
        '/ctn/tools/analytics': 'tier1',
        // Add more as needed
      };

      const requiredTier = pageRequirements[pagePath] || 'tier1'; // Default to tier1 for premium features
      const hasAccess = getTierLevel(userTier) >= getTierLevel(requiredTier);

      return {
        hasTierAccess: Boolean(hasAccess),
        userTier,
        requiredTier,
        tierLevel: getTierLevel(userTier),
        requiredLevel: getTierLevel(requiredTier),
        canUpgrade: getTierLevel(userTier) < getTierLevel(requiredTier)
      };
    } catch (error) {
      console.error('Error checking tier access:', error);
      return {
        hasTierAccess: false,
        userTier: 'tier0',
        requiredTier: 'tier1',
        tierLevel: 0,
        requiredLevel: 1,
        canUpgrade: true
      };
    }
  };

  const getUserExperience = async (): Promise<UserExperienceInfo | null> => {
    try {
      const { data: { user } } = await (supabase as any).auth.getUser();
      
      if (!user) return null;

      console.log('🔍 Making database query for user experience:', user.id);

      // Get user experience data directly from users table
      const { data: userExp } = await supabase
        .from('users')
        .select(`
          user_id,
          premium_tier,
          experience_points,
          level,
          daily_claim_streak,
          last_daily_claim,
          experience_active_until,
          consecutive_claim_boost,
          reputation_score
        `)
        .eq('user_id', user.id)
        .single();

      if (!userExp) {
        // Return default experience data if no record exists
        return {
          user_id: user.id,
          tier: 'tier0',
          experience_points: 0,
          level: 1,
          next_level_exp: 100,
          progress_percentage: 0,
          daily_claim_streak: 0,
          consecutive_claim_boost: 0,
          experience_active: false,
          experience_active_until: new Date().toISOString(),
          can_claim_daily: false, // Free users cannot claim daily rewards
          time_until_next_claim: 'Upgrade Required',
          reputation_score: 0
        };
      }

      const nextLevelExp = (userExp.level || 1) * 100;
      const progressPercentage = ((userExp.experience_points || 0) / nextLevelExp) * 100;
      
      // Check if user has tier1+ for experience system access
      const userTier = userExp.premium_tier || 'tier0';
      const hasTierAccess = getTierLevel(userTier) >= 1;
      
      // Check if experience system is currently active
      const experienceActiveUntil = userExp.experience_active_until ? new Date(userExp.experience_active_until) : null;
      const experienceActive = experienceActiveUntil && experienceActiveUntil > new Date();
      
      // Users can activate experience system if they have tier access and it's not already active
      const canActivateExperience = hasTierAccess && !experienceActive;

      return {
        user_id: user.id,
        tier: userExp.premium_tier || 'tier0',
        experience_points: userExp.experience_points || 0,
        level: userExp.level || 1,
        next_level_exp: nextLevelExp,
        progress_percentage: Math.round(progressPercentage),
        daily_claim_streak: userExp.daily_claim_streak || 0,
        consecutive_claim_boost: userExp.consecutive_claim_boost || 0,
        experience_active: Boolean(experienceActive),
        experience_active_until: userExp.experience_active_until || new Date().toISOString(),
        can_claim_daily: canActivateExperience,
        time_until_next_claim: !hasTierAccess ? 'Upgrade to Tier 1+' : (experienceActive ? `Active until ${experienceActiveUntil?.toLocaleTimeString()}` : 'Activate XP System'),
        reputation_score: userExp.reputation_score || 0
      };
    } catch (error) {
      console.error('Error fetching user experience:', error);
      return null;
    }
  };

  const processDailyClaim = async () => {
    try {
      const { data: { user }, error: authError } = await (supabase as any).auth.getUser();
      
      if (authError) {
        console.error('Auth error:', authError);
        return {
          success: false,
          message: 'Authentication failed',
          experience_gained: 0
        };
      }

      if (!user) {
        return {
          success: false,
          message: 'User not authenticated',
          experience_gained: 0
        };
      }

      console.log('Activating experience system for user:', user.id);

      // Get existing user data from users table including premium_tier
      const { data: userData, error: selectError } = await supabase
        .from('users')
        .select('experience_points, level, daily_claim_streak, last_daily_claim, premium_tier, experience_active_until')
        .eq('user_id', user.id)
        .maybeSingle();

      if (selectError) {
        console.error('Error fetching user data:', selectError);
        return {
          success: false,
          message: 'Failed to fetch user data',
          experience_gained: 0
        };
      }

      console.log('Current user data:', userData);

      // Check if user has tier1+ access for experience system
      const userTier = userData?.premium_tier || 'tier0';
      const userTierLevel = getTierLevel(userTier);
      
      if (userTierLevel < 1) {
        return {
          success: false,
          message: 'Experience system is only available for Tier 1+ subscribers. Please upgrade your account to access this feature.',
          experience_gained: 0
        };
      }

      // Check if experience system is already active
      const now = new Date();
      const experienceActiveUntil = userData?.experience_active_until ? new Date(userData.experience_active_until) : null;

      if (experienceActiveUntil && experienceActiveUntil > now) {
        const hoursRemaining = Math.ceil((experienceActiveUntil.getTime() - now.getTime()) / (1000 * 60 * 60));
        return {
          success: false,
          message: `Experience system is already active for ${hoursRemaining} more hours.`,
          experience_gained: 0
        };
      }

      // Activate experience system for 24 hours
      const activationEnd = new Date(now.getTime() + 24 * 60 * 60 * 1000);

      // Update user data to activate experience system
      const { data: updateResult, error: updateError } = await supabase
        .from('users')
        .update({
          experience_active_until: activationEnd.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .select()
        .single();

      if (updateError) {
        console.error('Error activating experience system:', updateError);
        return {
          success: false,
          message: 'Failed to activate experience system',
          experience_gained: 0
        };
      }

      console.log('Successfully activated experience system:', updateResult);

      // Refresh user experience data
      const updatedExperience = await getUserExperience();
      setUserExperience(updatedExperience);

      return { 
        success: true, 
        message: 'Experience system activated! You can now earn XP from article likes and comments for the next 24 hours.',
        experience_gained: 0 
      };
    } catch (error) {
      console.error('Error activating experience system:', error);

      // Return a more detailed error message
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      return {
        success: false,
        message: errorMessage,
        experience_gained: 0
      };
    }
  };

  // Award experience for article interactions (likes)
  const awardArticleInteractionExperience = async (
    giverUserId: string,
    receiverUserId: string,
    articleId: number,
    interactionType: 'like' | 'unlike'
  ) => {
    try {
      const { data, error } = await supabase.rpc('award_interaction_experience', {
        p_giver_user_id: giverUserId,
        p_receiver_user_id: receiverUserId,
        p_article_id: articleId,
        p_interaction_type: interactionType
      });

      if (error) {
        console.error('Error awarding article interaction experience:', error);
        return { success: false, error };
      }

      console.log('Article interaction experience result:', data);
      return { success: true, data };
    } catch (error) {
      console.error('Error in awardArticleInteractionExperience:', error);
      return { success: false, error };
    }
  };

  // Award experience for comment interactions
  const awardCommentInteractionExperience = async (
    userId: string,
    commentId: number,
    commentAuthorId: string,
    interactionType: 'comment' | 'like' | 'unlike'
  ) => {
    try {
      const { data, error } = await supabase.rpc('award_experience_for_comment_interaction', {
        p_user_id: userId,
        p_comment_id: commentId,
        p_comment_author_id: commentAuthorId,
        p_interaction_type: interactionType
      });

      if (error) {
        console.error('Error awarding comment interaction experience:', error);
        return { success: false, error };
      }

      console.log('Comment interaction experience result:', data);
      return { success: true, data };
    } catch (error) {
      console.error('Error in awardCommentInteractionExperience:', error);
      return { success: false, error };
    }
  };

  const redirectToUpgrade = () => {
    router.push('/ctn/subscription');
  };

  const redirectToLogin = () => {
    router.push('/signin');
  };

  const refreshUserExperience = async () => {
    // Clear cache and fetch fresh data
    const cacheKey = 'crypto_talks_user_experience_cache';
    localStorage.removeItem(cacheKey);
    
    console.log('Refreshing user experience data');
    const experience = await getUserExperience();
    setUserExperience(experience);
    
    // Update cache with fresh data
    if (experience) {
      const { data: { session } } = await supabase.auth.getSession();
      const sessionId = session?.user?.id || 'anonymous';
      
      localStorage.setItem(cacheKey, JSON.stringify({
        data: experience,
        timestamp: Date.now(),
        sessionId
      }));
    }
  };

  useEffect(() => {
    const initializeUserData = async () => {
      setLoading(true);
      
      // Check if we have cached data first
      const cacheKey = 'crypto_talks_user_experience_cache';
      const cachedData = localStorage.getItem(cacheKey);
      
      if (cachedData) {
        try {
          const { data, timestamp, sessionId } = JSON.parse(cachedData);
          const cacheAge = Date.now() - timestamp;
          const cacheValid = cacheAge < 5 * 60 * 1000; // 5 minutes cache
          
          // Check if session is still valid
          const { data: { session } } = await supabase.auth.getSession();
          const currentSessionId = session?.user?.id || 'anonymous';
          
          if (cacheValid && sessionId === currentSessionId && data) {
            console.log('Using cached user experience data');
            setUserExperience(data);
            setLoading(false);
            return;
          }
        } catch (error) {
          console.warn('Failed to parse cached user experience:', error);
        }
      }

      // Fetch fresh data if no valid cache
      console.log('Fetching fresh user experience data');
      const experience = await getUserExperience();
      setUserExperience(experience);
      
      // Cache the data
      if (experience) {
        const { data: { session } } = await supabase.auth.getSession();
        const sessionId = session?.user?.id || 'anonymous';
        
        localStorage.setItem(cacheKey, JSON.stringify({
          data: experience,
          timestamp: Date.now(),
          sessionId
        }));
      }
      
      setLoading(false);
    };

    initializeUserData();

    // Listen for auth state changes and invalidate cache
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      if (event === 'SIGNED_IN' || event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
        console.log('Auth state changed, clearing cache:', event);
        localStorage.removeItem('crypto_talks_user_experience_cache');
        
        // Refresh data for signed in users
        if (event === 'SIGNED_IN' && session) {
          initializeUserData();
        } else if (event === 'SIGNED_OUT') {
          setUserExperience(null);
          setLoading(false);
        }
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return {
    loading,
    userExperience,
    checkTierAccess,
    processDailyClaim,
    awardArticleInteractionExperience,
    awardCommentInteractionExperience,
    getTierLevel,
    getTierName,
    redirectToUpgrade,
    redirectToLogin,
    refreshUserExperience
  };
}
