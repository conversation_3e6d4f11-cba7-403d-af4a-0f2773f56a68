// React hook for tab-aware on-chain data management
// Provides cached data for all tabs without refetching on tab switches

import React, { useState, useEffect, useCallback, useRef, createContext, useContext, ReactNode } from 'react';
import { onChainPrefetchService, OnChainDataBundle } from '@/utils/onchain-prefetch-service';

export interface OnChainDataState {
  isLoading: boolean;
  isError: boolean;
  error: string | null;
  data: OnChainDataBundle | null;
  lastUpdated: number;
  isStale: boolean;
}

export interface UseOnChainDataReturn extends OnChainDataState {
  // Data getters for specific tabs
  getChainData: (chainId: string) => any;
  getComparisonData: () => any;
  getDexScreenerData: () => any;
  getAvailableChains: () => any[];
  
  // Actions
  refresh: () => Promise<void>;
  prefetchIfNeeded: () => Promise<void>;
  
  // Status
  bundleStatus: ReturnType<typeof onChainPrefetchService.getBundleStatus>;
}

export function useOnChainData(autoRefresh = true, autoStart = true): UseOnChainDataReturn {
  const [state, setState] = useState<OnChainDataState>({
    isLoading: true,
    isError: false,
    error: null,
    data: null,
    lastUpdated: 0,
    isStale: false,
  });

  const refreshTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const mountedRef = useRef(true);

  // Initialize and prefetch data
  const initializeData = useCallback(async (forceRefresh = false) => {
    if (!mountedRef.current) return;

    setState(prev => ({ 
      ...prev, 
      isLoading: true, 
      isError: false, 
      error: null 
    }));

    try {
      console.log('🔄 Initializing on-chain data...');
      const bundle = await onChainPrefetchService.prefetchAllOnChainData(forceRefresh);
      
      if (mountedRef.current) {
        setState({
          isLoading: false,
          isError: false,
          error: null,
          data: bundle,
          lastUpdated: bundle.bundleTimestamp,
          isStale: false,
        });
        
        console.log('✅ On-chain data initialized successfully');
      }
    } catch (error) {
      console.error('❌ Failed to initialize on-chain data:', error);
      
      if (mountedRef.current) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          isError: true,
          error: error instanceof Error ? error.message : 'Unknown error',
        }));
      }
    }
  }, []);

  // Setup auto-refresh
  const setupAutoRefresh = useCallback(() => {
    if (!autoRefresh) return;

    // Clear existing timeout
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }

    // Set up next refresh (10 minutes)
    refreshTimeoutRef.current = setTimeout(() => {
      if (mountedRef.current) {
        console.log('🔄 Auto-refreshing on-chain data...');
        initializeData(true);
        setupAutoRefresh(); // Schedule next refresh
      }
    }, 10 * 60 * 1000); // 10 minutes

  }, [autoRefresh, initializeData]);

  // Manual refresh function
  const refresh = useCallback(async () => {
    console.log('🔄 Manual refresh triggered');
    await initializeData(true);
    setupAutoRefresh(); // Reset auto-refresh timer
  }, [initializeData, setupAutoRefresh]);

  // Prefetch if needed (lighter check)
  const prefetchIfNeeded = useCallback(async () => {
    const status = onChainPrefetchService.getBundleStatus();
    
    if (status.needsRefresh && !state.isLoading) {
      setState(prev => ({ ...prev, isStale: true }));
      await initializeData(false); // Don't force, let service decide
    }
  }, [initializeData, state.isLoading]);

  // Data getter functions (these don't trigger API calls)
  const getChainData = useCallback((chainId: string) => {
    if (!state.data?.chains) return null;
    return state.data.chains[chainId] || null;
  }, [state.data]);

  const getComparisonData = useCallback(() => {
    return state.data?.comparison || { results: [], insights: [], lastUpdated: 0 };
  }, [state.data]);

  const getDexScreenerData = useCallback(() => {
    return state.data?.dexScreener || { tokenProfiles: [], trendingPairs: [], lastUpdated: 0 };
  }, [state.data]);

  const getAvailableChains = useCallback(() => {
    return state.data?.general?.availableChains || [];
  }, [state.data]);

  // Initialize on mount (only if autoStart is true)
  useEffect(() => {
    mountedRef.current = true;

    if (autoStart) {
      initializeData(false);
      setupAutoRefresh();
    }

    return () => {
      mountedRef.current = false;
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [initializeData, setupAutoRefresh, autoStart]);

  // Get current bundle status
  const bundleStatus = onChainPrefetchService.getBundleStatus();

  return {
    ...state,
    getChainData,
    getComparisonData,
    getDexScreenerData,
    getAvailableChains,
    refresh,
    prefetchIfNeeded,
    bundleStatus,
  };
}

// Lazy version of useOnChainData that doesn't auto-start
export function useLazyOnChainData(isActive: boolean = false): UseOnChainDataReturn {
  return useOnChainData(isActive, isActive);
}

// Specialized hooks for specific data types
export function useChainMetrics(chainId: string, isActive: boolean = true) {
  const { getChainData, isLoading, isError, refresh } = useOnChainData(true, isActive);

  const chainData = getChainData(chainId);

  return {
    metrics: chainData?.metrics || null,
    derivedMetrics: chainData?.derivedMetrics || null,
    marketData: chainData?.marketData || null,
    lastUpdated: chainData?.lastUpdated || 0,
    isLoading,
    isError,
    refresh,
  };
}

// Lazy version of useChainMetrics
export function useLazyChainMetrics(chainId: string, isActive: boolean = false) {
  return useChainMetrics(chainId, isActive);
}

export function useChainComparison(isActive: boolean = true) {
  const { getComparisonData, isLoading, isError, refresh } = useOnChainData(true, isActive);

  const comparisonData = getComparisonData();

  return {
    results: comparisonData.results,
    insights: comparisonData.insights,
    lastUpdated: comparisonData.lastUpdated,
    isLoading,
    isError,
    refresh,
  };
}

export function useDexScreenerData(isActive: boolean = true) {
  const { getDexScreenerData, isLoading, isError, refresh } = useOnChainData(true, isActive);

  const dexData = getDexScreenerData();

  return {
    tokenProfiles: dexData.tokenProfiles,
    trendingPairs: dexData.trendingPairs,
    lastUpdated: dexData.lastUpdated,
    isLoading,
    isError,
    refresh,
  };
}

// Lazy versions
export function useLazyChainComparison(isActive: boolean = false) {
  return useChainComparison(isActive);
}

export function useLazyDexScreenerData(isActive: boolean = false) {
  return useDexScreenerData(isActive);
}

// Context provider for managing global on-chain data state
const OnChainDataContext = createContext<UseOnChainDataReturn | null>(null);

export function OnChainDataProvider({ children, autoStart = false }: { children: ReactNode; autoStart?: boolean }) {
  const onChainData = useOnChainData(true, autoStart);

  return React.createElement(
    OnChainDataContext.Provider,
    { value: onChainData },
    children
  );
}

export function useOnChainDataContext(): UseOnChainDataReturn {
  const context = useContext(OnChainDataContext);
  if (!context) {
    throw new Error('useOnChainDataContext must be used within OnChainDataProvider');
  }
  return context;
}

// Loading state hook for tab switches
export function useTabLoadingState() {
  const { isLoading, isStale, bundleStatus } = useOnChainDataContext();
  
  return {
    isTabLoading: isLoading || isStale,
    showSkeletons: isLoading && !bundleStatus.isLoaded,
    showStaleIndicator: isStale,
    lastUpdateTime: bundleStatus.lastUpdate,
  };
}